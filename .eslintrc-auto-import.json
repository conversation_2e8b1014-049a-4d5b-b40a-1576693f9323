{"globals": {"$t": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "Debounce": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "addClass": true, "areaNameList": true, "bigNumberTransform": true, "checkFileUrl": true, "checkIDCard": true, "clearLoginInfo": true, "computed": true, "configDefInfo": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "encrypt": true, "flatten": true, "formatConfigInfo": true, "formatPrice": true, "getCurrentInstance": true, "getCurrentScope": true, "getDateTimeRange": true, "getLevels": true, "getParseTime": true, "getUUID": true, "h": true, "hasClass": true, "http": true, "idList": true, "inject": true, "isAuth": true, "isCreditCode": true, "isEmail": true, "isHtmlNull": true, "isMobile": true, "isPhone": true, "isProxy": true, "isQq": true, "isReactive": true, "isReadonly": true, "isRef": true, "isURL": true, "isUserName": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "removeClass": true, "removeHeadAndTailSpaces": true, "resolveComponent": true, "setDialogWidth": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "treeDataTranslate": true, "triggerRef": true, "unref": true, "uploadFile": true, "useAllinpayStore": true, "useAttrs": true, "useCommonStore": true, "useCssModule": true, "useCssVars": true, "useLanguageStore": true, "useLink": true, "useProdStore": true, "useRoute": true, "useRouter": true, "useRouterStore": true, "useSlots": true, "useUserStore": true, "useWebConfigStore": true, "validEmail": true, "validHtmlLength": true, "validNoEmptySpace": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "widthChange": true, "validPassword": true, "isPhoneStar": true, "isStarPhone": true, "DirectiveBinding": true, "MaybeRef": true, "MaybeRefOrGetter": true, "checkEmail": true, "onWatcherCleanup": true, "setSkuStock": true, "useId": true, "useModel": true, "useTemplateRef": true}}