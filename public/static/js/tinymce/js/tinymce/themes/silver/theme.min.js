/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.10.1 (2021-11-03)
 */
!function(){"use strict";function t(o){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===o;var n,e}}function n(n){return function(t){return typeof t===n}}function e(n){return function(t){return n===t}}function g(t){return null==t}function f(t,n){if(c(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return;return 1}}function st(){}function r(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}}function rt(t){return function(){return t}}function h(t){return t}function v(t,n){return t===n}var y=t("string"),x=t("object"),c=t("array"),b=e(null),w=n("boolean"),E=e(void 0),k=function(t){return!g(t)},S=n("function"),u=n("number");function C(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}function O(n){return function(t){return!n(t)}}function _(t){return function(){throw new Error(t)}}var T=rt(!1),D=rt(!0),o=tinymce.util.Tools.resolve("tinymce.ThemeManager"),lt=function(){return(lt=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function B(t,n){var e={};for(r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(e[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(e[r[o]]=t[r[o]]);return e}function H(t,n,e){if(e||2===arguments.length)for(var o,r=0,i=n.length;r<i;r++)!o&&r in n||((o=o||Array.prototype.slice.call(n,0,r))[r]=n[r]);return t.concat(o||Array.prototype.slice.call(n))}function i(){return a}var a={fold:function(t,n){return t()},isSome:T,isNone:D,getOr:h,getOrThunk:s,getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:rt(null),getOrUndefined:rt(void 0),or:h,orThunk:s,map:i,each:st,bind:i,exists:T,forall:D,filter:function(){return a},toArray:function(){return[]},toString:rt("none()")};function s(t){return t()}function l(t,n){return yt.call(t,n)}function d(t,n){for(var e=0,o=t.length;e<o;e++)if(n(t[e],e))return!0;return!1}function m(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e}function p(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=bt.call(t,o,o+n);e.push(r)}return e}function M(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o}function A(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r)?e:o).push(u)}return{pass:e,fail:o}}function F(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e}function I(t,o,r){return function(t){for(var n,e=t.length-1;0<=e;e--)n=t[e],r=o(r,n,e)}(t),r}function R(t,e,o){return St(t,function(t,n){o=e(o,t,n)}),o}function V(t,n){return function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return vt.some(i);if(e(i,o))break}return vt.none()}(t,n,T)}function P(t,n){for(var e=0,o=t.length;e<o;e++)if(n(t[e],e))return vt.some(e);return vt.none()}function ft(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!c(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);xt.apply(n,t[e])}return n}function z(t,n){return ft(M(t,n))}function N(t,n){for(var e=0,o=t.length;e<o;++e)if(!0!==n(t[e],e))return!1;return!0}function L(t){var n=bt.call(t,0);return n.reverse(),n}function W(t,n){return F(t,function(t){return!wt(n,t)})}function U(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e}function j(t){return[t]}function G(t,n){var e=bt.call(t,0);return e.sort(n),e}function X(t,n){return 0<=n&&n<t.length?vt.some(t[n]):vt.none()}function Y(t){return X(t,0)}function q(t){return X(t,t.length-1)}function K(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return vt.none()}function J(t,n){for(var e=Ct(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i)}}function dt(t,e){return _t(t,function(t,n){return{k:n,v:e(t,n)}})}function $(t,e){var o=[];return J(t,function(t,n){o.push(e(t,n))}),o}function Q(t,n){for(var e=Ct(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return vt.some(u)}return vt.none()}function Z(t){return $(t,h)}function tt(t,n){return Tt(t,n)?vt.from(t[n]):vt.none()}function nt(t,n){return Tt(t,n)&&void 0!==t[n]&&null!==t[n]}function mt(t,n,e){return void 0===e&&(e=v),t.exists(function(t){return e(t,n)})}function et(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n}function ot(t,n){return t?vt.some(n):vt.none()}function it(t,n,e){return""===n||t.length>=n.length&&t.substr(e,e+n.length)===n}function ut(t,n){return-1!==t.indexOf(n)}function at(t){return 0<t.length}function ct(t){return void 0!==t.style&&S(t.style.getPropertyValue)}function gt(t){if(null==t)throw new Error("Node cannot be null or undefined");return{dom:t}}var pt,ht=function(e){function t(){return r}function n(t){return t(e)}var o=rt(e),r={fold:function(t,n){return n(e)},isSome:D,isNone:T,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:t,orThunk:t,map:function(t){return ht(t(e))},each:function(t){t(e)},bind:n,exists:n,forall:n,filter:function(t){return t(e)?r:a},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return r},vt={some:ht,none:i,from:function(t){return null==t?a:ht(t)}},bt=Array.prototype.slice,yt=Array.prototype.indexOf,xt=Array.prototype.push,wt=function(t,n){return-1<l(t,n)},St=function(t,n){for(var e=0,o=t.length;e<o;e++)n(t[e],e)},kt=S(Array.from)?Array.from:function(t){return bt.call(t)},Ct=Object.keys,Ot=Object.hasOwnProperty,_t=function(t,o){var r={};return J(t,function(t,n){var e=o(t,n);r[e.k]=e.v}),r},Tt=function(t,n){return Ot.call(t,n)},Et=function(t,n,e){return t.isSome()&&n.isSome()?vt.some(e(t.getOrDie(),n.getOrDie())):vt.none()},Dt=function(t,n){return it(t,n,0)},Bt=function(t,n){return it(t,n,t.length-n.length)},Mt=(pt=/^\s+|\s+$/g,function(t){return t.replace(pt,"")}),At={fromHtml:function(t,n){var e=(n||document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return gt(e.childNodes[0])},fromTag:function(t,n){var e=(n||document).createElement(t);return gt(e)},fromText:function(t,n){var e=(n||document).createTextNode(t);return gt(e)},fromDom:gt,fromPoint:function(t,n,e){return vt.from(t.dom.elementFromPoint(n,e)).map(gt)}};function Ft(t){return t.dom.nodeName.toLowerCase()}function It(n){return function(t){return t.dom.nodeType===n}}function Rt(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}}function Vt(t,n){var e=String(n).toLowerCase();return V(t,function(t){return t.search(e)})}function Pt(n){return function(t){return ut(t,n)}}function Ht(t){return window.matchMedia(t).matches}function zt(t,n){var e=t.dom;if(1!==e.nodeType)return!1;var o=e;if(void 0!==o.matches)return o.matches(n);if(void 0!==o.msMatchesSelector)return o.msMatchesSelector(n);if(void 0!==o.webkitMatchesSelector)return o.webkitMatchesSelector(n);if(void 0!==o.mozMatchesSelector)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")}function Nt(t){return 1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount}function Lt(t,n){return t.dom===n.dom}function Wt(t,n){return le().browser.isIE()?(e=t.dom,o=n.dom,r=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(e.compareDocumentPosition(o)&r)):(i=t.dom)!==(u=n.dom)&&i.contains(u);var e,o,r,i,u}function Ut(t){return At.fromDom(t.dom.ownerDocument)}function jt(t){return qn(t)?t:Ut(t)}function Gt(t){return At.fromDom(jt(t).dom.documentElement)}function Xt(t){return At.fromDom(jt(t).dom.defaultView)}function Yt(t){return vt.from(t.dom.parentNode).map(At.fromDom)}function qt(t){return vt.from(t.dom.offsetParent).map(At.fromDom)}function Kt(t){return M(t.dom.childNodes,At.fromDom)}function Jt(t,n){var e=t.dom.childNodes;return vt.from(e[n]).map(At.fromDom)}function $t(t,n){return{element:t,offset:n}}function Qt(t,n){var e=Kt(t);return 0<e.length&&n<e.length?$t(e[n],0):$t(t,n)}function Zt(t){return Kn(t)&&k(t.dom.host)}function tn(t){return Zt(t)?t:At.fromDom(jt(t).dom.body)}function nn(t){return At.fromDom(t.dom.host)}function en(t,n,e){if(!(y(e)||w(e)||u(e)))throw console.error("Invalid call to Attribute.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")}function on(t,n,e){en(t.dom,n,e)}function rn(t,n){var e=t.dom;J(n,function(t,n){en(e,n,t)})}function un(t,n){var e=t.dom.getAttribute(n);return null===e?void 0:e}function an(t,n){return vt.from(un(t,n))}function cn(t,n){var e=t.dom;return!(!e||!e.hasAttribute)&&e.hasAttribute(n)}function sn(t,n){t.dom.removeAttribute(n)}function ln(t,n,e){if(!y(e))throw console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);ct(t)&&t.style.setProperty(n,e)}function fn(t,n){ct(t)&&t.style.removeProperty(n)}function dn(t,n,e){ln(t.dom,n,e)}function mn(t,n){var e=t.dom;J(n,function(t,n){ln(e,n,t)})}function gn(t,n){var e=t.dom;J(n,function(t,n){t.fold(function(){fn(e,n)},function(t){ln(e,n,t)})})}function pn(t,n){var e=t.dom,o=window.getComputedStyle(e).getPropertyValue(n);return""!==o||ve(t)?o:xe(e,n)}function hn(t,n){var e=t.dom,o=xe(e,n);return vt.from(o).filter(function(t){return 0<t.length})}function vn(t,n,e){var o=At.fromTag(t);return dn(o,n,e),hn(o,n).isSome()}function bn(t,n){fn(t.dom,n),mt(an(t,"style").map(Mt),"")&&sn(t,"style")}function yn(t){return t.dom.offsetWidth}function xn(o,r){function t(t){var n=r(t);if(n<=0||null===n){var e=pn(t,o);return parseFloat(e)||0}return n}function i(r,t){return R(t,function(t,n){var e=pn(r,n),o=void 0===e?0:parseInt(e,10);return isNaN(o)?t:t+o},0)}return{set:function(t,n){if(!u(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom;ct(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}}function wn(t){return we.get(t)}function Sn(t){return we.getOuter(t)}function kn(t,n){return void 0!==t?t:void 0!==n?n:0}function Cn(t){var n=t.dom.ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom)return ke(e.offsetLeft,e.offsetTop);var i=kn(null==o?void 0:o.pageYOffset,r.scrollTop),u=kn(null==o?void 0:o.pageXOffset,r.scrollLeft),a=kn(r.clientTop,e.clientTop),c=kn(r.clientLeft,e.clientLeft);return Ce(t).translate(u-c,i-a)}function On(t){return Oe.get(t)}function _n(t){return Oe.getOuter(t)}function Tn(t){function n(){return t.stopPropagation()}function e(){return t.preventDefault()}var o=r(e,n);return{target:At.fromDom(function(t){if(me()&&k(t.target)){var n=At.fromDom(t.target);if(Xn(n)&&he(n)&&t.composed&&t.composedPath){var e=t.composedPath();if(e)return Y(e)}}return vt.from(t.target)}(t).getOr(t.target)),x:t.clientX,y:t.clientY,stop:n,prevent:e,kill:o,raw:t}}function En(t,n,e,o,r){var i,u,a=(i=e,u=o,function(t){i(t)&&u(Tn(t))});return t.dom.addEventListener(n,a,r),{unbind:C(_e,t,n,a,r)}}function Dn(n,e){Yt(n).each(function(t){t.dom.insertBefore(e.dom,n.dom)})}function Bn(t,n){vt.from(t.dom.nextSibling).map(At.fromDom).fold(function(){Yt(t).each(function(t){Te(t,n)})},function(t){Dn(t,n)})}function Mn(n,e){Jt(n,0).fold(function(){Te(n,e)},function(t){n.dom.insertBefore(e.dom,t.dom)})}function An(n,t){St(t,function(t){Te(n,t)})}function Fn(t){t.dom.textContent="",St(Kt(t),function(t){Ee(t)})}function In(t){var n,e=Kt(t);0<e.length&&(n=t,St(e,function(t){Dn(n,t)})),Ee(t)}function Rn(t){var n=void 0!==t?t.dom:document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return ke(e,o)}function Vn(t,n,e){var o=(void 0!==e?e.dom:document).defaultView;o&&o.scrollTo(t,n)}function Pn(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}}function Hn(t){var o=void 0===t?window:t,n=o.document,r=Rn(At.fromDom(n)),e=void 0===o?window:o;return(le().browser.isFirefox()?vt.none():vt.from(e.visualViewport)).fold(function(){var t=o.document.documentElement,n=t.clientWidth,e=t.clientHeight;return Pn(r.left,r.top,n,e)},function(t){return Pn(Math.max(t.pageLeft,r.left),Math.max(t.pageTop,r.top),t.width,t.height)})}function zn(o){var t,r=Rn(At.fromDom(document)),n=(t=Be).owner(o),e=De(t,n);return vt.some(e).fold(C(Cn,o),function(t){var n=Ce(o),e=I(t,function(t,n){var e=Ce(n);return{left:t.left+e.left,top:t.top+e.top}},{left:0,top:0});return ke(e.left+n.left+r.left,e.top+n.top+r.top)})}function Nn(t){var n=zn(t),e=_n(t),o=Sn(t);return Me(n.left,n.top,e,o)}"undefined"!=typeof window||Function("return this;")();function Ln(){return Jn(0,0)}function Wn(t){function n(t){return function(){return e===t}}var e=t.current,o=t.version;return{current:e,version:o,isEdge:n("Edge"),isChrome:n("Chrome"),isIE:n("IE"),isOpera:n("Opera"),isFirefox:n(te),isSafari:n("Safari")}}function Un(t){function n(t){return function(){return e===t}}var e=t.current,o=t.version;return{current:e,version:o,isWindows:n(oe),isiOS:n("iOS"),isAndroid:n(re),isOSX:n("OSX"),isLinux:n("Linux"),isSolaris:n(ie),isFreeBSD:n(ue),isChromeOS:n(ae)}}var jn,Gn,Xn=It(1),Yn=It(3),qn=It(9),Kn=It(11),Jn=function(t,n){return{major:t,minor:n}},$n={nu:Jn,detect:function(t,n){var e,o,r=String(n).toLowerCase();return 0===t.length?Ln():(o=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}}(t,e=r))?Jn(i(1),i(2)):{major:0,minor:0};function i(t){return Number(e.replace(o,"$"+t))}},unknown:Ln},Qn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Zn={browsers:rt([{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return ut(t,"edge/")&&ut(t,"chrome")&&ut(t,"safari")&&ut(t,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qn],search:function(t){return ut(t,"chrome")&&!ut(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return ut(t,"msie")||ut(t,"trident")}},{name:"Opera",versionRegexes:[Qn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Pt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Pt("firefox")},{name:"Safari",versionRegexes:[Qn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(ut(t,"safari")||ut(t,"mobile/"))&&ut(t,"applewebkit")}}]),oses:rt([{name:"Windows",search:Pt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return ut(t,"iphone")||ut(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Pt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Pt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Pt("linux"),versionRegexes:[]},{name:"Solaris",search:Pt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Pt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Pt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}])},te="Firefox",ne=function(){return Wn({current:void 0,version:$n.unknown()})},ee=Wn,oe=(rt("Edge"),rt("Chrome"),rt("IE"),rt("Opera"),rt(te),rt("Safari"),"Windows"),re="Android",ie="Solaris",ue="FreeBSD",ae="ChromeOS",ce=function(){return Un({current:void 0,version:$n.unknown()})},se=Un,le=(rt(oe),rt("iOS"),rt(re),rt("Linux"),rt("OSX"),rt(ie),rt(ue),rt(ae),Rt(function(){return t=navigator.userAgent,n=vt.from(navigator.userAgentData),e=Ht,p=Zn.browsers(),h=Zn.oses(),v=n.bind(function(t){return o=p,K(t.brands,function(n){var e=n.brand.toLowerCase();return V(o,function(t){var n;return e===(null===(n=t.brand)||void 0===n?void 0:n.toLowerCase())}).map(function(t){return{current:t.name,version:$n.nu(parseInt(n.version,10),0)}})});var o}).orThunk(function(){return Vt(p,e=t).map(function(t){var n=$n.detect(t.versionRegexes,e);return{current:t.name,version:n}});var e}).fold(ne,ee),b=Vt(h,o=t).map(function(t){var n=$n.detect(t.versionRegexes,o);return{current:t.name,version:n}}).fold(ce,se),{browser:v,os:b,deviceType:(i=v,u=t,a=e,c=(r=b).isiOS()&&!0===/ipad/i.test(u),s=r.isiOS()&&!c,f=(l=r.isiOS()||r.isAndroid())||a("(pointer:coarse)"),d=c||!s&&l&&a("(min-device-width:768px)"),m=s||l&&!d,g=i.isSafari()&&r.isiOS()&&!1===/safari/i.test(u),{isiPad:rt(c),isiPhone:rt(s),isTablet:rt(d),isPhone:rt(m),isTouch:rt(f),isAndroid:r.isAndroid,isiOS:r.isiOS,isWebView:rt(g),isDesktop:rt(!m&&!d&&!g)})};var t,n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b})),fe=Yt,de=S(Element.prototype.attachShadow)&&S(Node.prototype.getRootNode),me=rt(de),ge=de?function(t){return At.fromDom(t.dom.getRootNode())}:jt,pe=function(t){var n=ge(t);return Zt(n)?vt.some(n):vt.none()},he=function(t){return k(t.dom.shadowRoot)},ve=function(t){var n=Yn(t)?t.dom.parentNode:t.dom;if(null==n||null===n.ownerDocument)return!1;var e,o,r=n.ownerDocument;return pe(At.fromDom(n)).fold(function(){return r.body.contains(n)},(e=ve,o=nn,function(t){return e(o(t))}))},be=function(){return ye(At.fromDom(document))},ye=function(t){var n=t.dom.body;if(null==n)throw new Error("Body is not available yet");return At.fromDom(n)},xe=function(t,n){return ct(t)?t.style.getPropertyValue(n):""},we=xn("height",function(t){var n=t.dom;return ve(t)?n.getBoundingClientRect().height:n.offsetHeight}),Se=function(e,o){return{left:e,top:o,translate:function(t,n){return Se(e+t,o+n)}}},ke=Se,Ce=function(t){var n,e=t.dom,o=e.ownerDocument.body;return o===e?ke(o.offsetLeft,o.offsetTop):ve(t)?(n=e.getBoundingClientRect(),ke(n.left,n.top)):ke(0,0)},Oe=xn("width",function(t){return t.dom.offsetWidth}),_e=function(t,n,e,o){t.dom.removeEventListener(n,e,o)},Te=function(t,n){t.dom.appendChild(n.dom)},Ee=function(t){var n=t.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},De=function(o,t){return o.view(t).fold(rt([]),function(t){var n=o.owner(t),e=De(o,n);return[t].concat(e)})},Be=Object.freeze({__proto__:null,view:function(t){var n;return(t.dom===document?vt.none():vt.from(null===(n=t.dom.defaultView)||void 0===n?void 0:n.frameElement)).map(At.fromDom)},owner:Ut}),Me=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Ae=function(t){var n=Cn(t),e=_n(t),o=Sn(t);return Me(n.left,n.top,e,o)},Fe=function(){return Hn(window)},Ie=function(e){return{isValue:D,isError:T,getOr:rt(e),getOrThunk:rt(e),getOrDie:rt(e),or:function(t){return Ie(e)},orThunk:function(t){return Ie(e)},fold:function(t,n){return n(e)},map:function(t){return Ie(t(e))},mapError:function(t){return Ie(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOptional:function(){return vt.some(e)}}},Re=function(e){return{isValue:T,isError:D,getOr:h,getOrThunk:function(t){return t()},getOrDie:function(){return _(String(e))()},or:h,orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return Re(e)},mapError:function(t){return Re(t(e))},each:st,bind:function(t){return Re(e)},exists:T,forall:D,toOptional:vt.none}},Ve={value:Ie,error:Re,fromOption:function(t,n){return t.fold(function(){return Re(n)},Ie)}};function Pe(t,n,e){return t.stype===jn.Error?n(t.serror):e(t.svalue)}function He(t){return{stype:jn.Value,svalue:t}}function ze(t){return{stype:jn.Error,serror:t}}function Ne(t,n,e,o){return{tag:"field",key:t,newKey:n,presence:e,prop:o}}function Le(t,n,e){switch(t.tag){case"field":return n(t.key,t.newKey,t.presence,t.prop);case"custom":return e(t.newKey,t.instantiator)}}function We(u){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r,i=t[o];for(r in i)Tt(i,r)&&(e[r]=u(e[r],i[r]))}return e}}function Ue(){return{tag:"required",process:{}}}function je(t){return{tag:"defaultedThunk",process:t}}function Ge(t){return je(rt(t))}function Xe(){return{tag:"option",process:{}}}function Ye(t){return{tag:"mergeWithThunk",process:t}}function qe(t){return x(t)&&100<Ct(t).length?" removed due to size":JSON.stringify(t,null,2)}function Ke(t,n){return Lo([{path:t,getErrorInfo:n}])}function Je(e){return{extract:function(n,t){return Uo(e(t),function(t){return Ke(n,rt(t))})},toString:rt("val")}}function $e(t,n,e,o){return o(tt(t,n).getOrThunk(function(){return e(t)}))}function Qe(n,e,o,r,i){function u(t){return i.extract(e.concat([r]),t)}function t(t){return t.fold(function(){return No(vt.none())},function(t){var n=i.extract(e.concat([r]),t);return jo(n,vt.some)})}var a,c,s,l,f,d;switch(n.tag){case"required":return s=e,d=u,tt(l=o,f=r).fold(function(){return t=f,n=l,Ke(s,function(){return'Could not find valid *required* value for "'+t+'" in '+qe(n)});var t,n},d);case"defaultedThunk":return $e(o,r,n.process,u);case"option":return t(tt(o,r));case"defaultedOptionThunk":return c=n.process,t(tt(a=o,r).map(function(t){return!0===t?c(a):t}));case"mergeWithThunk":return $e(o,r,rt({}),function(t){return u(Yo(n.process(o),t))})}}function Ze(e){return{extract:function(t,n){return e().extract(t,n)},toString:function(){return e().toString()}}}function to(t){var s=$o(t),l=I(t,function(e,t){return Le(t,function(t){var n;return Yo(e,((n={})[t]=!0,n))},rt(e))},{});return{extract:function(t,n){var e,o,r,i,u,a,c=F(w(n)?[]:Ct((r=k,i=o={},u=function(t,n){i[n]=t},a=st,J(n,function(t,n){(r(t,n)?u:a)(t,n)}),o)),function(t){return!nt(l,t)});return 0===c.length?s.extract(t,n):(e=c,Ke(t,function(){return"There are unsupported fields: ["+e.join(", ")+"] specified"}))},toString:s.toString}}function no(o){return{extract:function(e,t){var n=M(t,function(t,n){return o.extract(e.concat(["["+n+"]"]),t)});return Ko(n)},toString:function(){return"array("+o.toString()+")"}}}function eo(u){return{extract:function(t,n){for(var e=[],o=0,r=u;o<r.length;o++){var i=r[o].extract(t,n);if(i.stype===jn.Value)return i;e.push(i)}return Ko(e)},toString:function(){return"oneOf("+M(u,function(t){return t.toString()}).join(", ")+")"}}}function oo(e,o){return Je(function(t){var n=typeof t;return e(t)?No(t):Lo("Expected type: "+o+" but got: "+n)})}function ro(n,a){return{extract:function(i,u){return tt(u,n).fold(function(){return t=n,Ke(i,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return e=i,n=u,tt(o=a,r=t).fold(function(){return t=o,n=r,Ke(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+qe(t)});var t,n},function(t){return t.extract(e.concat(["branch: "+r]),n)});var e,n,o,r})},toString:function(){return"chooseOn("+n+"). Possible values: "+Ct(a)}}}function io(n){return Je(function(t){return n(t).fold(Lo,No)})}function uo(n,t){return r=function(t){return n(t).fold(ze,He)},i=t,{extract:function(e,o){var t=Ct(o),n=no(Je(r)).extract(e,t);return Wo(n,function(t){var n=M(t,function(t){return Ne(t,t,Ue(),i)});return $o(n).extract(e,o)})},toString:function(){return"setOf("+i.toString()+")"}};var r,i}function ao(t,n,e){return zo((r=n.extract([t],o=e),Go(r,function(t){return{input:o,errors:t}})));var o,r}function co(t){return t.fold(function(t){throw new Error(ur(t))},h)}function so(t,n,e){return co(ao(t,n,e))}function lo(t,n){return ro(t,dt(n,$o))}function fo(n){return io(function(t){return wt(n,t)?Ve.value(t):Ve.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})}function mo(t){return ar(t,t,Ue(),Zo())}function go(t,n){return ar(t,t,Ue(),n)}function po(t){return go(t,nr)}function ho(t,n){return ar(t,t,Ue(),fo(n))}function vo(t){return go(t,or)}function bo(t,n){return ar(t,t,Ue(),$o(n))}function yo(t,n){return ar(t,t,Ue(),Qo(n))}function xo(t,n){return ar(t,t,Ue(),no(n))}function wo(t){return ar(t,t,Xe(),Zo())}function So(t,n){return ar(t,t,Xe(),n)}function ko(t){return So(t,tr)}function Co(t){return So(t,nr)}function Oo(t){return So(t,or)}function _o(t,n){return So(t,no(n))}function To(t,n){return So(t,$o(n))}function Eo(t,n){return ar(t,t,Ge(n),Zo())}function Do(t,n,e){return ar(t,t,Ge(n),e)}function Bo(t,n){return Do(t,n,tr)}function Mo(t,n){return Do(t,n,nr)}function Ao(t,n,e){return Do(t,n,fo(e))}function Fo(t,n){return Do(t,n,er)}function Io(t,n){return Do(t,n,or)}function Ro(t,n,e){return Do(t,n,no(e))}function Vo(t,n,e){return Do(t,n,$o(e))}function Po(t){var n=t;return{get:function(){return n},set:function(t){n=t}}}(Gn=jn={})[Gn.Error=0]="Error",Gn[Gn.Value=1]="Value";function Ho(u){if(!c(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return St(u,function(t,o){var n=Ct(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(void 0!==e[r])throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!c(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);return{fold:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(t.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+t.length);return t[o].apply(null,e)},match:function(t){var n=Ct(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!N(a,function(t){return wt(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){console.log(t,{constructors:a,constructor:r,params:e})}}}}),e}var zo=function(t){return Pe(t,Ve.error,Ve.value)},No=He,Lo=ze,Wo=function(t,n){return t.stype===jn.Value?n(t.svalue):t},Uo=function(t,n){return t.stype===jn.Error?n(t.serror):t},jo=function(t,n){return t.stype===jn.Value?{stype:jn.Value,svalue:n(t.svalue)}:t},Go=function(t,n){return t.stype===jn.Error?{stype:jn.Error,serror:n(t.serror)}:t},Xo=Pe,Yo=We(function(t,n){return x(t)&&x(n)?Yo(t,n):n}),qo=We(function(t,n){return n}),Ko=function(t){var n,e,o=(n=[],e=[],St(t,function(t){Pe(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e});return 0<o.errors.length?r(Lo,ft)(o.errors):No(o.values)},Jo=Je(No),$o=function(e){return{extract:function(i,u){for(var a={},c=[],t=0,n=e;t<n.length;t++)Le(n[t],function(t,n,e,o){var r=Qe(e,i,u,t,o);Xo(r,function(t){c.push.apply(c,t)},function(t){a[n]=t})},function(t,n){a[t]=n(u)});return 0<c.length?Lo(c):No(a)},toString:function(){return"obj{\n"+M(e,function(t){return Le(t,function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"}}},Qo=r(no,$o),Zo=rt(Jo),tr=oo(u,"number"),nr=oo(y,"string"),er=oo(w,"boolean"),or=oo(S,"function"),rr=function(n){if(Object(n)!==n)return!0;switch({}.toString.call(n).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(n).every(function(t){return rr(n[t])});default:return!1}},ir=Je(function(t){return rr(t)?No(t):Lo("Expected value to be acceptable for sending via postMessage")}),ur=function(t){return"Errors: \n"+M(10<(n=t.errors).length?n.slice(0,10).concat([{path:[],getErrorInfo:rt("... (only showing first ten failures)")}]):n,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n")+"\n\nInput object: "+qe(t.input);var n},ar=Ne,cr=function(t,n){return{tag:"custom",newKey:t,instantiator:n}};function sr(t,n){return(e={})[t]=n,e;var e}function lr(t){return n={},St(t,function(t){n[t.key]=t.value}),n;var n}function fr(t){return S(t)?t:T}function dr(t,n,e){for(var o=t.dom,r=fr(e);o.parentNode;){var o=o.parentNode,i=At.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return vt.none()}function mr(t,n,e){var o=n(t),r=fr(e);return o.orThunk(function(){return r(t)?vt.none():dr(t,n,r)})}function gr(t,n){return Lt(t.element,n.event.target)}function pr(t){if(!nt(t,"can")&&!nt(t,"abort")&&!nt(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return lt(lt({},ai),t)}function hr(t){return rt("alloy."+t)}function vr(t,n){nu(t,t.element,n,{})}function br(t,n,e){nu(t,t.element,n,e)}function yr(t){vr(t,Ii())}function xr(t,n,e){nu(t,n,e,{})}function wr(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event)}function Sr(t,n){return{key:t,value:pr({abort:n})}}function kr(t){return{key:t,value:pr({run:function(t,n){n.event.prevent()}})}}function Cr(t,n){return{key:t,value:pr({run:n})}}function Or(t,e,o){return{key:t,value:pr({run:function(t,n){e.apply(void 0,[t,n].concat(o))}})}}function _r(t){return function(e){return{key:t,value:pr({run:function(t,n){gr(t,n)&&e(t,n)}})}}}function Tr(t,n,e){var o,r=n.partUids[e];return Cr(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){wr(t,t.element,o,n)})})}function Er(t,r){return Cr(t,function(n,t){var e=t.event,o=n.getSystem().getByDom(e.target).getOrThunk(function(){return mr(e.target,function(t){return n.getSystem().getByDom(t).toOptional()},T).getOr(n)});r(n,o,t)})}function Dr(t){return Cr(t,function(t,n){n.cut()})}function Br(t,n){return _r(t)(n)}function Mr(t){return t.dom.innerHTML}function Ar(t,n){var e,o,r=Ut(t).dom,i=At.fromDom(r.createDocumentFragment());An(i,(e=n,(o=(r||document).createElement("div")).innerHTML=e,Kt(At.fromDom(o)))),Fn(t),Te(t,i)}function Fr(t){if(Zt(t))return"#shadow-root";var n=At.fromDom(t.dom.cloneNode(!1)),e=At.fromTag("div"),o=At.fromDom(n.dom.cloneNode(!0));return Te(e,o),Mr(e)}function Ir(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++su+String(n)}function Rr(t){var n=Xn(t)?t.dom[mu]:null;return vt.from(n)}function Vr(n){function e(t){return"The component must be in a context to execute: "+t+(n?"\n"+Fr(n().element)+" is not in context.":"")}function t(t){return function(){throw new Error(e(t))}}function o(t){return function(){console.warn(e(t))}}return{debugInfo:rt("fake"),triggerEvent:o("triggerEvent"),triggerFocus:o("triggerFocus"),triggerEscape:o("triggerEscape"),broadcast:o("broadcast"),broadcastOn:o("broadcastOn"),broadcastEvent:o("broadcastEvent"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),isConnected:T}}function Pr(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:bu(i)}},t}function Hr(t){return sr(yu,t)}function zr(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(void 0,H([t.getApis(),t],n,!1))},e=(n=o.toString()).indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:bu(i.slice(1))}},t;var t,n,e,r,i}function Nr(t,r){var i={};return J(t,function(t,o){J(t,function(t,n){var e=tt(i,n).getOr([]);i[n]=e.concat([r(o,t)])})}),i}function Lr(t){return{classes:E(t.classes)?[]:t.classes,attributes:E(t.attributes)?{}:t.attributes,styles:E(t.styles)?{}:t.styles}}function Wr(t){return t.cHandler}function Ur(t,n){return{name:t,handler:n}}function jr(t,n,e){var o=n[e];return o?function(u,t,a){try{var n=G(t,function(t,n){var e=t.name,o=n.name,r=a.indexOf(e),i=a.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(a,null,2));return r<i?-1:i<r?1:0});return Ve.value(n)}catch(t){return Ve.error([t])}}("Event: "+e,t,o).map(function(t){var e,n,o,r,i=M(t,function(t){return t.handler});return{can:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return R(n,function(t,n){return t&&n.can.apply(void 0,e)},!0)},abort:(o=n=e=i,r=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return R(o,function(t,n){return t||r(n).apply(void 0,e)},!1)}),run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];St(e,function(t){t.run.apply(void 0,n)})}}}):Ve.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(M(t,function(t){return t.name}),null,2)])}function Gr(t,n){var e=un(t,n);return void 0===e||""===e?[]:e.split(" ")}function Xr(t){return void 0!==t.dom.classList}function Yr(t,n){var e,o,r;Xr(t)?t.dom.classList.add(n):(o=n,r=Gr(e=t,"class").concat([o]),on(e,"class",r.join(" ")))}function qr(t,n){var e,o,r,i;Xr(t)?t.dom.classList.remove(n):(r=n,0<(i=F(Gr(o=t,"class"),function(t){return t!==r})).length?on(o,"class",i.join(" ")):sn(o,"class")),0===(Xr(e=t)?e.dom.classList:Gr(e,"class")).length&&sn(e,"class")}function Kr(t,n){return Xr(t)&&t.dom.classList.contains(n)}function Jr(n,t){St(t,function(t){Yr(n,t)})}function $r(n,t){St(t,function(t){qr(n,t)})}function Qr(t){return t.dom.value}function Zr(t,n){if(void 0===n)throw new Error("Value.set was undefined");t.dom.value=n}function ti(t){var e,n,o,r,i=(e=tt(t,"behaviours").getOr({}),z(Ct(e),function(t){var n=e[t];return k(n)?[n.me]:[]}));return n=t,r=M(o=i,function(t){return To(t.name(),[mo("config"),Eo("state",xu)])}),{list:o,data:dt(ao("component.behaviours",$o(r),n.behaviours).fold(function(t){throw new Error(ur(t)+"\nComplete spec:\n"+JSON.stringify(n,null,2))},h),function(t){return rt(t.map(function(t){return{config:t.config,state:t.state.init(t.config)}}))})}}function ni(t,n,e){var o,r,i=lt(lt({},(o=t).dom),{uid:o.uid,domChildren:M(o.components,function(t){return t.element})}),u=t.domModification.fold(function(){return Lr({})},Lr),a=0<n.length?function(n,t,e,o){var r=lt({},t);function i(t){return I(t,function(t,n){return lt(lt({},n.modification),t)},{})}St(e,function(t){r[t.name()]=t.exhibit(n,o)});var u=Nr(r,function(t,n){return{name:t,modification:n}});return Lr({classes:I(u.classes,function(t,n){return n.modification.concat(t)},[]),attributes:i(u.attributes),styles:i(u.styles)})}(e,{"alloy.base.modification":u},n,i):u;return lt(lt({},r=i),{attributes:lt(lt({},r.attributes),a.attributes),styles:lt(lt({},r.styles),a.styles),classes:r.classes.concat(a.classes)})}function ei(t,n,e){var o,r,i,u,a,c={"alloy.base.behaviour":t.events},s=t.eventOrder;return r=e,i=n,o=Nr(lt(lt({},c),(u=r,a={},St(i,function(t){a[t.name()]=t.handlers(u)}),a)),Ur),Su(o,s).getOrDie()}function oi(t){var e,n,o,r,i,u,a,c,s,l,f=hu(t),d=f.events,m=B(f,["events"]),g=M(tt(m,"components").getOr([]),Tu),p=lt(lt({},m),{events:lt(lt({},cu),d),components:g});return Ve.value((n=Po(vu),o=co(ao("custom.definition",Cu,e=p)),r=ti(e),i=r.list,u=r.data,a=function(t){var n=At.fromTag(t.tag);rn(n,t.attributes),Jr(n,t.classes),mn(n,t.styles),t.innerHtml.each(function(t){return Ar(n,t)});var e=t.domChildren;return An(n,e),t.value.each(function(t){Zr(n,t)}),t.uid,gu(n,t.uid),n}(ni(o,i,u)),c=ei(o,i,u),s=Po(o.components),l={uid:e.uid,getSystem:n.get,config:function(t){var n=u;return(S(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(t){return S(u[t.name()])},spec:e,readState:function(t){return u[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return o.apis},connect:function(t){n.set(t)},disconnect:function(){n.set(Vr(h))},element:a,syncComponents:function(){var t=z(Kt(a),function(t){return n.get().getByDom(t).fold(function(){return[]},j)});s.set(t)},components:s.get,events:c}));function h(){return l}}function ri(t){var n=At.fromText(t);return Ou({element:n})}Ho([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var ii,ui,ai={can:D,abort:T,run:st},ci=rt,si=ci("touchstart"),li=ci("touchmove"),fi=ci("touchend"),di=ci("touchcancel"),mi=ci("mousedown"),gi=ci("mousemove"),pi=ci("mouseout"),hi=ci("mouseup"),vi=ci("mouseover"),bi=ci("focusin"),yi=ci("focusout"),xi=ci("keydown"),wi=ci("keyup"),Si=ci("input"),ki=ci("change"),Ci=ci("click"),Oi=ci("transitioncancel"),_i=ci("transitionend"),Ti=ci("transitionstart"),Ei=ci("selectstart"),Di={tap:hr("tap")},Bi=hr("focus"),Mi=hr("blur.post"),Ai=hr("paste.post"),Fi=hr("receive"),Ii=hr("execute"),Ri=hr("focus.item"),Vi=Di.tap,Pi=hr("longpress"),Hi=hr("sandbox.close"),zi=hr("typeahead.cancel"),Ni=hr("system.init"),Li=hr("system.touchmove"),Wi=hr("system.touchend"),Ui=hr("system.scroll"),ji=hr("system.resize"),Gi=hr("system.attached"),Xi=hr("system.detached"),Yi=hr("system.dismissRequested"),qi=hr("system.repositionRequested"),Ki=hr("focusmanager.shifted"),Ji=hr("slotcontainer.visibility"),$i=hr("change.tab"),Qi=hr("dismiss.tab"),Zi=hr("highlight"),tu=hr("dehighlight"),nu=function(t,n,e,o){var r=lt({target:n},o);t.getSystem().triggerEvent(e,n,r)},eu=lr,ou=_r(Gi()),ru=_r(Xi()),iu=_r(Ni()),uu=(ii=Ii(),function(t){return Cr(ii,t)}),au=eu([{key:Bi(),value:pr({can:function(t,n){var e,o=n.event,r=o.originator,i=o.target;return!(Lt(e=r,t.element)&&!Lt(e,i)&&(console.warn(Bi()+" did not get interpreted by the desired target. \nOriginator: "+Fr(r)+"\nTarget: "+Fr(i)+"\nCheck the "+Bi()+" event handlers"),1))}})}]),cu=Object.freeze({__proto__:null,events:au}),su=0,lu=rt("alloy-id-"),fu=rt("data-alloy-id"),du=lu(),mu=fu(),gu=function(t,n){Object.defineProperty(t.dom,mu,{value:n,writable:!0})},pu=Ir,hu=h,vu=Vr(),bu=function(t){return M(t,function(t){return Bt(t,"/*")?t.substring(0,t.length-"/*".length):t})},yu=Ir("alloy-premade"),xu={init:function(){return wu({readState:rt("No State required")})}},wu=function(t){return t},Su=function(t,a){var n,e,o,r,i,u,c=$(t,function(r,u){return(1===r.length?Ve.value(r[0].handler):jr(r,a,u)).map(function(t){var n,i,e=(i=S(n=t)?{can:D,abort:T,run:n}:n,function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(void 0,r)?n.stop():i.can.apply(void 0,r)&&i.run.apply(void 0,r)}),o=1<r.length?F(a[u],function(n){return d(r,function(t){return t.name===n})}).join(" > "):r[0].name;return sr(u,{handler:e,purpose:o})})});return n={},e=[],o=[],St(c,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),0<(u={errors:e,values:o}).errors.length?Ve.error(ft(u.errors)):(i=n,0===(r=u.values).length?Ve.value(i):Ve.value(Yo(i,qo.apply(void 0,r))))},ku="alloy.base.behaviour",Cu=$o([ar("dom","dom",Ue(),$o([mo("tag"),Eo("styles",{}),Eo("classes",[]),Eo("attributes",{}),wo("value"),wo("innerHtml")])),mo("components"),mo("uid"),Eo("events",{}),Eo("apis",{}),ar("eventOrder","eventOrder",((ui={})[Ii()]=["disabling",ku,"toggling","typeaheadevents"],ui[Bi()]=[ku,"focusing","keying"],ui[Ni()]=[ku,"disabling","toggling","representing"],ui[Si()]=[ku,"representing","streaming","invalidating"],ui[Xi()]=[ku,"representing","item-events","tooltipping"],ui[mi()]=["focusing",ku,"item-type-events"],ui[si()]=["focusing",ku,"item-type-events"],ui[vi()]=["item-type-events","tooltipping"],ui[Fi()]=["receiving","reflecting","tooltipping"],Ye(rt(ui))),Zo()),wo("domModification")]),Ou=function(t){var n=so("external.component",to([mo("element"),wo("uid")]),t),e=Po(Vr()),o=n.uid.getOrThunk(function(){return pu("external")});gu(n.element,o);var r={uid:o,getSystem:e.get,config:vt.none,hasConfigured:T,connect:function(t){e.set(t)},disconnect:function(){e.set(Vr(function(){return r}))},getApis:function(){return{}},element:n.element,spec:t,readState:rt("No state"),syncComponents:st,components:rt([]),events:{}};return Hr(r)},_u=pu,Tu=function(t){return tt(t,yu).getOrThunk(function(){return oi(Tt(t,"uid")?t:lt({uid:_u("")},t)).getOrDie()})},Eu=Hr;function Du(t,n,e,o,r){return t(e,o)?vt.some(e):S(r)&&r(e)?vt.none():n(e,o,r)}function Bu(t,n,e){for(var o=t.dom,r=S(e)?e:T;o.parentNode;){var o=o.parentNode,i=At.fromDom(o);if(n(i))return vt.some(i);if(r(i))break}return vt.none()}function Mu(t,n,e){return Du(function(t,n){return n(t)},Bu,t,n,e)}function Au(t,n,e){return Mu(t,n,e).isSome()}function Fu(t,n,e){return Bu(t,function(t){return zt(t,n)},e)}function Iu(t,n){return e=n,Nt(o=void 0===t?document:t.dom)?vt.none():vt.from(o.querySelector(e)).map(At.fromDom);var e,o}function Ru(t,n,e){return Du(zt,Fu,t,n,e)}function Vu(){var n=Ir("aria-owns");return{id:n,link:function(t){on(t,"aria-owns",n)},unlink:function(t){sn(t,"aria-owns")}}}var Pu,Hu,zu=function(n,t){return Au(t,function(t){return Lt(t,n.element)},T)||(e=n,Mu(t,function(t){if(!Xn(t))return!1;var n=un(t,"id");return void 0!==n&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=un(t,"id");return Iu(ge(t),'[aria-owns="'+n+'"]')}).exists(function(t){return zu(e,t)}));var e},Nu="unknown";function Lu(n,t,e){var o,r,i,u;switch(tt(Wu.get(),n).orThunk(function(){return K(Ct(Wu.get()),function(t){return-1<n.indexOf(t)?vt.some(Wu.get()[t]):vt.none()})}).getOr(Pu.NORMAL)){case Pu.NORMAL:return e(ju());case Pu.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();wt(["mousemove","mouseover","mouseout",Ni()],o)||console.log(o,{event:o,time:t-u,target:r.dom,sequence:M(i,function(t){return wt(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+Fr(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case Pu.STOP:return!0}}(Hu=Pu=Pu||{})[Hu.STOP=0]="STOP",Hu[Hu.NORMAL=1]="NORMAL",Hu[Hu.LOGGING=2]="LOGGING";var Wu=Po({}),Uu=["alloy/data/Fields","alloy/debugging/Debugging"],ju=rt({logEventCut:st,logEventStopped:st,logNoParent:st,logEventNoHandlers:st,logEventResponse:st,write:st}),Gu=rt([mo("menu"),mo("selectedMenu")]),Xu=rt([mo("item"),mo("selectedItem")]);function Yu(){return bo("markers",[mo("backgroundMenu")].concat(Gu()).concat(Xu()))}function qu(t){return bo("markers",M(t,mo))}function Ku(t,n,e){return void 0!==(o=new Error).stack&&V(o.stack.split("\n"),function(n){return 0<n.indexOf("alloy")&&!d(Uu,function(t){return-1<n.indexOf(t)})}).getOr(Nu),ar(n,n,e,io(function(e){return Ve.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(void 0,t)})}));var o}function Ju(t){return Ku(0,t,Ge(st))}function $u(t){return Ku(0,t,Ge(vt.none))}function Qu(t){return Ku(0,t,Ue())}function Zu(t){return Ku(0,t,Ue())}function ta(t,n){return cr(t,rt(n))}function na(t){return cr(t,h)}function ea(t,n,e,o,r,i,u,a){return{x:t,y:n,bubble:e,direction:o,placement:r,restriction:i,label:u+"-"+r,alwaysFit:a=void 0!==a&&a}}function oa(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r}function ra(e,n){return U(["left","right","top","bottom"],function(t){return tt(n,t).map(function(n){return function(t){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(e)})})}function ia(t,n){return t.x+t.width/2-n.width/2}function ua(t,n){return t.x+t.width-n.width}function aa(t,n){return t.y-n.height}function ca(t){return t.y+t.height}function sa(t,n){return t.y+t.height/2-n.height/2}function la(t,n,e){return ea(t.x+t.width,sa(t,n),e.east(),Wa(),"east",ra(t,{left:0}),Ga)}function fa(t,n,e){return ea(t.x-n.width,sa(t,n),e.west(),Ua(),"west",ra(t,{right:1}),Ga)}function da(){return[Xa,Ya,qa,Ka,$a,Ja,la,fa]}function ma(){return[Ya,Xa,Ka,qa,$a,Ja,la,fa]}function ga(){return[qa,Ka,Xa,Ya,Ja,$a]}function pa(){return[Ka,qa,Ya,Xa,Ja,$a]}function ha(){return[Xa,Ya,qa,Ka,$a,Ja]}function va(){return[Ya,Xa,Ka,qa,$a,Ja]}function ba(e,o,r){return iu(function(t,n){r(t,e,o)})}function ya(t){return{key:t,value:void 0}}function xa(t){var n,e,o,r,i,u,a,c,s=so("Creating behaviour: "+t.name,ec,t);return n=s.fields,e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,a=to(n),c=To(e,[So("config",to(n))]),tc(a,c,e,o,r,i,u)}function wa(t){var n,e=so("Creating behaviour: "+t.name,oc,t),o=lo(e.branchKey,e.branches),r=e.name,i=e.active,u=e.apis,a=e.extra,c=e.state,s=To(r,[So("config",n=o)]);return tc(n,s,r,i,u,a,c)}function Sa(){return At.fromDom(document)}function ka(t){return t.dom.focus()}function Ca(t){var n=ge(t).dom;return t.dom===n.activeElement}function Oa(t){return void 0===t&&(t=Sa()),vt.from(t.dom.activeElement).map(At.fromDom)}function _a(n){return Oa(ge(n)).filter(function(t){return n.dom.contains(t.dom)})}function Ta(t,e){var o=ge(e),n=Oa(o).bind(function(n){function t(t){return Lt(n,t)}var r,i;return t(e)?vt.some(e):(r=t,(i=function(t){for(var n=0;n<t.childNodes.length;n++){var e=At.fromDom(t.childNodes[n]);if(r(e))return vt.some(e);var o=i(t.childNodes[n]);if(o.isSome())return o}return vt.none()})(e.dom))}),r=t(e);return n.each(function(n){Oa(o).filter(function(t){return Lt(t,n)}).fold(function(){ka(n)},st)}),r}function Ea(t,n,e,o,r){function i(t){return t+"px"}return{position:t,left:n.map(i),top:e.map(i),right:o.map(i),bottom:r.map(i)}}function Da(t,n){var e;gn(t,lt(lt({},e=n),{position:vt.some(e.position)}))}function Ba(t,n,e,o,r,i){var u=n.rect,a=u.x-e,c=u.y-o,s=r-(a+u.width),l=i-(c+u.height),f=vt.some(a),d=vt.some(c),m=vt.some(s),g=vt.some(l),p=vt.none();return n.direction.fold(function(){return Ea(t,f,d,p,p)},function(){return Ea(t,p,d,m,p)},function(){return Ea(t,f,p,p,g)},function(){return Ea(t,p,p,m,g)},function(){return Ea(t,f,d,p,p)},function(){return Ea(t,f,p,p,g)},function(){return Ea(t,f,d,p,p)},function(){return Ea(t,p,d,m,p)})}function Ma(t,r){return t.fold(function(){var t=r.rect;return Ea("absolute",vt.some(t.x),vt.some(t.y),vt.none(),vt.none())},function(t,n,e,o){return Ba("absolute",r,t,n,e,o)},function(t,n,e,o){return Ba("fixed",r,t,n,e,o)})}function Aa(t,n){var e=C(zn,n),o=t.fold(e,e,function(){var t=Rn();return zn(n).translate(-t.left,-t.top)}),r=_n(n),i=Sn(n);return Me(o.left,o.top,r,i)}rt($o(Xu().concat(Gu())));var Fa=rt($o(Xu())),Ia=rt(bo("initSize",[mo("numColumns"),mo("numRows")])),Ra=Ho([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Va=Ra.southeast,Pa=Ra.southwest,Ha=Ra.northeast,za=Ra.northwest,Na=Ra.south,La=Ra.north,Wa=Ra.east,Ua=Ra.west,ja=function(t,n,e){return Math.min(Math.max(t,n),e)},Ga="layout",Xa=function(t,n,e){return ea(t.x,ca(t),e.southeast(),Va(),"southeast",ra(t,{left:1,top:3}),Ga)},Ya=function(t,n,e){return ea(ua(t,n),ca(t),e.southwest(),Pa(),"southwest",ra(t,{right:0,top:3}),Ga)},qa=function(t,n,e){return ea(t.x,aa(t,n),e.northeast(),Ha(),"northeast",ra(t,{left:1,bottom:2}),Ga)},Ka=function(t,n,e){return ea(ua(t,n),aa(t,n),e.northwest(),za(),"northwest",ra(t,{right:0,bottom:2}),Ga)},Ja=function(t,n,e){return ea(ia(t,n),aa(t,n),e.north(),La(),"north",ra(t,{bottom:2}),Ga)},$a=function(t,n,e){return ea(ia(t,n),ca(t),e.south(),Na(),"south",ra(t,{top:3}),Ga)},Qa=Object.freeze({__proto__:null,events:function(a){return eu([Cr(Fi(),function(r,t){var n,i=a.channels,e=Ct(i),u=t,o=(n=u).universal?e:F(e,function(t){return wt(n.channels,t)});St(o,function(t){var n=i[t],e=n.schema,o=so("channel["+t+"] data\nReceiver: "+Fr(r.element),e,u.data);n.onReceive(r,o)})})])}}),Za=[go("channels",uo(Ve.value,to([Qu("onReceive"),Eo("schema",Zo())])))],tc=function(e,t,f,n,o,r,i){function u(t){return nt(t,f)?t[f]():vt.none()}var a=dt(o,function(t,n){return r=f,e=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:rt(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(void 0,[e,t.config,t.state].concat(n))})},o=u=n,a=(i=t).toString(),c=a.indexOf(")")+1,s=a.indexOf("("),l=a.substring(s+1,c-1).split(/,\s*/),e.toFunctionAnnotation=function(){return{name:o,parameters:bu(l.slice(0,1).concat(l.slice(3)))}},e;var r,i,u,e,o,a,c,s,l}),c=dt(r,Pr),s=lt(lt(lt({},c),a),{revoke:C(ya,f),config:function(t){var n=so(f+"-config",e,t);return{key:f,value:{config:n,me:s,configAsRaw:Rt(function(){return so(f+"-config",e,t)}),initialConfig:t,state:i}}},schema:rt(t),exhibit:function(t,e){return Et(u(t),tt(n,"exhibit"),function(t,n){return n(e,t.config,t.state)}).getOrThunk(function(){return Lr({})})},name:rt(f),handlers:function(t){return u(t).map(function(t){return tt(n,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return s},nc=lr,ec=to([mo("fields"),mo("name"),Eo("active",{}),Eo("apis",{}),Eo("state",xu),Eo("extra",{})]),oc=to([mo("branchKey"),mo("branches"),mo("name"),Eo("active",{}),Eo("apis",{}),Eo("state",xu),Eo("extra",{})]),rc=rt(void 0),ic=xa({fields:Za,name:"receiving",active:Qa}),uc=Object.freeze({__proto__:null,exhibit:function(t,n){return Lr({classes:[],styles:n.useFixed()?{}:{position:"relative"}})}}),ac=Ho([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),cc=function(t,n,e){var o=ke(n,e);return t.fold(rt(o),rt(o),function(){var t=Rn();return o.translate(-t.left,-t.top)})};function sc(t){return an(t,Dc)}function lc(t,n,e,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T,E,D,B,M,A,F,I,R,V,P,H,z,N,L,W,U=t.bubble,j=U.offset,G=(V=o,P=t.restriction,H=j,z=ot("left",V.x),N=ot("top",V.y),L=ot("right",V.right),W=ot("bottom",V.bottom),Me(z,N,L-z,W-N)),X=t.x+j.left,Y=t.y+j.top,q=Me(X,Y,n,e),K=(r=G.x,i=G.y,u=G.right,a=G.bottom,c=q.x,s=q.y,l=q.right,f=q.bottom,d=q.width,m=q.height,{originInBounds:r<=c&&c<=u&&i<=s&&s<=a,sizeInBounds:l<=u&&r<=l&&f<=a&&i<=f,visibleW:Math.min(d,r<=c?u-c:l-r),visibleH:Math.min(m,i<=s?a-s:f-i)}),J=K.visibleW,$=K.visibleH,Q=K.originInBounds&&K.sizeInBounds,Z=Q?q:(g=G.x,p=G.y,h=G.right,v=G.bottom,b=q.x,y=q.y,x=q.width,w=q.height,S=Math.max(g,h-x),k=Math.max(p,v-w),C=ja(b,g,S),O=ja(y,p,k),_=Math.min(C+x,h)-C,T=Math.min(O+w,v)-O,Me(C,O,_,T)),tt=0<Z.width&&0<Z.height,nt=(E=t.direction,M=rt((D=Z).bottom-(B=o).y),A=rt(B.bottom-D.y),F=E.fold(A,A,M,M,A,M,A,A),I=rt(D.right-B.x),R=rt(B.right-D.x),{maxWidth:E.fold(R,I,R,I,R,R,R,I),maxHeight:F}),et={rect:Z,maxHeight:nt.maxHeight,maxWidth:nt.maxWidth,direction:t.direction,placement:t.placement,classes:{on:U.classesOn,off:U.classesOff},layout:t.label,testY:Y};function ot(r,i){return P[r].map(function(t){var n="top"===r||"bottom"===r,e=n?H.top:H.left,o=("left"===r||"top"===r?Math.max:Math.min)(t,i)+e;return n?ja(o,V.y,V.bottom):ja(o,V.x,V.right)}).getOr(i)}return Q||t.alwaysFit?Bc.fit(et):Bc.nofit(et,J,$,tt)}function fc(t){function n(){return e.get().each(t)}var e=Po(vt.none());return{clear:function(){n(),e.set(vt.none())},isSet:function(){return e.get().isSome()},get:function(){return e.get()},set:function(t){n(),e.set(vt.some(t))}}}function dc(){return fc(function(t){return t.destroy()})}function mc(){return fc(function(t){return t.unbind()})}function gc(){var n=fc(st);return lt(lt({},n),{on:function(t){return n.get().each(t)}})}function pc(t,n,e){return En(t,n,Mc,e,!1)}function hc(t,n,e){return En(t,n,Mc,e,!0)}function vc(o,e){function r(t){var n,e=null!==(n=t.raw.pseudoElement)&&void 0!==n?n:"";return Lt(t.target,o)&&!at(e)&&wt(Fc,t.raw.propertyName)}function t(t){var n;(g(t)||r(t))&&(a.clear(),c.clear(),!g(n=null==t?void 0:t.raw.type)&&n!==_i()||(clearTimeout(i),sn(o,Ic),$r(o,e.classes)))}function n(){a.set(pc(o,_i(),t)),c.set(pc(o,Oi(),t))}var i,u,a=mc(),c=mc();"ontransitionstart"in o.dom?u=pc(o,Ti(),function(t){r(t)&&(u.unbind(),n())}):n();var s,l,f=(s=o,l=d("transition-delay"),R(d("transition-duration"),function(t,n,e){var o=m(l[e])+m(n);return Math.max(t,o)},0));function d(t){var n=pn(s,t);return F(y(n)?n.split(/\s*,\s*/):[],at)}function m(t){if(y(t)&&/^[\d.]+/.test(t)){var n=parseFloat(t);return Bt(t,"ms")?n:1e3*n}return 0}requestAnimationFrame(function(){i=setTimeout(t,f+17),on(o,Ic,i)})}function bc(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(u=o,a=r,i.exists(function(t){var n=u.mode;return"all"===n||t[n]!==a[n]}));function h(t){return parseFloat(t).toFixed(3)}p||(g=t,N(o.classes,function(t){return Kr(g,t)}))?(dn(t,"position",e.position),c=Aa(n,t),s=Ma(n,lt(lt({},r),{rect:c})),l=U(Fc,function(t){return s[t]}),m=e,Q(l,function(t,n){var e,o,r,i=m[n].map(h),u=t.map(h);return!Et(e=i,o=u,r=void 0===r?v:r).getOr(e.isNone()&&o.isNone())}).isSome()&&(gn(t,l),p&&(Jr(f=t,(d=o).classes),an(f,Ic).each(function(t){clearTimeout(parseInt(t,10)),sn(f,Ic)}),vc(f,d)),yn(t))):$r(t,o.classes)}function yc(t,n,e,o){bn(n,"max-height"),bn(n,"max-width");var r,s,i,l,f,d,m,g,p,u={width:_n(r=n),height:Sn(r)};return s=n,i=o.preference,l=t,f=u,d=e,m=o.bounds,g=f.width,p=f.height,R(i,function(t,n){var e=C(a,n);return t.fold(rt(t),e)},Bc.nofit({rect:l,maxHeight:f.height,maxWidth:f.width,direction:Va(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:l.y},-1,-1,!1)).fold(h,h);function a(t,r,i,u,a){var c=lc(t(l,f,d,s,m),g,p,m);return c.fold(rt(c),function(t,n,e,o){return(a===o?u<e||i<n:!a&&o)?c:Bc.nofit(r,i,u,a)})}}function xc(t,n){var e=t,o=Math.floor(n);dn(e,"max-height",we.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"])+"px")}function wc(t,n,e){return void 0===t[n]?e:t[n]}function Sc(t,n,e,o){function r(t){return tt(e,t).getOr([])}function i(t,n,e){var o=W(Hc,e);return{offset:ke(t,n),classesOn:z(e,r),classesOff:z(o,r)}}var u=t*(o=void 0===o?1:o),a=n*o;return{southeast:function(){return i(-t,n,["top","alignLeft"])},southwest:function(){return i(t,n,["top","alignRight"])},south:function(){return i(-t/2,n,["top","alignCentre"])},northeast:function(){return i(-t,-n,["bottom","alignLeft"])},northwest:function(){return i(t,-n,["bottom","alignRight"])},north:function(){return i(-t/2,-n,["bottom","alignCentre"])},east:function(){return i(t,-n/2,["valignCentre","left"])},west:function(){return i(-t,-n/2,["valignCentre","right"])},insetNortheast:function(){return i(u,a,["top","alignLeft","inset"])},insetNorthwest:function(){return i(-u,a,["top","alignRight","inset"])},insetNorth:function(){return i(-u/2,a,["top","alignCentre","inset"])},insetSoutheast:function(){return i(u,-a,["bottom","alignLeft","inset"])},insetSouthwest:function(){return i(-u,-a,["bottom","alignRight","inset"])},insetSouth:function(){return i(-u/2,-a,["bottom","alignCentre","inset"])},insetEast:function(){return i(-u,-a/2,["valignCentre","right","inset"])},insetWest:function(){return i(u,-a/2,["valignCentre","left","inset"])}}}function kc(){return Sc(0,0,{})}function Cc(n,e){return function(t){return"rtl"===Nc(t)?e:n}}ac.none;var Oc,_c,Tc=ac.relative,Ec=ac.fixed,Dc="data-alloy-placement",Bc=Ho([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Mc=D,Ac=Tn,Fc=["top","bottom","right","left"],Ic="data-alloy-transition-timer",Rc=rt(function(t,n){xc(t,n),mn(t,{"overflow-x":"hidden","overflow-y":"auto"})}),Vc=rt(function(t,n){xc(t,n)}),Pc=function(t,n,e,o){var r,i,u,a,c=yc(t,n,e,o),s=n,l=c,f=Ma((r=o).origin,l);return r.transition.each(function(t){bc(s,r.origin,f,t,l,r.lastPlacement)}),Da(s,f),a=c.placement,on(n,Dc,a),$r(i=n,(u=c.classes).off),Jr(i,u.on),(0,o.maxHeightFunction)(n,c.maxHeight),(0,o.maxWidthFunction)(n,c.maxWidth),{layout:c.layout,placement:c.placement}},Hc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],zc=h,Nc=function(t){return"rtl"===pn(t,"direction")?"rtl":"ltr"};function Lc(t){return Au(t,function(t){return Xn(t)&&un(t,"data-alloy-vertical-dir")===Oc.BottomToTop})}function Wc(){return To("layouts",[mo("onLtr"),mo("onRtl"),wo("onBottomLtr"),wo("onBottomRtl")])}function Uc(n,t,e,o,r,i,u){var a=u.map(Lc).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(n)}),s=t.layouts.map(function(t){return t.onRtl(n)});return Cc(a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(n)})}).or(c).getOr(r):c.getOr(e),a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(n)})}).or(s).getOr(i):s.getOr(o))(n)}function jc(t){return t.fold(h,function(t,n,e){return t.translate(-n,-e)})}function Gc(t){return t.fold(h,h)}function Xc(t){return R(t,function(t,n){return t.translate(n.left,n.top)},ke(0,0))}function Yc(t){return Xc(M(t,Gc))}function qc(t,n,e){var o,r,i=Rn(Ut(t.element)),u=(o=t,r=Xt(e.root).dom,vt.from(r.frameElement).map(At.fromDom).filter(function(t){return Lt(Ut(t),Ut(o.element))}).map(Cn).getOr(i));return as(u,i.left,i.top)}function Kc(t,n,e,o){var r=us(ke(t,n));return vt.some({point:r,width:e,height:o})}function Jc(t,a,c,s,l){return t.map(function(t){var n=[a,t.point],e=s.fold(function(){return Yc(n)},function(){return Yc(n)},function(){return Xc(M(n,jc))}),o={x:e.left,y:e.top,width:t.width,height:t.height},r=(c.showAbove?ga:ha)(),i=(c.showAbove?pa:va)(),u=Uc(l,c,r,i,r,i,vt.none());return zc({anchorBox:o,bubble:c.bubble.getOr(kc()),overrides:c.overrides,layouts:u,placer:vt.none()})})}function $c(t,n,e){var o,r=t.document.createRange(),i=r;return n.fold(function(t){i.setStartBefore(t.dom)},function(t,n){i.setStart(t.dom,n)},function(t){i.setStartAfter(t.dom)}),o=r,e.fold(function(t){o.setEndBefore(t.dom)},function(t,n){o.setEnd(t.dom,n)},function(t){o.setEndAfter(t.dom)}),r}function Qc(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom,e),i.setEnd(o.dom,r),i}function Zc(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom,width:t.width,height:t.height}}function ts(t,n,e){return n(At.fromDom(e.startContainer),e.startOffset,At.fromDom(e.endContainer),e.endOffset)}function ns(i,t){return r=i,o=t.match({domRange:function(t){return{ltr:rt(t),rtl:vt.none}},relative:function(t,n){return{ltr:Rt(function(){return $c(r,t,n)}),rtl:Rt(function(){return vt.some($c(r,n,t))})}},exact:function(t,n,e,o){return{ltr:Rt(function(){return Qc(r,t,n,e,o)}),rtl:Rt(function(){return vt.some(Qc(r,e,o,t,n))})}}}),((e=(n=o).ltr()).collapsed?n.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return gs.rtl(At.fromDom(t.endContainer),t.endOffset,At.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return ts(0,gs.ltr,e)}):ts(0,gs.ltr,e)).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom,n),r.setEnd(e.dom,o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom,o),r.setEnd(t.dom,n),r}});var r,n,e,o}(Oc=Oc||{}).TopToBottom="toptobottom",Oc.BottomToTop="bottomtotop";var es="data-alloy-vertical-dir",os=[mo("hotspot"),wo("bubble"),Eo("overrides",{}),Wc(),ta("placement",function(t,n,e){var o=Aa(e,n.hotspot.element),r=Uc(t.element,n,ha(),va(),ga(),pa(),vt.some(n.hotspot.element));return vt.some(zc({anchorBox:o,bubble:n.bubble.getOr(kc()),overrides:n.overrides,layouts:r,placer:vt.none()}))})],rs=[mo("x"),mo("y"),Eo("height",0),Eo("width",0),Eo("bubble",kc()),Eo("overrides",{}),Wc(),ta("placement",function(t,n,e){var o=cc(e,n.x,n.y),r=Me(o.left,o.top,n.width,n.height),i=Uc(t.element,n,da(),ma(),da(),ma(),vt.none());return vt.some(zc({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:vt.none()}))})],is=Ho([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),us=is.screen,as=is.absolute,cs=[mo("node"),mo("root"),wo("bubble"),Wc(),Eo("overrides",{}),Eo("showAbove",!1),ta("placement",function(r,i,u){var a=qc(r,0,i);return i.node.filter(ve).bind(function(t){var n=t.dom.getBoundingClientRect(),e=Kc(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element);return Jc(e,a,i,u,o)})})],ss=function(t,n,e,o){return{start:t,soffset:n,finish:e,foffset:o}},ls=Ho([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),fs=(ls.before,ls.on,ls.after,function(t){return t.fold(h,h,h)}),ds=Ho([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),ms={domRange:ds.domRange,relative:ds.relative,exact:ds.exact,exactFromRange:function(t){return ds.exact(t.start,t.soffset,t.finish,t.foffset)},getWin:function(t){return Xt(t.match({domRange:function(t){return At.fromDom(t.startContainer)},relative:function(t,n){return fs(t)},exact:function(t,n,e,o){return t}}))},range:ss},gs=Ho([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function ps(t){return nf.getOption(t)}function hs(t){return ps(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||wt(ef,Ft(t))}function vs(t,n){return Nt(e=void 0===t?document:t.dom)?[]:M(e.querySelectorAll(n),At.fromDom);var e}function bs(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return vt.some(ss(At.fromDom(n.startContainer),n.startOffset,At.fromDom(e.endContainer),e.endOffset))}return vt.none()}function ys(t){if(null===t.anchorNode||null===t.focusNode)return bs(t);var n,e,o,r,i,u,a,c,s,l,f,d=At.fromDom(t.anchorNode),m=At.fromDom(t.focusNode);return n=d,e=t.anchorOffset,o=m,r=t.focusOffset,u=e,a=o,c=r,(s=Ut(i=n).dom.createRange()).setStart(i.dom,u),s.setEnd(a.dom,c),l=s,f=Lt(n,o)&&e===r,l.collapsed&&!f?vt.some(ss(d,t.anchorOffset,m,t.focusOffset)):bs(t)}function xs(t,n){var e,o,r=(e=ns(t,n)).getClientRects();return 0<(o=0<r.length?r[0]:e.getBoundingClientRect()).width||0<o.height?vt.some(o).map(Zc):vt.none()}function ws(t,n){return{element:t,offset:n}}function Ss(t,n){return(Yn(t)?ws:function(t,n){var e=Kt(t);if(0===e.length)return ws(t,n);if(n<e.length)return ws(e[n],0);var o=e[e.length-1];return ws(o,(Yn(o)?nf.get(o):Kt(o)).length)})(t,n)}function ks(t,n){return n.getSelection.getOrThunk(function(){return function(){return vt.from(t.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(ys)}})().map(function(t){var n=Ss(t.start,t.soffset),e=Ss(t.finish,t.foffset);return ms.range(n.element,n.offset,e.element,e.offset)})}function Cs(t){return t.x+t.width}function Os(t,n){return t.x-n.width}function _s(t,n){return t.y-n.height+t.height}function Ts(t,n,e){return ea(Cs(t),t.y,e.southeast(),Va(),"southeast",ra(t,{left:0,top:2}),rf)}function Es(t,n,e){return ea(Os(t,n),t.y,e.southwest(),Pa(),"southwest",ra(t,{right:1,top:2}),rf)}function Ds(t,n,e){return ea(Cs(t),_s(t,n),e.northeast(),Ha(),"northeast",ra(t,{left:0,bottom:3}),rf)}function Bs(t,n,e){return ea(Os(t,n),_s(t,n),e.northwest(),za(),"northwest",ra(t,{right:1,bottom:3}),rf)}function Ms(){return[Ts,Es,Ds,Bs]}function As(){return[Es,Ts,Bs,Ds]}function Fs(t,n,e,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v,b,y,x,w={anchorBox:e.anchorBox,origin:n};return a=w,c=r.element,s=e.bubble,l=e.layouts,f=i,d=o,m=e.overrides,g=u,h=wc(m,"maxHeightFunction",Rc()),v=wc(m,"maxWidthFunction",st),b=a.anchorBox,y=a.origin,x={bounds:(p=y,d.fold(function(){return p.fold(Fe,Fe,Me)},function(e){return p.fold(e,e,function(){var t=e(),n=cc(p,t.x,t.y);return Me(n.left,n.top,t.width,t.height)})})),origin:y,preference:l,maxHeightFunction:h,maxWidthFunction:v,lastPlacement:f,transition:g},Pc(b,c,s,x)}function Is(t,n){Te(t.element,n.element)}function Rs(n,t){var e,o=n.components();St((e=n).components(),function(t){return Ee(t.element)}),Fn(e.element),e.syncComponents();var r=W(o,t);St(r,function(t){df(t),n.getSystem().removeFromWorld(t)}),St(t,function(t){t.getSystem().isConnected()?Is(n,t):(n.getSystem().addToWorld(t),Is(n,t),ve(n.element)&&mf(t)),n.syncComponents()})}function Vs(t,n){gf(t,n,Te)}function Ps(t){df(t),Ee(t.element),t.getSystem().removeFromWorld(t)}function Hs(n){var t=Yt(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()});Ps(n),t.each(function(t){t.syncComponents()})}function zs(t){var n=t.components();St(n,Ps),Fn(t.element),t.syncComponents()}function Ns(t,n){pf(t,n,Te)}function Ls(n){var t=Kt(n.element);St(t,function(t){n.getByDom(t).each(df)}),Ee(n.element)}function Ws(n,t,e,o){e.get().each(function(t){zs(n)}),Vs(t.getAttachPoint(n),n);var r=n.getSystem().build(o);return Vs(n,r),e.set(r),r}function Us(t,n,e,o){var r=Ws(t,n,e,o);return n.onOpen(t,r),r}function js(n,e,o){o.get().each(function(t){zs(n),Hs(n),e.onClose(n,t),o.clear()})}function Gs(t,n,e){return e.isOpen()}function Xs(t){var e=so("Dismissal",Sf,t),n={};return n[yf()]={schema:to([mo("target")]),onReceive:function(n,t){bf.isOpen(n)&&(bf.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return bf.close(n)},function(t){return vr(n,t.event)}))}},n}function Ys(t){var e=so("Reposition",kf,t),n={};return n[xf()]={onReceive:function(n){bf.isOpen(n)&&e.fireEventInstead.fold(function(){return e.doReposition(n)},function(t){return vr(n,t.event)})}},n}function qs(t,n,e){n.store.manager.onLoad(t,n,e)}function Ks(t,n,e){n.store.manager.onUnload(t,n,e)}function Js(){var t=Po(null);return wu({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})}function $s(){var i=Po({}),u=Po({});return wu({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return tt(i.get(),t).orThunk(function(){return tt(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};St(t,function(n){tt(o[n.value]=n,"meta").each(function(t){tt(t,"text").each(function(t){r[t]=n})})}),i.set(lt(lt({},n),o)),u.set(lt(lt({},e),r))},clear:function(){i.set({}),u.set({})}})}function Qs(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)}function Zs(o,t){return Vo(o,{},M(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,ar(n,n,Xe(),Je(function(t){return Lo("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([cr("dump",h)]))}function tl(t){return t.dump}function nl(t,n){return lt(lt({},nc(n)),t.dump)}function el(t){return Tt(t,"uiType")}function ol(t){return t.fold(vt.some,vt.none,vt.some,vt.some)}function rl(t){function n(t){return t.name}return t.fold(n,n,n,n)}function il(e,o){return function(t){var n=so("Converting part type",o,t);return e(n)}}function ul(t,n,e,o){return Yo(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))}function al(r,t){var n={};return St(t,function(t){ol(t).each(function(e){var o=ed(r,e.pname);n[e.name]=function(t){var n=so("Part: "+e.name+" in "+r,$o(e.schema),t);return lt(lt({},o),{config:t,validated:n})}})}),n}function cl(t,n,e){return{uiType:Pf(),owner:t,name:n,config:e,validated:{}}}function sl(t){return z(t,function(t){return t.fold(vt.none,vt.some,vt.none,vt.none).map(function(t){return bo(t.name,t.schema.concat([na(td())]))}).toArray()})}function ll(t){return M(t,rl)}function fl(t,n,e){return o=n,r={},i={},St(e,function(t){t.fold(function(o){r[o.pname]=Rf(!0,function(t,n,e){return o.factory.sketch(ul(t,o,n,e))})},function(t){var n=o.parts[t.name];i[t.name]=rt(t.factory.sketch(ul(o,t,n[td()]),n))},function(o){r[o.pname]=Rf(!1,function(t,n,e){return o.factory.sketch(ul(t,o,n,e))})},function(o){r[o.pname]=Vf(!0,function(n,t,e){return M(n[o.name],function(t){return o.factory.sketch(Yo(o.defaults(n,t,e),t,o.overrides(n,t)))})})})}),{internals:rt(r),externals:rt(i)};var o,r,i}function dl(t,n,e){return o=vt.some(t),i=(r=n).components,s=dt(e,function(t,n){return o=t,r=!1,{name:rt(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),u=o,a=r,c=s,l=z(i,function(t){return If(u,a,t,c)}),J(s,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+o.getOr("none")+"\nComponents: "+JSON.stringify(r.components,null,2))}),l;var o,r,i,u,a,c,s,l}function ml(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOptional()}function gl(t,n,e){return ml(t,n,e).getOrDie("Could not find part: "+e)}function pl(t,n,e){var o={},r=n.partUids,i=t.getSystem();return St(e,function(t){o[t]=rt(i.getByUid(r[t]))}),o}function hl(t,n){var e=t.getSystem();return dt(n.partUids,function(t,n){return rt(e.getByUid(t))})}function vl(t){return Ct(t.partUids)}function bl(t,n,e){var o={},r=n.partUids,i=t.getSystem();return St(e,function(t){o[t]=rt(i.getByUid(r[t]).getOrDie())}),o}function yl(n,t){return lr(M(ll(t),function(t){return{key:t,value:n+"-"+t}}))}function xl(n){return ar("partUids","partUids",Ye(function(t){return yl(t.uid,n)}),Zo())}function wl(t,n,e,o,r){var i;return so(t+" [SpecSchema]",to((i=r,(0<o.length?[bo("parts",o)]:[]).concat([mo("uid"),Eo("dom",{}),Eo("components",[]),na("originalSpec"),Eo("debug.sketcher",{})]).concat(i)).concat(n)),e)}function Sl(t,n,e,o,r){var i=rd(r),u=wl(t,n,i,sl(e),[xl(e)]),a=fl(0,u,e);return o(u,dl(t,u,a.internals()),i,a.externals())}function kl(t){var r=so("Sketcher for "+t.name,id,t),n=dt(r.apis,zr),e=dt(r.extraApis,Pr);return lt(lt({name:r.name,configFields:r.configFields,sketch:function(t){return n=r.name,e=r.configFields,(0,r.factory)(wl(n,e,o=rd(t),[],[]),o);var n,e,o}},n),e)}function Cl(t){var n=so("Sketcher for "+t.name,ud,t),e=al(n.name,n.partFields),o=dt(n.apis,zr),r=dt(n.extraApis,Pr);return lt(lt({name:n.name,partFields:n.partFields,configFields:n.configFields,sketch:function(t){return Sl(n.name,n.configFields,n.partFields,n.factory,t)},parts:e},o),r)}function Ol(t){return"input"===Ft(t)&&"radio"!==un(t,"type")||"textarea"===Ft(t)}function _l(t,n,e){(n.disabled()?ld:fd)(t,n)}function Tl(t,n){return!0===n.useNative&&wt(sd,Ft(t.element))}function El(t,n){return Tl(t,n)?cn(t.element,"disabled"):"true"===un(t.element,"aria-disabled")}function Dl(e,o,t,r){var n=vs(e.element,"."+o.highlightClass);St(n,function(n){d(r,function(t){return t.element===n})||(qr(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),vr(t,tu())}))})}function Bl(t,n,e,o){Dl(t,n,0,[o]),Yl(0,n,0,o)||(Yr(o.element,n.highlightClass),n.onHighlight(t,o),vr(o,Zi()))}function Ml(e,n,t,o){var r=vs(e.element,"."+n.itemClass);return P(r,function(t){return Kr(t,n.highlightClass)}).bind(function(t){var n=oa(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOptional()})}function Al(t,n,e){var o=L(t.slice(0,n)),r=L(t.slice(n+1));return V(o.concat(r),e)}function Fl(t,n,e){return V(L(t.slice(0,n)),e)}function Il(t,n,e){var o=t.slice(0,n);return V(t.slice(n+1).concat(o),e)}function Rl(t,n,e){return V(t.slice(n+1),e)}function Vl(e){return function(t){var n=t.raw;return wt(e,n.which)}}function Pl(t){return function(n){return N(t,function(t){return t(n)})}}function Hl(t){return!0===t.raw.shiftKey}function zl(t){return!0===t.raw.ctrlKey}function Nl(t,n){return{matches:t,classification:n}}function Ll(t,n,e){n.exists(function(n){return e.exists(function(t){return Lt(t,n)})})||br(t,Ki(),{prevFocus:n,newFocus:e})}function Wl(){function o(t){return _a(t.element)}return{get:o,set:function(t,n){var e=o(t);t.getSystem().triggerFocus(n,t.element),Ll(t,e,o(t))}}}function Ul(){function r(t){return hd.getHighlighted(t).map(function(t){return t.element})}return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold(st,function(t){hd.highlight(n,t)});var o=r(n);Ll(n,e,o)}}}gs.ltr,gs.rtl;function jl(t,n,e,o,r,i){var u=i.map(Ae);return lf(t,n,e,o,r,u)}function Gl(t,n,e){var o,r,i,u=n.getAttachPoint(t);dn(t.element,"position",ff.getMode(u)),i=n.cloakVisibilityAttr,hn((o=t).element,r="visibility").fold(function(){sn(o.element,i)},function(t){on(o.element,i,t)}),dn(o.element,r,"hidden")}function Xl(t,n,e){var o,r,i,u=t.element;d(["top","left","right","bottom"],function(t){return hn(u,t).isSome()})||bn(t.element,"position"),r="visibility",i=n.cloakVisibilityAttr,an((o=t).element,i).fold(function(){return bn(o.element,r)},function(t){return dn(o.element,r,t)})}function Yl(t,n,e,o){return Kr(o.element,n.highlightClass)}function ql(n,t,e){return Iu(n.element,"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}function Kl(n,t,e){var o=vs(n.element,"."+t.itemClass);return(0<o.length?vt.some(o[o.length-1]):vt.none()).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}function Jl(n,t,e){return et(M(vs(n.element,"."+t.itemClass),function(t){return n.getSystem().getByDom(t).toOptional()}))}var $l,Ql,Zl,tf,nf=($l=Yn,{get:function(t){if(!$l(t))throw new Error("Can only get text value of a text node");return Ql(t).getOr("")},getOption:Ql=function(t){return $l(t)?vt.from(t.dom.nodeValue):vt.none()},set:function(t,n){if(!$l(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}),ef=["img","br"],of=[wo("getSelection"),mo("root"),wo("bubble"),Wc(),Eo("overrides",{}),Eo("showAbove",!1),ta("placement",function(t,n,e){var r=Xt(n.root).dom,o=qc(t,0,n);return Jc(ks(r,n).bind(function(e){return t=r,n=ms.exactFromRange(e),(0<(o=ns(t,n).getBoundingClientRect()).width||0<o.height?vt.some(o).map(Zc):vt.none()).orThunk(function(){var t=At.fromText("\ufeff");Dn(e.start,t);var n=xs(r,ms.exact(t,0,t,1));return Ee(t),n}).bind(function(t){return Kc(t.left,t.top,t.width,t.height)});var t,n,o}),o,n,e,ks(r,n).bind(function(t){return Xn(t.start)?vt.some(t.start):fe(t.start)}).getOr(t.element))})],rf="link-layout",uf=lo("type",{selection:of,node:cs,hotspot:os,submenu:[mo("item"),Wc(),Eo("overrides",{}),ta("placement",function(t,n,e){var o=Aa(e,n.item.element),r=Uc(t.element,n,Ms(),As(),Ms(),As(),vt.none());return vt.some(zc({anchorBox:o,bubble:kc(),overrides:n.overrides,layouts:r,placer:vt.none()}))})],makeshift:rs}),af=[xo("classes",nr),Ao("mode","all",["all","layout","placement"])],cf=[Eo("useFixed",T),wo("getBounds")],sf=[go("anchor",uf),To("transition",af)],lf=function(c,s,l,f,t,d){var m=so("placement.info",$o(sf),t),g=m.anchor,p=f.element,h=l.get(f.uid);Ta(function(){dn(p,"position","fixed");var t=hn(p,"visibility");dn(p,"visibility","hidden");var n,e,o,r,i=s.useFixed()?(r=document.documentElement,Ec(0,0,r.clientWidth,r.clientHeight)):(e=Cn((n=c).element),o=n.element.dom.getBoundingClientRect(),Tc(e.left,e.top,o.width,o.height)),u=g.placement,a=d.map(rt).or(s.getBounds);u(c,g,i).each(function(t){var n=t.placer.getOr(Fs)(c,i,t,a,f,h,m.transition);l.set(f.uid,n)}),t.fold(function(){bn(p,"visibility")},function(t){dn(p,"visibility",t)}),hn(p,"left").isNone()&&hn(p,"top").isNone()&&hn(p,"right").isNone()&&hn(p,"bottom").isNone()&&mt(hn(p,"position"),"fixed")&&bn(p,"position")},p)},ff=xa({fields:cf,name:"positioning",active:uc,apis:Object.freeze({__proto__:null,position:function(t,n,e,o,r){jl(t,n,e,o,r,vt.none())},positionWithin:jl,positionWithinBounds:lf,getMode:function(t,n,e){return n.useFixed()?"fixed":"absolute"},reset:function(t,n,e,o){var r=o.element;St(["position","left","right","top","bottom"],function(t){return bn(r,t)}),sn(r,Dc),e.clear(o.uid)}}),state:Object.freeze({__proto__:null,init:function(){var e={};return wu({readState:function(){return e},clear:function(t){k(t)?delete e[t]:e={}},set:function(t,n){e[t]=n},get:function(t){return tt(e,t)}})}})}),df=function(t){vr(t,Xi());var n=t.components();St(n,df)},mf=function(t){var n=t.components();St(n,mf),vr(t,Gi())},gf=function(t,n,e){t.getSystem().addToWorld(n),e(t.element,n.element),ve(t.element)&&mf(n),t.syncComponents()},pf=function(t,n,e){e(t,n.element);var o=Kt(n.element);St(o,function(t){n.getByDom(t).each(mf)})},hf=Object.freeze({__proto__:null,cloak:Gl,decloak:Xl,open:Us,openWhileCloaked:function(t,n,e,o,r){Gl(t,n),Us(t,n,e,o),r(),Xl(t,n)},close:js,isOpen:Gs,isPartOf:function(n,e,t,o){return Gs(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()},setContent:function(t,n,e,o){return e.get().map(function(){return Ws(t,n,e,o)})}}),vf=Object.freeze({__proto__:null,events:function(e,o){return eu([Cr(Hi(),function(t,n){js(t,e,o)})])}}),bf=xa({fields:[Ju("onOpen"),Ju("onClose"),mo("isPartOf"),mo("getAttachPoint"),Eo("cloakVisibilityAttr","data-precloak-visibility")],name:"sandboxing",active:vf,apis:hf,state:Object.freeze({__proto__:null,init:function(){var t=gc(),n=rt("not-implemented");return wu({readState:n,isOpen:t.isSet,clear:t.clear,set:t.set,get:t.get})}})}),yf=rt("dismiss.popups"),xf=rt("reposition.popups"),wf=rt("mouse.released"),Sf=to([Eo("isExtraPart",T),To("fireEventInstead",[Eo("event",Yi())])]),kf=to([To("fireEventInstead",[Eo("event",qi())]),vo("doReposition")]),Cf=Object.freeze({__proto__:null,onLoad:qs,onUnload:Ks,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),Of=Object.freeze({__proto__:null,events:function(e,o){var t=e.resetOnDom?[ou(function(t,n){qs(t,e,o)}),ru(function(t,n){Ks(t,e,o)})]:[ba(e,o,qs)];return eu(t)}}),_f=Object.freeze({__proto__:null,memory:Js,dataset:$s,manual:function(){return wu({readState:st})},init:function(t){return t.store.manager.state(t)}}),Tf=[wo("initialValue"),mo("getFallbackEntry"),mo("getDataKey"),mo("setValue"),ta("manager",{setValue:Qs,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).getOrThunk(function(){return o.getFallbackEntry(r)})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){Qs(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:$s})],Ef=[mo("getValue"),Eo("setValue",st),wo("initialValue"),ta("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:st,state:xu.init})],Df=xa({fields:[Do("store",{mode:"memory"},lo("mode",{memory:[wo("initialValue"),ta("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:Js})],manual:Ef,dataset:Tf})),Ju("onSetValue"),Eo("resetOnDom",!1)],name:"representing",active:Of,apis:Cf,extra:{setValueFrom:function(t,n){var e=Df.getValue(n);Df.setValue(t,e)}},state:_f}),Bf=Zs,Mf=nl,Af="placeholder",Ff=Ho([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),If=function(r,i,u,a){return t=r,e=a,(el(n=u)&&n.uiType===Af?(c=n,s=e,(o=t).exists(function(t){return t!==c.owner})?Ff.single(!0,rt(c)):tt(s,c.name).fold(function(){throw new Error("Unknown placeholder component: "+c.name+"\nKnown: ["+Ct(s)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(c,null,2))},function(t){return t.replace()})):Ff.single(!1,rt(n))).fold(function(t,n){var e=el(u)?n(i,u.config,u.validated):n(i),o=z(tt(e,"components").getOr([]),function(t){return If(r,i,t,a)});return[lt(lt({},e),{components:o})]},function(t,n){if(el(u)){var e=n(i,u.config,u.validated);return u.validated.preprocess.getOr(h)(e)}return n(i)});var t,n,e,o,c,s},Rf=Ff.single,Vf=Ff.multiple,Pf=rt(Af),Hf=Ho([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),zf=Eo("factory",{sketch:h}),Nf=Eo("schema",[]),Lf=mo("name"),Wf=ar("pname","pname",je(function(t){return"<alloy."+Ir(t.name)+">"}),Zo()),Uf=cr("schema",function(){return[wo("preprocess")]}),jf=Eo("defaults",rt({})),Gf=Eo("overrides",rt({})),Xf=$o([zf,Nf,Lf,Wf,jf,Gf]),Yf=$o([zf,Nf,Lf,jf,Gf]),qf=$o([zf,Nf,Lf,Wf,jf,Gf]),Kf=$o([zf,Uf,Lf,mo("unit"),Wf,jf,Gf]),Jf=il(Hf.required,Xf),$f=il(Hf.external,Yf),Qf=il(Hf.optional,qf),Zf=il(Hf.group,Kf),td=rt("entirety"),nd=Object.freeze({__proto__:null,required:Jf,external:$f,optional:Qf,group:Zf,asNamedPart:ol,name:rl,asCommon:function(t){return t.fold(h,h,h,h)},original:td}),ed=function(t,n){return{uiType:Pf(),owner:t,name:n}},od=Object.freeze({__proto__:null,generate:al,generateOne:cl,schemas:sl,names:ll,substitutes:fl,components:dl,defaultUids:yl,defaultUidsSchema:xl,getAllParts:hl,getAllPartNames:vl,getPart:ml,getPartOrDie:gl,getParts:pl,getPartsOrDie:bl}),rd=function(t){return Tt(t,"uid")?t:lt(lt({},t),{uid:pu("uid")})},id=to([mo("name"),mo("factory"),mo("configFields"),Eo("apis",{}),Eo("extraApis",{})]),ud=to([mo("name"),mo("factory"),mo("configFields"),mo("partFields"),Eo("apis",{}),Eo("extraApis",{})]),ad=Object.freeze({__proto__:null,getCurrent:function(t,n,e){return n.find(t)}}),cd=xa({fields:[mo("find")],name:"composing",apis:ad}),sd=["input","button","textarea","select"],ld=function(n,t,e){t.disableClass.each(function(t){Yr(n.element,t)}),(Tl(n,t)?function(t){on(t.element,"disabled","disabled")}:function(t){on(t.element,"aria-disabled","true")})(n),t.onDisabled(n)},fd=function(n,t,e){t.disableClass.each(function(t){qr(n.element,t)}),(Tl(n,t)?function(t){sn(t.element,"disabled")}:function(t){on(t.element,"aria-disabled","false")})(n),t.onEnabled(n)},dd=Object.freeze({__proto__:null,enable:fd,disable:ld,isDisabled:El,onLoad:_l,set:function(t,n,e,o){(o?ld:fd)(t,n)}}),md=Object.freeze({__proto__:null,exhibit:function(t,n){return Lr({classes:n.disabled()?n.disableClass.toArray():[]})},events:function(e,t){return eu([Sr(Ii(),function(t,n){return El(t,e)}),ba(e,t,_l)])}}),gd=xa({fields:[Io("disabled",T),Eo("useNative",!0),wo("disableClass"),Ju("onDisabled"),Ju("onEnabled")],name:"disabling",active:md,apis:dd}),pd=Object.freeze({__proto__:null,dehighlightAll:function(t,n,e){return Dl(t,n,0,[])},dehighlight:function(t,n,e,o){Yl(0,n,0,o)&&(qr(o.element,n.highlightClass),n.onDehighlight(t,o),vr(o,tu()))},highlight:Bl,highlightFirst:function(n,e,t){ql(n,e).each(function(t){Bl(n,e,0,t)})},highlightLast:function(n,e,t){Kl(n,e).each(function(t){Bl(n,e,0,t)})},highlightAt:function(n,e,t,o){var r,i,u;i=o,u=vs((r=n).element,"."+e.itemClass),vt.from(u[i]).fold(function(){return Ve.error(new Error("No element found with index "+i))},r.getSystem().getByDom).fold(function(t){throw t},function(t){Bl(n,e,0,t)})},highlightBy:function(n,e,t,o){V(Jl(n,e),o).each(function(t){Bl(n,e,0,t)})},isHighlighted:Yl,getHighlighted:function(n,t,e){return Iu(n.element,"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},getFirst:ql,getLast:Kl,getPrevious:function(t,n,e){return Ml(t,n,0,-1)},getNext:function(t,n,e){return Ml(t,n,0,1)},getCandidates:Jl}),hd=xa({fields:[mo("highlightClass"),mo("itemClass"),Ju("onHighlight"),Ju("onDehighlight")],name:"highlighting",apis:pd}),vd=[8],bd=[9],yd=[13],xd=[27],wd=[32],Sd=[37],kd=[38],Cd=[39],Od=[40],_d=O(Hl);function Td(t,n,e,o,a){function c(n,e,t,o,r){var i=t(n,e,o,r),u=e.event;return V(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(n,e,o,r)})}var r={schema:function(){return t.concat([Eo("focusManager",Wl()),Do("focusInside","onFocus",io(function(t){return wt(["onFocus","onEnterOrSpace","onApi"],t)?Ve.value(t):Ve.error("Invalid value for focusInside")})),ta("handler",r),ta("state",n),ta("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==Zl.OnFocusMode?vt.none():a(i).map(function(e){return Cr(Bi(),function(t,n){e(t,i,u),n.stop()})}),n=[Cr(xi(),function(o,r){c(o,r,e,i,u).fold(function(){var n=o,e=r,t=Vl(wd.concat(yd))(e.event);i.focusInside===Zl.OnEnterOrSpaceMode&&t&&gr(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),Cr(wi(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})];return eu(t.toArray().concat(n))}};return r}function Ed(t){function a(t,n){return 0<wn(t.visibilitySelector.bind(function(t){return Ru(n,t)}).getOr(n))}function n(n,e,t){var o=e,r=F(vs(n.element,o.selector),function(t){return a(o,t)});vt.from(r[o.firstTabstop]).each(function(t){e.focusManager.set(n,t)})}function o(e,t,r,i){var n,u=vs(e.element,r.selector);return(n=r).focusManager.get(e).bind(function(t){return Ru(t,n.selector)}).bind(function(t){return P(u,C(Lt,t)).bind(function(t){return n=e,o=r,i(u,t,function(t){return a(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?vt.some(!0):vt.none()},function(t){return o.focusManager.set(n,t),vt.some(!0)});var n,o})})}var e=[wo("onEscape"),wo("onEnter"),Eo("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Eo("firstTabstop",0),Eo("useTabstopAt",D),wo("visibilitySelector")].concat([t]),r=rt([Nl(Pl([Hl,Vl(bd)]),function(t,n,e){return o(t,0,e,e.cyclic?Al:Fl)}),Nl(Vl(bd),function(t,n,e){return o(t,0,e,e.cyclic?Il:Rl)}),Nl(Vl(xd),function(n,e,t){return t.onEscape.bind(function(t){return t(n,e)})}),Nl(Pl([_d,Vl(yd)]),function(n,e,t){return t.onEnter.bind(function(t){return t(n,e)})})]),i=rt([]);return Td(e,xu.init,r,i,function(){return vt.some(n)})}function Dd(t,n,e){return Ol(e)&&Vl(wd)(n.event)?vt.none():(xr(t,e,Ii()),vt.some(!0))}function Bd(t,n){return vt.some(!0)}function Md(t,n,e){return e.execute(t,n,t.element)}function Ad(){var e=gc();return wu({readState:function(){return e.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set({numRows:t,numColumns:n})},getNumRows:function(){return e.get().map(function(t){return t.numRows})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns})}})}function Fd(i){return function(t,n,e,o){var r=i(t.element);return tg(r,t,n,e,o)}}function Id(t,n){return Fd(Cc(t,n))}function Rd(t,n){return Fd(Cc(n,t))}function Vd(r){return function(t,n,e,o){return tg(r,t,n,e,o)}}function Pd(t){return!((n=t.dom).offsetWidth<=0&&n.offsetHeight<=0);var n}function Hd(t,n,e){var o,r=F(vs(t,e),Pd);return P(o=r,function(t){return Lt(t,n)}).map(function(t){return{index:t,candidates:o}})}function zd(t,n){return P(t,function(t){return Lt(n,t)})}function Nd(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row*o+t.column;return 0<=n&&n<e.length?vt.some(e[n]):vt.none()})}function Ld(r,t,i,u,a){return Nd(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=oa(n,a,0,e-1);return vt.some({row:t,column:o})})}function Wd(i,t,u,a,c){return Nd(i,t,a,function(t,n){var e=oa(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=ja(n,0,o-1);return vt.some({row:e,column:r})})}function Ud(n,e,t){Iu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})}function jd(r){return function(t,n,e,o){return Hd(t,n,e.selector).bind(function(t){return r(t.candidates,t.index,o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Gd(t,n,e){return e.captureTab?vt.some(!0):vt.none()}function Xd(t,n,e,i){var u=function(t,n,e){var o,r=oa(n,i,0,e.length-1);return r===t?vt.none():"button"===Ft(o=e[r])&&"disabled"===un(o,"disabled")?u(t,r,e):vt.from(e[r])};return Hd(t,e,n).bind(function(t){var n=t.index,e=t.candidates;return u(n,n,e)})}function Yd(n,e,o){return(r=o).focusManager.get(n).bind(function(t){return Ru(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var r}function qd(n,e,t){e.getInitial(n).orThunk(function(){return Iu(n.element,e.selector)}).each(function(t){e.focusManager.set(n,t)})}function Kd(t,n,e){return Xd(t,e.selector,n,-1)}function Jd(t,n,e){return Xd(t,e.selector,n,1)}function $d(r){return function(t,n,e,o){return r(t,n,e,o).bind(function(){return e.executeOnMove?Yd(t,n,e):vt.some(!0)})}}function Qd(t,n,e){return e.onEscape(t,n)}function Zd(t,n,e){return vt.from(t[n]).bind(function(t){return vt.from(t[e]).map(function(t){return{rowIndex:n,columnIndex:e,cell:t}})})}function tm(t,n,e,o){return Zd(t,n,oa(e,o,0,t[n].length-1))}function nm(t,n,e,o){var r=oa(e,o,0,t.length-1),i=t[r].length;return Zd(t,r,ja(n,0,i-1))}function em(t,n,e,o){var r=t[n].length;return Zd(t,n,ja(e+o,0,r-1))}function om(t,n,e,o){var r=ja(e+o,0,t.length-1),i=t[r].length;return Zd(t,r,ja(n,0,i-1))}function rm(n,e,t){e.previousSelector(n).orThunk(function(){var t=e.selectors;return Iu(n.element,t.cell)}).each(function(t){e.focusManager.set(n,t)})}function im(t,o){return function(n,e,i){var u=i.cycles?t:o;return Ru(e,i.selectors.row).bind(function(t){return zd(vs(t,i.selectors.cell),e).bind(function(o){var r=vs(n,i.selectors.row);return zd(r,t).bind(function(t){var n,e=(n=i,M(r,function(t){return vs(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell})})})})}}function um(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})}function am(n,e,t){Iu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})}function cm(t,n,e){return Xd(t,e.selector,n,-1)}function sm(t,n,e){return Xd(t,e.selector,n,1)}function lm(t,n,e,o){var r=t.getSystem().build(o);gf(t,r,e)}function fm(t,n,e,o){V(Ig(t),function(t){return Lt(o.element,t.element)}).each(Hs)}function dm(n,t,e,r,o){var i=Ig(n);return vt.from(i[r]).map(function(t){return fm(n,0,0,t),o.each(function(t){lm(n,0,function(t,n){var e,o=n;Jt(e=t,r).fold(function(){Te(e,o)},function(t){Dn(t,o)})},t)}),t})}function mm(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=eu(n),xa({fields:[mo("enabled")],name:e,active:{events:rt(o)}})),configAsRaw:rt({}),initialConfig:{},state:xu}}}function gm(t,n){n.ignore||(ka(t.element),n.onFocus(t))}function pm(t,n,e){var o=n.aria;o.update(t,o,e.get())}function hm(n,t,e){t.toggleClass.each(function(t){(e.get()?Yr:qr)(n.element,t)})}function vm(t,n,e){Ym(t,n,e,!e.get())}function bm(t,n,e){e.set(!0),hm(t,n,e),pm(t,n,e)}function ym(t,n,e){e.set(!1),hm(t,n,e),pm(t,n,e)}function xm(t,n,e){Ym(t,n,e,n.selected)}function wm(){function t(t,n){n.stop(),yr(t)}return[Cr(Ci(),t),Cr(Vi(),t),Dr(si()),Dr(mi())]}function Sm(t){return eu(ft([t.map(function(e){return uu(function(t,n){e(t),n.stop()})}).toArray(),wm()]))}function km(t){(_a(t.element).isNone()||Hg.isFocused(t))&&(Hg.isFocused(t)||Hg.focus(t),br(t,Wg,{item:t}))}function Cm(t){br(t,Ug,{item:t})}function Om(t,n){return t.x+t.width/2-n.width/2}function _m(t,n){return t.x+t.width-n.width}function Tm(t,n){return t.y+t.height-n.height}function Em(t,n){return t.y+t.height/2-n.height/2}function Dm(t,n,e){return ea(_m(t,n),Tm(t,n),e.insetSouthwest(),za(),"southwest",ra(t,{right:0,bottom:3}),ap)}function Bm(t,n,e){return ea(t.x,Tm(t,n),e.insetSoutheast(),Ha(),"southeast",ra(t,{left:1,bottom:3}),ap)}function Mm(t,n,e){return ea(_m(t,n),t.y,e.insetNorthwest(),Pa(),"northwest",ra(t,{right:0,top:2}),ap)}function Am(t,n,e){return ea(t.x,t.y,e.insetNortheast(),Va(),"northeast",ra(t,{left:1,top:2}),ap)}function Fm(t,n,e){return ea(_m(t,n),Em(t,n),e.insetEast(),Ua(),"east",ra(t,{right:0}),ap)}function Im(t,n,e){return ea(t.x,Em(t,n),e.insetWest(),Wa(),"west",ra(t,{left:1}),ap)}function Rm(t){switch(t){case"north":return cp;case"northeast":return Am;case"northwest":return Mm;case"south":return sp;case"southeast":return Bm;case"southwest":return Dm;case"east":return Fm;case"west":return Im}}function Vm(t,n,e,o,r){return sc(o).map(Rm).getOr(cp)(t,n,e,o,r)}function Pm(t){switch(t){case"north":return sp;case"northeast":return Bm;case"northwest":return Dm;case"south":return cp;case"southeast":return Am;case"southwest":return Mm;case"east":return Im;case"west":return Fm}}function Hm(t,n,e,o,r){return sc(o).map(Pm).getOr(cp)(t,n,e,o,r)}function zm(t){var n=void 0!==t.uid&&nt(t,"uid")?t.uid:pu("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).toOptional()},asSpec:function(){return lt(lt({},t),{uid:n})}}}function Nm(t){return function(){return tt(t,gp).getOr("!not found!")}}function Lm(t,n){var e,o=t.toLowerCase();if(dp.isRtl()){var r=Bt(e=o,"-rtl")?e:e+"-rtl";return Tt(n,r)?r:o}return o}function Wm(t,n){return tt(n,Lm(t,n))}function Um(t,n){var e=n();return Wm(t,e).getOrThunk(Nm(e))}function jm(){return mm("add-focusable",[ou(function(t){var n,e,o;n=t.element,e="svg",o=function(t){return zt(t,e)},V(n.dom.childNodes,function(t){return o(At.fromDom(t))}).map(At.fromDom).each(function(t){return on(t,"focusable","false")})})])}function Gm(t,n,e,o){var r,i,u,a=(u=n,dp.isRtl()&&Tt(mp,u)?["tox-icon--flip"]:[]),c=tt(e,Lm(n,e)).or(o).getOrThunk(Nm(e));return{dom:{tag:t.tag,attributes:null!==(r=t.attributes)&&void 0!==r?r:{},classes:t.classes.concat(a),innerHtml:c},behaviours:nc(H(H([],null!==(i=t.behaviours)&&void 0!==i?i:[],!0),[jm()],!1))}}function Xm(t,n,e,o){return void 0===o&&(o=vt.none()),Gm(n,t,e(),o)}(tf=Zl=Zl||{}).OnFocusMode="onFocus",tf.OnEnterOrSpaceMode="onEnterOrSpace",tf.OnApiMode="onApi";function Ym(t,n,e,o){(o?bm:ym)(t,n,e)}function qm(t,n,e){on(t.element,"aria-expanded",e)}function Km(t){return"prepared"===t.type?vt.some(t.menu):vt.none()}var Jm=Ed(cr("cyclic",T)),$m=Ed(cr("cyclic",D)),Qm=Td([Eo("execute",Dd),Eo("useSpace",!1),Eo("useEnter",!0),Eo("useControlEnter",!1),Eo("useDown",!1)],xu.init,function(t,n,e,o){var r=e.useSpace&&!Ol(t.element)?wd:[],i=e.useEnter?yd:[],u=e.useDown?Od:[];return[Nl(Vl(r.concat(i).concat(u)),Md)].concat(e.useControlEnter?[Nl(Pl([zl,Vl(yd)]),Md)]:[])},function(t,n,e,o){return e.useSpace&&!Ol(t.element)?[Nl(Vl(wd),Bd)]:[]},function(){return vt.none()}),Zm=Object.freeze({__proto__:null,flatgrid:Ad,init:function(t){return t.state(t)}}),tg=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element,t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},ng=Vd,eg=Vd,og=Vd,rg=jd(function(t,n,e,o){return Ld(t,n,e,o,-1)}),ig=jd(function(t,n,e,o){return Ld(t,n,e,o,1)}),ug=jd(function(t,n,e,o){return Wd(t,n,e,o,-1)}),ag=jd(function(t,n,e,o){return Wd(t,n,e,o,1)}),cg=Td([mo("selector"),Eo("execute",Dd),$u("onEscape"),Eo("captureTab",!1),Ia()],Ad,rt([Nl(Vl(Sd),Id(rg,ig)),Nl(Vl(Cd),Rd(rg,ig)),Nl(Vl(kd),ng(ug)),Nl(Vl(Od),eg(ag)),Nl(Pl([Hl,Vl(bd)]),Gd),Nl(Pl([_d,Vl(bd)]),Gd),Nl(Vl(xd),function(t,n,e){return e.onEscape(t,n)}),Nl(Vl(wd.concat(yd)),function(n,e,o,t){return(r=o).focusManager.get(n).bind(function(t){return Ru(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var r})]),rt([Nl(Vl(wd),Bd)]),function(){return vt.some(Ud)}),sg=[mo("selector"),Eo("getInitial",vt.none),Eo("execute",Dd),$u("onEscape"),Eo("executeOnMove",!1),Eo("allowVertical",!0)],lg=rt([Nl(Vl(wd),Bd)]),fg=Td(sg,xu.init,function(t,n,e,o){var r=Sd.concat(e.allowVertical?kd:[]),i=Cd.concat(e.allowVertical?Od:[]);return[Nl(Vl(r),$d(Id(Kd,Jd))),Nl(Vl(i),$d(Rd(Kd,Jd))),Nl(Vl(yd),Yd),Nl(Vl(wd),Yd),Nl(Vl(xd),Qd)]},lg,function(){return vt.some(qd)}),dg=[bo("selectors",[mo("row"),mo("cell")]),Eo("cycles",!0),Eo("previousSelector",vt.none),Eo("execute",Dd)],mg=im(function(t,n,e){return tm(t,n,e,-1)},function(t,n,e){return em(t,n,e,-1)}),gg=im(function(t,n,e){return tm(t,n,e,1)},function(t,n,e){return em(t,n,e,1)}),pg=im(function(t,n,e){return nm(t,e,n,-1)},function(t,n,e){return om(t,e,n,-1)}),hg=im(function(t,n,e){return nm(t,e,n,1)},function(t,n,e){return om(t,e,n,1)}),vg=rt([Nl(Vl(Sd),Id(mg,gg)),Nl(Vl(Cd),Rd(mg,gg)),Nl(Vl(kd),ng(pg)),Nl(Vl(Od),eg(hg)),Nl(Vl(wd.concat(yd)),function(n,e,o){return _a(n.element).bind(function(t){return o.execute(n,e,t)})})]),bg=rt([Nl(Vl(wd),Bd)]),yg=Td(dg,xu.init,vg,bg,function(){return vt.some(rm)}),xg=[mo("selector"),Eo("execute",Dd),Eo("moveOnTab",!1)],wg=rt([Nl(Vl(kd),og(cm)),Nl(Vl(Od),og(sm)),Nl(Pl([Hl,Vl(bd)]),function(t,n,e,o){return e.moveOnTab?og(cm)(t,n,e,o):vt.none()}),Nl(Pl([_d,Vl(bd)]),function(t,n,e,o){return e.moveOnTab?og(sm)(t,n,e,o):vt.none()}),Nl(Vl(yd),um),Nl(Vl(wd),um)]),Sg=rt([Nl(Vl(wd),Bd)]),kg=Td(xg,xu.init,wg,Sg,function(){return vt.some(am)}),Cg=Td([$u("onSpace"),$u("onEnter"),$u("onShiftEnter"),$u("onLeft"),$u("onRight"),$u("onTab"),$u("onShiftTab"),$u("onUp"),$u("onDown"),$u("onEscape"),Eo("stopSpaceKeyup",!1),wo("focusIn")],xu.init,function(t,n,e){return[Nl(Vl(wd),e.onSpace),Nl(Pl([_d,Vl(yd)]),e.onEnter),Nl(Pl([Hl,Vl(yd)]),e.onShiftEnter),Nl(Pl([Hl,Vl(bd)]),e.onShiftTab),Nl(Pl([_d,Vl(bd)]),e.onTab),Nl(Vl(kd),e.onUp),Nl(Vl(Od),e.onDown),Nl(Vl(Sd),e.onLeft),Nl(Vl(Cd),e.onRight),Nl(Vl(wd),e.onSpace),Nl(Vl(xd),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[Nl(Vl(wd),Bd)]:[]},function(t){return t.focusIn}),Og=Jm.schema(),_g=$m.schema(),Tg=fg.schema(),Eg=cg.schema(),Dg=yg.schema(),Bg=Qm.schema(),Mg=kg.schema(),Ag=Cg.schema(),Fg=wa({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Og,cyclic:_g,flow:Tg,flatgrid:Eg,matrix:Dg,execution:Bg,menu:Mg,special:Ag}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element,n.element)},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){nt(e,"setGridSize")?e.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:Zm}),Ig=function(t,n){return t.components()},Rg=xa({fields:[],name:"replacing",apis:Object.freeze({__proto__:null,append:function(t,n,e,o){lm(t,0,Te,o)},prepend:function(t,n,e,o){lm(t,0,Mn,o)},remove:fm,replaceAt:dm,replaceBy:function(n,t,e,o,r){return P(Ig(n),o).bind(function(t){return dm(n,0,0,t,r)})},set:function(n,t,e,o){Ta(function(){var t=M(o,n.getSystem().build);Rs(n,t)},n.element)},contents:Ig})}),Vg=Object.freeze({__proto__:null,focus:gm,blur:function(t,n){n.ignore||t.element.dom.blur()},isFocused:function(t){return Ca(t.element)}}),Pg=Object.freeze({__proto__:null,exhibit:function(t,n){return Lr(n.ignore?{}:{attributes:{tabindex:"-1"}})},events:function(e){return eu([Cr(Bi(),function(t,n){gm(t,e),n.stop()})].concat(e.stopMousedown?[Cr(mi(),function(t,n){n.event.prevent()})]:[]))}}),Hg=xa({fields:[Ju("onFocus"),Eo("stopMousedown",!1),Eo("ignore",!1)],name:"focusing",active:Pg,apis:Vg}),zg=Object.freeze({__proto__:null,onLoad:xm,toggle:vm,isOn:function(t,n,e){return e.get()},on:bm,off:ym,set:Ym}),Ng=Object.freeze({__proto__:null,exhibit:function(){return Lr({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=vm,uu(function(t){r(t,e,o)})),u=ba(t,n,xm);return eu(ft([t.toggleOnExecute?[i]:[],[u]]))}}),Lg=xa({fields:[Eo("selected",!1),wo("toggleClass"),Eo("toggleOnExecute",!0),Do("aria",{mode:"none"},lo("mode",{pressed:[Eo("syncWithExpanded",!1),ta("update",function(t,n,e){on(t.element,"aria-pressed",e),n.syncWithExpanded&&qm(t,0,e)})],checked:[ta("update",function(t,n,e){on(t.element,"aria-checked",e)})],expanded:[ta("update",qm)],selected:[ta("update",function(t,n,e){on(t.element,"aria-selected",e)})],none:[ta("update",st)]}))],name:"toggling",active:Ng,apis:zg,state:{init:function(){var n=Po(!1);return{get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(!1)},readState:function(){return n.get()}}}}}),Wg="alloy.item-hover",Ug="alloy.item-focus",jg=rt(Wg),Gg=rt(Ug),Xg=[mo("data"),mo("components"),mo("dom"),Eo("hasSubmenu",!1),wo("toggling"),Bf("itemBehaviours",[Lg,Hg,Fg,Df]),Eo("ignoreFocus",!1),Eo("domModification",{}),ta("builder",function(t){return{dom:t.dom,domModification:lt(lt({},t.domModification),{attributes:lt(lt(lt({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Mf(t.itemBehaviours,[t.toggling.fold(Lg.revoke,function(t){return Lg.config(lt({aria:{mode:"checked"}},t))}),Hg.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){Cm(t)}}),Fg.config({mode:"execution"}),Df.config({store:{mode:"memory",initialValue:t.data}}),mm("item-type-events",H(H([],wm(),!0),[Cr(vi(),km),Cr(Ri(),Hg.focus)],!1))]),components:t.components,eventOrder:t.eventOrder}}),Eo("eventOrder",{})],Yg=[mo("dom"),mo("components"),ta("builder",function(t){return{dom:t.dom,components:t.components,events:eu([Cr(Ri(),function(t,n){n.stop()})])}})],qg=rt("item-widget"),Kg=rt([Jf({name:"widget",overrides:function(n){return{behaviours:nc([Df.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:st}})])}}})]),Jg=lo("type",{widget:[mo("uid"),mo("data"),mo("components"),mo("dom"),Eo("autofocus",!1),Eo("ignoreFocus",!1),Bf("widgetBehaviours",[Df,Hg,Fg]),Eo("domModification",{}),xl(Kg()),ta("builder",function(e){function o(t){return ml(t,e,"widget").map(function(t){return Fg.focusIn(t),t})}function t(t,n){return Ol(n.event.target)||e.autofocus&&n.setSource(t.element),vt.none()}var n=fl(qg(),e,Kg()),r=dl(qg(),e,n.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:eu([uu(function(t,n){o(t).each(function(t){n.stop()})}),Cr(vi(),km),Cr(Ri(),function(t,n){e.autofocus?o(t):Hg.focus(t)})]),behaviours:Mf(e.widgetBehaviours,[Df.config({store:{mode:"memory",initialValue:e.data}}),Hg.config({ignore:e.ignoreFocus,onFocus:function(t){Cm(t)}}),Fg.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:rc(),onLeft:t,onRight:t,onEscape:function(t,n){return Hg.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element),vt.none()):(Hg.focus(t),vt.some(!0))}})])}})],item:Xg,separator:Yg}),$g=rt([Zf({factory:{sketch:function(t){var n=so("menu.spec item",Jg,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return Tt(n,"uid")?n:lt(lt({},n),{uid:pu("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Qg=rt([mo("value"),mo("items"),mo("dom"),mo("components"),Eo("eventOrder",{}),Zs("menuBehaviours",[hd,Df,cd,Fg]),Do("movement",{mode:"menu",moveOnTab:!0},lo("mode",{grid:[Ia(),ta("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[ta("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),mo("rowSelector")],menu:[Eo("moveOnTab",!0),ta("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),go("markers",Fa()),Eo("fakeFocus",!1),Eo("focusManager",Wl()),Ju("onHighlight")]),Zg=rt("alloy.menu-focus"),tp=Cl({name:"Menu",configFields:Qg(),partFields:$g(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:nl(t.menuBehaviours,[hd.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),Df.config({store:{mode:"memory",initialValue:t.value}}),cd.config({find:vt.some}),Fg.config(t.movement.config(t,t.movement))]),events:eu([Cr(Gg(),function(n,e){var t=e.event;n.getSystem().getByDom(t.target).each(function(t){hd.highlight(n,t),e.stop(),br(n,Zg(),{menu:n,item:t})})}),Cr(jg(),function(t,n){var e=n.event.item;hd.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),np=function(e,o,r,t){return tt(r,t).bind(function(t){return tt(e,t).bind(function(t){var n=np(e,o,r,t);return vt.some([t].concat(n))})}).getOr([])},ep=function(){function a(t){return n(t).bind(Km)}function e(t){return tt(c.get(),t)}var c=Po({}),i=Po({}),s=Po({}),l=gc(),u=Po({}),n=function(t){return tt(i.get(),t)};return{setMenuBuilt:function(t,n){var e;i.set(lt(lt({},i.get()),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){l.set(t),c.set(e),i.set(n),u.set(o);var r=function(t,n){var e={};J(t,function(t,n){St(t,function(t){e[t]=n})});var o=n,r=_t(n,function(t,n){return{k:t,v:n}}),i=dt(r,function(t,n){return[n].concat(np(e,o,r,n))});return dt(e,function(t){return tt(i,t).getOr([t])})}(o,e);s.set(r)},expand:function(e){return tt(c.get(),e).map(function(t){var n=tt(s.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return tt(s.get(),t)},collapse:function(t){return tt(s.get(),t).bind(function(t){return 1<t.length?vt.some(t.slice(1)):vt.none()})},lookupMenu:n,lookupItem:e,otherMenus:function(t){var n=u.get();return W(Ct(n),t)},getPrimary:function(){return l.get().bind(a)},getMenus:function(){return i.get()},clear:function(){c.set({}),i.set({}),s.set({}),l.clear()},isClear:function(){return l.get().isNone()},getTriggeringPath:function(t,u){var n=F(e(t).toArray(),function(t){return a(t).isSome()});return tt(s.get(),t).bind(function(t){var e=L(n.concat(t));return function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return vt.none();n.push(o.getOrDie())}return vt.some(n)}(z(e,function(t,n){return o=t,r=u,i=e.slice(0,n+1),a(o).bind(function(n){return e=o,Q(c.get(),function(t,n){return t===e}).bind(function(t){return r(t).map(function(t){return{triggeredMenu:n,triggeringItem:t,triggeringPath:i}})});var e}).fold(function(){return mt(l.get(),t)?[]:[vt.none()]},function(t){return[vt.some(t)]});var o,r,i}))})}}},op=Km,rp=rt("collapse-item"),ip=kl({name:"TieredMenu",configFields:[Zu("onExecute"),Zu("onEscape"),Qu("onOpenMenu"),Qu("onOpenSubmenu"),Ju("onRepositionMenu"),Ju("onCollapseMenu"),Eo("highlightImmediately",!0),bo("data",[mo("primary"),mo("menus"),mo("expansions")]),Eo("fakeFocus",!1),Ju("onHighlight"),Ju("onHover"),Yu(),mo("dom"),Eo("navigateOnHover",!0),Eo("stayInDom",!1),Zs("tmenuBehaviours",[Fg,hd,cd,Rg]),Eo("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:function(a,t){function e(t){var o,r,n=(o=t,r=a.data.primary,dt(a.data.menus,function(t,n){function e(){return tp.sketch(lt(lt({},t),{value:n,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?Ul:Wl)()}))}return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),e=dt(a.data.menus,function(t,n){return z(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})});return g.setContents(a.data.primary,n,a.data.expansions,e),g.getPrimary()}function c(t){return Df.getValue(t).value}function u(n,t){hd.highlight(n,t),hd.getHighlighted(t).orThunk(function(){return hd.getFirst(t)}).each(function(t){xr(n,t.element,Ri())})}function s(n,t){return et(M(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?vt.some(t.menu):vt.none()})}))}function l(n,t,e){var o=s(t,t.otherMenus(e));St(o,function(t){$r(t.element,[a.markers.backgroundMenu]),a.stayInDom||Rg.remove(n,t)})}function f(t,o){var n;J((n=t,r.get().getOrThunk(function(){var e={},t=F(vs(n.element,"."+a.markers.item),function(t){return"true"===un(t,"aria-haspopup")});return St(t,function(t){n.getSystem().getByDom(t).each(function(t){var n=c(t);e[n]=t})}),r.set(e),e})),function(t,n){var e=wt(o,n);on(t.element,"aria-expanded",e)})}function d(o,r,i){return vt.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return vt.none();var n=t.menu,e=s(r,i.slice(1));return St(e,function(t){Yr(t.element,a.markers.backgroundMenu)}),ve(n.element)||Rg.append(o,Eu(n)),$r(n.element,[a.markers.backgroundMenu]),u(o,n),l(o,r,i),vt.some(n)})})}var m,n,r=gc(),g=ep();function i(r,i,u){if(void 0===u&&(u=m.HighlightSubmenu),i.hasConfigured(gd)&&gd.isDisabled(i))return vt.some(i);var t=c(i);return g.expand(t).bind(function(o){return f(r,o),vt.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return g.setMenuBuilt(n,o),o}(r,e,t);return ve(n.element)||Rg.append(r,Eu(n)),a.onOpenSubmenu(r,i,n,L(o)),u===m.HighlightSubmenu?(hd.highlightFirst(n),d(r,g,o)):(hd.dehighlightAll(n),vt.some(i))})})})}function o(n,e){var t=c(e);return g.collapse(t).bind(function(t){return f(n,t),d(n,g,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})}function p(e){return function(n,t){return Ru(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOptional().bind(function(t){return e(n,t).map(D)})})}}function h(t){return hd.getHighlighted(t).bind(hd.getHighlighted)}(n=m={})[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var v=eu([Cr(Zg(),function(e,o){var t=o.event.item;g.lookupItem(c(t)).each(function(){var t=o.event.menu;hd.highlight(e,t);var n=c(o.event.item);g.refresh(n).each(function(t){return l(e,g,t)})})}),uu(function(n,t){var e=t.event.target;n.getSystem().getByDom(e).each(function(t){0===c(t).indexOf("collapse-item")&&o(n,t),i(n,t,m.HighlightSubmenu).fold(function(){a.onExecute(n,t)},st)})}),ou(function(n,t){e(n).each(function(t){Rg.append(n,Eu(t)),a.onOpenMenu(n,t),a.highlightImmediately&&u(n,t)})})].concat(a.navigateOnHover?[Cr(jg(),function(t,n){var e=n.event.item,o=t,r=c(e);g.refresh(r).bind(function(t){return f(o,t),d(o,g,t)}),i(t,e,m.HighlightParent),a.onHover(t,e)})]:[])),b={collapseMenu:function(n){h(n).each(function(t){o(n,t)})},highlightPrimary:function(n){g.getPrimary().each(function(t){u(n,t)})},repositionMenus:function(o){g.getPrimary().bind(function(n){return h(o).bind(function(t){var n=c(t),e=et(M(Z(g.getMenus()),op));return g.getTriggeringPath(n,function(t){return n=t,K(e,function(t){return t.getSystem().isConnected()?V(hd.getCandidates(t),function(t){return c(t)===n}):vt.none()});var n})}).map(function(t){return{primary:n,triggeringPath:t}})}).fold(function(){vt.from(o.components()[0]).filter(function(t){return"menu"===un(t.element,"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var n=t.primary,e=t.triggeringPath;a.onRepositionMenu(o,n,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:nl(a.tmenuBehaviours,[Fg.config({mode:"special",onRight:p(function(t,n){return Ol(n.element)?vt.none():i(t,n,m.HighlightSubmenu)}),onLeft:p(function(t,n){return Ol(n.element)?vt.none():o(t,n)}),onEscape:p(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){g.getPrimary().each(function(t){xr(n,t.element,Ri())})}}),hd.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),cd.config({find:function(t){return hd.getHighlighted(t)}}),Rg.config({})]),eventOrder:a.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:sr(t,n),expansions:{}}},collapseItem:function(t){return{value:Ir(rp()),meta:{text:t}}}}}),up=kl({name:"InlineView",configFields:[mo("lazySink"),Ju("onShow"),Ju("onHide"),Oo("onEscape"),Zs("inlineBehaviours",[bf,Df,ic]),To("fireDismissalEventInstead",[Eo("event",Yi())]),To("fireRepositionEventInstead",[Eo("event",qi())]),Eo("getRelated",vt.none),Eo("isExtraPart",T),Eo("eventOrder",vt.none)],factory:function(d,t){function n(e){bf.isOpen(e)&&Df.getValue(e).each(function(t){switch(t.mode){case"menu":bf.getState(e).each(ip.repositionMenus);break;case"position":var n=d.lazySink(e).getOrDie();ff.positionWithinBounds(n,e,t.config,t.getBounds())}})}function o(t,n,e,o){i(t,n,e,function(){return o.map(function(t){return Ae(t)})})}function r(t,n,e,o){var r,i,u,a,c,s=(r=d,i=t,u=n,a=o,c="horizontal"===e.type?{layouts:{onLtr:ha,onRtl:va}}:{},ip.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,highlightImmediately:e.menu.highlightImmediately,onEscape:function(){return bf.close(i),r.onEscape.map(function(t){return t(i)}),vt.some(!0)},onExecute:function(){return vt.some(!0)},onOpenMenu:function(t,n){ff.positionWithinBounds(l().getOrDie(),n,u,a())},onOpenSubmenu:function(t,n,e,o){var r=l().getOrDie();ff.position(r,e,{anchor:lt({type:"submenu",item:n},f(o))})},onRepositionMenu:function(t,n,e){var o=l().getOrDie();ff.positionWithinBounds(o,n,u,a()),St(e,function(t){var n=f(t.triggeringPath);ff.position(o,t.triggeredMenu,{anchor:lt({type:"submenu",item:t.triggeringItem},n)})})}}));function l(){return r.lazySink(i)}function f(t){return 2===t.length?c:{}}bf.open(t,s),Df.setValue(t,vt.some({mode:"menu",menu:s}))}var i=function(t,n,e,o){var r=d.lazySink(t).getOrDie();bf.openWhileCloaked(t,n,function(){return ff.positionWithinBounds(r,t,e,o())}),Df.setValue(t,vt.some({mode:"position",config:e,getBounds:o}))},e={setContent:function(t,n){bf.setContent(t,n)},showAt:function(t,n,e){o(t,n,e,vt.none())},showWithin:o,showWithinBounds:i,showMenuAt:function(t,n,e){r(t,n,e,vt.none)},showMenuWithinBounds:r,hide:function(t){bf.isOpen(t)&&(Df.setValue(t,vt.none()),bf.close(t))},getContent:function(t){return bf.getState(t)},reposition:n,isOpen:bf.isOpen};return{uid:d.uid,dom:d.dom,behaviours:nl(d.inlineBehaviours,[bf.config({isPartOf:function(t,n,e){return zu(n,e)||(o=e,d.getRelated(t).exists(function(t){return zu(t,o)}));var o},getAttachPoint:function(t){return d.lazySink(t).getOrDie()},onOpen:function(t){d.onShow(t)},onClose:function(t){d.onHide(t)}}),Df.config({store:{mode:"memory",initialValue:vt.none()}}),ic.config({channels:lt(lt({},Xs(lt({isExtraPart:t.isExtraPart},d.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),Ys(lt(lt({},d.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:n})))})]),eventOrder:d.eventOrder,apis:e}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),ap="layout-inset",cp=function(t,n,e){return ea(Om(t,n),t.y,e.insetNorth(),Na(),"north",ra(t,{top:2}),ap)},sp=function(t,n,e){return ea(Om(t,n),Tm(t,n),e.insetSouth(),La(),"south",ra(t,{bottom:3}),ap)},lp=tinymce.util.Tools.resolve("tinymce.util.Delay"),fp=kl({name:"Button",factory:function(t){function e(n){return tt(t.dom,"attributes").bind(function(t){return tt(t,n)})}var n=Sm(t.action),o=t.dom.tag;return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:Mf(t.buttonBehaviours,[Hg.config({}),Fg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var t=e("type").getOr("button"),n=e("role").map(function(t){return{role:t}}).getOr({});return lt({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[Eo("uid",void 0),mo("dom"),Eo("components",[]),Bf("buttonBehaviours",[Hg,Fg]),wo("action"),wo("role"),Eo("eventOrder",{})]}),dp=tinymce.util.Tools.resolve("tinymce.util.I18n"),mp={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},gp="temporary-placeholder",pp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},hp=kl({name:"Notification",factory:function(n){function e(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}}function o(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}}var t,r,i,u,a=zm({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:nc([Rg.config({})])}),c=zm({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:nc([Rg.config({})])}),s={updateProgress:function(t,n){t.getSystem().isConnected()&&c.getOpt(t).each(function(t){Rg.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(n)]},o(n)])})},updateText:function(t,n){var e;t.getSystem().isConnected()&&(e=a.get(t),Rg.set(e,[ri(n)]))}},l=ft([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return vt.from(pp[t])}).toArray()]),f=zm(fp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Xm("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":n.translationProvider("Close")}},n.iconProvider)],action:function(t){n.onAction(t)}})),d=[(t=l,r={tag:"div",classes:["tox-notification__icon"]},i=n.iconProvider,u=i(),Gm(r,V(t,function(t){return Tt(u,Lm(t,u))}).getOr(gp),u,vt.none())),{dom:{tag:"div",classes:["tox-notification__body"]},components:[a.asSpec()],behaviours:nc([Rg.config({})])}];return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},behaviours:nc([Hg.config({}),mm("notification-events",[Cr(bi(),function(t){f.getOpt(t).each(Hg.focus)})])]),components:d.concat(n.progress?[c.asSpec()]:[]).concat(n.closeButton?[f.asSpec()]:[]),apis:s}},configFields:[wo("level"),mo("progress"),mo("icon"),mo("onAction"),mo("text"),mo("iconProvider"),mo("translationProvider"),Fo("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function vp(e,o){function r(){b(i)||(clearTimeout(i),i=null)}var i=null;return{cancel:r,throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r(),i=setTimeout(function(){i=null,e.apply(null,t)},o)}}}function bp(o,t,n,e,r){var i=Bp(o,function(t){return(n=o).isBlock(e=t)||wt(["BR","IMG","HR","INPUT"],e.nodeName)||"false"===n.getContentEditable(e);var n,e});return vt.from(i.backwards(t,n,e,r))}function yp(e,n){return Mp(At.fromDom(e.selection.getNode())).getOrThunk(function(){var i,u,t=At.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());return Te(t,At.fromDom(n.extractContents())),n.insertNode(t.dom),Yt(t).each(function(t){return t.dom.normalize()}),i=hs,(u=function(t){for(var n=Kt(t),e=n.length-1;0<=e;e--){var o=n[e];if(i(o))return vt.some(o);var r=u(o);if(r.isSome())return r}return vt.none()})(t).map(function(t){var n;e.selection.setCursorLocation(t.dom,"img"===Ft(n=t)?1:ps(n).fold(function(){return Kt(n).length},function(t){return t.length}))}),t})}function xp(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function wp(t){return""!==t&&-1!==" \xa0\f\n\r\t\v".indexOf(t)}function Sp(t,n){return t.substring(n.length)}function kp(o,t,r,n){return void 0===n&&(n=0),Mp(At.fromDom(t.startContainer)).fold(function(){return function(t,o,i,r){if(void 0===r&&(r=0),!o.collapsed||3!==o.startContainer.nodeType)return vt.none();var n=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return bp(t,o.startContainer,o.startOffset,function(t,r,n){return function(t,n){for(var e=r-1;0<=e;e--){var o=t.charAt(e);if(wp(o))return vt.none();if(o===n)break}return vt.some(e)}(n,i).getOr(r)},n).bind(function(t){var n=o.cloneRange();if(n.setStart(t.container,t.offset),n.setEnd(o.endContainer,o.endOffset),n.collapsed)return vt.none();var e=xp(n);return 0!==e.lastIndexOf(i)||Sp(e,i).length<r?vt.none():vt.some({text:Sp(e,i),range:n,triggerChar:i})})}(o,t,r,n)},function(t){var n=o.createRng();n.selectNode(t.dom);var e=xp(n);return vt.some({range:n,text:Sp(e,r),triggerChar:r})})}function Cp(t,n){return{container:t,offset:n}}function Op(t){return ao("toolbarbutton",zp,t)}function _p(t){return ao("ToggleButton",Lp,t)}function Tp(n,t,e,o){void 0===o&&(o={});var r=t(),i=n.selection.getRng().startContainer.nodeValue,u=F(r.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOrThunk(function(){return e=n.dom,function(t){var n=Ip(t.startContainer,t.startOffset);return!bp(e,n.container,n.offset,function(t,n){return 0===n?-1:n},e.getRoot()).filter(function(t){return!wp(t.container.data.charAt(t.offset-1))}).isSome()};var e})(e.range,i,e.text)});if(0===u.length)return vt.none();var a=Ap.all(M(u,function(n){return n.fetch(e.text,n.maxResults,o).then(function(t){return{matchText:e.text,items:t,columns:n.columns,onAction:n.onAction,highlightOn:n.highlightOn}})}));return vt.some({lookupData:a,context:e})}var Ep,Dp,Bp=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),Mp=function(t){return Ru(t,"[data-mce-autocompleter]")},Ap=tinymce.util.Tools.resolve("tinymce.util.Promise"),Fp=function(t){if(3===t.nodeType)return Cp(t,t.data.length);var n=t.childNodes;return 0<n.length?Fp(n[n.length-1]):Cp(t,n.length)},Ip=function(t,n){var e=t.childNodes;return 0<e.length&&n<e.length?Ip(e[n],0):0<e.length&&1===t.nodeType&&e.length===n?Fp(e[e.length-1]):Cp(t,n)},Rp=$o([po("type"),Co("text")]),Vp=$o([Eo("type","autocompleteitem"),Eo("active",!1),Eo("disabled",!1),Eo("meta",{}),po("value"),Co("text"),Co("icon")]),Pp=$o([po("type"),po("ch"),Bo("minChars",1),Eo("columns",1),Bo("maxResults",10),Oo("matches"),vo("fetch"),vo("onAction"),Ro("highlightOn",[],nr)]),Hp=[Fo("disabled",!1),Co("tooltip"),Co("icon"),Co("text"),Io("onSetup",function(){return st})],zp=$o([po("type"),vo("onAction")].concat(Hp)),Np=[Fo("active",!1)].concat(Hp),Lp=$o(Np.concat([po("type"),vo("onAction")])),Wp=[Io("predicate",T),Ao("scope","node",["node","editor"]),Ao("position","selection",["node","selection","line"])],Up=Hp.concat([Eo("type","contextformbutton"),Eo("primary",!1),vo("onAction"),cr("original",h)]),jp=Np.concat([Eo("type","contextformbutton"),Eo("primary",!1),vo("onAction"),cr("original",h)]),Gp=Hp.concat([Eo("type","contextformbutton")]),Xp=Np.concat([Eo("type","contextformtogglebutton")]),Yp=lo("type",{contextformbutton:Up,contextformtogglebutton:jp}),qp=$o([Eo("type","contextform"),Io("initValue",rt("")),Co("label"),xo("commands",Yp),So("launch",lo("type",{contextformbutton:Gp,contextformtogglebutton:Xp}))].concat(Wp)),Kp=$o([Eo("type","contexttoolbar"),po("items")].concat(Wp));function Jp(t){return tt(_h,t).getOr(kh)}function $p(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}}function Qp(t){var n=$p(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:Jp(t)}}function Zp(t,n,e){return{dom:{tag:"div",classes:ft([[$p(e).tieredMenu]])},markers:Qp(e)}}function th(n,e){return function(t){return M(p(t,e),function(t){return{dom:n,components:t}})}}function nh(t,e){var o=[],r=[];return St(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],Tt(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),M(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})}function eh(n,e){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[tp.parts.items({preprocess:function(t){return"auto"!==n&&1<n?th({tag:"div",classes:["tox-collection__group"]},n)(t):nh(t,function(t,n){return"separator"===e[n].type})}})]}}function oh(t){return d(t,function(t){return"icon"in t&&void 0!==t.icon})}function rh(t){return console.error(ur(t)),console.log(t),vt.none()}function ih(t,n,e,o,r){var i,u=(i=e,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[tp.parts.items({preprocess:function(t){return nh(t,function(t,n){return"separator"===i[n].type})}})]});return{value:t,dom:u.dom,components:u.components,items:e}}function uh(t,n,e,o,r){var i,u;return"color"===r?{value:t,dom:(i={dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[tp.parts.items({preprocess:"auto"!==o?th({tag:"div",classes:["tox-swatches__row"]},o):h})]}]}).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(i=eh(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:t,dom:(i=eh(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:t,dom:(i=eh(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:{tag:"div",classes:ft([[(u=$p(r)).menu,"tox-menu-"+o+"-column"],n?[u.hasIcons]:[]])},components:Ih,items:e}:{value:t,dom:(i={dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[tp.parts.items({preprocess:th({tag:"div",classes:["tox-collection__group"]},o)})]}).dom,components:i.components,items:e}}function ah(t,o,n){var r=vs(t.element,"."+n);if(0<r.length){var e=P(r,function(t){var n=t.dom.getBoundingClientRect().top,e=r[0].dom.getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return vt.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return vt.none()}function ch(t,n,e){t.getSystem().broadcastOn([Jh],{})}function sh(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))}function lh(t){return t.getParam("width",ev.DOM.getStyle(t.getElement(),"width"))}function fh(t){return vt.from(t.getParam("min_width")).filter(u)}function dh(t){return vt.from(t.getParam("min_height")).filter(u)}function mh(t){return vt.from(t.getParam("max_width")).filter(u)}function gh(t){return vt.from(t.getParam("max_height")).filter(u)}function ph(t){return!1!==t.getParam("menubar",!0,"boolean")}function hh(t){var n=t.getParam("toolbar",!0),e=!0===n,o=y(n),r=c(n)&&0<n.length;return!rv(t)&&(r||o||e)}function vh(n){var t=F(m(9,function(t){return n.getParam("toolbar"+(t+1),!1,"string")}),function(t){return"string"==typeof t});return 0<t.length?vt.some(t):vt.none()}(Dp=Ep={})[Dp.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Dp[Dp.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var bh,yh,xh,wh,Sh=Ep,kh="tox-menu-nav__js",Ch="tox-collection__item",Oh="tox-swatch",_h={normal:kh,color:Oh},Th="tox-collection__item--enabled",Eh="tox-collection__item-icon",Dh="tox-collection__item-label",Bh="tox-collection__item-caret",Mh="tox-collection__item--active",Ah="tox-collection__item-container",Fh="tox-collection__item-container--row",Ih=[tp.parts.items({})],Rh=[po("type"),po("src"),Co("alt"),Ro("classes",[],nr)],Vh=$o(Rh),Ph=[po("type"),po("text"),Co("name"),Ro("classes",["tox-collection__item-label"],nr)],Hh=$o(Ph),zh=Ze(function(){return ro("type",{cardimage:Vh,cardtext:Hh,cardcontainer:Nh})}),Nh=$o([po("type"),Mo("direction","horizontal"),Mo("align","left"),Mo("valign","middle"),xo("items",zh)]),Lh=[Fo("disabled",!1),Co("text"),Co("shortcut"),ar("value","value",je(function(){return Ir("menuitem-value")}),Zo()),Eo("meta",{})],Wh=$o([po("type"),Co("label"),xo("items",zh),Io("onSetup",function(){return st}),Io("onAction",st)].concat(Lh)),Uh=$o([po("type"),Fo("active",!1),Co("icon")].concat(Lh)),jh=[po("type"),po("fancytype"),Io("onAction",st)],Gh=lo("fancytype",{inserttable:[Eo("initData",{})].concat(jh),colorswatch:[Vo("initData",{},[Fo("allowCustomColors",!0),_o("colors",Zo())])].concat(jh)}),Xh=$o([po("type"),Io("onSetup",function(){return st}),Io("onAction",st),Co("icon")].concat(Lh)),Yh=$o([po("type"),vo("getSubmenuItems"),Io("onSetup",function(){return st}),Co("icon")].concat(Lh)),qh=$o([po("type"),Co("icon"),Fo("active",!1),Io("onSetup",function(){return st}),vo("onAction")].concat(Lh)),Kh=function(t){return n=Ir("unnamed-events"),nc([mm(n,t)]);var n},Jh=Ir("tooltip.exclusive"),$h=Ir("tooltip.show"),Qh=Ir("tooltip.hide"),Zh=Object.freeze({__proto__:null,hideAllExclusive:ch,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&Rg.set(t,o)})}}),tv=Object.freeze({__proto__:null,events:function(r,i){function e(n){i.getTooltip().each(function(t){Hs(t),r.onHide(n,t),i.clearTooltip()}),i.clearTimer()}return eu(ft([[Cr($h,function(o){i.resetTimer(function(){var t,n,e=o;i.isShowing()||(ch(e),t=r.lazySink(e).getOrDie(),n=e.getSystem().build({dom:r.tooltipDom,components:r.tooltipComponents,events:eu("normal"===r.mode?[Cr(vi(),function(t){vr(e,$h)}),Cr(pi(),function(t){vr(e,Qh)})]:[]),behaviours:nc([Rg.config({})])}),i.setTooltip(n),Vs(t,n),r.onShow(e,n),ff.position(t,n,{anchor:r.anchor(e)}))},r.delay)}),Cr(Qh,function(t){i.resetTimer(function(){e(t)},r.delay)}),Cr(Fi(),function(t,n){n.universal||wt(n.channels,Jh)&&e(t)}),ru(function(t){e(t)})],"normal"===r.mode?[Cr(bi(),function(t){vr(t,$h)}),Cr(Mi(),function(t){vr(t,Qh)}),Cr(vi(),function(t){vr(t,$h)}),Cr(pi(),function(t){vr(t,Qh)})]:[Cr(Zi(),function(t,n){vr(t,$h)}),Cr(tu(),function(t){vr(t,Qh)})]]))}}),nv=xa({fields:[mo("lazySink"),mo("tooltipDom"),Eo("exclusive",!0),Eo("tooltipComponents",[]),Eo("delay",300),Ao("mode","normal",["normal","follow-highlight"]),Eo("anchor",function(t){return{type:"hotspot",hotspot:t,layouts:{onLtr:rt([$a,Ja,Xa,qa,Ya,Ka]),onRtl:rt([$a,Ja,Xa,qa,Ya,Ka])}}}),Ju("onHide"),Ju("onShow")],name:"tooltipping",active:tv,state:Object.freeze({__proto__:null,init:function(){function e(){o.on(clearTimeout)}var o=gc(),t=gc(),n=rt("not-implemented");return wu({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:e,resetTimer:function(t,n){e(),o.set(setTimeout(t,n))},readState:n})}}),apis:Zh}),ev=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ov=tinymce.util.Tools.resolve("tinymce.EditorManager"),rv=function(t){return vh(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},D)};function iv(t){return t.getParam("toolbar_mode","","string")}function uv(t){return t.getParam("toolbar_location",xh.auto,"string")}function av(t){return uv(t)===xh.bottom}function cv(t){if(!t.inline)return vt.none();var n=t.getParam("fixed_toolbar_container","","string");if(0<n.length)return Iu(be(),n);var e=t.getParam("fixed_toolbar_container_target");return k(e)?vt.some(At.fromDom(e)):vt.none()}function sv(t){return t.inline&&cv(t).isSome()}function lv(t){return cv(t).getOrThunk(function(){return tn(ge(At.fromDom(t.getElement())))})}function fv(t){return t.inline&&!ph(t)&&!hh(t)&&!rv(t)}function dv(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!sv(t)&&!fv(t)}function mv(t,n){var e=t.outerContainer.element;n&&(t.mothership.broadcastOn([yf()],{target:e}),t.uiMothership.broadcastOn([yf()],{target:e})),t.mothership.broadcastOn([Kv],{readonly:n}),t.uiMothership.broadcastOn([Kv],{readonly:n})}function gv(t,n){t.on("init",function(){t.mode.isReadOnly()&&mv(n,!0)}),t.on("SwitchMode",function(){return mv(n,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")}function pv(){var t;return ic.config({channels:((t={})[Kv]={schema:Jv,onReceive:function(t,n){gd.set(t,n.readonly)}},t)})}function hv(t,n){var e=t.getApi(n);return function(t){t(e)}}function vv(e,o){return ou(function(t){hv(e,t)(function(t){var n=e.onSetup(t);S(n)&&o.set(n)})})}function bv(n,e){return ru(function(t){return hv(n,t)(e.get())})}function yv(t,n,e,o){var r,i,u=Po(st);return{type:"item",dom:n.dom,components:tb(n.optComponents),data:t.data,eventOrder:Zv,hasSubmenu:t.triggersSubmenu,itemBehaviours:nc([mm("item-events",[(r=t,i=e,uu(function(t,n){hv(r,t)(r.onAction),r.triggersSubmenu||i!==Sh.CLOSE_ON_EXECUTE||(vr(t,Hi()),n.stop())})),vv(t,u),bv(t,u)]),gd.config({disabled:function(){return t.disabled||o.isDisabled()},disableClass:"tox-collection__item--state-disabled"}),pv(),Rg.config({})].concat(t.itemBehaviours))}}function xv(t){return{value:t.value,meta:lt({text:t.text.getOr("")},t.meta)}}function wv(t,n,e){return Xm(t,{tag:"div",classes:e=void 0===e?[Eh]:e},n)}function Sv(t){return{dom:{tag:"div",classes:[Dh]},components:[ri(dp.translate(t))]}}function kv(t,n){return{dom:{tag:"div",classes:n,innerHtml:t}}}function Cv(t,n){return{dom:{tag:"div",classes:[Dh]},components:[{dom:{tag:t.tag,styles:t.styles},components:[ri(dp.translate(n))]}]}}function Ov(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(e=nb.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},n=M(t.split("+"),function(t){var n=t.toLowerCase().trim();return Tt(e,n)?e[n]:t}),nb.mac?n.join(""):n.join("+"))}};var e,n}function _v(t){return wv("checkmark",t,["tox-collection__item-checkmark"])}function Tv(t){var n=t.map(function(t){return{attributes:{title:dp.translate(t)}}}).getOr({});return lt({tag:"div",classes:[kh,Ch]},n)}function Ev(t,n,e,o){return void 0===o&&(o=vt.none()),"color"===t.presets?(r=n,i=o,c=t.ariaLabel,s=t.value,{dom:(u=t.iconContent.map(function(t){return n=r.icons,e=i,Wm(t,o=n()).or(e).getOrThunk(Nm(o));var n,e,o}).getOr(""),a={tag:"div",attributes:c.map(function(t){return{title:r.translate(t)}}).getOr({}),classes:["tox-swatch"]},lt(lt({},a),"custom"===s?{tag:"button",classes:H(H([],a.classes,!0),["tox-swatches__picker-btn"],!1),innerHtml:u}:"remove"===s?{classes:H(H([],a.classes,!0),["tox-swatch--remove"],!1),innerHtml:u}:{attributes:lt(lt({},a.attributes),{"data-mce-color":s}),styles:{"background-color":s}})),optComponents:[]}):(l=t,f=n,d=o,m={tag:"div",classes:[Eh]},g=e?l.iconContent.map(function(t){return Xm(t,m,f.icons,d)}).orThunk(function(){return vt.some({dom:m})}):vt.none(),p=l.checkMark,h=vt.from(l.meta).fold(function(){return Sv},function(t){return Tt(t,"style")?C(Cv,t.style):Sv}),v=l.htmlContent.fold(function(){return l.textContent.map(h)},function(t){return vt.some(kv(t,[Dh]))}),{dom:Tv(l.ariaLabel),optComponents:[g,v,l.shortcutContent.map(Ov),p,l.caret]});var r,i,u,a,c,s,l,f,d,m,g,p,h,v}function Dv(t,n){return tt(t,"tooltipWorker").map(function(e){return[nv.config({lazySink:n.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{type:"submenu",item:t,overrides:{maxHeightFunction:Vc}}},mode:"follow-highlight",onShow:function(n,t){e(function(t){nv.setComponents(n,[Ou({element:At.fromDom(t)})])})}})]}).getOr([])}function Bv(t,n){var e=dp.translate(t),o=ev.DOM.encode(e);if(0<n.length){var r=new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return o.replace(r,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return o}function Mv(t){return{value:t}}function Av(t){return rb.test(t)||ib.test(t)}function Fv(t){var n=t.toString(16);return(1===n.length?"0"+n:n).toUpperCase()}function Iv(t){return Mv(Fv(t.red)+Fv(t.green)+Fv(t.blue))}function Rv(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}}function Vv(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255}function Pv(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100,i=ab(0,ub(i,1)),u=ab(0,ub(u,1));if(0===i)return Rv(n=e=o=cb(255*u),e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return Rv(n=cb(255*(n+l)),e=cb(255*(e+l)),o=cb(255*(o+l)),1)}function Hv(t){var n,e,o=(n={value:t.value.replace(rb,function(t,n,e,o){return n+n+e+e+o+o})},null===(e=ib.exec(n.value))?["FFFFFF","FF","FF","FF"]:e);return Rv(parseInt(o[1],16),parseInt(o[2],16),parseInt(o[3],16),1)}function zv(t,n,e,o){return Rv(parseInt(t,10),parseInt(n,10),parseInt(e,10),parseFloat(o))}function Nv(t){if("transparent"===t)return vt.some(Rv(0,0,0,0));var n=sb.exec(t);if(null!==n)return vt.some(zv(n[1],n[2],n[3],"1"));var e=lb.exec(t);return null!==e?vt.some(zv(e[1],e[2],e[3],e[4])):vt.none()}function Lv(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"}function Wv(t,n){return t.fire("ResizeContent",n)}function Uv(t,n,e){return{hue:t,saturation:n,value:e}}function jv(t){var n,e,o=0,r=t.red/255,i=t.green/255,u=t.blue/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?Uv(0,0,100*(o=a)):(e=60*((r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),n=(c-a)/c,o=c,Uv(Math.round(e),Math.round(100*n),Math.round(100*o)))}function Gv(t){return Iv(Pv(t))}function Xv(o){return(Av(n=o)?vt.some({value:(Dt(t=n,"#")?t.substring("#".length):t).toUpperCase()}):vt.none()).orThunk(function(){return Nv(o).map(Iv)}).getOrThunk(function(){var t=document.createElement("canvas");t.height=1,t.width=1;var n=t.getContext("2d");n.clearRect(0,0,t.width,t.height),n.fillStyle="#FFFFFF",n.fillStyle=o,n.fillRect(0,0,1,1);var e=n.getImageData(0,0,1,1).data;return Iv(Rv(e[0],e[1],e[2],e[3]))});var t,n}(yh=bh=bh||{}).default="wrap",yh.floating="floating",yh.sliding="sliding",yh.scrolling="scrolling",(wh=xh=xh||{}).auto="auto",wh.top="top",wh.bottom="bottom";function Yv(t){return gd.config({disabled:t,disableClass:"tox-tbtn--disabled"})}var qv,Kv="silver.readonly",Jv=$o([go("readonly",er)]),$v=function(t){return gd.config({disabled:t})},Qv=function(t){return gd.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},Zv=((qv={})[Ii()]=["disabling","alloy.base.behaviour","toggling","item-events"],qv),tb=et,nb=tinymce.util.Tools.resolve("tinymce.Env"),eb=function(t,a){return M(t,function(t){switch(t.type){case"cardcontainer":return r=eb((o=t).items,a),i="vertical"===o.direction?"tox-collection__item-container--column":Fh,u="left"===o.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right",{dom:{tag:"div",classes:[Ah,i,u,function(){switch(o.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}}()]},components:r};case"cardimage":return e=t.src,{dom:{tag:"img",classes:t.classes,attributes:{src:e,alt:t.alt.getOr("")}}};case"cardtext":var n=t.name.exists(function(t){return wt(a.cardText.highlightOn,t)})?vt.from(a.cardText.matchText).getOr(""):"";return kv(Bv(t.text,n),t.classes)}var e,o,r,i,u})},ob=al(qg(),Kg()),rb=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,ib=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ub=Math.min,ab=Math.max,cb=Math.round,sb=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,lb=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,fb=Rv(255,0,0,1),db=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),mb="tinymce-custom-colors";function gb(t){return!1!==t.getParam("custom_colors")}function pb(t){var n=t.getParam("color_map");return void 0!==n?function(t){for(var n=[],e=0;e<t.length;e+=2)n.push({text:t[e+1],value:"#"+Xv(t[e]).value,type:"choiceitem"});return n}(n):Mb}function hb(t){Ab.add(t)}function vb(t){var n,e=(n=pb(t).length,Math.max(5,Math.ceil(Math.sqrt(n))));return t.getParam("color_cols",e,"number")}function bb(t){var n="choiceitem",e={type:n,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return t?[e,{type:n,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}function yb(n,e,t,o){"custom"===t?Ib(n)(function(t){t.each(function(t){hb(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},Fb):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))}function xb(t,n){return t.concat(M(Ab.state(),function(t){return{type:Bb,text:t,value:t}}).concat(bb(n)))}function wb(n,e){return function(t){t(xb(n,e))}}function Sb(t,n,e){t.setIconFill("forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",e)}function kb(i,e,u,t,o){i.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){var o,r=u;return i.dom.getParents(i.selection.getStart(),function(t){var n;(n=t.style["forecolor"===r?"color":"background-color"])&&(o=o||n)}),vt.from(o).bind(function(t){return Nv(t).map(function(t){var n=Iv(t).value;return ut(e.toLowerCase(),n)})}).getOr(!1)},columns:vb(i),fetch:wb(pb(i),gb(i)),onAction:function(t){yb(i,u,o.get(),st)},onItemAction:function(t,n){yb(i,u,n,function(t){o.set(t),i.fire("TextColorChange",{name:e,color:t})})},onSetup:function(n){function t(t){t.name===e&&Sb(n,t.name,t.color)}return Sb(n,e,o.get()),i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})}function Cb(n,t,e,o){n.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){yb(n,e,t.value,st)}}]}})}function Ob(t,n,e,o,r,i,u,a){return uh(t,oh(n),Rb(n,e,o,"color"!==r?"normal":"color",i,u,a),o,r)}function _b(t,n){var e=Qp(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}}function Tb(t,n){return ri(n+"x"+t)}var Eb,Db,Bb="choiceitem",Mb=[{type:Bb,text:"Light Green",value:"#BFEDD2"},{type:Bb,text:"Light Yellow",value:"#FBEEB8"},{type:Bb,text:"Light Red",value:"#F8CAC6"},{type:Bb,text:"Light Purple",value:"#ECCAFA"},{type:Bb,text:"Light Blue",value:"#C2E0F4"},{type:Bb,text:"Green",value:"#2DC26B"},{type:Bb,text:"Yellow",value:"#F1C40F"},{type:Bb,text:"Red",value:"#E03E2D"},{type:Bb,text:"Purple",value:"#B96AD9"},{type:Bb,text:"Blue",value:"#3598DB"},{type:Bb,text:"Dark Turquoise",value:"#169179"},{type:Bb,text:"Orange",value:"#E67E23"},{type:Bb,text:"Dark Red",value:"#BA372A"},{type:Bb,text:"Dark Purple",value:"#843FA1"},{type:Bb,text:"Dark Blue",value:"#236FA1"},{type:Bb,text:"Light Gray",value:"#ECF0F1"},{type:Bb,text:"Medium Gray",value:"#CED4D9"},{type:Bb,text:"Gray",value:"#95A5A6"},{type:Bb,text:"Dark Gray",value:"#7E8C8D"},{type:Bb,text:"Navy Blue",value:"#34495E"},{type:Bb,text:"Black",value:"#000000"},{type:Bb,text:"White",value:"#ffffff"}],Ab=function(e){void 0===e&&(e=10);function o(t){i.splice(t,1)}var t,n=db.getItem(mb),r=y(n)?JSON.parse(n):[],i=e-(t=r).length<0?t.slice(0,e):t;return{add:function(t){var n;(-1===(n=l(i,t))?vt.none():vt.some(n)).each(o),i.unshift(t),i.length>e&&i.pop(),db.setItem(mb,JSON.stringify(i))},state:function(){return i.slice(0)}}}(10),Fb="#000000",Ib=function(r){return function(e,t){var o=!1;r.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:{colorpicker:t},onAction:function(t,n){"hex-valid"===n.name&&(o=n.value)},onSubmit:function(t){var n=t.getData().colorpicker;o?(e(vt.from(n)),t.close()):r.windowManager.alert(r.translate(["Invalid hex color code: {0}",n]))},onClose:st,onCancel:function(){e(vt.none())}})}},Rb=function(e,o,r,i,u,a,c){return et(M(e,function(n){return"choiceitem"===n.type?ao("choicemenuitem",Uh,n).fold(rh,function(t){return vt.some(function(n,t,e,o,r,i,u,a){void 0===a&&(a=!0);var c=Ev({presets:e,textContent:t?n.text:vt.none(),htmlContent:vt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:vt.none(),checkMark:t?vt.some(_v(u.icons)):vt.none(),caret:vt.none(),value:n.value},u,a);return Yo(yv({data:xv(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){Lg.set(n,t)},isActive:function(){return Lg.isOn(n)},isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),st},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:Th,toggleOnExecute:!1,selected:n.active}})}(t,1===r,i,o,a(n.value),u,c,oh(e)))}):vt.none()}))},Vb=Ir("cell-over"),Pb=Ir("cell-execute"),Hb={inserttable:function(u){var t=Ir("size-label"),a=function(t){for(var n=[],e=0;e<10;e++){for(var o=[],r=0;r<10;r++)o.push(function(n,e,t){function o(t){return br(t,Pb,{row:n,col:e})}function r(t,n){n.stop(),o(t)}var i;return Tu({dom:{tag:"div",attributes:((i={role:"button"})["aria-labelledby"]=t,i)},behaviours:nc([mm("insert-table-picker-cell",[Cr(vi(),Hg.focus),Cr(Ii(),o),Cr(Ci(),r),Cr(Vi(),r)]),Lg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Hg.config({onFocus:function(t){return br(t,Vb,{row:n,col:e})}})])})}(e,r,t));n.push(o)}return n}(t),n=Tb(0,0),c=zm({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:nc([Rg.config({})])});return{type:"widget",data:{value:Ir("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ob.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:z(a,function(t){return M(t,Eu)}).concat(c.asSpec()),behaviours:nc([mm("insert-table-picker",[ou(function(t){Rg.set(c.get(t),[n])}),Er(Vb,function(t,n,e){var o=e.event,r=o.row,i=o.col;!function(t,n,e){for(var o=0;o<10;o++)for(var r=0;r<10;r++)Lg.set(t[o][r],o<=n&&r<=e)}(a,r,i),Rg.set(c.get(t),[Tb(r+1,i+1)])}),Er(Pb,function(t,n,e){var o=e.event,r=o.row,i=o.col;u.onAction({numRows:r+1,numColumns:i+1}),vr(t,Hi())})]),Fg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function(n,t){var e,o,r,i=(o=t,r=(e=n).initData.allowCustomColors&&o.colorinput.hasCustomColors(),e.initData.colors.fold(function(){return xb(o.colorinput.getColors(),r)},function(t){return t.concat(bb(r))})),u=t.colorinput.getColorCols(),a=Ob(Ir("menu-value"),i,function(t){n.onAction({value:t})},u,"color",Sh.CLOSE_ON_EXECUTE,T,t.shared.providers),c=lt(lt({},a),{markers:Qp("color"),movement:_b(u,"color")});return{type:"widget",data:{value:Ir("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[ob.widget(tp.sketch(c))]}}},zb=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:lt({tag:"div",classes:[Ch,"tox-collection__group-heading"]},n),components:[]}},Nb=function(t,n,e,o){void 0===o&&(o=!0);var r=Ev({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,caret:vt.none(),checkMark:vt.none(),shortcutContent:t.shortcut},e,o);return yv({data:xv(t),getApi:function(n){return{isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e)},Lb=function(t,n,e,o,r){void 0===o&&(o=!0);var i=(r=void 0!==r&&r)?wv("chevron-down",e.icons,[Bh]):wv("chevron-right",e.icons,[Bh]),u=Ev({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,caret:vt.some(i),checkMark:vt.none(),shortcutContent:t.shortcut},e,o);return yv({data:xv(t),getApi:function(n){return{isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)}}},disabled:t.disabled,onAction:st,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,n,e)},Wb=function(t,n,e,o){void 0===o&&(o=!0);var r=Ev({iconContent:t.icon,textContent:t.text,htmlContent:vt.none(),ariaLabel:t.text,checkMark:vt.some(_v(e.icons)),caret:vt.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return Yo(yv({data:xv(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){Lg.set(n,t)},isActive:function(){return Lg.isOn(n)},isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e),{toggling:{toggleClass:Th,toggleOnExecute:!1,selected:t.active}})},Ub=function(n,e){return tt(Hb,n.fancytype).map(function(t){return t(n,e)})};function jb(t,u,a,n,c,s,l){var e=1===n,o=!e||oh(t);return et(M(t,function(t){switch(t.type){case"separator":return ao("Autocompleter.Separator",Rp,t).fold(rh,function(t){return vt.some(zb(t))});case"cardmenuitem":return ao("cardmenuitem",Wh,t).fold(rh,function(n){return vt.some((t=lt(lt({},n),{onAction:function(t){n.onAction(t),a(n.value,n.meta)}}),e=c,o=s,r={itemBehaviours:Dv(n.meta,s),cardText:{matchText:u,highlightOn:l}},i={dom:Tv(t.label),optComponents:[vt.some({dom:{tag:"div",classes:[Ah,Fh]},components:eb(t.items,r)})]},yv({data:xv(lt({text:vt.none()},t)),disabled:t.disabled,getApi:function(e){return{isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(n){gd.set(e,n),St(vs(e.element,"*"),function(t){e.getSystem().getByDom(t).each(function(t){t.hasConfigured(gd)&&gd.set(t,n)})})}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:vt.from(r.itemBehaviours).getOr([])},i,e,o.providers)));var t,e,o,r,i});default:return ao("Autocompleter.Item",Vp,t).fold(rh,function(t){return vt.some(function(n,e,t,o,r,i,u,a){void 0===a&&(a=!0);var c=Ev({presets:o,textContent:vt.none(),htmlContent:t?n.text.map(function(t){return Bv(t,e)}):vt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:vt.none(),checkMark:vt.none(),caret:vt.none(),value:n.value},u.providers,a,n.icon);return yv({data:xv(n),disabled:n.disabled,getApi:rt({}),onAction:function(t){return r(n.value,n.meta)},onSetup:rt(st),triggersSubmenu:!1,itemBehaviours:Dv(n.meta,u)},c,i,u.providers)}(t,u,e,"normal",a,c,s,o))})}}))}function Gb(t,n,e,o,r){var i=oh(n),u=et(M(n,function(t){function n(t){return function(t,n,e,o,r){function i(t){return r?lt(lt({},t),{shortcut:vt.none(),icon:t.text.isSome()?vt.none():t.icon}):t}var u=e.shared.providers;switch(t.type){case"menuitem":return ao("menuitem",Xh,t).fold(rh,function(t){return vt.some(Nb(i(t),n,u,o))});case"nestedmenuitem":return ao("nestedmenuitem",Yh,t).fold(rh,function(t){return vt.some(Lb(i(t),n,u,o,r))});case"togglemenuitem":return ao("togglemenuitem",qh,t).fold(rh,function(t){return vt.some(Wb(i(t),n,u,o))});case"separator":return ao("separatormenuitem",Rp,t).fold(rh,function(t){return vt.some(zb(t))});case"fancymenuitem":return ao("fancymenuitem",Gh,t).fold(rh,function(t){return Ub(i(t),e)});default:return console.error("Unknown item in general menu",t),vt.none()}}(t,e,o,r?!Tt(t,"text"):i,r)}return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?n(lt(lt({},t),{disabled:!0})):n(t)}));return(r?ih:uh)(t,i,u,1,"normal")}function Xb(t){return ip.singleData(t.value,t)}function Yb(t,n,e){return Ru(t,n,e).isSome()}function qb(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=setTimeout(function(){e.apply(null,t),r=null},o)}}}function Kb(t){var n=t.raw;return void 0===n.touches||1!==n.touches.length?vt.none():vt.some(n.touches[0])}function Jb(){return le().browser.isFirefox()}function $b(n,t){var e,o,r,i,u,a,c,s,l,f=lt({stopBackspace:!0},t),d=(u=f,a=gc(),c=Po(!1),s=qb(function(t){u.triggerEvent(Pi(),t),c.set(!0)},400),l=lr([{key:si(),value:function(e){return Kb(e).each(function(t){s.cancel();var n={x:t.clientX,y:t.clientY,target:e.target};s.schedule(e),c.set(!1),a.set(n)}),vt.none()}},{key:li(),value:function(t){return s.cancel(),Kb(t).each(function(i){a.on(function(t){var n=i,e=t,o=Math.abs(n.clientX-e.x),r=Math.abs(n.clientY-e.y);(5<o||5<r)&&a.clear()})}),vt.none()}},{key:fi(),value:function(n){return s.cancel(),a.get().filter(function(t){return Lt(t.target,n.target)}).map(function(t){return c.get()?(n.prevent(),!1):u.triggerEvent(Vi(),n)})}}]),{fireIfReady:function(n,t){return tt(l,t).bind(function(t){return t(n)})}}),m=M(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return pc(n,t,function(n){d.fireIfReady(n,t).each(function(t){t&&n.kill()}),f.triggerEvent(t,n)&&n.kill()})}),g=gc(),p=pc(n,"paste",function(n){d.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),f.triggerEvent("paste",n)&&n.kill(),g.set(setTimeout(function(){f.triggerEvent(Ai(),n)},0))}),h=pc(n,"keydown",function(t){var n;f.triggerEvent("keydown",t)?t.kill():!f.stopBackspace||(n=t).raw.which!==vd[0]||wt(["input","textarea"],Ft(n.target))||Yb(n.target,'[contenteditable="true"]')||t.prevent()}),v=(e=n,o=function(t){f.triggerEvent("focusin",t)&&t.kill()},Jb()?hc(e,"focus",o):pc(e,"focusin",o)),b=gc(),y=(r=n,i=function(t){f.triggerEvent("focusout",t)&&t.kill(),b.set(setTimeout(function(){f.triggerEvent(Mi(),t)},0))},Jb()?hc(r,"blur",i):pc(r,"focusout",i));return{unbind:function(){St(m,function(t){t.unbind()}),h.unbind(),v.unbind(),y.unbind(),p.unbind(),g.on(clearTimeout),b.on(clearTimeout)}}}function Qb(t,n){return Po(tt(t,"target").getOr(n))}function Zb(t,o,n,e,r,i){var u,a,c=t(o,e),s=(u=Po(!1),a=Po(!1),{stop:function(){u.set(!0)},cut:function(){a.set(!0)},isStopped:u.get,isCut:a.get,event:n,setSource:r.set,getSource:r.get});return c.fold(function(){return i.logEventNoHandlers(o,e),ay.complete()},function(n){var e=n.descHandler;return Wr(e)(s),s.isStopped()?(i.logEventStopped(o,n.element,e.purpose),ay.stopped()):s.isCut()?(i.logEventCut(o,n.element,e.purpose),ay.complete()):Yt(n.element).fold(function(){return i.logNoParent(o,n.element,e.purpose),ay.complete()},function(t){return i.logEventResponse(o,n.element,e.purpose),ay.resume(t)})})}function ty(){function r(t){Rr(t.element).each(function(t){delete a[t],i.unregisterId(t)})}var u,i=(u={},{registerId:function(r,i,t){J(t,function(t,n){var e,o=void 0!==u[n]?u[n]:{};o[i]={cHandler:C.apply(void 0,[(e=t).handler].concat(r)),purpose:e.purpose},u[n]=o})},unregisterId:function(e){J(u,function(t,n){Tt(t,e)&&delete t[e]})},filterByType:function(t){return tt(u,t).map(function(t){return $(t,function(t,n){return{id:n,descHandler:t}})}).getOr([])},find:function(t,n,e){return tt(u,n).bind(function(o){return mr(e,function(t){return n=o,Rr(e=t).bind(function(t){return tt(n,t)}).map(function(t){return{element:e,descHandler:t}});var n,e},t)})}}),a={};return{find:function(t,n,e){return i.find(t,n,e)},filter:function(t){return i.filterByType(t)},register:function(t){var e,o=Rr((e=t).element).getOrThunk(function(){return t=e.element,n=Ir(du+"uid-"),gu(t,n),n;var t,n});nt(a,o)&&function(t){var n=a[o];if(n!==t)throw new Error('The tagId "'+o+'" is already used by: '+Fr(n.element)+"\nCannot use it for: "+Fr(t.element)+"\nThe conflicting element is"+(ve(n.element)?" ":" not ")+"already in the DOM");r(t)}(t),i.registerId([t],o,t.events),a[o]=t},unregister:r,getById:function(t){return tt(a,t)}}}function ny(e){function o(n){return Yt(e.element).fold(D,function(t){return Lt(n,t)})}function s(t,n){return i.find(o,t,n)}function r(n){var t=i.filter(Fi());St(t,function(t){Wr(t.descHandler)(n)})}var i=ty(),t=$b(e.element,{triggerEvent:function(n,e){return Lu(n,e.target,function(t){return sy(s,n,e,e.target,t)})}}),u={debugInfo:rt("real"),triggerEvent:function(n,e,o){Lu(n,e,function(t){return sy(s,n,o,e,t)})},triggerFocus:function(a,c){Rr(a).fold(function(){ka(a)},function(t){Lu(Bi(),a,function(t){var n,e,o=s,r=Bi(),i=t,u=Qb(n={originator:c,kill:st,prevent:st,target:a},e=a);return Zb(o,r,n,e,u,i),!1})})},triggerEscape:function(t,n){u.triggerEvent("keydown",t.element,n.event)},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:Tu,addToGui:function(t){c(t)},removeFromGui:function(t){l(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){a(t)},broadcast:function(t){f(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:D},n=function(t){t.connect(u),Yn(t.element)||(i.register(t),St(t.components(),n),u.triggerEvent(Ni(),t.element,{target:t.element}))},a=function(t){Yn(t.element)||(St(t.components(),a),i.unregister(t)),t.disconnect()},c=function(t){Vs(e,t)},l=function(t){Hs(t)},f=function(t){r({universal:!0,data:t})},d=function(t,n){r({universal:!1,channels:t,data:n})},m=function(t,n){var e,o,r=i.filter(t);return o={stop:function(){e.set(!0)},cut:st,isStopped:(e=Po(!1)).get,isCut:T,event:n,setSource:_("Cannot set source of a broadcasted event"),getSource:_("Cannot get source of a broadcasted event")},St(r,function(t){Wr(t.descHandler)(o)}),o.isStopped()},g=function(t){return i.getById(t).fold(function(){return Ve.error(new Error('Could not find component with uid: "'+t+'" in system.'))},Ve.value)},p=function(t){var n=Rr(t).getOr("not found");return g(n)};return n(e),{root:e,element:e.element,destroy:function(){t.unbind(),Ee(e.element)},add:c,remove:l,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:a,broadcast:f,broadcastOn:d,broadcastEvent:m}}function ey(t,n,e,o){var r=vy(t,n,e,o);return my.sketch(r)}function oy(t,n){return my.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})}function ry(t){return nc([Hg.config({onFocus:t.selectOnFocus?function(t){var n=t.element,e=Qr(n);n.dom.setSelectionRange(0,e.length)}:st})])}function iy(t){return{tag:t.tag,attributes:lt({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}}(Db=Eb={})[Db.ContentFocus=0]="ContentFocus",Db[Db.UiFocus=1]="UiFocus";function uy(f,c){function e(){return r.get().isSome()}function s(){e()&&up.hide(d)}var o,t,r=gc(),l=Po(!1),d=Tu(up.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:nc([mm("dismissAutocompleter",[Cr(Yi(),function(){return m()})])]),lazySink:c.getSink})),m=function(){var t;e()&&(t=r.get().map(function(t){return t.element}),Mp(t.getOr(At.fromDom(f.selection.getNode()))).each(In),s(),r.clear(),l.set(!1))},u=Rt(function(){return e=dt(f.ui.registry.getAll().popups,function(t){return ao("Autocompleter",Pp,t).fold(function(t){throw new Error(ur(t))},h)}),t=$(e,function(t){return t.ch}),n={},St(t,function(t){n[t]={}}),o=Ct(n),r=Z(e),{dataset:e,triggerChars:o,lookupByChar:function(n){return F(r,function(t){return t.ch===n})}};var t,n,e,o,r}),g=function(t){var n=t;r.get().map(function(t){return kp(f.dom,f.selection.getRng(),t.triggerChar).bind(function(t){return Tp(f,u,t,n)})}).getOrThunk(function(){return n=f,t=(e=u)(),o=n.selection.getRng(),r=n.dom,i=o,K(t.triggerChars,function(t){return kp(r,i,t)}).bind(function(t){return Tp(n,e,t)});var n,e,t,o,r,i}).fold(m,function(a){var t,n=a.context;e()||(t=yp(f,n.range),r.set({triggerChar:n.triggerChar,element:t,matchLength:n.text.length}),l.set(!1)),a.lookupData.then(function(u){r.get().map(function(t){var n,e,o,r,i=a.context;t.triggerChar===i.triggerChar&&(e=i.triggerChar,r=K(o=u,function(t){return vt.from(t.columns)}).getOr(1),0<(n=z(o,function(i){return jb(i.items,i.matchText,function(o,r){var t=f.selection.getRng();kp(f.dom,t,e).fold(function(){return console.error("Lost context. Cursor probably moved")},function(t){var n=t.range,e={hide:function(){m()},reload:function(t){s(),g(t)}};l.set(!0),i.onAction(e,n,o,r),l.set(!1)})},r,Sh.BUBBLE_TO_SANDBOX,c,i.highlightOn)})).length?function(t,n,e,o){t.matchLength=n.text.length;var r,i,u,a,c,s,l=K(e,function(t){return vt.from(t.columns)}).getOr(1);up.showAt(d,tp.sketch((r=uh("autocompleter-value",!0,o,l,"normal"),i=l,a=((u=Eb.ContentFocus)===Eb.ContentFocus?Ul:Wl)(),c=_b(i,"normal"),s=Qp("normal"),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:s.selectedItem,item:s.item},movement:c,fakeFocus:u===Eb.ContentFocus,focusManager:a,menuBehaviours:Kh("auto"!==i?[]:[ou(function(o,t){ah(o,4,s.item).each(function(t){var n=t.numColumns,e=t.numRows;Fg.setGridSize(o,e,n)})})])})),{anchor:{type:"node",root:At.fromDom(f.getBody()),node:vt.from(t.element)}}),up.getContent(d).each(hd.highlightFirst)}(t,i,u,n):(10<=i.text.length-t.matchLength?m:s)())})})})},n={onKeypress:vp(function(t){27!==t.which&&g()},50),cancelIfNecessary:m,isMenuOpen:function(){return up.isOpen(d)},isActive:e,isProcessingAction:l.get,getView:function(){return up.getContent(d)}};function i(t,n){br(t,xi(),{raw:n})}!1===f.hasPlugin("rtc")&&(o=n,(t=f).on("keypress compositionend",o.onKeypress.throttle),t.on("remove",o.onKeypress.cancel),t.on("keydown",function(n){function t(){return o.getView().bind(hd.getHighlighted)}8===n.which&&o.onKeypress.throttle(n),o.isActive()&&(27===n.which&&o.cancelIfNecessary(),o.isMenuOpen()?13===n.which?(t().each(yr),n.preventDefault()):40===n.which?(t().fold(function(){o.getView().each(hd.highlightFirst)},function(t){i(t,n)}),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){i(t,n),n.preventDefault(),n.stopImmediatePropagation()}):13!==n.which&&38!==n.which&&40!==n.which||o.cancelIfNecessary())}),t.on("NodeChange",function(t){o.isActive()&&!o.isProcessingAction()&&Mp(At.fromDom(t.element)).isNone()&&o.cancelIfNecessary()}))}var ay=Ho([{stopped:[]},{resume:["element"]},{complete:[]}]),cy=function(n,e,o,t,r,i){return Zb(n,e,o,t,r,i).fold(D,function(t){return cy(n,e,o,t,r,i)},T)},sy=function(t,n,e,o,r){var i=Qb(e,o);return cy(t,n,e,o,i,r)},ly=kl({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=B(n,["attributes"]);return{uid:t.uid,dom:lt({tag:"div",attributes:lt({role:"presentation"},e)},o),components:t.components,behaviours:tl(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[Eo("components",[]),Zs("containerBehaviours",[]),Eo("events",{}),Eo("domModification",{}),Eo("eventOrder",{})]}),fy=rt([Eo("prefix","form-field"),Zs("fieldBehaviours",[cd,Df])]),dy=rt([Qf({schema:[mo("dom")],name:"label"}),Qf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[mo("text")],name:"aria-descriptor"}),Jf({factory:{sketch:function(t){var e,o,n=(e=["factory"],o={},J(t,function(t,n){wt(e,n)||(o[n]=t)}),o);return t.factory.sketch(n)}},schema:[mo("factory")],name:"field"})]),my=Cl({name:"FormField",configFields:fy(),partFields:dy(),factory:function(r,t,n,e){var o=nl(r.fieldBehaviours,[cd.config({find:function(t){return ml(t,r,"field")}}),Df.config({store:{mode:"manual",getValue:function(t){return cd.getCurrent(t).bind(Df.getValue)},setValue:function(t,n){cd.getCurrent(t).each(function(t){Df.setValue(t,n)})}}})]),i=eu([ou(function(t,n){var o=pl(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=Ir(r.prefix);o.label().each(function(t){on(t.element,"for",n),on(e.element,"id",n)}),o["aria-descriptor"]().each(function(t){var n=Ir(r.prefix);on(t.element,"id",n),on(e.element,"aria-describedby",n)})})})]);return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:{getField:function(t){return ml(t,r,"field")},getLabel:function(t){return ml(t,r,"label")}}}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),gy=Object.freeze({__proto__:null,exhibit:function(t,n){return Lr({attributes:lr([{key:n.tabAttr,value:"true"}])})}}),py=xa({fields:[Eo("tabAttr","data-alloy-tabstop")],name:"tabstopping",active:gy}),hy=tinymce.util.Tools.resolve("tinymce.html.Entities"),vy=function(t,n,e,o){return{dom:by(e),components:t.toArray().concat([n]),fieldBehaviours:nc(o)}},by=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},yy=Ir("form-component-change"),xy=Ir("form-close"),wy=Ir("form-cancel"),Sy=Ir("form-action"),ky=Ir("form-submit"),Cy=Ir("form-block"),Oy=Ir("form-unblock"),_y=Ir("form-tabchange"),Ty=Ir("form-resize"),Ey=rt([wo("data"),Eo("inputAttributes",{}),Eo("inputStyles",{}),Eo("tag","input"),Eo("inputClasses",[]),Ju("onSetValue"),Eo("styles",{}),Eo("eventOrder",{}),Zs("inputBehaviours",[Df,Hg]),Eo("selectOnFocus",!0)]),Dy=kl({name:"Input",configFields:Ey(),factory:function(t,n){return{uid:t.uid,dom:iy(t),components:[],behaviours:lt(lt({},ry(e=t)),nl(e.inputBehaviours,[Df.config({store:lt(lt({mode:"manual"},e.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return Qr(t.element)},setValue:function(t,n){Qr(t.element)!==n&&Zr(t.element,n)}}),onSetValue:e.onSetValue})])),eventOrder:t.eventOrder};var e}}),By={},My={exports:By};function Ay(t){setTimeout(function(){throw t},0)}function Fy(t){var n=Ft(t);return wt(Xy,n)}function Iy(t,n){qr(n.getRoot(t).getOr(t.element),n.invalidClass),n.notify.each(function(n){Fy(t.element)&&on(t.element,"aria-invalid",!1),n.getContainer(t).each(function(t){Ar(t,n.validHtml)}),n.onValid(t)})}function Ry(n,t,e,o){Yr(t.getRoot(n).getOr(n.element),t.invalidClass),t.notify.each(function(t){Fy(n.element)&&on(n.element,"aria-invalid",!0),t.getContainer(n).each(function(t){Ar(t,o)}),t.onInvalid(n,o)})}function Vy(n,t,e){return t.validator.fold(function(){return Gy(Ve.value(!0))},function(t){return t.validate(n)})}function Py(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),Vy(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return Ry(n,e,0,t),Ve.error(t)},function(t){return Iy(n,e),Ve.value(t)}):Ve.error("No longer in system")})}!function(){var t=this,n=function(){var t,n,e,o={exports:{}};function r(){}function i(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],f(t,this)}function u(e,o){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,i._immediateFn(function(){var t,n=1===e._state?o.onFulfilled:o.onRejected;if(null!==n){try{t=n(e._value)}catch(t){return void c(o.promise,t)}a(o.promise,t)}else(1===e._state?a:c)(o.promise,e._value)})):e._deferreds.push(o)}function a(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void s(n);if("function"==typeof e)return void f((o=e,r=t,function(){o.apply(r,arguments)}),n)}n._state=1,n._value=t,s(n)}catch(t){c(n,t)}var o,r}function c(t,n){t._state=2,t._value=n,s(t)}function s(t){2===t._state&&0===t._deferreds.length&&i._immediateFn(function(){t._handled||i._unhandledRejectionFn(t._value)});for(var n=0,e=t._deferreds.length;n<e;n++)u(t,t._deferreds[n]);t._deferreds=null}function l(t,n,e){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=e}function f(t,n){var e=!1;try{t(function(t){e||(e=!0,a(n,t))},function(t){e||(e=!0,c(n,t))})}catch(t){if(e)return;e=!0,c(n,t)}}t=o,n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e=setTimeout,i.prototype.catch=function(t){return this.then(null,t)},i.prototype.then=function(t,n){var e=new this.constructor(r);return u(this,new l(t,n,e)),e},i.all=function(t){var a=Array.prototype.slice.call(t);return new i(function(r,i){if(0===a.length)return r([]);for(var u=a.length,t=0;t<a.length;t++)!function n(e,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var o=t.then;if("function"==typeof o)return o.call(t,function(t){n(e,t)},i),0}a[e]=t,0==--u&&r(a)}catch(t){i(t)}}(t,a[t])})},i.resolve=function(n){return n&&"object"==typeof n&&n.constructor===i?n:new i(function(t){t(n)})},i.reject=function(e){return new i(function(t,n){n(e)})},i.race=function(r){return new i(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},i._immediateFn="function"==typeof setImmediate?function(t){setImmediate(t)}:function(t){e(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},i._setImmediateFn=function(t){i._immediateFn=t},i._setUnhandledRejectionFn=function(t){i._unhandledRejectionFn=t},t.exports?t.exports=i:n.Promise||(n.Promise=i);var d=o.exports;return{boltExport:("undefined"!=typeof window?window:Function("return this;")()).Promise||d}};"object"==typeof By&&void 0!==My?My.exports=n():(t="undefined"!=typeof globalThis?globalThis:t||self).EphoxContactWrapper=n()}();var Hy,zy,Ny=My.exports.boltExport,Ly=function(t){function o(t){r()?i(t):n.push(t)}var e=vt.none(),n=[],r=function(){return e.isSome()},i=function(n){e.each(function(t){setTimeout(function(){n(t)},0)})};return t(function(t){r()||(e=vt.some(t),St(n,i),n=[])}),{get:o,map:function(e){return Ly(function(n){o(function(t){n(e(t))})})},isReady:r}},Wy={nu:Ly,pure:function(n){return Ly(function(t){t(n)})}},Uy=function(e){function t(t){e().then(t,Ay)}return{map:function(t){return Uy(function(){return e().then(t)})},bind:function(n){return Uy(function(){return e().then(function(t){return n(t).toPromise()})})},anonBind:function(t){return Uy(function(){return e().then(function(){return t.toPromise()})})},toLazy:function(){return Wy.nu(t)},toCached:function(){var t=null;return Uy(function(){return t=null===t?e():t})},toPromise:e,get:t}},jy=function(t){return Uy(function(){return new Ny(t)})},Gy=function(t){return Uy(function(){return Ny.resolve(t)})},Xy=["input","textarea"],Yy=Object.freeze({__proto__:null,markValid:Iy,markInvalid:Ry,query:Vy,run:Py,isInvalid:function(t,n){return Kr(n.getRoot(t).getOr(t.element),n.invalidClass)}}),qy=Object.freeze({__proto__:null,events:function(n,t){return n.validator.map(function(t){return eu([Cr(t.onEvent,function(t){Py(t,n).get(h)})].concat(t.validateOnLoad?[ou(function(t){Py(t,n).get(st)})]:[]))}).getOr({})}}),Ky=xa({fields:[mo("invalidClass"),Eo("getRoot",vt.none),To("notify",[Eo("aria","alert"),Eo("getContainer",vt.none),Eo("validHtml",""),Ju("onValid"),Ju("onInvalid"),Ju("onValidate")]),To("validator",[mo("validate"),Eo("onEvent","input"),Eo("validateOnLoad",!0)])],name:"invalidating",active:qy,apis:Yy,extra:{validation:function(e){return function(t){var n=Df.getValue(t);return Gy(e(n))}}}}),Jy=Object.freeze({__proto__:null,getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),$y=xa({fields:[go("others",uo(Ve.value,Zo()))],name:"coupling",apis:Jy,state:Object.freeze({__proto__:null,init:function(){var i={},t=rt({});return wu({readState:t,getOrCreate:function(e,o,r){var t=Ct(o.others);if(t)return tt(i,r).getOrThunk(function(){var t=tt(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),Qy=rt("sink"),Zy=rt(Qf({name:Qy(),overrides:rt({dom:{tag:"div"},behaviours:nc([ff.config({useFixed:D})]),events:eu([Dr(xi()),Dr(mi()),Dr(Ci())])})}));function tx(t,n){var e=t.getHotspot(n).getOr(n),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{type:"hotspot",hotspot:e,overrides:o}},function(t){return{type:"hotspot",hotspot:e,overrides:o,layouts:t}})}function nx(t,n,e,o,r,i,u){var a,c=tx(t,e),s=e,l=o,f=r,d=u,m=n,g=(0,(a=t).fetch)(s).map(m),p=mw(s,a);return g.map(function(t){return t.bind(function(t){return vt.from(ip.sketch(lt(lt({},f.menu()),{uid:pu(""),data:t,highlightImmediately:d===Hy.HighlightFirst,onOpenMenu:function(t,n){var e=p().getOrDie();ff.position(e,n,{anchor:c}),bf.decloak(l)},onOpenSubmenu:function(t,n,e){var o=p().getOrDie();ff.position(o,e,{anchor:{type:"submenu",item:n}}),bf.decloak(l)},onRepositionMenu:function(t,n,e){var o=p().getOrDie();ff.position(o,n,{anchor:c}),St(e,function(t){ff.position(o,t.triggeredMenu,{anchor:{type:"submenu",item:t.triggeringItem}})})},onEscape:function(){return Hg.focus(s),bf.close(l),vt.some(!0)}})))})}).map(function(t){return t.fold(function(){bf.isOpen(o)&&bf.close(o)},function(t){bf.cloak(o),bf.open(o,t),i(o)}),o})}function ex(t,n,e,o,r,i){var u=$y.getCoupled(e,"sandbox");return(bf.isOpen(u)?function(t,n,e,o,r,i,u){return bf.close(o),Gy(o)}:nx)(t,n,e,u,o,r,i)}function ox(t){bf.getState(t).each(function(t){ip.repositionMenus(t)})}function rx(s,l,f){var d=Vu(),t=mw(l,s);return{dom:{tag:"div",classes:s.sandboxClasses,attributes:{id:d.id,role:"listbox"}},behaviours:Mf(s.sandboxBehaviours,[Df.config({store:{mode:"memory",initialValue:l}}),bf.config({onOpen:function(t,n){var e,o,r,i,u,a,c=tx(s,l);d.link(l.element),s.matchWidth&&(e=c.hotspot,o=n,r=s.useMinWidth,u=cd.getCurrent(o).getOr(o),a=On(e.element),r?dn(u.element,"min-width",a+"px"):(i=u.element,Oe.set(i,a))),s.onOpen(c,t,n),void 0!==f&&void 0!==f.onOpen&&f.onOpen(t,n)},onClose:function(t,n){d.unlink(l.element),void 0!==f&&void 0!==f.onClose&&f.onClose(t,n)},isPartOf:function(t,n,e){return zu(n,e)||zu(l,e)},getAttachPoint:function(){return t().getOrDie()}}),cd.config({find:function(t){return bf.getState(t).bind(function(t){return cd.getCurrent(t)})}}),ic.config({channels:lt(lt({},Xs({isExtraPart:T})),Ys({doReposition:ox}))})])}}function ix(t){ox($y.getCoupled(t,"sandbox"))}function ux(){return[Eo("sandboxClasses",[]),Bf("sandboxBehaviours",[cd,ic,bf,Df])]}function ax(n){return Qf({name:n+"-edge",overrides:function(t){return t.model.manager.edgeActions[n].fold(function(){return{}},function(o){return{events:eu([Or(si(),function(t,n,e){return o(t,e)},[t]),Or(mi(),function(t,n,e){return o(t,e)},[t]),Or(gi(),function(t,n,e){e.mouseIsDown.get()&&o(t,e)},[t])])}})}})}function cx(t){var n=t.event.raw;return-1===n.type.indexOf("touch")?void 0!==n.clientX?vt.some(n).map(function(t){return ke(t.clientX,t.clientY)}):vt.none():void 0!==n.touches&&1===n.touches.length?vt.some(n.touches[0]).map(function(t){return ke(t.clientX,t.clientY)}):vt.none()}function sx(t){return t.model.minX}function lx(t){return t.model.minY}function fx(t){return t.model.minX-1}function dx(t){return t.model.minY-1}function mx(t){return t.model.maxX}function gx(t){return t.model.maxY}function px(t){return t.model.maxX+1}function hx(t){return t.model.maxY+1}function vx(t,n,e){return n(t)-e(t)}function bx(t){return vx(t,mx,sx)}function yx(t){return vx(t,gx,lx)}function xx(t){return bx(t)/2}function wx(t){return yx(t)/2}function Sx(t){return t.stepSize}function kx(t){return t.snapToGrid}function Cx(t){return t.snapStart}function Ox(t){return t.rounded}function _x(t,n){return void 0!==t[n+"-edge"]}function Tx(t){return _x(t,"left")}function Ex(t){return _x(t,"right")}function Dx(t){return _x(t,"top")}function Bx(t){return _x(t,"bottom")}function Mx(t){return t.model.value.get()}function Ax(t,n){return{x:t,y:n}}function Fx(t,n){br(t,Bw(),{value:n})}function Ix(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)}function Rx(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)}function Vx(t,n,e){return Math.max(n,Math.min(e,t))}function Px(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=l?e+1:e;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w=Vx((x=f,Math.min(d,Math.max(r,x))-x)/m*o+n,g,p);return u&&n<=w&&w<=e?(h=w,v=n,b=e,y=i,a.fold(function(){var t=Math.round((h-v)/y)*y;return Vx(v+t,v-1,b+1)},function(t){var n=Math.round((h-t)%y/y),e=Math.floor((h-t)/y),o=Math.floor((b-t)/y),r=Math.min(o,e+n);return Math.max(t,t+r*y)})):c?Math.round(w):w}function Hx(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:l:(r-n)/o*c}function zx(t){return t.element.dom.getBoundingClientRect()}function Nx(t){return zx(t)[Mw]}function Lx(t){return zx(t).right}function Wx(t){return zx(t).top}function Ux(t){return zx(t).bottom}function jx(t){return zx(t).width}function Gx(t){return zx(t).height}function Xx(t,n){var e=zx(t),o=zx(n);return(e[Mw]+e.right)/2-o[Mw]}function Yx(t,n){var e=zx(t),o=zx(n);return(e.top+e.bottom)/2-o.top}function qx(t,n){br(t,Bw(),{value:n})}function Kx(t,n,e){return Px({min:sx(n),max:mx(n),range:bx(n),value:e,step:Sx(n),snap:kx(n),snapStart:Cx(n),rounded:Ox(n),hasMinEdge:Tx(n),hasMaxEdge:Ex(n),minBound:Nx(t),maxBound:Lx(t),screenRange:jx(t)})}function Jx(r){return function(t,n){return qx(t,{x:o=(0<r?Rx:Ix)(Mx(e=n).x,sx(e),mx(e),Sx(e))}),vt.some(o).map(D);var e,o}}function $x(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g=(a=i,c=e,s=o,l=r,f=jx(u=n),d=s.bind(function(t){return vt.some(Xx(t,u))}).getOr(0),m=l.bind(function(t){return vt.some(Xx(t,u))}).getOr(f),Hx({min:sx(a),max:mx(a),range:bx(a),value:c,hasMinEdge:Tx(a),hasMaxEdge:Ex(a),minBound:Nx(u),minOffset:0,maxBound:Lx(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m}));return Nx(n)-Nx(t)+g}function Qx(t,n){br(t,Bw(),{value:n})}function Zx(t,n,e){return Px({min:lx(n),max:gx(n),range:yx(n),value:e,step:Sx(n),snap:kx(n),snapStart:Cx(n),rounded:Ox(n),hasMinEdge:Dx(n),hasMaxEdge:Bx(n),minBound:Wx(t),maxBound:Ux(t),screenRange:Gx(t)})}function t0(r){return function(t,n){return Qx(t,{y:o=(0<r?Rx:Ix)(Mx(e=n).y,lx(e),gx(e),Sx(e))}),vt.some(o).map(D);var e,o}}function n0(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g=(a=i,c=e,s=o,l=r,f=Gx(u=n),d=s.bind(function(t){return vt.some(Yx(t,u))}).getOr(0),m=l.bind(function(t){return vt.some(Yx(t,u))}).getOr(f),Hx({min:lx(a),max:gx(a),range:yx(a),value:c,hasMinEdge:Dx(a),hasMaxEdge:Bx(a),minBound:Wx(u),minOffset:0,maxBound:Ux(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m}));return Wx(n)-Wx(t)+g}function e0(t,n){br(t,Bw(),{value:n})}function o0(t,n){return{x:t,y:n}}function r0(u,a){return function(t,n){return o=n,r=0<u?Rx:Ix,e0(t,o0(i=(e=a)?Mx(o).x:r(Mx(o).x,sx(o),mx(o),Sx(o)),e?r(Mx(o).y,lx(o),gx(o),Sx(o)):Mx(o).y)),vt.some(i).map(D);var e,o,r,i}}function i0(t){return"<alloy.field."+t+">"}function u0(f,d,m,g){function p(t,n,e,o,r){var i,u,a=f(uS+"range"),c=[my.parts.label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),my.parts.field({data:r,factory:Dy,inputAttributes:lt({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[d("textfield")],inputBehaviours:nc([(i=n,u=t,Ky.config({invalidClass:d("invalid"),notify:{onValidate:function(t){br(t,iS,{type:i})},onValid:function(t){br(t,oS,{type:i,value:Df.getValue(t)})},onInvalid:function(t){br(t,rS,{type:i,value:Df.getValue(t)})}},validator:{validate:function(t){var n=Df.getValue(t),e=u(n)?Ve.value(!0):Ve.error(f("aria.input.invalid"));return Gy(e)},validateOnLoad:!1}})),py.config({})]),onSetValue:function(t){Ky.isInvalid(t)&&Ky.run(t).get(st)}})],s="hex"!==n?[my.parts["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}}function h(t,n){var e=n.red,o=n.green,r=n.blue;Df.setValue(t,{red:e,green:o,blue:r})}function v(t,n){b.getOpt(t).each(function(t){dn(t.element,"background-color","#"+n.value)})}var b=zm({dom:{tag:"div",classes:[d("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return kl({factory:function(){function a(t){return o[t].get()}function c(t,n){o[t].set(n)}function n(t,n){var e=n.event;"hex"!==e.type?c(e.type,vt.none()):g(t)}function e(t,n){var r,e,o,i,u=n.event;"hex"===u.type?function(t,n){m(t);var e=Mv(n);c("hex",vt.some(n));var o=Hv(e);h(t,o),s(o),br(t,Qw,{hex:e}),v(t,e)}(t,u.value):(r=t,e=u.type,o=u.value,i=parseInt(o,10),c(e,vt.some(i)),a("red").bind(function(e){return a("green").bind(function(n){return a("blue").map(function(t){return Rv(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=Iv(t),eS.getField(n,"hex").each(function(t){Hg.isFocused(t)||Df.setValue(n,{hex:e.value})}),e);br(r,Qw,{hex:o}),v(r,o)}))}function t(t){return{label:f(uS+t+".label"),description:f(uS+t+".description")}}function s(t){var n=t.red,e=t.green,o=t.blue;c("red",vt.some(n)),c("green",vt.some(e)),c("blue",vt.some(o))}var o={red:Po(vt.some(255)),green:Po(vt.some(255)),blue:Po(vt.some(255)),hex:Po(vt.some("ffffff"))},r=t("red"),i=t("green"),u=t("blue"),l=t("hex");return Yo(eS.sketch(function(t){return{dom:{tag:"form",classes:[d("rgb-form")],attributes:{"aria-label":f("aria.color.picker")}},components:[t.field("red",my.sketch(p(Vv,"red",r.label,r.description,255))),t.field("green",my.sketch(p(Vv,"green",i.label,i.description,255))),t.field("blue",my.sketch(p(Vv,"blue",u.label,u.description,255))),t.field("hex",my.sketch(p(Av,"hex",l.label,l.description,"ffffff"))),b.asSpec()],formBehaviours:nc([Ky.config({invalidClass:d("form-invalid")}),mm("rgb-form-events",[Cr(oS,e),Cr(rS,n),Cr(iS,n)])])}}),{apis:{updateHex:function(t,n){var e;Df.setValue(t,{hex:n.value}),h(t,e=Hv(n)),s(e),v(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})}function a0(x,w){return kl({name:"ColourPicker",configFields:[mo("dom"),Eo("onValidHex",st),Eo("onInvalidHex",st)],factory:function(t){function n(t,n,e){v.getOpt(t).each(function(t){g.setHue(t,e)})}function e(t,n){b.getOpt(t).each(function(t){m.updateHex(t,n)})}function r(n,e,o,t){var r=o,i=Hv(e);p.paletteRgba.set(i),p.paletteHue.set(r),St(t,function(t){t(n,e,o)})}var o,i,u,a,c,s,l,f,d,m=u0(x,w,t.onValidHex,t.onInvalidHex),g=(l=w,f=$w.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[l("sv-palette-spectrum")]}}),d=$w.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[l("sv-palette-thumb")],innerHtml:"<div class="+l("sv-palette-inner-thumb")+' role="presentation"></div>'}}),kl({factory:function(t){var n=rt({x:0,y:0}),e=nc([cd.config({find:vt.some}),Hg.config({})]);return $w.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[l("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[f,d],onChange:function(t,n,e){br(t,tS,{value:e})},onInit:function(t,n,e,o){y(e.element.dom,Lv(fb))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:function(t,n,e){var o=e;y(n.components()[0].element.dom,Lv(Pv(Uv(o,100,100))))},setThumb:function(t,n,e){var o=n,r=jv(Hv(e));$w.setValue(o,{x:r.saturation,y:100-r.value})}},extraApis:{}})),p={paletteRgba:Po(fb),paletteHue:Po(0)},h=zm((i=$w.parts.spectrum({dom:{tag:"div",classes:[(o=w)("hue-slider-spectrum")],attributes:{role:"presentation"}}}),u=$w.parts.thumb({dom:{tag:"div",classes:[o("hue-slider-thumb")],attributes:{role:"presentation"}}}),$w.sketch({dom:{tag:"div",classes:[o("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:rt({y:0})},components:[i,u],sliderBehaviours:nc([Hg.config({})]),onChange:function(t,n,e){br(t,Zw,{value:e})}}))),v=zm(g.sketch({})),b=zm(m.sketch({}));function y(t,n){var e,o,r=t.width,i=t.height,u=t.getContext("2d");null!==u&&(u.fillStyle=n,u.fillRect(0,0,r,i),(e=u.createLinearGradient(0,0,r,0)).addColorStop(0,"rgba(255,255,255,1)"),e.addColorStop(1,"rgba(255,255,255,0)"),u.fillStyle=e,u.fillRect(0,0,r,i),(o=u.createLinearGradient(0,0,0,i)).addColorStop(0,"rgba(0,0,0,0)"),o.addColorStop(1,"rgba(0,0,0,1)"),u.fillStyle=o,u.fillRect(0,0,r,i))}return{uid:t.uid,dom:t.dom,components:[v.asSpec(),h.asSpec(),b.asSpec()],behaviours:nc([mm("colour-picker-events",[Cr(Qw,(s=[n,function(t,n,e){h.getOpt(t).each(function(t){$w.setValue(t,{y:100-e/360*100})})},function(t,n){v.getOpt(t).each(function(t){g.setThumb(t,n)})}],function(t,n){var e=n.event.hex;r(t,e,jv(Hv(e)).hue,s)})),Cr(tS,(c=[e],function(t,n){var e=n.event.value,o=p.paletteHue.get();r(t,Gv(Uv(o,e.x,100-e.y)),o,c)})),Cr(Zw,(a=[n,e],function(t,n){var e=(100-n.event.value.y)/100*360,o=jv(p.paletteRgba.get());r(t,Gv(Uv(e,o.saturation,o.value)),e,a)}))]),cd.config({find:function(t){return b.getOpt(t)}}),Fg.config({mode:"acyclic"})])}}})}function c0(t){return sS[t]}function s0(t,n,e){return Df.config(Yo({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))}function l0(r,i){function n(t,n){n.stop()}function e(t){return function(n,e){St(t,function(t){t(n,e)})}}function o(t,n){var e;gd.isDisabled(t)||(e=n.event.raw,a(t,e.dataTransfer.files))}function u(t,n){var e=n.event.raw.target;a(t,e.files)}function a(t,n){var e,o;Df.setValue(t,(e=n,o=fS.explode(i.getSetting("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string")),F(kt(e),function(n){return d(o,function(t){return Bt(n.name.toLowerCase(),"."+t.toLowerCase())})}))),br(t,yy,{name:r.name})}var c=zm({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:nc([mm("input-file-events",[Dr(Ci()),Dr(Vi())])])});return ey(r.label.map(function(t){return oy(t,i)}),my.parts.field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:nc([pS([]),aw(),gd.config({}),Lg.config({toggleClass:"dragenter",toggleOnExecute:!1}),mm("dropzone-events",[Cr("dragenter",e([n,Lg.toggle])),Cr("dragleave",e([n,Lg.toggle])),Cr("dragover",n),Cr("drop",e([n,o])),Cr(ki(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:i.translate("Drop an image here")}},fp.sketch({dom:{tag:"button",innerHtml:i.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element.dom.click()},buttonBehaviours:nc([py.config({}),$v(i.isDisabled),pv()])})]}]}}}}),["tox-form__group--stretched"],[])}function f0(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:nc([Hg.config({ignore:!0}),py.config({})])}}function d0(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[f0([hS]),t,f0([vS])],behaviours:nc([cS(1)])}}function m0(t,n){br(t,xi(),{raw:{which:9,shiftKey:n}})}function g0(t,n){var e=n.element;Kr(e,hS)?m0(t,!0):Kr(e,vS)&&m0(t,!1)}function p0(t){return Yb(t,["."+hS,"."+vS].join(","),T)}function h0(t,n){return xS(document.createElement("canvas"),t,n)}function v0(t){var n=h0(t.width,t.height);return yS(n).drawImage(t,0,0),n}function b0(t){return t.naturalWidth||t.width}function y0(t){return t.naturalHeight||t.height}function x0(t,o,r){return o=o||"image/png",S(HTMLCanvasElement.prototype.toBlob)?new Ny(function(n,e){t.toBlob(function(t){t?n(t):e()},o,r)}):(g=t.toDataURL(o,r),new Ny(function(t,n){!function(){var t=g.split(","),n=/data:([^;]+)/.exec(t[0]);if(!n)return vt.none();for(var e=n[1],o=t[1],r=atob(o),i=r.length,u=Math.ceil(i/1024),a=new Array(u),c=0;c<u;++c){for(var s=1024*c,l=Math.min(1024+s,i),f=new Array(l-s),d=s,m=0;d<l;++m,++d)f[m]=r[d].charCodeAt(0);a[c]=new Uint8Array(f)}return vt.some(new Blob(a,{type:e}))}().fold(function(){n("uri is not base64: "+g)},t)}));var g}function w0(t,n,e){function o(n,e){return t.then(function(t){return t.toDataURL(n||"image/png",e)})}return{getType:rt(n.type),toBlob:function(){return Ny.resolve(n)},toDataURL:rt(e),toBase64:function(){return e.split(",")[1]},toAdjustedBlob:function(n,e){return t.then(function(t){return x0(t,n,e)})},toAdjustedDataURL:o,toAdjustedBase64:function(t,n){return o(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(v0)}}}function S0(n,t){return x0(n,t).then(function(t){return w0(Ny.resolve(n),t,n.toDataURL())})}function k0(n){return e=n,new Ny(function(t){var n=new FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)}).then(function(t){return w0((a=n,new Ny(function(t,n){function e(){r.removeEventListener("load",i),r.removeEventListener("error",u)}var o=URL.createObjectURL(a),r=new Image,i=function(){e(),t(r)},u=function(){e(),n("Unable to load data of type "+a.type+": "+o)};r.addEventListener("load",i),r.addEventListener("error",u),r.src=o,r.complete&&setTimeout(i,0)}).then(function(t){wS(t);var n=h0(b0(t),y0(t));return yS(n).drawImage(t,0,0),n})),n,t);var a});var e}function C0(t,n,e){var o="string"==typeof t?parseFloat(t):t;return e<o?o=e:o<n&&(o=n),o}function O0(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}function _0(t,n){for(var e=[],o=new Array(25),r=0;r<5;r++){for(var i=0;i<5;i++)e[i]=n[i+5*r];for(i=0;i<5;i++){for(var u=0,a=0;a<5;a++)u+=t[i+5*a]*e[a];o[i+5*r]=u}}return o}function T0(n,e){return n.toCanvas().then(function(t){return kS(t,n.getType(),e)})}function E0(e){return function(t,n){return T0(t,e(O0(),n))}}function D0(t,n){void 0===n&&(n=2);var e=Math.pow(10,n),o=Math.round(t*e);return Math.ceil(o/e)}function B0(t){return CS(t)}function M0(t){return TS(t)}function A0(t,n){return ES(t,n)}function F0(t,n){return OS(t,n)}function I0(t,n){return _S(t,n)}function R0(t,n){return o=n,(e=t).toCanvas().then(function(t){return AS(t,e.getType(),o)});var e,o}function V0(t,n){return o=n,(e=t).toCanvas().then(function(t){return MS(t,e.getType(),o)});var e,o}function P0(t,n,e){return Xm(t,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:e},n)}function H0(t,n){return P0(t,n,[])}function z0(t,n){return P0(t,n,[Rg.config({})])}function N0(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:nc([Rg.config({})])}}function L0(n,e,o){function t(t,n){var e=Df.getValue(t);return Hg.focus(e),br(e,"keydown",{raw:n.event.raw}),hw.close(e),vt.some(!0)}var r=Po(st),i=n.text.map(function(t){return zm(N0(t,e,o.providers))}),u=n.icon.map(function(t){return zm(z0(t,o.providers.icons))}),a=n.role.fold(function(){return{}},function(t){return{role:t}}),c=n.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}}),s=Xm("chevron-down",{tag:"div",classes:[e+"__select-chevron"]},o.providers.icons);return zm(hw.sketch(lt(lt(lt({},n.uid?{uid:n.uid}:{}),a),{dom:{tag:"button",classes:[e,e+"--select"].concat(M(n.classes,function(t){return e+"--"+t})),attributes:lt({},c)},components:tb([u.map(function(t){return t.asSpec()}),i.map(function(t){return t.asSpec()}),vt.some(s)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:nc(H(H([],n.dropdownBehaviours,!0),[$v(function(){return n.disabled||o.providers.isDisabled()}),pv(),vw.config({}),Rg.config({}),mm("dropdown-events",[vv(n,r),bv(n,r)]),mm("menubutton-update-display-text",[Cr(VS,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){Rg.set(t,[ri(o.providers.translate(e.event.text))])})}),Cr(PS,function(n,e){u.bind(function(t){return t.getOpt(n)}).each(function(t){Rg.set(t,[z0(e.event.icon,o.providers.icons)])})})])],!1)),eventOrder:Yo(RS,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:nc([Fg.config({mode:"special",onLeft:t,onRight:t})]),lazySink:o.getSink,toggleClass:e+"--active",parts:{menu:Zp(0,n.columns,n.presets)},fetch:function(t){return jy(C(n.fetch,t))}}))).asSpec()}function W0(t){return"separator"===t.type}function U0(t,e,o,n){var r=Ir("primary-menu"),i=zS(t,o.shared.providers.menuItems());if(0===i.items.length)return vt.none();var u=Gb(r,i.items,e,o,n),a=dt(i.menus,function(t,n){return Gb(n,t,e,o,!1)}),c=Yo(a,sr(r,u));return vt.from(ip.tieredData(r,c,i.expansions))}function j0(e){return{isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)},setActive:function(t){var n=e.element;t?(Yr(n,"tox-tbtn--enabled"),on(n,"aria-pressed",!0)):(qr(n,"tox-tbtn--enabled"),sn(n,"aria-pressed"))},isActive:function(){return Kr(e.element,"tox-tbtn--enabled")}}}function G0(e,t,o,n){return L0({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:function(t,n){e.fetch(function(t){n(U0(t,Sh.CLOSE_ON_EXECUTE,o,!1))})},onSetup:e.onSetup,getApi:j0,columns:1,presets:"normal",classes:[],dropdownBehaviours:[py.config({})]},t,o.shared)}function X0(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold(function(){return{}},function(t){return{action:t}}),a=lt({buttonBehaviours:nc([$v(function(){return t.disabled||i.isDisabled()}),pv(),py.config({}),mm("button press",[kr("click"),kr("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=Yo(a,{dom:o});return Yo(c,{components:r})}function Y0(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return H0(t,e.icons)});return X0(t,n,o,r,tb([i]),e)}function q0(t,n,e,o){void 0===o&&(o=[]);var r=Y0(t,vt.some(n),e,o);return fp.sketch(r)}function K0(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map(function(t){return H0(t,e.icons)}):vt.none(),a=u.isSome()?tb([u]):[],c=u.isSome()?{}:{innerHtml:i},s=H(H(H(H([],t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],!0),u.isSome()?["tox-button--icon"]:[],!0),t.borderless?["tox-button--naked"]:[],!0),r,!0);return X0(t,n,o,lt(lt({tag:"button",classes:s},c),{attributes:{title:i}}),a,e)}function J0(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=K0(t,vt.some(n),e,o,r);return fp.sketch(i)}function $0(n,e){return function(t){"custom"===e?br(t,Sy,{name:n,value:{}}):"submit"===e?vr(t,ky):"cancel"===e?vr(t,wy):console.error("Unknown button type: ",e)}}function Q0(n,t,e){if("menu"===t){var o=n,r=zm(G0(lt(lt({},n),{onSetup:function(t){return t.setDisabled(n.disabled),st},fetch:(i=o.items,u=function(){return r},a=e,function(t){t(M(i,function(t){var n,e,o=t.text.fold(function(){return{}},function(t){return{text:t}});return lt(lt({type:t.type,active:!1},o),{onAction:function(t){var n=!t.isActive();t.setActive(n),e.storage.set(n),a.shared.getSink().each(function(t){u().getOpt(t).each(function(t){ka(t.element),br(t,Sy,{name:e.name,value:e.storage.get()})})})},onSetup:(n=e=t,function(t){t.setActive(n.storage.get())})})}))})}),"tox-tbtn",e,vt.none()));return r.asSpec()}var i,u,a;if("custom"===t||"cancel"===t||"submit"===t){var c=$0(n.name,t);return J0(lt(lt({},n),{borderless:!1}),c,e.shared.providers,[])}console.error("Unknown footer button type: ",t)}function Z0(t,n){return Jf({factory:my,name:t,overrides:function(o){return{fieldBehaviours:nc([mm("coupled-input-behaviour",[Cr(Si(),function(e){ml(e,o,n).bind(cd.getCurrent).each(function(n){ml(e,o,"lock").each(function(t){Lg.isOn(t)&&o.onLockedChange(e,n,t)})})})])])}}})}function tw(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return Ve.error(t);var e=parseFloat(n[1]),o=n[2];return Ve.value({value:e,unit:o})}function nw(t,n){function e(t){return Tt(o,t)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1};return t.unit===n?vt.some(t.value):e(t.unit)&&e(n)?o[t.unit]===o[n]?vt.some(t.value):vt.some(t.value/o[t.unit]*o[n]):vt.none()}function ew(t){return vt.none()}function ow(o,n){function t(t){return Xm(t,{tag:"span",classes:["tox-icon","tox-lock-icon__"+t]},n.icons)}function e(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}}function r(e){return my.parts.field({factory:Dy,inputClasses:["tox-textfield"],inputBehaviours:nc([gd.config({disabled:function(){return o.disabled||n.isDisabled()}}),pv(),py.config({}),mm("size-input-events",[Cr(bi(),function(t,n){br(t,u,{isField1:e})}),Cr(ki(),function(t,n){br(t,yy,{name:o.name})})])]),selectOnFocus:!1})}function i(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}}var l=ew,u=Ir("ratio-event"),a=WS.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[t("lock"),t("unlock")],buttonBehaviours:nc([gd.config({disabled:function(){return o.disabled||n.isDisabled()}}),pv(),py.config({})])}),c=WS.parts.field1(e([my.parts.label(i("Width")),r(!0)])),s=WS.parts.field2(e([my.parts.label(i("Height")),r(!1)]));return WS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([i("&nbsp;"),a])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){tw(Df.getValue(t)).each(function(t){l(t).each(function(t){var n,e,o,r;Df.setValue(i,(r=-1!==(r=(n=t).value.toFixed((e=n.unit)in(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4})?o[e]:1)).indexOf(".")?r.replace(/\.?0*$/,""):r)+n.unit)})})},coupledFieldBehaviours:nc([gd.config({disabled:function(){return o.disabled||n.isDisabled()},onDisabled:function(t){WS.getField1(t).bind(my.getField).each(gd.disable),WS.getField2(t).bind(my.getField).each(gd.disable),WS.getLock(t).each(gd.disable)},onEnabled:function(t){WS.getField1(t).bind(my.getField).each(gd.enable),WS.getField2(t).bind(my.getField).each(gd.enable),WS.getLock(t).each(gd.enable)}}),pv(),mm("size-input-events2",[Cr(u,function(t,n){var e,o,r,i=n.event.isField1,u=i?WS.getField1(t):WS.getField2(t),a=i?WS.getField2(t):WS.getField1(t),c=u.map(Df.getValue).getOr(""),s=a.map(Df.getValue).getOr("");e=s,o=tw(c).toOptional(),r=tw(e).toOptional(),l=Et(o,r,function(t,o){return nw(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return nw(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(ew)}).getOr(ew)})])])})}function rw(f,c){function t(t,n,e,o){return zm(J0({name:t,text:t,disabled:e,primary:o,icon:vt.none(),borderless:!1},n,c))}function n(t,n,e,o){return zm(q0({name:t,icon:vt.some(t),tooltip:vt.some(n),disabled:o,primary:!1,borderless:!1},e,c))}function d(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(gd)&&gd.disable(n)})}function m(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(gd)&&gd.enable(n)})}function r(t,n,e){br(t,n,e)}function i(t){return vr(t,YS.disable()),0}function u(t){return vr(t,YS.enable()),0}function g(t,n){i(t),r(t,US.transform(),{transform:n}),u(t)}function e(t){return function(){q.getOpt(t).each(function(t){Rg.set(t,[Y])})}}function s(t,n){i(t),r(t,US.transformApply(),{transform:n,swap:e(t)}),u(t)}function p(){return t("Back",function(t){return r(t,US.back(),{swap:e(t)})},!1,!1)}function o(){return zm({dom:{tag:"div",classes:["tox-spacer"]},behaviours:nc([gd.config({})])})}function h(){return t("Apply",function(t){return r(t,US.apply(),{swap:e(t)})},!0,!0)}function v(n,e){return function(t){return n(t,e)}}function a(t,n){var e,o=n;i(e=t),r(e,US.tempTransform(),{transform:o}),u(e)}function b(t,n,e,o,r){var i=$w.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=$w.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=$w.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return zm($w.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:rt({x:o})},components:[i,u,a],sliderBehaviours:nc([Hg.config({})]),onChoose:n}))}function l(t,n,e,o,r){var i,u,a,c,s,l,f=(i=t,u=n,a=e,c=o,s=r,[p(),(l=u,b(i,function(t,n,e){g(t,v(l,e.x/100))},a,c,s)),h()]);return ly.sketch({dom:w,components:f.map(function(t){return t.asSpec()}),containerBehaviours:nc([mm("image-tools-filter-panel-buttons-events",[Cr(YS.disable(),function(t,n){d(f,t)}),Cr(YS.enable(),function(t,n){m(f,t)})])])})}function y(t){return b(t,function(l,t,n){var e=z.getOpt(l),o=L.getOpt(l),r=N.getOpt(l);e.each(function(s){o.each(function(c){r.each(function(t){var o,r,i,n=Df.getValue(s).x/100,e=Df.getValue(t).x/100,u=Df.getValue(c).x/100,a=(o=n,r=e,i=u,function(t){return T0(t,(n=r,e=i,_0(O0(),[C0(o,0,2),0,0,0,0,0,C0(n,0,2),0,0,0,0,0,C0(e,0,2),0,0,0,0,0,1,0,0,0,0,0,1])));var n,e});g(l,a)})})})},0,100,200)}function x(n,e,o){return function(t){r(t,US.swap(),{transform:e,swap:function(){q.getOpt(t).each(function(t){Rg.set(t,[n]),o(t)})}})}}var w={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},S=st,k=[p(),o(),t("Apply",function(t){s(t,function(t){var n,e,o,r,i,u,a,c,s,l=f.getRect();return n=l.x,e=l.y,o=l.w,r=l.h,u=n,a=e,c=o,s=r,(i=t).toCanvas().then(function(t){return FS(t,i.getType(),u,a,c,s)})}),f.hideCrop()},!1,!0)],C=ly.sketch({dom:w,components:k.map(function(t){return t.asSpec()}),containerBehaviours:nc([mm("image-tools-crop-buttons-events",[Cr(YS.disable(),function(t,n){d(k,t)}),Cr(YS.enable(),function(t,n){m(k,t)})])])}),O=zm(ow({name:"size",label:vt.none(),constrain:!0,disabled:!1},c)),_=[p(),o(),O,o(),t("Apply",function(a){O.getOpt(a).each(function(t){var r,i,n=Df.getValue(t),e=parseInt(n.width,10),o=parseInt(n.height,10),u=(r=e,i=o,function(t){return e=r,o=i,(n=t).toCanvas().then(function(t){return DS(t,e,o).then(function(t){return S0(t,n.getType())})});var n,e,o});s(a,u)})},!1,!0)],T=ly.sketch({dom:w,components:_.map(function(t){return t.asSpec()}),containerBehaviours:nc([mm("image-tools-resize-buttons-events",[Cr(YS.disable(),function(t,n){d(_,t)}),Cr(YS.enable(),function(t,n){m(_,t)})])])}),E=v(R0,"h"),D=v(R0,"v"),B=v(V0,-90),M=v(V0,90),A=[p(),o(),n("flip-horizontally","Flip horizontally",function(t){a(t,E)},!1),n("flip-vertically","Flip vertically",function(t){a(t,D)},!1),n("rotate-left","Rotate counterclockwise",function(t){a(t,B)},!1),n("rotate-right","Rotate clockwise",function(t){a(t,M)},!1),o(),h()],F=ly.sketch({dom:w,components:A.map(function(t){return t.asSpec()}),containerBehaviours:nc([mm("image-tools-fliprotate-buttons-events",[Cr(YS.disable(),function(t,n){d(A,t)}),Cr(YS.enable(),function(t,n){m(A,t)})])])}),I=[p(),o(),h()],R=ly.sketch({dom:w,components:I.map(function(t){return t.asSpec()})}),V=l("Brightness",F0,-100,0,100),P=l("Contrast",I0,-100,0,100),H=l("Gamma",A0,-100,0,100),z=y("R"),N=y("G"),L=y("B"),W=[p(),z,N,L,h()],U=ly.sketch({dom:w,components:W.map(function(t){return t.asSpec()})}),j=vt.some(M0),G=vt.some(B0),X=[n("crop","Crop",x(C,vt.none(),function(t){f.showCrop()}),!1),n("resize","Resize",x(T,vt.none(),function(t){O.getOpt(t).each(function(t){var n=f.getMeasurements(),e=n.width,o=n.height;Df.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",x(F,vt.none(),S),!1),n("brightness","Brightness",x(V,vt.none(),S),!1),n("sharpen","Sharpen",x(R,j,S),!1),n("contrast","Contrast",x(P,vt.none(),S),!1),n("color-levels","Color levels",x(U,vt.none(),S),!1),n("gamma","Gamma",x(H,vt.none(),S),!1),n("invert","Invert",x(R,G,S),!1)],Y=ly.sketch({dom:w,components:X.map(function(t){return t.asSpec()})}),q=zm(ly.sketch({dom:{tag:"div"},components:[Y],containerBehaviours:nc([Rg.config({})])}));return{memContainer:q,getApplyButton:function(t){return q.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}}function iw(t){if(k(t.changedTouches))for(var n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]}(zy=Hy=Hy||{})[zy.HighlightFirst=0]="HighlightFirst",zy[zy.HighlightNone=1]="HighlightNone";function uw(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:nl(o.formBehaviours,[Df.config({store:{mode:"manual",getValue:function(t){return dt(hl(t,o),function(t,o){return t().bind(function(t){var n=cd.getCurrent(t),e=new Error("Cannot find a current component to extract the value from for form part '"+o+"': "+Fr(t.element));return n.fold(function(){return Ve.error(e)},Ve.value)}).map(Df.getValue)})},setValue:function(e,t){J(t,function(n,t){ml(e,o,t).each(function(t){cd.getCurrent(t).each(function(t){Df.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return ml(t,o,n).bind(cd.getCurrent)}}}}function aw(){return cd.config({find:vt.some})}function cw(t){return n=Mr,e=Ar,s0(t,function(t){return n(t.element)},function(t,n){return e(t.element,n)});var n,e}var sw,lw,fw,dw,mw=function(n,t){return n.getSystem().getByUid(t.uid+"-"+Qy()).map(function(t){return function(){return Ve.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return Ve.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},gw=rt([mo("dom"),mo("fetch"),Ju("onOpen"),$u("onExecute"),Eo("getHotspot",vt.some),Eo("getAnchorOverrides",rt({})),Wc(),Zs("dropdownBehaviours",[Lg,$y,Fg,Hg]),mo("toggleClass"),Eo("eventOrder",{}),wo("lazySink"),Eo("matchWidth",!1),Eo("useMinWidth",!1),wo("role")].concat(ux())),pw=rt([$f({schema:[Yu()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),Zy()]),hw=Cl({name:"Dropdown",configFields:gw(),partFields:pw(),factory:function(n,t,e,o){function r(t){bf.getState(t).each(function(t){ip.highlightPrimary(t)})}function i(t,n){return yr(t),vt.some(!0)}var u,a={expand:function(t){Lg.isOn(t)||ex(n,h,t,o,st,Hy.HighlightNone).get(st)},open:function(t){Lg.isOn(t)||ex(n,h,t,o,st,Hy.HighlightFirst).get(st)},isOpen:Lg.isOn,close:function(t){Lg.isOn(t)&&ex(n,h,t,o,st,Hy.HighlightFirst).get(st)},repositionMenus:function(t){Lg.isOn(t)&&ix(t)}};return{uid:n.uid,dom:n.dom,components:t,behaviours:nl(n.dropdownBehaviours,[Lg.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(t){return rx(n,t,{onOpen:function(){return Lg.on(t)},onClose:function(){return Lg.off(t)}})}}}),Fg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(t,n){return hw.isOpen(t)?r($y.getCoupled(t,"sandbox")):hw.open(t),vt.some(!0)},onEscape:function(t,n){return hw.isOpen(t)?(hw.close(t),vt.some(!0)):vt.none()}}),Hg.config({})]),events:Sm(vt.some(function(t){ex(n,h,t,o,r,Hy.HighlightFirst).get(st)})),eventOrder:lt(lt({},n.eventOrder),((u={})[Ii()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:a,domModification:{attributes:lt(lt({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}})),"button"===n.dom.tag?{type:tt(n.dom,"attributes").bind(function(t){return tt(t,"type")}).getOr("button")}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),vw=xa({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:function(){return eu([Sr(Ei(),D)])},exhibit:function(){return Lr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),bw=Ir("color-input-change"),yw=Ir("color-swatch-change"),xw=Ir("color-picker-cancel"),ww=Qf({schema:[mo("dom")],name:"label"}),Sw=ax("top-left"),kw=ax("top"),Cw=ax("top-right"),Ow=ax("right"),_w=ax("bottom-right"),Tw=ax("bottom"),Ew=ax("bottom-left"),Dw=[ww,ax("left"),Ow,kw,Tw,Sw,Cw,Ew,_w,Jf({name:"thumb",defaults:rt({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:eu([Tr(si(),t,"spectrum"),Tr(li(),t,"spectrum"),Tr(fi(),t,"spectrum"),Tr(mi(),t,"spectrum"),Tr(gi(),t,"spectrum"),Tr(hi(),t,"spectrum")])}}}),Jf({schema:[cr("mouseIsDown",function(){return Po(!1)})],name:"spectrum",overrides:function(e){function o(n,t){return r.getValueFromEvent(t).map(function(t){return r.setValueFrom(n,e,t)})}var r=e.model.manager;return{behaviours:nc([Fg.config({mode:"special",onLeft:function(t){return r.onLeft(t,e)},onRight:function(t){return r.onRight(t,e)},onUp:function(t){return r.onUp(t,e)},onDown:function(t){return r.onDown(t,e)}}),Hg.config({})]),events:eu([Cr(si(),o),Cr(li(),o),Cr(mi(),o),Cr(gi(),function(t,n){e.mouseIsDown.get()&&o(t,n)})])}}})],Bw=rt("slider.change.value"),Mw="left",Aw=Jx(-1),Fw=Jx(1),Iw=vt.none,Rw=vt.none,Vw={"top-left":vt.none(),top:vt.none(),"top-right":vt.none(),right:vt.some(function(t,n){Fx(t,{x:px(n)})}),"bottom-right":vt.none(),bottom:vt.none(),"bottom-left":vt.none(),left:vt.some(function(t,n){Fx(t,{x:fx(n)})})},Pw=Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=Kx(t,n,e);return qx(t,{x:o}),o},setToMin:function(t,n){qx(t,{x:sx(n)})},setToMax:function(t,n){qx(t,{x:mx(n)})},findValueOfOffset:Kx,getValueFromEvent:function(t){return cx(t).map(function(t){return t.left})},findPositionOfValue:$x,setPositionFromValue:function(t,n,e,o){var r=Mx(e),i=$x(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=On(n.element)/2;dn(n.element,"left",i-u+"px")},onLeft:Aw,onRight:Fw,onUp:Iw,onDown:Rw,edgeActions:Vw}),Hw=vt.none,zw=vt.none,Nw=t0(-1),Lw=t0(1),Ww={"top-left":vt.none(),top:vt.some(function(t,n){Fx(t,{y:dx(n)})}),"top-right":vt.none(),right:vt.none(),"bottom-right":vt.none(),bottom:vt.some(function(t,n){Fx(t,{y:hx(n)})}),"bottom-left":vt.none(),left:vt.none()},Uw=Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=Zx(t,n,e);return Qx(t,{y:o}),o},setToMin:function(t,n){Qx(t,{y:lx(n)})},setToMax:function(t,n){Qx(t,{y:gx(n)})},findValueOfOffset:Zx,getValueFromEvent:function(t){return cx(t).map(function(t){return t.top})},findPositionOfValue:n0,setPositionFromValue:function(t,n,e,o){var r=Mx(e),i=n0(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),u=wn(n.element)/2;dn(n.element,"top",i-u+"px")},onLeft:Hw,onRight:zw,onUp:Nw,onDown:Lw,edgeActions:Ww}),jw=cx,Gw=r0(-1,!1),Xw=r0(1,!1),Yw=r0(-1,!0),qw=r0(1,!0),Kw={"top-left":vt.some(function(t,n){Fx(t,Ax(fx(n),dx(n)))}),top:vt.some(function(t,n){Fx(t,Ax(xx(n),dx(n)))}),"top-right":vt.some(function(t,n){Fx(t,Ax(px(n),dx(n)))}),right:vt.some(function(t,n){Fx(t,Ax(px(n),wx(n)))}),"bottom-right":vt.some(function(t,n){Fx(t,Ax(px(n),hx(n)))}),bottom:vt.some(function(t,n){Fx(t,Ax(xx(n),hx(n)))}),"bottom-left":vt.some(function(t,n){Fx(t,Ax(fx(n),hx(n)))}),left:vt.some(function(t,n){Fx(t,Ax(fx(n),wx(n)))})},Jw=Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=o0(Kx(t,n,e.left),Zx(t,n,e.top));return e0(t,o),o},setToMin:function(t,n){e0(t,o0(sx(n),lx(n)))},setToMax:function(t,n){e0(t,o0(mx(n),gx(n)))},getValueFromEvent:jw,setPositionFromValue:function(t,n,e,o){var r=Mx(e),i=$x(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=n0(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),a=On(n.element)/2,c=wn(n.element)/2;dn(n.element,"left",i-a+"px"),dn(n.element,"top",u-c+"px")},onLeft:Gw,onRight:Xw,onUp:Yw,onDown:qw,edgeActions:Kw}),$w=Cl({name:"Slider",configFields:[Eo("stepSize",1),Eo("onChange",st),Eo("onChoose",st),Eo("onInit",st),Eo("onDragStart",st),Eo("onDragEnd",st),Eo("snapToGrid",!1),Eo("rounded",!0),wo("snapStart"),go("model",lo("mode",{x:[Eo("minX",0),Eo("maxX",100),cr("value",function(t){return Po(t.mode.minX)}),mo("getInitialValue"),ta("manager",Pw)],y:[Eo("minY",0),Eo("maxY",100),cr("value",function(t){return Po(t.mode.minY)}),mo("getInitialValue"),ta("manager",Uw)],xy:[Eo("minX",0),Eo("maxX",100),Eo("minY",0),Eo("maxY",100),cr("value",function(t){return Po({x:t.mode.minX,y:t.mode.minY})}),mo("getInitialValue"),ta("manager",Jw)]})),Zs("sliderBehaviours",[Fg,Df]),cr("mouseIsDown",function(){return Po(!1)})],partFields:Dw,factory:function(i,t,n,e){function u(t){return gl(t,i,"thumb")}function a(t){return gl(t,i,"spectrum")}function o(t){return ml(t,i,"left-edge")}function r(t){return ml(t,i,"right-edge")}function c(t){return ml(t,i,"top-edge")}function s(t){return ml(t,i,"bottom-edge")}function l(t,n){v.setPositionFromValue(t,n,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function f(t,n){h.value.set(n),l(t,u(t))}function d(e){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&ml(e,i,"thumb").each(function(t){var n=h.value.get();i.onChoose(e,t,n)})}function m(t,n){n.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))}function g(t,n){n.stop(),i.onDragEnd(t,u(t)),d(t)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:t,behaviours:nl(i.sliderBehaviours,[Fg.config({mode:"special",focusIn:function(t){return ml(t,i,"spectrum").map(Fg.focusIn).map(D)}}),Df.config({store:{mode:"manual",getValue:function(t){return h.value.get()}}}),ic.config({channels:((p={})[wf()]={onReceive:d},p)})]),events:eu([Cr(Bw(),function(t,n){!function(t,n){f(t,n);var e=u(t);i.onChange(t,e,n),vt.some(!0)}(t,n.event.value)}),ou(function(t,n){var e=h.getInitialValue();h.value.set(e);var o=u(t);l(t,o);var r=a(t);i.onInit(t,o,r,h.value.get())}),Cr(si(),m),Cr(fi(),g),Cr(mi(),m),Cr(hi(),g)]),apis:{resetToMin:function(t){v.setToMin(t,i)},resetToMax:function(t){v.setToMax(t,i)},setValue:f,refresh:l},domModification:{styles:{position:"relative"}}}},apis:{setValue:function(t,n,e){t.setValue(n,e)},resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),Qw=Ir("rgb-hex-update"),Zw=Ir("slider-update"),tS=Ir("palette-update"),nS=[Zs("formBehaviours",[Df])],eS={getField:zr(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n={field:function(t,n){return e.push(t),cl("form",i0(t),n)},record:rt(e=[])},o=t(n),r=M(n.record(),function(t){return Jf({name:t,pname:i0(t)})});return Sl("form",nS,r,uw,o)}},oS=Ir("valid-input"),rS=Ir("invalid-input"),iS=Ir("validating-input"),uS="colorcustom.rgb.",aS=function(t){return cd.config({find:t.getOpt})},cS=function(t){return cd.config({find:function(n){return Jt(n.element,t).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}})},sS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},lS=tinymce.util.Tools.resolve("tinymce.Resource"),fS=tinymce.util.Tools.resolve("tinymce.util.Tools"),dS=$o([Eo("preprocess",h),Eo("postprocess",h)]),mS=function(r,t){var i=so("RepresentingConfigs.memento processors",dS,t);return Df.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=Df.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);Df.setValue(o,e)}}})},gS=s0,pS=function(t){return Df.config({store:{mode:"memory",initialValue:t}})},hS=Ir("alloy-fake-before-tabstop"),vS=Ir("alloy-fake-after-tabstop"),bS=!(le().browser.isIE()||le().browser.isEdge()),yS=function(t){return t.getContext("2d")},xS=function(t,n,e){return t.width=n,t.height=e,t},wS=function(t){URL.revokeObjectURL(t.src)},SS=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10],kS=function(t,n,T){var e=yS(t),o=function(t){for(var n,e,o,r,i=t.data,u=T[0],a=T[1],c=T[2],s=T[3],l=T[4],f=T[5],d=T[6],m=T[7],g=T[8],p=T[9],h=T[10],v=T[11],b=T[12],y=T[13],x=T[14],w=T[15],S=T[16],k=T[17],C=T[18],O=T[19],_=0;_<i.length;_+=4)n=i[_],e=i[_+1],o=i[_+2],r=i[_+3],i[_]=n*u+e*a+o*c+r*s+l,i[_+1]=n*f+e*d+o*m+r*g+p,i[_+2]=n*h+e*v+o*b+r*y+x,i[_+3]=n*w+e*S+o*k+r*C+O;return t}(e.getImageData(0,0,t.width,t.height));return e.putImageData(o,0,0),S0(t,n)},CS=(sw=[-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1],function(t){return T0(t,sw)}),OS=E0(function(t,n){return _0(t,[1,0,0,0,n=C0(255*n,-255,255),0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])}),_S=E0(function(t,n){var e;return n=C0(n,-1,1),_0(t,[(e=(n*=100)<0?127+n/100*127:127*(0==(e=n%1)?SS[n]:SS[Math.floor(n)]*(1-e)+SS[Math.floor(n)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),TS=(lw=[0,-1,0,-1,5,-1,0,-1,0],function(t){return a=lw,(u=t).toCanvas().then(function(t){return n=t,e=u.getType(),o=a,r=yS(n),i=function(t,n,e){for(var o=function(t,n,e){return e<t?t=e:t<n&&(t=n),t},r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return n}(r.getImageData(0,0,n.width,n.height),r.getImageData(0,0,n.width,n.height),o),r.putImageData(i,0,0),S0(n,e);var n,e,o,r,i});var u,a}),ES=(fw=function(t,n){return 255*Math.pow(t/255,1-n)},function(n,e){return n.toCanvas().then(function(t){return function(t,n,e){for(var o=yS(t),r=new Array(256),i=0;i<r.length;i++)r[i]=fw(i,e);var u=function(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),S0(t,n)}(t,n.getType(),e)})}),DS=function(t,n,e){var o=b0(t),r=y0(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=BS(t,i,u);return a?c.then(function(t){return DS(t,n,e)}):c},BS=function(u,a,c){return new Ny(function(t){var n=b0(u),e=y0(u),o=Math.floor(n*a),r=Math.floor(e*c),i=h0(o,r);yS(i).drawImage(u,0,0,n,e,0,0,o,r),t(i)})},MS=function(t,n,e){var o=(e<0?360+e:e)*Math.PI/180,r=t.width,i=t.height,u=Math.sin(o),a=Math.cos(o),c=D0(Math.abs(r*a)+Math.abs(i*u)),s=D0(Math.abs(r*u)+Math.abs(i*a)),l=h0(c,s),f=yS(l);return f.translate(c/2,s/2),f.rotate(o),f.drawImage(t,-r/2,-i/2),S0(l,n)},AS=function(t,n,e){var o=h0(t.width,t.height),r=yS(o);return"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0)),S0(o,n)},FS=function(t,n,e,o,r,i){var u=h0(r,i);return yS(u).drawImage(t,-e,-o),S0(u,n)},IS=Ir("toolbar.button.execute"),RS=((dw={})[Ii()]=["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],dw),VS=Ir("update-menu-text"),PS=Ir("update-menu-icon"),HS={type:"separator"},zS=function(t,l){var n,e,o;return I((n=y(t)?t.split(" "):t,e=l,0<(o=R(n,function(t,n){return y(n)?""===n?t:"|"===n?0<t.length&&!W0(t[t.length-1])?t.concat([HS]):t:Tt(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[])).length&&W0(o[o.length-1])&&o.pop(),o),function(t,n){var e,o,r,i,u,a,c=function(t){if(W0(t))return t;var n=tt(t,"value").getOrThunk(function(){return Ir("generated-menu-item")});return Yo({value:n},t)}(n),s=(o=l,Tt(e=c,"getSubmenuItems")?(i=o,u=(r=e).getSubmenuItems(),a=zS(u,i),{item:r,menus:Yo(a.menus,sr(r.value,a.items)),expansions:Yo(a.expansions,sr(r.value,r.value))}):{item:e,menus:{},expansions:{}});return{menus:Yo(t.menus,s.menus),items:[s.item].concat(t.items),expansions:Yo(t.expansions,s.expansions)}},{menus:{},expansions:{},items:[]})},NS=rt([Eo("field1Name","field1"),Eo("field2Name","field2"),Qu("onLockedChange"),qu(["lockClass"]),Eo("locked",!1),Bf("coupledFieldBehaviours",[cd,Df])]),LS=rt([Z0("field1","field2"),Z0("field2","field1"),Jf({factory:fp,schema:[mo("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:nc([Lg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),WS=Cl({name:"FormCoupledInputs",configFields:NS(),partFields:LS(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:Mf(o.coupledFieldBehaviours,[cd.config({find:vt.some}),Df.config({store:{mode:"manual",getValue:function(t){var n=bl(t,o,["field1","field2"]),e={};return e[o.field1Name]=Df.getValue(n.field1()),e[o.field2Name]=Df.getValue(n.field2()),e},setValue:function(t,n){var e=bl(t,o,["field1","field2"]);nt(n,o.field1Name)&&Df.setValue(e.field1(),n[o.field1Name]),nt(n,o.field2Name)&&Df.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return ml(t,o,"field1")},getField2:function(t){return ml(t,o,"field2")},getLock:function(t){return ml(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),US={undo:rt(Ir("undo")),redo:rt(Ir("redo")),zoom:rt(Ir("zoom")),back:rt(Ir("back")),apply:rt(Ir("apply")),swap:rt(Ir("swap")),transform:rt(Ir("transform")),tempTransform:rt(Ir("temp-transform")),transformApply:rt(Ir("transform-apply"))},jS=rt("save-state"),GS=rt("disable"),XS=rt("enable"),YS={formActionEvent:Sy,saveState:jS,disable:GS,enable:XS},qS=tinymce.util.Tools.resolve("tinymce.geom.Rect"),KS=tinymce.util.Tools.resolve("tinymce.util.Observable"),JS=tinymce.util.Tools.resolve("tinymce.util.VK");function $S(t,d){function n(t){var n,e,o,r,i,u,a,c,s=t.raw,l=(n=Math.max,e=y.documentElement,o=y.body,r=n(e.scrollWidth,o.scrollWidth),i=n(e.clientWidth,o.clientWidth),u=n(e.offsetWidth,o.offsetWidth),a=n(e.scrollHeight,o.scrollHeight),c=n(e.clientHeight,o.clientHeight),{width:r<u?i:r,height:a<n(e.offsetHeight,o.offsetHeight)?c:a});iw(s),t.prevent(),p=s.button,h=s.screenX,v=s.screenY;var f=pn(w,"cursor");mn(g=At.fromTag("div",y),{position:"absolute",top:"0",left:"0",width:l.width+"px",height:l.height+"px","z-index":"2147483647",opacity:"0.0001",cursor:f}),Te(ye(x),g),b.push(pc(x,"mousemove",m),pc(x,"touchmove",m),pc(x,"mouseup",S),pc(x,"touchend",S)),d.start(s)}function m(t){var n=t.raw;if(iw(n),n.button!==p)return S(t);n.deltaX=n.screenX-h,n.deltaY=n.screenY-v,t.prevent(),d.drag(n)}var e,o,r,g,p,h,v,i=[],b=[],y=null!==(e=d.document)&&void 0!==e?e:document,u=null!==(o=d.root)&&void 0!==o?o:y,x=At.fromDom(y),w=At.fromDom(u.getElementById(null!==(r=d.handle)&&void 0!==r?r:t)),S=function(t){iw(t.raw),St(b,function(t){return t.unbind()}),b=[],Ee(g),d.stop&&d.stop(t.raw)};return i.push(pc(w,"mousedown",n),pc(w,"touchstart",n)),{destroy:function(){St(b.concat(i),function(t){return t.unbind()}),b=[],i=[],k(g)&&Ee(g)}}}function QS(t,n,e,o,r){return q0({name:t,icon:vt.some(n),disabled:e,tooltip:vt.some(t),primary:!1,borderless:!1},o,r)}function ZS(t,n){n?gd.enable(t):gd.disable(t)}var tk=0,nk=function(s,n,l,t,e){function f(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}}function u(t,n,e,o){var r=n.x+e*t.deltaX,i=n.y+o*t.deltaY,u=Math.max(20,n.w+e*t.deltaW),a=Math.max(20,n.h+o*t.deltaH),c=(s=qS.clamp({x:r,y:i,w:u,h:a},l,"move"===t.name),f(l,s));b.fire("updateRect",{rect:c}),h(c)}function o(t){r(s=t)}function r(e){function t(t,n){Iu(m,"#"+d+"-"+t).each(function(t){mn(t,{left:n.x+"px",top:n.y+"px",width:Math.max(0,n.w)+"px",height:Math.max(0,n.h)+"px"})})}St(g,function(n){Iu(m,"#"+d+"-"+n.name).each(function(t){mn(t,{left:e.w*n.xMul+e.x+"px",top:e.h*n.yMul+e.y+"px"})})}),t("top",{x:n.x,y:n.y,w:n.w,h:e.y-n.y}),t("right",{x:e.x+e.w,y:e.y,w:n.w-e.x-e.w+n.x,h:e.h}),t("bottom",{x:n.x,y:e.y+e.h,w:n.w,h:n.h-e.y-e.h+n.y}),t("left",{x:n.x,y:e.y,w:e.x-n.x,h:e.h}),t("move",e)}var i,a,c=[],d="tox-crid-"+tk++,m=At.fromDom(t),g=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],p=["top","right","bottom","left"],h=function(t){var n;o((n=l,{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}))};function v(t){on(t.target,"aria-grabbed","focus"===t.raw.type?"true":"false")}rn(a=At.fromTag("div"),{id:d,class:"tox-croprect-container",role:"grid","aria-dropeffect":"execute"}),Te(m,a),St(p,function(e){Iu(m,"#"+d).each(function(t){var n=At.fromTag("div");rn(n,{id:d+"-"+e,class:"tox-croprect-block","data-mce-bogus":"all"}),dn(n,"display","none"),Te(t,n)})}),St(g,function(e){Iu(m,"#"+d).each(function(t){var n=At.fromTag("div");rn(n,{id:d+"-"+e.name,"aria-label":e.label,"aria-grabbed":"false","data-mce-bogus":"all",role:"gridcell",tabindex:"-1",title:e.label}),Jr(n,["tox-croprect-handle","tox-croprect-handle-"+e.name]),dn(n,"display","none"),Te(t,n)})}),i=M(g,function(n){var e;return $S(d,{document:t.ownerDocument,root:ge(m).dom,handle:d+"-"+n.name,start:function(){e=s},drag:function(t){u(n,e,t.deltaX,t.deltaY)}})}),r(s),c.push(pc(m,"focusin",v),pc(m,"focusout",v),pc(m,"keydown",function(n){var i;function t(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),u(i,e,o,r)}switch(St(g,function(t){if(un(n.target,"id")===d+"-"+t.name)return i=t,!1}),n.raw.keyCode){case JS.LEFT:t(n,0,s,-10,0);break;case JS.RIGHT:t(n,0,s,10,0);break;case JS.UP:t(n,0,s,0,-10);break;case JS.DOWN:t(n,0,s,0,10);break;case JS.ENTER:case JS.SPACEBAR:n.prevent(),e()}}));var b=lt(lt({},KS),{toggleVisibility:function(t){var n=H(H([],M(g,function(t){return"#"+d+"-"+t.name}),!0),M(p,function(t){return"#"+d+"-"+t}),!0).join(","),e=vs(m,n);St(e,t?function(t){return bn(t,"display")}:function(t){return dn(t,"display","none")})},setClampRect:function(t){l=t,r(s)},setRect:o,getInnerRect:function(){return f(l,s)},setInnerRect:h,setViewPortRect:function(t){n=t,r(s)},destroy:function(){St(i,function(t){return t.destroy()}),i=[],St(c,function(t){return t.unbind()}),c=[]}});return b};function ek(t){var e,o,n,r,i=Po(t),u=gc(),a=(o=-1,{data:e=[],add:function(t){var n=e.splice(++o);return e.push(t),{state:t,removed:n}},undo:function(){if(n())return e[--o]},redo:function(){if(r())return e[++o]},canUndo:n=function(){return 0<o},canRedo:r=function(){return-1!==o&&o<e.length-1}});function c(t){i.set(t)}function s(t){URL.revokeObjectURL(t.url)}function l(t){var n=f(t);c(n);var e=a.add(n).removed;return fS.each(e,s),n.url}a.add(t);function f(t){return{blob:t,url:URL.createObjectURL(t)}}function d(){u.on(s),u.clear()}return{getBlobState:function(){return i.get()},setBlobState:c,addBlobState:l,getTempState:function(){return u.get().getOrThunk(i.get)},updateTempState:function(t){var n=f(t);return d(),u.set(n),n.url},addTempState:function(t){var n=f(t);return u.set(n),n.url},applyTempState:function(n){return u.get().fold(st,function(t){l(t.blob),n()})},destroyTempState:d,undo:function(){var t=a.undo();return c(t),t.url},redo:function(){var t=a.redo();return c(t),t.url},getHistoryStates:function(){return{undoEnabled:a.canUndo(),redoEnabled:a.canRedo()}}}}function ok(t,n){function i(t){var n=y.getHistoryStates();k.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),br(t,YS.formActionEvent,{name:YS.saveState(),value:n.undoEnabled})}function u(t){return t.toBlob()}function a(t){br(t,YS.formActionEvent,{name:YS.disable(),value:{}})}function c(n,t,e,o,r){a(n),k0(t).then(e).then(u).then(o).then(function(t){return w(n,t)}).then(function(){i(n),r(),x(n)}).catch(function(t){console.log(t),n.getSystem().isConnected()&&x(n)})}function r(t,n,e){c(t,y.getBlobState().blob,n,function(t){return y.updateTempState(t)},e)}function s(t){var n=y.getBlobState().url;return y.destroyTempState(),i(t),n}var e,o,l,f,d,m,g,p,h,v,b,y=ek(t.currentState),x=function(t){C.getApplyButton(t).each(function(t){gd.enable(t)}),br(t,YS.formActionEvent,{name:YS.enable(),value:{}})},w=function(t,n){return a(t),S.updateSrc(t,n)},S=(f=t.currentState.url,m=zm({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),g=Po(1),d=dc(),p=lt(lt({},d),{run:function(t){return d.get().each(t)}}),h=Po({x:0,y:0,w:1,h:1}),v=Po({x:0,y:0,w:1,h:1}),{memContainer:b=zm(ly.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[m.asSpec(),{dom:{tag:"img",attributes:{src:f}}},{dom:{tag:"div"},behaviours:nc([mm("image-panel-crop-events",[ou(function(t){b.getOpt(t).each(function(t){var n=t.element.dom,e=nk({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,st);e.toggleVisibility(!1),e.on("updateRect",function(t){var n=t.rect,e=g.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};h.set(o)}),p.set(e)})}),ru(function(){p.clear()})])])}],containerBehaviours:nc([Rg.config({}),mm("image-panel-events",[ou(function(t){_(t,f)})])])})),updateSrc:_,zoom:function(t,n){var e=g.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);g.set(o),b.getOpt(t).each(function(t){var n=t.components()[1].element;O(t,n)})},showCrop:function(){p.run(function(t){t.toggleVisibility(!0)})},hideCrop:function(){p.run(function(t){t.toggleVisibility(!1)})},getRect:function(){return h.get()},getMeasurements:function(){var t=v.get();return{width:t.w,height:t.h}}}),k=(o=zm(QS("Undo","undo",!0,function(t){br(t,US.undo(),{direction:1})},e=n)),l=zm(QS("Redo","redo",!0,function(t){br(t,US.redo(),{direction:1})},e)),{container:ly.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),l.asSpec(),QS("Zoom in","zoom-in",!1,function(t){br(t,US.zoom(),{direction:1})},e),QS("Zoom out","zoom-out",!1,function(t){br(t,US.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){ZS(t,n)}),l.getOpt(t).each(function(t){ZS(t,e)})}}),C=rw(S,n);function O(t,s){b.getOpt(t).each(function(t){var e=g.get(),o=On(t.element),r=wn(t.element),i=s.dom.naturalWidth*e,u=s.dom.naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};mn(s,n),m.getOpt(t).each(function(t){mn(t.element,n)}),p.run(function(t){var n=h.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})}function _(t,n){var e,i=At.fromTag("img");return on(i,"src",n),e=i.dom,new Ap(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)}).then(function(){t.getSystem().isConnected()&&b.getOpt(t).map(function(t){var n=Ou({element:i});Rg.replaceAt(t,1,vt.some(n));var e=v.get(),o={x:0,y:0,w:i.dom.naturalWidth,h:i.dom.naturalHeight};v.set(o);var u,r=qS.inflate(o,-20,-20);h.set(r),e.w===o.w&&e.h===o.h||(u=i,b.getOpt(t).each(function(t){var n=On(t.element),e=wn(t.element),o=u.dom.naturalWidth,r=u.dom.naturalHeight,i=Math.min(n/o,e/r);1<=i?g.set(1):g.set(i)})),O(t,i)})})}return{dom:{tag:"div",attributes:{role:"presentation"}},components:[C.memContainer.asSpec(),S.memContainer.asSpec(),k.container],behaviours:nc([Df.config({store:{mode:"manual",getValue:function(){return y.getBlobState()}}}),mm("image-tools-events",[Cr(US.undo(),function(n,t){var e=y.undo();w(n,e).then(function(t){x(n),i(n)})}),Cr(US.redo(),function(n,t){var e=y.redo();w(n,e).then(function(t){x(n),i(n)})}),Cr(US.zoom(),function(t,n){var e=n.event.direction;S.zoom(t,e)}),Cr(US.back(),function(t,n){var e,o=s(e=t);w(e,o).then(function(t){x(e)}),(0,n.event.swap)(),S.hideCrop()}),Cr(US.apply(),function(t,n){y.applyTempState(function(){s(t),(0,n.event.swap)()})}),Cr(US.transform(),function(t,n){return r(t,n.event.transform,st)}),Cr(US.tempTransform(),function(t,n){var e=n.event.transform;c(t,y.getTempState().blob,e,function(t){return y.addTempState(t)},st)}),Cr(US.transformApply(),function(t,n){var e=t,o=n.event.transform,r=n.event.swap,i=y.getBlobState().blob;c(e,i,o,function(t){var n=y.addBlobState(t);return s(e),n},r)}),Cr(US.swap(),function(n,t){k.updateButtonUndoStates(n,!1,!1);var e=t.event.transform,o=t.event.swap;e.fold(function(){o()},function(t){r(n,t,o)})})]),aw()])}}function rk(t){return!Tt(t,"items")}function ik(t,n){function e(t){return{dom:{tag:"td",innerHtml:n.translate(t)}}}return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:M(t.header,function(t){return{dom:{tag:"th",innerHtml:n.translate(t)}}})}]},{dom:{tag:"tbody"},components:M(t.cells,function(t){return{dom:{tag:"tr"},components:M(t,e)}})}],behaviours:nc([py.config({}),Hg.config({})])}}function uk(e,n){var t=e.label.map(function(t){return oy(t,n)}),o=[gd.config({disabled:function(){return e.disabled||n.isDisabled()}}),pv(),Fg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return vr(t,ky),vt.some(!0)}}),mm("textfield-change",[Cr(Si(),function(t,n){br(t,yy,{name:e.name})}),Cr(Ai(),function(t,n){br(t,yy,{name:e.name})})]),py.config({})],r=e.validation.map(function(o){return Ky.config({getRoot:function(t){return Yt(t.element)},invalidClass:"tox-invalid",validator:{validate:function(t){var n=Df.getValue(t),e=o.validator(n);return Gy(!0===e?Ve.value(n):Ve.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(rt({}),function(t){return{placeholder:n.translate(t)}}),u=e.inputMode.fold(rt({}),function(t){return{inputmode:t}}),a=lt(lt({},i),u);return ey(t,my.parts.field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:nc(ft([o,r])),selectOnFocus:!1,factory:Dy}),(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),[gd.config({disabled:function(){return e.disabled||n.isDisabled()},onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}}),pv()])}function ak(t){var n=Po(null);return wu({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})}function ck(t,n,e){var o=Df.getValue(e);Df.setValue(n,o),s1(n)}function sk(t,n){var e=t.element,o=Qr(e),r=e.dom;"number"!==un(e,"type")&&n(r,o)}function lk(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:st}}function fk(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:void 0},onAction:st}}function dk(t,n){return e=t,M(F(n,function(t){return t.type===e}),lk);var e}function mk(t,n){var e=t.toLowerCase();return F(n,function(t){return ut((void 0!==t.meta&&void 0!==t.meta.text?t.meta:t).text.toLowerCase(),e)||ut(t.value.toLowerCase(),e)})}function gk(u,a,c){function r(t){var n=Df.getValue(t);c.addToHistory(n.value,u.filetype)}var t,n,e,o,i=a.shared.providers,s=my.parts.field({factory:m1,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":v1,type:"url"},minChars:0,responseTime:0,fetch:function(t){var e,o,n,r,i=U0((e=u.filetype,o=c,n=Df.getValue(t),r=void 0!==n.meta.text?n.meta.text:n.value,o.getLinkInformation().fold(function(){return[]},function(t){var n=mk(r,M(o.getHistory(e),function(t){return fk(t,t)}));return"file"===e?R([n,mk(r,dk("header",t.targets)),mk(r,ft([vt.from(t.anchorTop).map(function(t){return fk("<top>",t)}).toArray(),dk("anchor",t.targets),vt.from(t.anchorBottom).map(function(t){return fk("<bottom>",t)}).toArray()]))],function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat(h1,n)},[]):n})),Sh.BUBBLE_TO_SANDBOX,a,!1);return Gy(i)},getHotspot:function(t){return p.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(Ky)&&Ky.run(t).get(st)},typeaheadBehaviours:nc(ft([c.getValidationHandler().map(function(e){return Ky.config({getRoot:function(t){return Yt(t.element)},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){f.getOpt(t).each(function(t){on(t.element,"title",i.translate(n))})}},validator:{validate:function(t){var n=Df.getValue(t);return p1(function(o){e({type:u.filetype,url:n.value},function(t){var n,e;"invalid"===t.status?(n=Ve.error(t.message),o(n)):(e=Ve.value(t.message),o(e))})})},validateOnLoad:!1}})}).toArray(),[gd.config({disabled:function(){return u.disabled||i.isDisabled()}}),py.config({}),mm("urlinput-events",ft(["file"===u.filetype?[Cr(Si(),function(t){br(t,yy,{name:u.name})})]:[],[Cr(ki(),function(t){br(t,yy,{name:u.name}),r(t)}),Cr(Ai(),function(t){br(t,yy,{name:u.name}),r(t)})]]))]])),eventOrder:((t={})[Si()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:a.shared.getSink,parts:{menu:Zp(0,0,"normal")},onExecute:function(t,n,e){br(n,ky,{})},onItemExecute:function(t,n,e,o){r(t),br(t,yy,{name:u.name})}}),l=u.label.map(function(t){return oy(t,i)}),f=zm((n="invalid",e=vt.some(v1),Xm("warning",{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+n],attributes:lt({title:i.translate(o=void 0===o?n:o),"aria-live":"polite"},e.fold(function(){return{}},function(t){return{id:t}}))},i.icons))),d=zm({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[f.asSpec()]}),m=c.getUrlPicker(u.filetype),g=Ir("browser.url.event"),p=zm({dom:{tag:"div",classes:["tox-control-wrap"]},components:[s,d.asSpec()],behaviours:nc([gd.config({disabled:function(){return u.disabled||i.isDisabled()}})])}),h=zm(J0({name:u.name,icon:vt.some("browse"),text:u.label.getOr(""),disabled:u.disabled,primary:!1,borderless:!0},function(t){return vr(t,g)},i,[],["tox-browse-url"]));return my.sketch({dom:by([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:ft([[p.asSpec()],m.map(function(){return h.asSpec()}).toArray()])}]),fieldBehaviours:nc([gd.config({disabled:function(){return u.disabled||i.isDisabled()},onDisabled:function(t){my.getField(t).each(gd.disable),h.getOpt(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable),h.getOpt(t).each(gd.enable)}}),pv(),mm("url-input-events",[Cr(g,function(o){cd.getCurrent(o).each(function(n){var t=Df.getValue(n),e=lt({fieldname:u.name},t);m.each(function(t){t(e).get(function(t){Df.setValue(n,t),br(o,yy,{name:u.name})})})})})])])})}function pk(r){return function(n,e,o){return tt(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}}function hk(n,t,e){var o=Yo(e,{shared:{interpreter:function(t){return x1(n,t,o)}}});return x1(n,t,o)}function vk(t,n,e){function o(){return At.fromDom(t.getContentAreaContainer())}function r(){return p||!e()}var i,u,a,c,s,l,f,d,m,g,p=sv(t);return{inlineDialog:(f=o,d=n,m=r,g={maxHeightFunction:Vc()},function(){return m()?{type:"node",root:tn(f()),node:vt.from(f()),bubble:Sc(12,12,w1),layouts:{onRtl:function(){return[Am]},onLtr:function(){return[Mm]}},overrides:g}:{type:"hotspot",hotspot:d(),bubble:Sc(-12,12,w1),layouts:{onRtl:function(){return[Xa]},onLtr:function(){return[Ya]}},overrides:g}}),banner:(c=o,s=n,l=r,function(){return l()?{type:"node",root:tn(c()),node:vt.from(c()),layouts:{onRtl:function(){return[cp]},onLtr:function(){return[cp]}}}:{type:"hotspot",hotspot:s(),layouts:{onRtl:function(){return[$a]},onLtr:function(){return[$a]}}}}),cursor:(u=t,function(){return{type:"selection",root:a(),getSelection:function(){var t=u.selection.getRng();return vt.some(ms.range(At.fromDom(t.startContainer),t.startOffset,At.fromDom(t.endContainer),t.endOffset))}}}),node:(i=a=function(){return At.fromDom(t.getBody())},function(t){return{type:"node",root:i(),node:t}})}}function bk(i){return vt.from(i.getParam("style_formats")).filter(c).map(function(t){var n,e,o=(n=i,e=k1(t),n.formatter?r(e.customFormats):n.on("init",function(){r(e.customFormats)}),e.formats);function r(t){St(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})}return i.getParam("style_formats_merge",!1,"boolean")?S1.concat(o):o}).getOr(S1)}function yk(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return Yo(t,o)}function xk(c,t,s,l){var f=function(t){return M(t,function(t){var n,e,o,r,i,u=Ct(t);if(nt(t,"items")){var a=f(t.items);return Yo(Yo(t,{type:"submenu"}),{getStyleItems:rt(a)})}return nt(t,"format")?yk(t,s,l):1===u.length&&wt(u,"title")?Yo(t,{type:"separator"}):(r={type:"formatter",format:o="custom-"+(e=y((n=t).name)?n.name:Ir(n.title)),isSelected:s(o),getStylePreview:l(o)},i=Yo(n,r),c.formatter.register(e,i),i)})};return f(t)}function wk(e){return function(t){if(k(n=t)&&1===n.nodeType){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}var n;return!1}}function Sk(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}}function kk(t){return t.innerText||t.textContent}function Ck(t){return t&&"A"===t.nodeName&&void 0!==(t.id||t.name)&&JC(t)}function Ok(t){return t&&/^(H[1-6])$/.test(t.nodeName)}function _k(t){return Ok(t)&&JC(t)}function Tk(t){var n,e=t.id||Ir("h");return Sk("header",kk(t),"#"+e,Ok(n=t)?parseInt(n.nodeName.substr(1),10):0,function(){t.id=e})}function Ek(t){var n=t.id||t.name;return Sk("anchor",kk(t)||"#"+n,"#"+n,0,st)}function Dk(t){return 0<C1(t.title).length}function Bk(t){return y(t)&&/^https?/.test(t)}function Mk(t){return x(t)&&Q(t,function(t){return!(c(n=t)&&n.length<=5&&N(n,Bk));var n}).isNone()}function Ak(){var t,n=db.getItem(E1);if(null===n)return{};try{t=JSON.parse(n)}catch(t){if(t instanceof SyntaxError)return console.log("Local storage "+E1+" was not valid JSON",t),{};throw t}return Mk(t)?t:(console.log("Local storage "+E1+" was not valid format",t),{})}function Fk(t){return tt(Ak(),t).getOr([])}function Ik(n,t){var e,o;Bk(n)&&(o=F(tt(e=Ak(),t).getOr([]),function(t){return t!==n}),e[t]=[n].concat(o).slice(0,5),function(t){if(!Mk(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));db.setItem(E1,JSON.stringify(t))}(e))}function Rk(t){return!!t}function Vk(t){return dt(fS.makeMap(t,/[, ]/),Rk)}function Pk(t){return vt.from(t.getParam("file_picker_callback")).filter(S)}function Hk(t){return vt.from(t).filter(y).getOrUndefined()}function zk(l){return{getHistory:Fk,addToHistory:Ik,getLinkInformation:function(){return!1===(t=l).getParam("typeahead_urls")?vt.none():vt.some({targets:T1(t.getBody()),anchorTop:Hk(t.getParam("anchor_top","#top")),anchorBottom:Hk(t.getParam("anchor_bottom","#bottom"))});var t},getValidationHandler:function(){return vt.from(void 0===(n=(t=l).getParam("file_picker_validator_handler",void 0,"function"))?t.getParam("filepicker_validator_handler",void 0,"function"):n);var t,n},getUrlPicker:function(t){return n=c=l,e=s=t,r=vt.some((o=n).getParam("file_picker_types")).filter(Rk),i=vt.some(o.getParam("file_browser_callback_types")).filter(Rk),u=r.or(i).map(Vk),a=Pk(o).fold(T,function(t){return u.fold(D,function(t){return 0<Ct(t).length&&t})}),(w(a)?a?Pk(n):vt.none():a[e]?Pk(n):vt.none()).map(function(o){return function(n){return jy(function(e){var t=lt({filetype:s,fieldname:n.fieldname},vt.from(n.meta).getOr({}));o.call(c,function(t,n){if(!y(t))throw new Error("Expected value to be string");if(void 0!==n&&!x(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var n,e,o,r,i,u,a,c,s}}}function Nk(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p,h=Po(!1),v={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=Po(av(n)?"bottom":"top")).get,setDockingMode:o.set},b={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:dp.translate,isDisabled:function(){return n.mode.isReadOnly()||n.ui.isDisabled()},getSetting:n.getParam.bind(n)},interpreter:function(t){return x1(y1,t,b)},anchors:vk(n,e,v.isPositionedAtTop),header:v,getSink:function(){return Ve.value(t)}},urlinput:zk(n),styleselect:(f=Po([]),d=Po([]),m=Po([]),g=Po([]),p=Po(!(l=function(t){var n=t.items;return void 0!==n&&0<n.length?z(n,l):[t.format]})),(s=n).on("PreInit",function(t){var n=bk(s),e=xk(s,n,y,x);f.set(e),d.set(z(e,l))}),s.on("addStyleModifications",function(t){var n=xk(s,t.items,y,x);m.set(n),p.set(t.replace),g.set(z(n,l))}),{getData:function(){var t=p.get()?[]:f.get(),n=m.get();return t.concat(n)},getFlattenedKeys:function(){var t=p.get()?[]:d.get(),n=g.get();return t.concat(n)}}),colorinput:{colorPicker:function(t,n){Ib(c)(t,n)},hasCustomColors:function(){return gb(a)},getColors:function(){return pb(u)},getColorCols:(i=u=a=c=n,function(){return vb(i)})},dialog:{isDraggableModal:(r=n,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return h.get()},setContextMenuState:function(t){return h.set(t)}};function y(t){return function(){return s.formatter.match(t)}}function x(n){return function(){var t=s.formatter.get(n);return void 0!==t?vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:s.dom.parseStyle(s.formatter.getCssText(n))}):vt.none()}}return b}function Lk(t){return(mt(hn(t,"position"),"fixed")?vt.none():qt(t)).orThunk(function(){var e=At.fromTag("span");return Yt(t).bind(function(t){Te(t,e);var n=qt(e);return Ee(e),n})})}function Wk(t){return Lk(t).map(Cn).getOrThunk(function(){return ke(0,0)})}function Uk(t,n){var e=t.element;Yr(e,n.transitionClass),qr(e,n.fadeOutClass),Yr(e,n.fadeInClass),n.onShow(t)}function jk(t,n){var e=t.element;Yr(e,n.transitionClass),qr(e,n.fadeInClass),Yr(e,n.fadeOutClass),n.onHide(t)}function Gk(t,n,e){return N(t,function(t){switch(t){case"bottom":return n.bottom<=e.bottom;case"top":return n.y>=e.y}})}function Xk(n,t){return t.getInitialPos().map(function(t){return Me(t.bounds.x,t.bounds.y,On(n),wn(n))})}function Yk(e,o,r){return r.getInitialPos().bind(function(t){switch(r.clearInitialPos(),t.position){case"static":return vt.some(z1.static());case"absolute":var n=Lk(e).map(Ae).getOrThunk(function(){return Ae(be())});return vt.some(z1.absolute(Ea("absolute",tt(t.style,"left").map(function(t){return o.x-n.x}),tt(t.style,"top").map(function(t){return o.y-n.y}),tt(t.style,"right").map(function(t){return n.right-o.right}),tt(t.style,"bottom").map(function(t){return n.bottom-o.bottom}))));default:return vt.none()}})}function qk(t,n,e){var o,r,i,u=t.element;return mt(hn(u,"position"),"fixed")?(r=n,Xk(o=u,i=e).filter(function(t){return Gk(i.getModes(),t,r)}).bind(function(t){return Yk(o,t,i)})):function(t,n,e){var r,o,i=Ae(t);if(Gk(e.getModes(),i,n))return vt.none();r=t,o=i,e.setInitialPos({style:function(){var t={},n=r.dom;if(ct(n))for(var e=0;e<n.style.length;e++){var o=n.style.item(e);t[o]=n.style[o]}return t}(),position:pn(r,"position")||"static",bounds:o});var u=Fe(),a=i.x-u.x,c=n.y-u.y,s=u.bottom-n.bottom,l=i.y<=n.y;return vt.some(z1.fixed(Ea("fixed",vt.some(a),l?vt.some(c):vt.none(),vt.none(),l?vt.none():vt.some(s))))}(u,n,e)}function Kk(n,t,e){e.setDocked(!1),St(["left","right","top","bottom","position"],function(t){return bn(n.element,t)}),t.onUndocked(n)}function Jk(t,n,e,o){var r="fixed"===o.position;e.setDocked(r),Da(t.element,o),(r?n.onDocked:n.onUndocked)(t)}function $k(o,t,r,i,u){void 0===u&&(u=!1),t.contextual.each(function(e){e.lazyContext(o).each(function(t){var n=t.y<i.bottom&&t.bottom>i.y;n!==r.isVisible()&&(r.setVisible(n),u&&!n?(Jr(o.element,[e.fadeOutClass]),e.onHide(o)):(n?Uk:jk)(o,e))})})}function Qk(t,n,e){var o,r,i,u,a,c;e.isDocked()&&(r=n,i=e,c=(o=t).element,i.setDocked(!1),Xk(a=o.element,u=i).bind(function(t){return Yk(a,t,u)}).each(function(t){t.fold(function(){return Kk(o,r,i)},function(t){return Jk(o,r,i,t)},st)}),i.setVisible(!0),r.contextual.each(function(t){$r(c,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(o)}),N1(o,r,i))}function Zk(t,n){return wt(U1.getModes(t),n)}function tC(r){var i=r.element;Yt(i).each(function(t){var n,e,o="padding-"+U1.getModes(r)[0];U1.isDocked(r)?(n=On(t),dn(i,"width",n+"px"),dn(t,o,Sn(e=i)+(parseInt(pn(e,"margin-top"),10)||0)+(parseInt(pn(e,"margin-bottom"),10)||0)+"px")):(bn(i,"width"),bn(t,o))})}function nC(t,n){n?(qr(t,G1.fadeOutClass),Jr(t,[G1.transitionClass,G1.fadeInClass])):(qr(t,G1.fadeInClass),Jr(t,[G1.fadeOutClass,G1.transitionClass]))}function eC(t,n){var e=At.fromDom(t.getContainer());n?(Yr(e,X1),qr(e,Y1)):(Yr(e,Y1),qr(e,X1))}function oC(u,t){function o(n){r().each(function(t){return n(t.element)})}function n(t){u.inline||tC(t),eC(u,U1.isDocked(t)),t.getSystem().broadcastOn([xf()],{}),r().each(function(t){return t.getSystem().broadcastOn([xf()],{})})}var e,i=gc(),r=t.getSink,a=u.inline?[]:[ic.config({channels:((e={})[j1()]={onReceive:tC},e)})];return H([Hg.config({}),U1.config({contextual:lt({lazyContext:function(t){var n=Sn(t.element),e=u.inline?u.getContentAreaContainer():u.getContainer(),o=Ae(At.fromDom(e)),r=o.height-n,i=o.y+(Zk(t,"top")?0:n);return vt.some(Me(o.x,i,o.width,r))},onShow:function(){o(function(t){return nC(t,!0)})},onShown:function(r){o(function(t){return $r(t,[G1.transitionClass,G1.fadeInClass])}),i.get().each(function(t){var n,e=r.element,o=Ut(n=t);Oa(o).filter(function(t){return!Lt(n,t)}).filter(function(t){return Lt(t,At.fromDom(o.dom.body))||Wt(e,t)}).each(function(){return ka(n)}),i.clear()})},onHide:function(t){var n=t.element,e=r;_a(n).orThunk(function(){return e().toOptional().bind(function(t){return _a(t.element)})}).fold(i.clear,i.set),o(function(t){return nC(t,!1)})},onHidden:function(){o(function(t){return $r(t,[G1.transitionClass])})}},G1),lazyViewport:function(t){var n=Fe(),e=u.getParam("toolbar_sticky_offset",0,"number"),o=n.y+(Zk(t,"top")?e:0),r=n.height-(Zk(t,"bottom")?e:0);return Me(n.x,o,n.width,r)},modes:[t.header.getDockingMode()],onDocked:n,onUndocked:n})],a,!0)}function rC(t){return ao("menubutton",$1,t)}function iC(n,t){return t.getAnimationRoot.fold(function(){return n.element},function(t){return t(n)})}function uC(t){return t.dimension.property}function aC(t,n){return t.dimension.getDimension(n)}function cC(t,n){$r(iC(t,n),[n.shrinkingClass,n.growingClass])}function sC(t,n){qr(t.element,n.openClass),Yr(t.element,n.closedClass),dn(t.element,uC(n),"0px"),yn(t.element)}function lC(t,n){qr(t.element,n.closedClass),Yr(t.element,n.openClass),bn(t.element,uC(n))}function fC(t,n,e,o){e.setCollapsed(),dn(t.element,uC(n),aC(n,t.element)),yn(t.element),cC(t,n),sC(t,n),n.onStartShrink(t),n.onShrunk(t)}function dC(t,n,e){var o=aC(n,t.element);("0px"===o?fC:function(t,n,e,o){var r=o.getOrThunk(function(){return aC(n,t.element)});e.setCollapsed(),dn(t.element,uC(n),r),yn(t.element);var i=iC(t,n);qr(i,n.growingClass),Yr(i,n.shrinkingClass),sC(t,n),n.onStartShrink(t)})(t,n,e,vt.some(o))}function mC(t,n,e){var o=iC(t,n),r=Kr(o,n.shrinkingClass),i=aC(n,t.element);lC(t,n);var u=aC(n,t.element);(r?function(){dn(t.element,uC(n),i),yn(t.element)}:function(){sC(t,n)})(),qr(o,n.shrinkingClass),Yr(o,n.growingClass),lC(t,n),dn(t.element,uC(n),u),e.setExpanded(),n.onStartGrow(t)}function gC(t,n,e){return!0===Kr(iC(t,n),n.growingClass)}function pC(t,n,e){return!0===Kr(iC(t,n),n.shrinkingClass)}function hC(t){return"<alloy.field."+t+">"}function vC(t){return{element:function(){return t.element.dom}}}function bC(t,e){cd.getCurrent(t).each(function(t){return Rg.set(t,[(n=e,uO.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:(e=t,r=M(Ct(o=n),function(t){var n=o[t],e=co(ao("sidebar",aO,n));return{name:t,getApi:vC,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}}),M(r,function(t){var n=Po(st);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Kh([vv(t,n),bv(t,n),Cr(Ji(),function(n,t){var e=t.event;V(r,function(t){return t.name===e.name}).each(function(t){(e.visible?t.onShow:t.onHide)(t.getApi(n))})})])})})),slotBehaviours:Kh([ou(function(t){return uO.hideAllSlots(t)})])};var e,o,r}))]);var n})}function yC(t){return cd.getCurrent(t).bind(function(t){return eO.isGrowing(t)||eO.hasGrown(t)?cd.getCurrent(t).bind(function(n){return V(uO.getSlotNames(n),function(t){return uO.isShowing(n,t)})}):vt.none()})}function xC(t){var n=At.fromHtml(t),e=Kt(n),o=R(void 0!==n.dom.attributes?n.dom.attributes:[],function(t,n){var e;return"class"===n.name?t:lt(lt({},t),((e={})[n.name]=n.value,e))},{}),r=Array.prototype.slice.call(n.dom.classList,0),i=0===e.length?{}:{innerHtml:Mr(n)};return lt({tag:Ft(n),classes:r,attributes:o},i)}function wC(t){return cd.getCurrent(t).each(function(t){return ka(t.element)})}function SC(f,d,m){function n(t){var n;!g.get()||"focusin"===(n=t).type&&(n.composed?Y(n.composedPath()):vt.from(n.target)).map(At.fromDom).filter(Xn).exists(function(t){return Kr(t,"mce-pastebin")})||(t.preventDefault(),wC(d()),f.editorManager.setActive(f))}var g=Po(!1),e=gc();function o(t){var n,e,o,r,i,u,a,c,s,l;t!==g.get()&&(g.set(t),n=f,e=d(),o=t,r=m.providers,c=e.element,s=o,l="data-mce-"+(i="tabindex"),vt.from(n.iframeElement).map(At.fromDom).each(function(n){s?(an(n,i).each(function(t){return on(n,l,t)}),on(n,i,-1)):(sn(n,i),an(n,l).each(function(t){on(n,i,t),sn(n,l)}))}),o?(fO.block(e,(a=r,function(t,n){return{dom:{tag:"div",attributes:{"aria-label":a.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:xC('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})),bn(c,"display"),sn(c,"aria-hidden"),n.hasFocus()&&wC(e)):(u=cd.getCurrent(e).exists(function(t){return Ca(t.element)}),fO.unblock(e),dn(c,"display","none"),on(c,"aria-hidden","true"),u&&n.focus()),f.fire("AfterProgressState",{state:t}))}f.inline||f.on("PreInit",function(){f.dom.bind(f.getWin(),"focusin",n),f.on("BeforeExecCommand",function(t){"mcefocus"===t.command.toLowerCase()&&!0!==t.value&&n(t)})}),f.on("ProgressState",function(t){var n;e.on(lp.clearTimeout),u(t.time)?(n=lp.setEditorTimeout(f,function(){return o(t.state)},t.time),e.set(n)):(o(t.state),e.clear())})}function kC(t,n,e){return{within:t,extra:n,withinWidth:e}}function CC(t,n,o){var e,r=(e=function(t,n){var e=o(t);return vt.some({element:t,start:n,finish:n+e,width:e})},R(t,function(n,t){return e(t,n.len).fold(rt(n),function(t){return{len:t.finish,list:n.list.concat([t])}})},{len:0,list:[]}).list),i=F(r,function(t){return t.finish<=n}),u=I(i,function(t,n){return t+n.width},0);return{within:i,extra:r.slice(i.length),withinWidth:u}}function OC(t){return M(t,function(t){return t.element})}function _C(t,n){var e=M(n,function(t){return Eu(t)});V1.setGroups(t,e)}function TC(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k=n.builtGroups.get();0!==k.length&&(o=gl(t,n,"primary"),r=$y.getCoupled(t,"overflowGroup"),dn(o.element,"visibility","hidden"),u=K(i=k.concat([r]),function(n){return _a(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}),e([]),_C(o,i),a=On(o.element),0===(s=a,l=n.builtGroups.get(),d=r,y=(0===(m=CC(l,s,f=function(t){return On(t.element)})).extra.length?vt.some(m):vt.none()).getOrThunk(function(){return CC(l,s-f(d),f)}),x=y.within,w=y.extra,S=y.withinWidth,(c=1===w.length&&w[0].width<=f(d)?(b=S,kC(OC(x.concat(w)),[],b)):1<=w.length?(p=w,h=d,v=S,kC(OC(x).concat([h]),OC(p),v)):(g=S,kC(OC(x),[],g))).extra.length)?(Rg.remove(o,r),e([])):(_C(o,c.within),e(c.extra)),bn(o.element,"visibility"),yn(o.element),u.each(Hg.focus))}function EC(t,n){var e=$y.getCoupled(t,"toolbarSandbox");bf.isOpen(e)?bf.close(e):bf.open(e,n.toolbar())}function DC(t,n,e,o){var r=e.getBounds.map(function(t){return t()}),i=e.lazySink(t).getOrDie();ff.positionWithinBounds(i,n,{anchor:{type:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:pO()}}},r)}function BC(t,n,e,o,r){V1.setGroups(n,r),DC(t,n,e,o),Lg.on(t)}function MC(t){return M(t,function(t){return Eu(t)})}function AC(t,e,o){TC(t,o,function(n){o.overflowGroups.set(n),e.getOpt(t).each(function(t){bO.setGroups(t,MC(n))})})}function FC(n,e){ml(n,e,"overflow-button").bind(function(){return ml(n,e,"overflow")}).each(function(t){QC(n,e),eO.toggleGrow(t)})}function IC(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:lt({tag:"div",classes:["tox-toolbar__group"]},n),components:[wO.parts.items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:nc([py.config({}),Hg.config({})])}}function RC(t){return wO.sketch(IC(t))}function VC(e,t){var n=ou(function(t){var n=M(e.initGroups,RC);V1.setGroups(t,n)});return nc([Qv(e.providers.isDisabled),pv(),Fg.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),mm("toolbar-events",[n])])}function PC(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":IC({title:vt.none(),items:[]}),"overflow-button":Y0({name:"more",icon:vt.some("more-drawer"),disabled:!1,tooltip:vt.some("More..."),primary:!1,borderless:!1},vt.none(),t.providers)},splitToolbarBehaviours:VC(t,n)}}function HC(t){var n=t.cyclicKeying?"cyclic":"acyclic";return V1.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===bh.scrolling?["tox-toolbar--scrolling"]:[])},components:[V1.parts.groups({})],toolbarBehaviours:VC(t,n)})}function zC(t){return"string"==typeof t?t.split(" "):t}function NC(i,u){var a=lt(lt({},IO),u.menus),e=0<Ct(u.menus).length,t=F(void 0===u.menubar||!0===u.menubar?zC("file edit view insert format tools table help"):zC(!1===u.menubar?"":u.menubar),function(t){var n=Tt(IO,t);return e?n||tt(u.menus,t).exists(function(t){return Tt(t,"items")}):n});return F(M(t,function(t){var n=a[t],e={title:n.title,items:zC(n.items)},o=u,r=i.getParam("removed_menuitems","").split(/[ ,]/);return{text:e.title,getItems:function(){return z(e.items,function(t){var n=t.toLowerCase();return 0===n.trim().length||d(r,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:o.menuItems[n]?[o.menuItems[n]]:[]})}}}),function(t){return 0<t.getItems().length&&d(t.getItems(),function(t){return"separator"!==t.type})})}function LC(t){function n(){t._skinLoaded=!0,t.fire("SkinLoaded")}return function(){t.initialized?n():t.on("init",n)}}function WC(e,o,r){return new Ap(function(t,n){r.load(o,t,n),e.on("remove",function(){return r.unload(o)})})}function UC(t,n){var e,o,r,i,u,a,c,s,l,f=(r=(e=n).getParam("skin"),i=e.getParam("skin_url"),!1!==r&&(o=r||"oxide",i=i?e.documentBaseURI.toAbsolute(i):ov.baseURL+"/skins/ui/"+o),i);f&&n.contentCSS.push(f+(t?"/content.inline":"/content")+".min.css"),!1===n.getParam("skin")==0&&y(f)?Ap.all([WC(n,f+"/skin.min.css",n.ui.styleSheetLoader),(c=n,s=f,l=At.fromDom(c.getElement()),pe(l).isSome()?WC(c,s+"/skin.shadowdom.min.css",ev.DOM.styleSheetLoader):Ap.resolve())]).then(LC(n),(u=n,a="Skin could not be loaded",function(){return u.fire("SkinLoadError",{message:a})})):LC(n)()}function jC(o,r){return function(n){function t(){n.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,n.setActive);e.set(t)}var e=mc();return o.initialized?t():o.once("init",t),function(){o.off("init",t),e.clear()}}}function GC(o,r,i){return function(t){function n(){return i(t)}function e(){i(t),o.on(r,n)}return o.initialized?e():o.once("init",e),function(){o.off("init",e),o.off(r,n)}}}function XC(n){return function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("mceToggleFormat",!1,t.format)})}}}function YC(t,n){return function(){return t.execCommand(n)}}function qC(t,n,e){var u,a,c,o=e.dataset,r="basic"===o.type?function(){return M(o.data,function(t){return yk(t,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:(u=n,a=e,c=function(t,n,e){var o="formatter"===t.type&&a.isInvalid(t);return 0===n?o?[]:i(t,n,!1,e).toArray():i(t,n,o,e).toArray()},{validateItems:s,getFetch:function(e,o){return function(t,n){n(U0(s(o()),Sh.CLOSE_ON_EXECUTE,e,!1))}}}),getStyleItems:r};function i(t,n,e,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return vt.some({type:"separator",text:r});if("submenu"!==t.type)return vt.some(lt({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:e,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=z(t.getStyleItems(),function(t){return c(t,n,o)});return 0===n&&i.length<=0?vt.none():vt.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return z(t.getStyleItems(),function(t){return c(t,n,o)})}})}function s(t){var n=a.getCurrentValue(),e=a.shouldHide?0:1;return z(t,function(t){return c(t,e,n)})}}function KC(t,n,e){var o=qC(0,n,e),r=o.items,i=o.getStyleItems,u=GC(t,"NodeChange",function(t){var n=t.getComponent();e.updateText(n)});return L0({text:e.icon.isSome()?vt.none():e.text,icon:e.icon,tooltip:vt.from(e.tooltip),role:vt.none(),fetch:r.getFetch(n,i),onSetup:u,getApi:function(t){return{getComponent:rt(t)}},columns:1,presets:"normal",classes:e.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}function JC(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return O1(t)}return!1}(t)&&!_1(t)}function $C(r,t){function n(t){return vl(r)}function e(e,o){return function(t,n){return ml(t,r,n).map(function(t){return e(t,n)}).getOr(o)}}function o(t,n){return"true"!==un(t.element,"aria-hidden")}var i,u=e(o,!1),a=e(function(t,n){var e;o(t)&&(dn(e=t.element,"display","none"),on(e,"aria-hidden","true"),br(t,Ji(),{name:n,visible:!1}))}),c=(i=a,function(n,t){St(t,function(t){return i(n,t)})}),s=e(function(t,n){var e;o(t)||(bn(e=t.element,"display"),sn(e,"aria-hidden"),br(t,Ji(),{name:n,visible:!0}))});return{uid:r.uid,dom:r.dom,components:t,behaviours:tl(r.slotBehaviours),apis:{getSlotNames:n,getSlot:function(t,n){return ml(t,r,n)},isShowing:u,hideSlot:a,hideAllSlots:function(t){return c(t,n())},showSlot:s}}}function QC(t,n){ml(t,n,"overflow").each(function(e){TC(t,n,function(t){var n=M(t,function(t){return Eu(t)});V1.setGroups(e,n)}),ml(t,n,"overflow-button").each(function(t){eO.hasGrown(e)&&Lg.on(t)}),eO.refresh(e)})}var ZC,t1,n1,e1="data-value",o1=function(n,e,t,o){return M(t,function(t){return rk(t)?{type:"togglemenuitem",text:t.text,value:t.value,active:t.value===o,onAction:function(){Df.setValue(n,t.value),br(n,yy,{name:e}),Hg.focus(n)}}:{type:"nestedmenuitem",text:t.text,getSubmenuItems:function(){return o1(n,e,t.items,o)}}})},r1=function(t,n){return K(t,function(t){return rk(t)?ot(t.value===n,t):r1(t.items,n)})},i1=kl({name:"HtmlSelect",configFields:[mo("options"),Zs("selectBehaviours",[Hg,Df]),Eo("selectClasses",[]),Eo("selectAttributes",{}),wo("data")],factory:function(e,t){var n=M(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return sr("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:nl(e.selectBehaviours,[Hg.config({}),Df.config({store:lt({mode:"manual",getValue:function(t){return Qr(t.element)},setValue:function(t,n){V(e.options,function(t){return t.value===n}).isSome()&&Zr(t.element,n)}},o)})])}}}),u1=Object.freeze({__proto__:null,events:function(t,n){var e=t.stream.streams.setup(t,n);return eu([Cr(t.event,e),ru(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[Cr(t,function(){return n.cancel()})]}).getOr([])))}}),a1=Object.freeze({__proto__:null,throttle:ak,init:function(t){return t.stream.streams.state(t)}}),c1=xa({fields:[go("stream",lo("mode",{throttle:[mo("delay"),Eo("stopEvent",!0),ta("streams",{setup:function(t,n){var e=t.stream,o=vp(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:ak})]})),Eo("event","input"),wo("cancelEvent"),Qu("onStream")],name:"streaming",active:u1,state:a1}),s1=function(t){sk(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},l1=rt("alloy.typeahead.itemexecute"),f1=rt([wo("lazySink"),mo("fetch"),Eo("minChars",5),Eo("responseTime",1e3),Ju("onOpen"),Eo("getHotspot",vt.some),Eo("getAnchorOverrides",rt({})),Eo("layouts",vt.none()),Eo("eventOrder",{}),Vo("model",{},[Eo("getDisplayText",function(t){return void 0!==t.meta&&void 0!==t.meta.text?t.meta.text:t.value}),Eo("selectsOver",!0),Eo("populateFromBrowse",!0)]),Ju("onSetValue"),$u("onExecute"),Ju("onItemExecute"),Eo("inputClasses",[]),Eo("inputAttributes",{}),Eo("inputStyles",{}),Eo("matchWidth",!0),Eo("useMinWidth",!1),Eo("dismissOnBlur",!0),qu(["openClass"]),wo("initialData"),Zs("typeaheadBehaviours",[Hg,Df,c1,Fg,Lg,$y]),cr("previewing",function(){return Po(!0)})].concat(Ey()).concat(ux())),d1=rt([$f({schema:[Yu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){!function(t,n,o){if(t.selectsOver){var e=Df.getValue(n),r=t.getDisplayText(e),i=Df.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?vt.some(function(){var e;ck(0,n,o),e=r.length,sk(n,function(t,n){return t.setSelectionRange(e,n.length)})}):vt.none()}return vt.none()}(o.model,t,e).fold(function(){return hd.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&ck(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOptional().map(function(t){return br(t,l1(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&ck(o.model,t,n)})}}}})]),m1=Cl({name:"Typeahead",configFields:f1(),partFields:d1(),factory:function(r,t,n,i){function e(t,n,e){r.previewing.set(!1);var o=$y.getCoupled(t,"sandbox");bf.isOpen(o)?cd.getCurrent(o).each(function(t){hd.getHighlighted(t).fold(function(){e(t)},function(){wr(o,t.element,"keydown",n)})}):nx(r,u(t),t,o,i,function(t){cd.getCurrent(t).each(e)},Hy.HighlightFirst).get(st)}function u(e){return function(t){return t.map(function(t){var n=z(Z(t.menus),function(t){return F(t.items,function(t){return"item"===t.type})});return Df.getState(e).update(M(n,function(t){return t.data})),t})}}var o=ry(r),a=[Hg.config({}),Df.config({onSetValue:r.onSetValue,store:lt({mode:"dataset",getDataKey:function(t){return Qr(t.element)},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){Zr(t.element,r.model.getDisplayText(n))}},r.initialData.map(function(t){return sr("initialValue",t)}).getOr({}))}),c1.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e,o=$y.getCoupled(t,"sandbox");Hg.isFocused(t)&&Qr(t.element).length>=r.minChars&&(e=cd.getCurrent(o).bind(function(t){return hd.getHighlighted(t).map(Df.getValue)}),r.previewing.set(!0),nx(r,u(t),t,o,i,function(t){cd.getCurrent(o).each(function(t){e.fold(function(){r.model.selectsOver&&hd.highlightFirst(t)},function(n){hd.highlightBy(t,function(t){return Df.getValue(t).value===n.value}),hd.getHighlighted(t).orThunk(function(){return hd.highlightFirst(t),vt.none()})})})},Hy.HighlightFirst).get(st))},cancelEvent:zi()}),Fg.config({mode:"special",onDown:function(t,n){return e(t,n,hd.highlightFirst),vt.some(!0)},onEscape:function(t){var n=$y.getCoupled(t,"sandbox");return bf.isOpen(n)?(bf.close(n),vt.some(!0)):vt.none()},onUp:function(t,n){return e(t,n,hd.highlightLast),vt.some(!0)},onEnter:function(n){var t=$y.getCoupled(n,"sandbox"),e=bf.isOpen(t);if(e&&!r.previewing.get())return cd.getCurrent(t).bind(function(t){return hd.getHighlighted(t)}).map(function(t){return br(n,l1(),{item:t}),!0});var o=Df.getValue(n);return vr(n,zi()),r.onExecute(t,n,o),e&&bf.close(t),vt.some(!0)}}),Lg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),$y.config({others:{sandbox:function(t){return rx(r,t,{onOpen:function(){return Lg.on(t)},onClose:function(){return Lg.off(t)}})}}}),mm("typeaheadevents",[uu(function(t){ex(r,u(t),t,i,st,Hy.HighlightFirst).get(st)}),Cr(l1(),function(t,n){var e=$y.getCoupled(t,"sandbox");ck(r.model,t,n.event.item),vr(t,zi()),r.onItemExecute(t,e,n.event.item,Df.getValue(t)),bf.close(e),s1(t)})].concat(r.dismissOnBlur?[Cr(Mi(),function(t){var n=$y.getCoupled(t,"sandbox");_a(n.element).isNone()&&bf.close(n)})]:[]))];return{uid:r.uid,dom:iy(Yo(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:lt(lt({},o),nl(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),g1=function(i){return lt(lt({},i),{toCached:function(){return g1(i.toCached())},bindFuture:function(n){return g1(i.bind(function(t){return t.fold(function(t){return Gy(Ve.error(t))},function(t){return n(t)})}))},bindResult:function(n){return g1(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return g1(i.map(function(t){return t.map(n)}))},mapError:function(n){return g1(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return g1(jy(function(n){var e=!1,o=setTimeout(function(){e=!0,n(Ve.error(r()))},t);i.get(function(t){e||(clearTimeout(o),n(t))})}))}})},p1=function(t){return g1(jy(t))},h1={type:"separator"},v1=Ir("aria-invalid"),b1={bar:pk(function(t,n){return e=n.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:M(t.items,e.interpreter)};var e}),collection:pk(function(t,n){return u=t,a=n.shared.providers,c=u.label.map(function(t){return oy(t,a)}),s=e(function(t,n,e,o){n.stop(),a.isDisabled()||br(t,Sy,{name:u.name,value:o})}),l=[Cr(vi(),e(function(t,n,e){ka(e)})),Cr(Ci(),s),Cr(Vi(),s),Cr(bi(),e(function(t,n,e){Iu(t.element,"."+Mh).each(function(t){qr(t,Mh)}),Yr(e,Mh)})),Cr(yi(),e(function(t){Iu(t.element,"."+Mh).each(function(t){qr(t,Mh)})})),uu(e(function(t,n,e,o){br(t,Sy,{name:u.name,value:o})}))],ey(c,my.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:h},behaviours:nc([gd.config({disabled:a.isDisabled,onDisabled:function(t){o(t,function(t){Yr(t,"tox-collection__item--state-disabled"),on(t,"aria-disabled",!0)})},onEnabled:function(t){o(t,function(t){qr(t,"tox-collection__item--state-disabled"),sn(t,"aria-disabled")})}}),pv(),Rg.config({}),Df.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){var n=o,e=M(t,function(t){var n=dp.translate(t.text),e=1===u.columns?'<div class="tox-collection__item-label">'+n+"</div>":"",o='<div class="tox-collection__item-icon">'+t.icon+"</div>",r={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,function(t){return r[t]});return'<div class="tox-collection__item'+(a.isDisabled()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+hy.encodeAllRaw(t.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),r=M("auto"!==u.columns&&1<u.columns?p(e,u.columns):[e],function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"});Ar(n.element,r.join("")),"auto"===u.columns&&ah(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;Fg.setGridSize(o,n,e)}),vr(o,Ty)}}),py.config({}),Fg.config(1===(i=u.columns)?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===i?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:"."+Ch}}),mm("collection-events",l)]),eventOrder:((r={})[Ii()]=["disabling","alloy.base.behaviour","collection-events"],r)}),["tox-form__group--collection"],[]);function e(o){return function(n,e){Ru(e.event.target,"[data-collection-item-value]").each(function(t){o(n,e,t,un(t,"data-collection-item-value"))})}}function o(t,n){return M(vs(t.element,".tox-collection__item"),n)}var u,a,r,i,c,s,l}),alertbanner:pk(function(t,n){return o=n.shared.providers,ly.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+(e=t).level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[fp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Um(e.icon,o.icons),attributes:{title:o.translate(e.iconTooltip)}},action:function(t){br(t,Sy,{name:"alert-banner",value:e.url})},buttonBehaviours:nc([jm()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:pk(function(t,n){return o=n.shared.providers,uk({name:(e=t).name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:e.disabled,classname:"tox-textfield",validation:vt.none(),maximized:e.maximized},o);var e,o}),textarea:pk(function(t,n){return o=n.shared.providers,uk({name:(e=t).name,multiline:!0,label:e.label,inputMode:vt.none(),placeholder:e.placeholder,flex:!0,disabled:e.disabled,classname:"tox-textarea",validation:vt.none(),maximized:e.maximized},o);var e,o}),label:pk(function(t,n){return r={dom:{tag:"label",innerHtml:(o=n.shared).providers.translate((e=t).label),classes:["tox-label"]}},i=M(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:nc([aw(),Rg.config({}),cw(vt.none()),Fg.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(ZC=function(t,n){return e=t,o=n.shared.providers,u=bS&&e.sandboxed,a=lt(lt({},e.label.map(function(t){return{title:t}}).getOr({})),u?{sandbox:"allow-scripts allow-same-origin"}:{}),r=u,i=Po(""),c={getValue:function(t){return i.get()},setValue:function(t,n){var e;r?on(t.element,"srcdoc",n):(on(t.element,"src","javascript:''"),(e=t.element.dom.contentWindow.document).open(),e.write(n),e.close()),i.set(n)}},ey(e.label.map(function(t){return oy(t,o)}),my.parts.field({factory:{sketch:function(t){return d0({uid:t.uid,dom:{tag:"iframe",attributes:a},behaviours:nc([py.config({}),Hg.config({}),gS(vt.none(),c.getValue,c.setValue)])})}}}),["tox-form__group--stretched"],[]);var e,o,r,i,u,a,c},function(t,n,e){var o=Yo(n,{source:"dynamic"});return pk(ZC)(t,o,e)}),button:pk(function(t,n){return e=t,o=n.shared.providers,r=$0(e.name,"custom"),ey(vt.none(),my.parts.field(lt({factory:fp},K0(e,vt.some(r),o,[pS(""),aw()]))),[],[]);var e,o,r}),checkbox:pk(function(t,n){return r=t,i=n.shared.providers,u=Df.config({store:{mode:"manual",getValue:function(t){return t.element.dom.checked},setValue:function(t,n){t.element.dom.checked=n}}}),a=my.parts.field({factory:{sketch:h},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:nc([aw(),gd.config({disabled:function(){return r.disabled||i.isDisabled()}}),py.config({}),Hg.config({}),u,Fg.config({mode:"special",onEnter:e,onSpace:e,stopSpaceKeyup:!0}),mm("checkbox-events",[Cr(ki(),function(t,n){br(t,yy,{name:r.name})})])])}),c=my.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:i.translate(r.label)},behaviours:nc([vw.config({})])}),s=zm({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]}),my.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[a,s.asSpec(),c],fieldBehaviours:nc([gd.config({disabled:function(){return r.disabled||i.isDisabled()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}}),pv()])});function e(t){return t.element.dom.click(),vt.some(!0)}function o(t){return Xm("checked"===t?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t]},i.icons)}var r,i,u,a,c,s}),colorinput:pk(function(t,n){return e=t,r=n.shared,i=n.colorinput,c=my.parts.field({factory:Dy,inputClasses:["tox-textfield"],onSetValue:function(t){return Ky.run(t).get(st)},inputBehaviours:nc([gd.config({disabled:r.providers.isDisabled}),pv(),py.config({}),Ky.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return Yt(t.element)},notify:{onValid:function(t){var n=Df.getValue(t);br(t,bw,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=Df.getValue(t);if(0===n.length)return Gy(Ve.value(!0));var e=At.fromTag("span");dn(e,"background-color",n);var o=hn(e,"background-color").fold(function(){return Ve.error("blah")},function(t){return Ve.value(n)});return Gy(o)}}})]),selectOnFocus:!1}),s=e.label.map(function(t){return oy(t,r.providers)}),l=zm((u={dom:{tag:"span",attributes:{"aria-label":r.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Ya,Xa,$a]},onLtr:function(){return[Xa,Ya,$a]}},components:[],fetch:wb(i.getColors(),i.hasCustomColors()),columns:i.getColorCols(),presets:"color",onItemAction:function(t,e){l.getOpt(t).each(function(n){"custom"===e?i.colorPicker(function(t){t.fold(function(){return vr(n,xw)},function(t){o(n,t),hb(t)})},"#ffffff"):o(n,"remove"===e?"":e)})}},hw.sketch({dom:u.dom,components:u.components,toggleClass:"mce-active",dropdownBehaviours:nc([$v((a=r).providers.isDisabled),pv(),vw.config({}),py.config({})]),layouts:u.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:a.getSink,fetch:function(n){return jy(function(t){return u.fetch(t)}).map(function(t){return vt.from(Xb(Yo(Ob(Ir("menu-value"),t,function(t){u.onItemAction(n,t)},u.columns,u.presets,Sh.CLOSE_ON_EXECUTE,T,a.providers),{movement:_b(u.columns,u.presets)})))})},parts:{menu:Zp(0,0,u.presets)}}))),my.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:s.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[c,l.asSpec()]}]),fieldBehaviours:nc([mm("form-field-events",[Cr(bw,function(t,n){l.getOpt(t).each(function(t){dn(t.element,"background-color",n.event.color)}),br(t,yy,{name:e.name})}),Cr(yw,function(n,e){my.getField(n).each(function(t){Df.setValue(t,e.event.value),cd.getCurrent(n).each(Hg.focus)})}),Cr(xw,function(n,t){my.getField(n).each(function(t){cd.getCurrent(n).each(Hg.focus)})})])])});function o(t,n){br(t,yw,{value:n})}var e,r,i,u,a,c,s,l}),colorpicker:pk(function(t){var r=zm(a0(c0,function(t){return"tox-"+t}).sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){br(t,Sy,{name:"hex-valid",value:!0})},onInvalidHex:function(t){br(t,Sy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:nc([Df.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return cd.getCurrent(n).bind(function(t){return Df.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);cd.getCurrent(o).fold(function(){console.log("Can not find form")},function(t){Df.setValue(t,{hex:vt.from(e[1]).getOr("")}),eS.getField(t,"hex").each(function(t){vr(t,Si())})})}}}),aw()])}}),dropzone:pk(function(t,n){return l0(t,n.shared.providers)}),grid:pk(function(t,n){return e=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+t.columns+"col"]},components:M(t.items,e.interpreter)};var e}),listbox:pk(function(t,n){return e=t,r=(o=n).shared.providers,i=Y(e.items).filter(rk),u=e.label.map(function(t){return oy(t,r)}),a={dom:{tag:"div",classes:["tox-listboxfield"]},components:[my.parts.field({dom:{},factory:{sketch:function(t){return L0({uid:t.uid,text:i.map(function(t){return t.text}),icon:vt.none(),tooltip:e.label,role:vt.none(),fetch:function(t,n){n(U0(o1(t,e.name,e.items,Df.getValue(t)),Sh.CLOSE_ON_EXECUTE,o,!1))},onSetup:rt(st),getApi:rt({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[py.config({}),Df.config({store:{mode:"manual",initialValue:i.map(function(t){return t.value}).getOr(""),getValue:function(t){return un(t.element,e1)},setValue:function(n,t){r1(e.items,t).each(function(t){on(n.element,e1,t.value),br(n,VS,{text:t.text})})}}})]},"tox-listbox",o.shared)}}})]},my.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ft([u.toArray(),[a]]),fieldBehaviours:nc([gd.config({disabled:rt(e.disabled),onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}})])});var e,o,r,i,u,a}),selectbox:pk(function(t,n){return e=t,o=n.shared.providers,r=M(e.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=e.label.map(function(t){return oy(t,o)}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:ft([[my.parts.field({dom:{},selectAttributes:{size:e.size},options:r,factory:i1,selectBehaviours:nc([gd.config({disabled:function(){return e.disabled||o.isDisabled()}}),py.config({}),mm("selectbox-change",[Cr(ki(),function(t,n){br(t,yy,{name:e.name})})])])})],(1<e.size?vt.none():vt.some(Xm("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},o.icons))).toArray()])},my.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:ft([i.toArray(),[u]]),fieldBehaviours:nc([gd.config({disabled:function(){return e.disabled||o.isDisabled()},onDisabled:function(t){my.getField(t).each(gd.disable)},onEnabled:function(t){my.getField(t).each(gd.enable)}}),pv()])});var e,o,r,i,u}),sizeinput:pk(function(t,n){return ow(t,n.shared.providers)}),urlinput:pk(function(t,n){return gk(t,n,n.urlinput)}),customeditor:pk(function(e){var o=gc(),n=zm({dom:{tag:e.tag}}),r=gc();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:nc([mm("custom-editor-events",[ou(function(t){n.getOpt(t).each(function(n){(Tt(e,"init")?e.init(n.element.dom):lS.load(e.scriptId,e.scriptUrl).then(function(t){return t(n.element.dom,e.settings)})).then(function(n){r.on(function(t){n.setValue(t)}),r.clear(),o.set(n)})})})]),Df.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){o.get().fold(function(){r.set(n)},function(t){return t.setValue(n)})}}}),aw()]),components:[n.asSpec()]}}),htmlpanel:pk(function(t){return"presentation"===t.presets?ly.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):ly.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:nc([py.config({}),Hg.config({})])})}),imagetools:pk(function(t,n){return ok(t,n.shared.providers)}),table:pk(function(t,n){return ik(t,n.shared.providers)}),panel:pk(function(t,n){return{dom:{tag:"div",classes:t.classes},components:M(t.items,n.shared.interpreter)}})},y1={field:function(t,n){return n}},x1=function(n,e,o){return tt(b1,e.type).fold(function(){return console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},w1={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},S1=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],k1=function(t){return R(t,function(t,n){if(Tt(n,"items")){var e=k1(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(Tt(n,"inline")||Tt(n,"block")||Tt(n,"selector")){var o="custom-"+(y(n.name)?n.name:n.title.toLowerCase());return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return lt(lt({},t),{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},C1=fS.trim,O1=wk("true"),_1=wk("false"),T1=function(t){var n=M(vs(At.fromDom(t),"h1,h2,h3,h4,h5,h6,a:not([href])"),function(t){return t.dom});return F(M(F(n,_k),Tk).concat(M(F(n,Ck),Ek)),Dk)},E1="tinymce-url-history",D1=od,B1=nd,M1=rt([Eo("shell",!1),mo("makeItem"),Eo("setupItem",st),Bf("listBehaviours",[Rg])]),A1=rt([Qf({name:"items",overrides:function(){return{behaviours:nc([Rg.config({})])}}})]),F1=Cl({name:rt("CustomList")(),configFields:M1(),partFields:A1(),factory:function(s,t,n,e){var o=s.shell?{behaviours:[Rg.config({})],components:[]}:{behaviours:[],components:t};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:nl(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){var t;t=a,(s.shell?vt.some(t):ml(t,s,"items")).fold(function(){throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(n){var t=Rg.contents(n),e=c.length,o=e-t.length,r=0<o?m(o,function(){return s.makeItem()}):[],i=t.slice(e);St(i,function(t){return Rg.remove(n,t)}),St(r,function(t){return Rg.append(n,t)});var u=Rg.contents(n);St(u,function(t,n){s.setupItem(a,t,c[n],n)})})}}}},apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),I1=rt([mo("dom"),Eo("shell",!0),Zs("toolbarBehaviours",[Rg])]),R1=rt([Qf({name:"groups",overrides:function(){return{behaviours:nc([Rg.config({})])}}})]),V1=Cl({name:"Toolbar",configFields:I1(),partFields:R1(),factory:function(o,t,n,e){var r=o.shell?{behaviours:[Rg.config({})],components:[]}:{behaviours:[],components:t};return{uid:o.uid,dom:o.dom,components:r.components,behaviours:nl(o.toolbarBehaviours,r.behaviours),apis:{setGroups:function(t,n){var e;e=t,(o.shell?vt.some(e):ml(e,o,"groups")).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){Rg.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),P1=rt([]),H1=Object.freeze({__proto__:null,setup:st,isDocked:T,getBehaviours:P1}),z1=Ho([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),N1=function(t,n,e){var o,r,i,u;t.getSystem().isConnected()&&(i=e,u=(r=n).lazyViewport(o=t),i.isDocked()&&$k(o,r,i,u),qk(o,u,i).each(function(t){t.fold(function(){return Kk(o,r,i)},function(t){return Jk(o,r,i,t)},function(t){$k(o,r,i,u,!0),Jk(o,r,i,t)})}))},L1=Object.freeze({__proto__:null,refresh:N1,reset:Qk,isDocked:function(t,n,e){return e.isDocked()},getModes:function(t,n,e){return e.getModes()},setModes:function(t,n,e,o){return e.setModes(o)}}),W1=Object.freeze({__proto__:null,events:function(o,r){return eu([Br(_i(),function(n,e){o.contextual.each(function(t){Kr(n.element,t.transitionClass)&&($r(n.element,[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(n)),e.stop()})}),Cr(Ui(),function(t,n){N1(t,o,r)}),Cr(ji(),function(t,n){Qk(t,o,r)})])}}),U1=xa({fields:[To("contextual",[po("fadeInClass"),po("fadeOutClass"),po("transitionClass"),vo("lazyContext"),Ju("onShow"),Ju("onShown"),Ju("onHide"),Ju("onHidden")]),Io("lazyViewport",Fe),Ro("modes",["top","bottom"],nr),Ju("onDocked"),Ju("onUndocked")],name:"docking",active:W1,apis:L1,state:Object.freeze({__proto__:null,init:function(t){var n=Po(!1),e=Po(!0),o=gc(),r=Po(t.modes);return wu({isDocked:n.get,setDocked:n.set,getInitialPos:o.get,setInitialPos:o.set,clearInitialPos:o.clear,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")}})}})}),j1=rt(Ir("toolbar-height-change")),G1={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},X1="tox-tinymce--toolbar-sticky-on",Y1="tox-tinymce--toolbar-sticky-off",q1=Object.freeze({__proto__:null,setup:function(t,n,e){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",function(){e().each(U1.reset)}),t.on("ResizeWindow ResizeEditor",function(){e().each(tC)}),t.on("SkinLoaded",function(){e().each(function(t){U1.isDocked(t)?U1.reset(t):U1.refresh(t)})}),t.on("FullscreenStateChanged",function(){e().each(U1.reset)})),t.on("AfterScrollIntoView",function(b){e().each(function(t){U1.refresh(t);var n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v=t.element;Pd(v)&&(n=b,r=(o=Ut(e=v)).dom.defaultView.innerHeight,i=Rn(o),a=Nn(u=At.fromDom(n.elm)),c=wn(u),l=(s=a.y)+c,f=Cn(e),d=wn(e),g=(m=f.top)+d,p=Math.abs(m-i.top)<2,h=Math.abs(g-(i.top+r))<2,p&&s<g?Vn(i.left,s-d,o):h&&m<l&&Vn(i.left,s-r+c+d,o))})}),t.on("PostRender",function(){eC(t,!1)})},isDocked:function(t){return t().map(U1.isDocked).getOr(!1)},getBehaviours:oC}),K1=$o([po("type"),go("items",eo([Qo([po("name"),xo("items",nr)]),nr]))].concat(Hp)),J1=[Co("text"),Co("tooltip"),Co("icon"),vo("fetch"),Io("onSetup",function(){return st})],$1=$o(H([po("type")],J1,!0)),Q1=$o([po("type"),Co("tooltip"),Co("icon"),Co("text"),Oo("select"),vo("fetch"),Io("onSetup",function(){return st}),Ao("presets","normal",["normal","color","listpreview"]),Eo("columns",1),vo("onAction"),vo("onItemAction")]),Z1=kl({factory:function(n,o){var t={focus:Fg.focusIn,setMenus:function(t,n){var e=M(n,function(n){return G0(rC({type:"menubutton",text:n.text,fetch:function(t){t(n.getItems())}}).mapError(function(t){return ur(t)}).getOrDie(),"tox-mbtn",o.backstage,vt.some("menuitem"))});Rg.set(t,e)}};return{uid:n.uid,dom:n.dom,components:[],behaviours:nc([Rg.config({}),mm("menubar-events",[ou(function(t){n.onSetup(t)}),Cr(vi(),function(e,t){Iu(e.element,".tox-mbtn--active").each(function(n){Ru(t.event.target,".tox-mbtn").each(function(t){Lt(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){hw.expand(t),hw.close(n),Hg.focus(t)})})})})}),Cr(Ki(),function(e,t){t.event.prevFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(n){t.event.newFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(t){hw.isOpen(n)&&(hw.expand(t),hw.close(n))})})})]),Fg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return n.onEscape(t),vt.some(!0)}}),py.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[mo("dom"),mo("uid"),mo("onEscape"),mo("backstage"),Eo("onSetup",st)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),tO=Object.freeze({__proto__:null,refresh:function(t,n,e){var o;e.isExpanded()&&(bn(t.element,uC(n)),o=aC(n,t.element),dn(t.element,uC(n),o))},grow:function(t,n,e){e.isExpanded()||mC(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&dC(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&fC(t,n,e)},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:gC,isShrinking:pC,isTransitioning:function(t,n,e){return gC(t,n)||pC(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?dC:mC)(t,n,e)},disableTransitions:cC}),nO=Object.freeze({__proto__:null,exhibit:function(t,n,e){return Lr(n.expanded?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:sr(n.dimension.property,"0px")})},events:function(e,o){return eu([Br(_i(),function(t,n){n.event.raw.propertyName===e.dimension.property&&(cC(t,e),o.isExpanded()&&bn(t.element,e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),eO=xa({fields:[mo("closedClass"),mo("openClass"),mo("shrinkingClass"),mo("growingClass"),wo("getAnimationRoot"),Ju("onShrunk"),Ju("onStartShrink"),Ju("onGrown"),Ju("onStartGrow"),Eo("expanded",!1),go("dimension",lo("property",{width:[ta("property","width"),ta("getDimension",function(t){return On(t)+"px"})],height:[ta("property","height"),ta("getDimension",function(t){return wn(t)+"px"})]}))],name:"sliding",active:nO,apis:tO,state:Object.freeze({__proto__:null,init:function(t){var n=Po(t.expanded);return wu({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:C(n.set,!1),setExpanded:C(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),oO="container",rO=[Zs("slotBehaviours",[])],iO=dt({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},zr),uO=lt(lt({},iO),{sketch:function(t){var e,n={slot:function(t,n){return e.push(t),cl(oO,hC(t),n)},record:rt(e=[])},o=t(n),r=M(n.record(),function(t){return Jf({name:t,pname:hC(t)})});return Sl(oO,rO,r,$C,o)}}),aO=$o([Co("icon"),Co("tooltip"),Io("onShow",st),Io("onHide",st),Io("onSetup",function(){return st})]),cO=Ir("FixSizeEvent"),sO=Ir("AutoSizeEvent"),lO=Object.freeze({__proto__:null,block:function(t,n,e,o){on(t.element,"aria-busy",!0);var r=n.getRoot(t).getOr(t),i=nc([Fg.config({mode:"special",onTab:function(){return vt.some(!0)},onShiftTab:function(){return vt.some(!0)}}),Hg.config({})]),u=o(r,i),a=r.getSystem().build(u);Rg.append(r,Eu(a)),a.hasConfigured(Fg)&&n.focus&&Fg.focusIn(a),e.isBlocked()||n.onBlock(t),e.blockWith(function(){return Rg.remove(r,a)})},unblock:function(t,n,e){sn(t.element,"aria-busy"),e.isBlocked()&&n.onUnblock(t),e.clear()}}),fO=xa({fields:[Io("getRoot",vt.none),Fo("focus",!0),Ju("onBlock"),Ju("onUnblock")],name:"blocking",apis:lO,state:Object.freeze({__proto__:null,init:function(){var n=dc();return wu({readState:n.isSet,blockWith:function(t){n.set({destroy:t})},clear:n.clear,isBlocked:n.isSet})}})}),dO=rt([Zs("splitToolbarBehaviours",[$y]),cr("builtGroups",function(){return Po([])})]),mO=rt([qu(["overflowToggledClass"]),Oo("getOverflowBounds"),mo("lazySink"),cr("overflowGroups",function(){return Po([])})].concat(dO())),gO=rt([Jf({factory:V1,schema:I1(),name:"primary"}),$f({schema:I1(),name:"overflow"}),$f({name:"overflow-button"}),$f({name:"overflow-group"})]),pO=rt(function(t,n){var e=t,o=Math.floor(n);dn(e,"max-width",Oe.max(e,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"])+"px")}),hO=rt([qu(["toggledClass"]),mo("lazySink"),vo("fetch"),Oo("getBounds"),To("fireDismissalEventInstead",[Eo("event",Yi())]),Wc()]),vO=rt([$f({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:nc([Lg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),$f({factory:V1,schema:I1(),name:"toolbar",overrides:function(n){return{toolbarBehaviours:nc([Fg.config({mode:"cyclic",onEscape:function(t){return ml(t,n,"button").each(Hg.focus),vt.none()}})])}}})]),bO=Cl({name:"FloatingToolbarButton",factory:function(u,t,a,n){return lt(lt({},fp.sketch(lt(lt({},n.button()),{action:function(t){EC(t,n)},buttonBehaviours:Mf({dump:n.button().buttonBehaviours},[$y.config({others:{toolbarSandbox:function(t){return o=t,e=a,r=u,{dom:{tag:"div",attributes:{id:(i=Vu()).id}},behaviours:nc([Fg.config({mode:"special",onEscape:function(t){return bf.close(t),vt.some(!0)}}),bf.config({onOpen:function(t,n){r.fetch().get(function(t){BC(o,n,r,e.layouts,t),i.link(o.element),Fg.focusIn(n)})},onClose:function(){Lg.off(o),Hg.focus(o),i.unlink(o.element)},isPartOf:function(t,n,e){return zu(n,e)||zu(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),ic.config({channels:lt(lt({},Xs(lt({isExtraPart:T},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),Ys({doReposition:function(){bf.getState($y.getCoupled(o,"toolbarSandbox")).each(function(t){DC(o,t,r,e.layouts)})}}))})])};var o,e,r,i}}})])}))),{apis:{setGroups:function(n,e){bf.getState($y.getCoupled(n,"toolbarSandbox")).each(function(t){BC(n,t,u,a.layouts,e)})},reposition:function(n){bf.getState($y.getCoupled(n,"toolbarSandbox")).each(function(t){DC(n,t,u,a.layouts)})},toggle:function(t){EC(t,n)},getToolbar:function(t){return bf.getState($y.getCoupled(t,"toolbarSandbox"))},isOpen:function(t){return bf.isOpen($y.getCoupled(t,"toolbarSandbox"))}}})},configFields:hO(),partFields:vO(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)},isOpen:function(t,n){return t.isOpen(n)}}}),yO=rt([mo("items"),qu(["itemSelector"]),Zs("tgroupBehaviours",[Fg])]),xO=rt([Zf({name:"items",unit:"item"})]),wO=Cl({name:"ToolbarGroup",configFields:yO(),partFields:xO(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:nl(t.tgroupBehaviours,[Fg.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),SO=Cl({name:"SplitFloatingToolbar",configFields:mO(),partFields:gO(),factory:function(e,t,n,o){var r=zm(bO.sketch({fetch:function(){return jy(function(t){t(MC(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[Ya,Xa]},onRtl:function(){return[Xa,Ya]},onBottomLtr:function(){return[Ka,qa]},onBottomRtl:function(){return[qa,Ka]}},getBounds:n.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:nl(e.splitToolbarBehaviours,[$y.config({others:{overflowGroup:function(){return wO.sketch(lt(lt({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,n){e.builtGroups.set(M(n,t.getSystem().build)),AC(t,r,e)},refresh:function(t){return AC(t,r,e)},toggle:function(t){r.getOpt(t).each(function(t){bO.toggle(t)})},isOpen:function(t){return r.getOpt(t).map(bO.isOpen).getOr(!1)},reposition:function(t){r.getOpt(t).each(function(t){bO.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind(bO.getToolbar)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),kO=rt([qu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Ju("onOpened"),Ju("onClosed")].concat(dO())),CO=rt([Jf({factory:V1,schema:I1(),name:"primary"}),Jf({factory:V1,schema:I1(),name:"overflow",overrides:function(n){return{toolbarBehaviours:nc([eO.config({dimension:{property:"height"},closedClass:n.markers.closedClass,openClass:n.markers.openClass,shrinkingClass:n.markers.shrinkingClass,growingClass:n.markers.growingClass,onShrunk:function(t){ml(t,n,"overflow-button").each(function(t){Lg.off(t),Hg.focus(t)}),n.onClosed(t)},onGrown:function(t){Fg.focusIn(t),n.onOpened(t)},onStartGrow:function(t){ml(t,n,"overflow-button").each(Lg.on)}}),Fg.config({mode:"acyclic",onEscape:function(t){return ml(t,n,"overflow-button").each(Hg.focus),vt.some(!0)}})])}}}),$f({name:"overflow-button",overrides:function(t){return{buttonBehaviours:nc([Lg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),$f({name:"overflow-group"})]),OO=Cl({name:"SplitSlidingToolbar",configFields:kO(),partFields:CO(),factory:function(o,t,n,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:nl(o.splitToolbarBehaviours,[$y.config({others:{overflowGroup:function(n){return wO.sketch(lt(lt({},e["overflow-group"]()),{items:[fp.sketch(lt(lt({},e["overflow-button"]()),{action:function(t){vr(n,r)}}))]}))}}}),mm("toolbar-toggle-events",[Cr(r,function(t){FC(t,o)})])]),apis:{setGroups:function(t,n){var e=M(n,t.getSystem().build);o.builtGroups.set(e),QC(t,o)},refresh:function(t){return QC(t,o)},toggle:function(t){return FC(t,o)},isOpen:function(t){return ml(t,o,"overflow").map(eO.hasGrown).getOr(!1)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)}}}),_O=B1.optional({factory:Z1,name:"menubar",schema:[mo("backstage")]}),TO=B1.optional({factory:{sketch:function(t){return F1.sketch({uid:t.uid,dom:t.dom,listBehaviours:nc([Fg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return HC({type:t.type,uid:Ir("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return t.onEscape(),vt.some(!0)}})},setupItem:function(t,n,e,o){V1.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[mo("dom"),mo("onEscape")]}),EO=B1.optional({factory:{sketch:function(t){return(t.type===bh.sliding?function(t){var n=OO.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=OO.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=PC(t);return OO.sketch(lt(lt({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([j1()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([j1()],{type:"closed"})}}))}:t.type===bh.floating?function(i){var t=PC(i),n=SO.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return SO.sketch(lt(lt({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element,n=Nn(t),e=Gt(t),o=Nn(e),r=Math.max(e.dom.scrollHeight,o.height);return Me(n.x+4,o.y,n.width-8,r)},parts:lt(lt({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[n],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}:HC)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),vt.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[mo("dom"),mo("onEscape"),mo("getSink")]}),DO=B1.optional({factory:{sketch:function(t){var n=t.editor,e=t.sticky?oC:P1;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:nc(e(n,t.sharedBackstage))}}},name:"header",schema:[mo("dom")]}),BO=B1.optional({name:"socket",schema:[mo("dom")]}),MO=B1.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:nc([py.config({}),Hg.config({}),eO.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){cd.getCurrent(t).each(uO.hideAllSlots),vr(t,sO)},onGrown:function(t){vr(t,sO)},onStartGrow:function(t){br(t,cO,{width:hn(t.element,"width").getOr("")})},onStartShrink:function(t){br(t,cO,{width:On(t.element)+"px"})}}),Rg.config({}),cd.config({find:function(t){return Y(Rg.contents(t))}})])}],behaviours:nc([cS(0),mm("sidebar-sliding-events",[Cr(cO,function(t,n){dn(t.element,"width",n.event.width)}),Cr(sO,function(t,n){bn(t.element,"width")})])])}}},name:"sidebar",schema:[mo("dom")]}),AO=B1.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:nc([Rg.config({}),fO.config({focus:!1}),cd.config({find:function(t){return Y(t.components())}})]),components:[]}}},name:"throbber",schema:[mo("dom")]}),FO=Cl({name:"OuterContainer",factory:function(e,t,n){return{uid:e.uid,dom:e.dom,components:t,apis:{getSocket:function(t){return D1.getPart(t,e,"socket")},setSidebar:function(t,n){D1.getPart(t,e,"sidebar").each(function(t){return bC(t,n)})},toggleSidebar:function(t,n){D1.getPart(t,e,"sidebar").each(function(t){var e=n;cd.getCurrent(t).each(function(n){cd.getCurrent(n).each(function(t){eO.hasGrown(n)?uO.isShowing(t,e)?eO.shrink(n):(uO.hideAllSlots(t),uO.showSlot(t,e)):(uO.hideAllSlots(t),uO.showSlot(t,e),eO.grow(n))})})})},whichSidebar:function(t){return D1.getPart(t,e,"sidebar").bind(yC).getOrNull()},getHeader:function(t){return D1.getPart(t,e,"header")},getToolbar:function(t){return D1.getPart(t,e,"toolbar")},setToolbar:function(t,n){D1.getPart(t,e,"toolbar").each(function(t){t.getApis().setGroups(t,n)})},setToolbars:function(t,n){D1.getPart(t,e,"multiple-toolbar").each(function(t){F1.setItems(t,n)})},refreshToolbar:function(t){D1.getPart(t,e,"toolbar").each(function(t){return t.getApis().refresh(t)})},toggleToolbarDrawer:function(t){D1.getPart(t,e,"toolbar").each(function(n){var t=n.getApis().toggle;null!=t?vt.some(function(t){return t(n)}(t)):vt.none()})},isToolbarDrawerToggled:function(t){return D1.getPart(t,e,"toolbar").bind(function(n){return vt.from(n.getApis().isOpen).map(function(t){return t(n)})}).getOr(!1)},getThrobber:function(t){return D1.getPart(t,e,"throbber")},focusToolbar:function(t){D1.getPart(t,e,"toolbar").orThunk(function(){return D1.getPart(t,e,"multiple-toolbar")}).each(function(t){Fg.focusIn(t)})},setMenubar:function(t,n){D1.getPart(t,e,"menubar").each(function(t){Z1.setMenus(t,n)})},focusMenubar:function(t){D1.getPart(t,e,"menubar").each(function(t){Z1.focus(t)})}},behaviours:e.behaviours}},configFields:[mo("dom"),mo("behaviours")],partFields:[DO,_O,EO,TO,BO,MO,AO],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=M(e,RC);t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=M(e,function(t){return M(t,RC)});t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},toggleToolbarDrawer:function(t,n){t.toggleToolbarDrawer(n)},isToolbarDrawerToggled:function(t,n){return t.isToolbarDrawerToggled(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),IO={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},RO=C(UC,!1),VO=C(UC,!0);function PO(t,n,e,o){var r;return{type:"basic",data:(r=t.getParam(n,e,"string"),M(o===t1.SemiColon?r.replace(/;$/,"").split(";"):r.split(" "),function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}}function HO(e){var t={type:"basic",data:v_};return{tooltip:"Align",text:vt.none(),icon:vt.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(t){return vt.none},onAction:function(n){return function(){return V(v_,function(t){return t.format===n.format}).each(function(t){return e.execCommand(t.command)})}},updateText:function(t){var n=V(v_,function(t){return e.formatter.match(t.format)}).fold(rt("left"),function(t){return t.title.toLowerCase()});br(t,PS,{icon:"align-"+n})},dataset:t,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}}function zO(t){return M(t.split(/\s*,\s*/),function(t){return t.replace(/^['"]+|['"]+$/g,"")})}function NO(r){function i(){function e(t){return t?zO(t)[0]:""}var t=r.queryCommandValue("FontName"),n=a.data,o=t?t.toLowerCase():"";return{matchOpt:V(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return ot(0===(t=o).indexOf("-apple-system")&&(n=zO(t.toLowerCase()),N(b_,function(t){return-1<n.indexOf(t.toLowerCase())})),{title:u,format:o});var t,n}),font:t}}var u="System Font",a=PO(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",t1.SemiColon);return{tooltip:"Fonts",text:vt.some(u),icon:vt.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return vt.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},updateText:function(t){var n=i(),e=n.matchOpt,o=n.font,r=e.fold(rt(o),function(t){return t.title});br(t,VS,{text:r})},dataset:a,shouldHide:!1,isInvalid:T}}function LO(e){function i(){var a=vt.none(),c=o.data,s=e.queryCommandValue("FontSize");if(s)for(var t=function(t){var n,e,o,r,i=(e=t,/[0-9.]+px$/.test(n=s)?(o=72*parseInt(n,10)/96,r=Math.pow(10,e||0),Math.round(o*r)/r+"pt"):tt(x_,n).getOr(n)),u=tt(y_,i).getOr("");a=V(c,function(t){return t.format===s||t.format===i||t.format===u})},n=3;a.isNone()&&0<=n;n--)t(n);return{matchOpt:a,size:s}}var t=rt(vt.none),o=PO(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",t1.Space);return{tooltip:"Font sizes",text:vt.some("12pt"),icon:vt.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,t.format)})}},updateText:function(t){var n=i(),e=n.matchOpt,o=n.size,r=e.fold(rt(o),function(t){return t.title});br(t,VS,{text:r})},dataset:o,shouldHide:!1,isInvalid:T}}function WO(t,n){var e=n(),o=M(e,function(t){return t.format});return vt.from(t.formatter.closest(o)).bind(function(n){return V(e,function(t){return t.format===n})}).orThunk(function(){return ot(t.formatter.match("p"),{title:"Paragraph",format:"p"})})}function UO(e){var o=PO(e,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",t1.SemiColon);return{tooltip:"Blocks",text:vt.some("Paragraph"),icon:vt.none(),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(n){return function(){var t=e.formatter.get(n);return vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(n))})}},onAction:XC(e),updateText:function(t){var n=WO(e,function(){return o.data}).fold(rt("Paragraph"),function(t){return t.title});br(t,VS,{text:n})},dataset:o,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}}function jO(r,t){return{tooltip:"Formats",text:vt.some("Paragraph"),icon:vt.none(),isSelectedFor:function(t){return function(){return r.formatter.match(t)}},getCurrentValue:vt.none,getPreviewFor:function(n){return function(){var t=r.formatter.get(n);return void 0!==t?vt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:r.dom.parseStyle(r.formatter.getCssText(n))}):vt.none()}},onAction:XC(r),updateText:function(t){var e=function(t){var n=t.items;return void 0!==n&&0<n.length?z(n,e):[{title:t.title,format:t.format}]},n=z(bk(r),e),o=WO(r,rt(n)).fold(rt("Paragraph"),function(t){return t.title});br(t,VS,{text:o})},shouldHide:r.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!r.formatter.canApply(t.format)},dataset:t}}function GO(n){return{isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)}}}function XO(n){return{setActive:function(t){Lg.set(n,t)},isActive:function(){return Lg.isOn(n)},isDisabled:function(){return gd.isDisabled(n)},setDisabled:function(t){return gd.set(n,t)}}}function YO(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})}function qO(n,e,t,o,r,i){var u;return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]),attributes:YO(t,i)},components:tb([n.map(function(t){return H0(t,i.icons)}),e.map(function(t){return N0(t,"tox-tbtn",i)})]),eventOrder:((u={})[mi()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:nc([Qv(i.isDisabled),pv(),mm("common-button-display-events",[Cr(mi(),function(t,n){n.event.prevent(),vr(t,T_)})])].concat(o.map(function(t){return k_.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return tb([t.icon.map(function(t){return H0(t,i.icons)}),t.text.map(function(t){return N0(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function KO(t,n,e){var o,r=Po(st),i=qO(t.icon,t.text,t.tooltip,vt.none(),vt.none(),e);return fp.sketch({dom:i.dom,components:i.components,eventOrder:RS,buttonBehaviours:nc([mm("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},uu(function(n,t){hv(o,n)(function(t){br(n,IS,{buttonApi:t}),o.onAction(t)})})),vv(n,r),bv(n,r)]),Qv(function(){return t.disabled||e.isDisabled()}),pv()].concat(n.toolbarButtonBehaviours))})}function JO(r,i){return function(t,n,e){var o=r(t).mapError(function(t){return ur(t)}).getOrDie();return i(o,n,e)}}function $O(e,t,o,r){var n,i=t.outerContainer,u=o.toolbar,a=o.buttons;f(u,y)?(n=u.map(function(t){var n={toolbar:t,buttons:a,allowToolbarGroups:o.allowToolbarGroups};return F_(e,n,{backstage:r},vt.none())}),FO.setToolbars(i,n)):FO.setToolbar(i,F_(e,o,{backstage:r},vt.none()))}function QO(t){return/^[0-9\.]+(|px)$/i.test(""+t)?vt.some(parseInt(""+t,10)):vt.none()}function ZO(t){return u(t)?t+"px":t}function t_(n,t,e){var o=t.filter(function(t){return n<t}),r=e.filter(function(t){return t<n});return o.or(r).getOr(n)}function n_(t){var n=lh(t),e=fh(t),o=mh(t);return QO(n).map(function(t){return t_(t,e,o)})}function e_(u,a,t,n,c){function s(){return x.get()&&!u.removed}function l(t){return y?t.fold(rt(0),function(t){return 1<t.components().length?wn(t.components()[1].element):0}):0}function f(){r.broadcastOn([xf()],{})}function o(t){var n,e,o,r,i;void 0===t&&(t=!1),s()&&(m||(n=p.getOrThunk(function(){var t=QO(pn(be(),"margin-left")).getOr(0);return On(be())-Cn(a).left+t}),dn(c.get().element,"max-width",n+"px")),y&&FO.refreshToolbar(d),m||(e=l(FO.getToolbar(d)),o=Ae(a),r=v()?Math.max(o.y-wn(c.get().element)+e,0):o.bottom,mn(d.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(o.x)+"px"})),g&&(i=c.get(),t?U1.reset(i):U1.refresh(i)),f())}function e(t){var n,e;void 0===t&&(t=!0),!m&&g&&s()&&(n=h.getDockingMode(),(e=function(t){switch(uv(u)){case xh.auto:var n=l(FO.getToolbar(d)),e=wn(t.element)-n,o=Ae(a);if(o.y>e)return"top";var r=Gt(a),i=Math.max(r.dom.scrollHeight,wn(r));return o.bottom<i-e||Fe().bottom<o.bottom-e?"bottom":"top";case xh.bottom:return"bottom";default:return xh.top,"top"}}(c.get()))!==n&&(function(t){var n=c.get();U1.setModes(n,[t]),h.setDockingMode(t);var e=v()?Oc.TopToBottom:Oc.BottomToTop;on(n.element,es,e)}(e),t&&o(!0)))}var r=t.uiMothership,d=t.outerContainer,i=ev.DOM,m=sv(u),g=dv(u),p=mh(u).or(n_(u)),h=n.shared.header,v=h.isPositionedAtTop,b=iv(u),y=b===bh.sliding||b===bh.floating,x=Po(!1);return{isVisible:s,isPositionedAtTop:v,show:function(){x.set(!0),dn(d.element,"display","flex"),i.addClass(u.getBody(),"mce-edit-focus"),bn(r.element,"display"),e(!1),o()},hide:function(){x.set(!1),t.outerContainer&&(dn(d.element,"display","none"),i.removeClass(u.getBody(),"mce-edit-focus")),dn(r.element,"display","none")},update:o,updateMode:e,repositionPopups:f}}function o_(t,n){var e=Ae(t);return{pos:n?e.y:e.bottom,bounds:e}}function r_(i,u){return Cr(IS,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return vr(e,Hi())},getValue:function(){return Df.getValue(e)}});u.onAction(r,n.event.buttonApi)})}function i_(t,n,e){return n.bottom-t.y>=(e=void 0===e?.01:e)&&t.bottom-n.y>=e}function u_(t){var n=function(t){var n=t.getBoundingClientRect();if(n.height<=0&&n.width<=0){var e=Qt(At.fromDom(t.startContainer),t.startOffset).element;return(Yn(e)?Yt(e):vt.some(e)).filter(Xn).map(function(t){return t.dom.getBoundingClientRect()}).getOr(n)}return n}(t.selection.getRng());if(t.inline){var e=Rn();return Me(e.left+n.left,e.top+n.top,n.width,n.height)}var o=Nn(At.fromDom(t.getBody()));return Me(o.x+n.left,o.y+n.top,n.width,n.height)}function a_(t,n,e,o){void 0===o&&(o=0);var r,i,u,a,c=Hn(window),s=Ae(At.fromDom(t.getContentAreaContainer())),l=ph(t)||hh(t)||rv(t),f=(r=s,i=c,u=o,{x:a=Math.max(r.x+u,i.x),width:Math.min(r.right-u,i.right)-a}),d=f.x,m=f.width;if(t.inline&&!l)return Me(d,c.y,m,c.height);var g=function(t,n,e,o,r,i){var u=At.fromDom(t.getContainer()),a=Iu(u,".tox-editor-header").getOr(u),c=Ae(a),s=c.y>=n.bottom,l=o&&!s;if(t.inline&&l)return{y:Math.max(c.bottom+i,e.y),bottom:e.bottom};if(t.inline&&!l)return{y:e.y,bottom:Math.min(c.y-i,e.bottom)};var f="line"===r?Ae(u):n;return l?{y:Math.max(c.bottom+i,e.y),bottom:Math.min(f.bottom-i,e.bottom)}:{y:Math.max(f.y+i,e.y),bottom:Math.min(c.y-i,e.bottom)}}(t,s,c,n.header.isPositionedAtTop(),e,o),p=g.y,h=g.bottom;return Me(d,p,m,h-p)}function c_(t){return"node"===t}function s_(t,r,n,i,e){var u=u_(t),o=i.lastElement().exists(function(t){return Lt(n,t)}),a=n,c=t.selection.getRng(),s=Qt(At.fromDom(c.startContainer),c.startOffset);return c.startContainer===c.endContainer&&c.startOffset===c.endOffset-1&&Lt(s.element,a)?o?Vm:cp:o?function(n,t){var e=hn(n,"position");dn(n,"position",t);var o=i_(u,Ae(r))&&!i.isReposition()?Hm:Vm;return e.each(function(t){return dn(n,"position",t)}),o}(r,i.getMode()):("fixed"===i.getMode()?e.y+Rn().top:e.y)+(wn(r)+12)<=u.y?cp:sp}function l_(n,t){var e=A(F(t,function(t){return t.predicate(n.dom)}),function(t){return"contexttoolbar"===t.type});return{contextToolbars:e.pass,contextForms:e.fail}}function f_(e,t){function o(t){return Lt(t,r)}var n,r=At.fromDom(t.getBody()),i=At.fromDom(t.selection.getNode());return o(n=i)||Wt(r,n)?function(t,n,e){var o=l_(t,n);if(0<o.contextForms.length)return vt.some({elem:t,toolbars:[o.contextForms[0]]});var r=l_(t,e);if(0<r.contextForms.length)return vt.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;function n(n){return d(t,function(t){return t.position===n})}function e(n){return F(t,function(t){return t.position===n})}var o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=M(e("selection"),function(t){return lt(lt({},t),{position:"node"})});return i.concat(u)}return e(o?"selection":"node")}return e("line")}(o.contextToolbars.concat(r.contextToolbars));return vt.some({elem:t,toolbars:i})}return vt.none()}(i,e.inNodeScope,e.inEditorScope).orThunk(function(){return a=e,(t=o)(n=i)?vt.none():dr(n,function(t){if(Xn(t)){var n=l_(t,a.inNodeScope),e=n.contextToolbars,o=n.contextForms,r=0<o.length?o:(u=e).length<=1?u:i("selection").orThunk(function(){return i("node")}).orThunk(function(){return i("line")}).map(function(t){return t.position}).fold(function(){return[]},function(n){return F(u,function(t){return t.position===n})});return 0<r.length?vt.some({elem:t,toolbars:r}):vt.none()}function i(n){return V(u,function(t){return t.position===n})}var u;return vt.none()},t);var t,n,a}):vt.none()}function d_(a,c){var s={},l=[],f=[],d={},m={},t=Ct(a);return St(t,function(t){var n,e,o,r,i,u=a[t];"contextform"===u.type?(o=t,i=co(ao("ContextForm",qp,r=u)),(s[o]=i).launch.map(function(t){d["form:"+o]=lt(lt({},r.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){c(i)}})}),("editor"===i.scope?f:l).push(i),m[o]=i):"contexttoolbar"===u.type&&(n=t,ao("ContextToolbar",Kp,e=u).each(function(t){("editor"===e.scope?f:l).push(t),m[n]=t}))}),{forms:s,inNodeScope:l,inEditorScope:f,lookupTable:m,formNavigators:d}}function m_(d,t,m,u){function a(){var t=y.get().getOr("node"),n=c_(t)?1:0;return a_(d,p,t,n)}function c(){return!(d.removed||h()&&g.isContextMenuOpen())}function s(){if(c()){var t=a(),n=mt(y.get(),"node")?(e=d,v.get().filter(ve).map(Nn).getOrThunk(function(){return u_(e)})):u_(d);return t.height<=0||!i_(n,t)}return 1;var e}function n(){v.clear(),b.clear(),y.clear(),up.hide(x)}function e(){var t;up.isOpen(x)&&(bn(t=x.element,"display"),s()?dn(t,"display","none"):(b.set(0),up.reposition(x)))}function l(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:nc([Fg.config({mode:"acyclic"}),mm("pop-dialog-wrap-events",[ou(function(t){d.shortcuts.add("ctrl+F9","focus statusbar",function(){return Fg.focusIn(t)})}),ru(function(t){d.shortcuts.remove("ctrl+F9")})])])}}function f(t,n){var e,o,r,i,c,s,u,a="node"===t?p.anchors.node(n):p.anchors.cursor(),l=(e=d,o=t,r=h(),i={lastElement:v.get,isReposition:function(){return mt(b.get(),0)},getMode:function(){return ff.getMode(m)}},"line"===o?{bubble:Sc(12,0,N_),layouts:{onLtr:function(){return[la]},onRtl:function(){return[fa]}},overrides:L_}:{bubble:Sc(0,12,N_,1/12),layouts:(c=e,s=i,u=o,r?{onLtr:function(t){return[$a,Xa,Ya,qa,Ka,Ja].concat(f(t))},onRtl:function(t){return[$a,Ya,Xa,Ka,qa,Ja].concat(f(t))}}:{onLtr:function(t){return[Ja,$a,qa,Xa,Ka,Ya].concat(f(t))},onRtl:function(t){return[Ja,$a,Ka,Ya,qa,Xa].concat(f(t))}}),overrides:L_});function f(t){return c_(u)?[(a=t,function(t,n,e,o,r){var i=s_(c,o,a,s,r),u=lt(lt({},t),{y:r.y,height:r.height});return lt(lt({},i(u,n,e,o,r)),{alwaysFit:!0})})]:[];var a}return Yo(a,l)}function o(t,n){var e,o,r,i;k.cancel(),c()&&(e=S(t),r=f(o=t[0].position,n),y.set(o),b.set(1),bn(i=x.element,"display"),mt(Et(n,v.get(),Lt),!0)||(qr(i,X_),ff.reset(m,x)),up.showWithinBounds(x,l(e),{anchor:r,transition:{classes:[X_],mode:"placement"}},function(){return vt.some(a())}),n.fold(v.clear,v.set),s()&&dn(i,"display","none"))}var r,i,g=u.backstage,p=g.shared,h=le().deviceType.isTouch,v=gc(),b=gc(),y=gc(),x=Tu((r={sink:m,onEscape:function(){return d.focus(),vt.some(!0)}},i=Po([]),up.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){i.set([]),up.getContent(t).each(function(t){bn(t.element,"visibility")}),qr(t.element,G_),bn(t.element,"width")},inlineBehaviours:nc([mm("context-toolbar-events",[Br(_i(),function(t,n){"width"===n.event.raw.propertyName&&(qr(t.element,G_),bn(t.element,"width"))}),Cr(j_,function(t,n){var e=t.element;bn(e,"width");var o=On(e);up.setContent(t,n.event.contents),Yr(e,G_);var r=On(e);dn(e,"width",o+"px"),up.getContent(t).each(function(t){n.event.focus.bind(function(t){return ka(t),_a(e)}).orThunk(function(){return Fg.focusIn(t),Oa(ge(e))})}),lp.setTimeout(function(){dn(t.element,"width",r+"px")},0)}),Cr(W_,function(n,t){up.getContent(n).each(function(t){i.set(i.get().concat([{bar:t,focus:Oa(ge(n.element))}]))}),br(n,j_,{contents:t.event.forwardContents,focus:vt.none()})}),Cr(U_,function(n,t){q(i.get()).each(function(t){i.set(i.get().slice(0,i.get().length-1)),br(n,j_,{contents:Eu(t.bar),focus:t.focus})})})]),Fg.config({mode:"special",onEscape:function(n){return q(i.get()).fold(function(){return r.onEscape()},function(t){return vr(n,U_),vt.some(!0)})}})]),lazySink:function(){return Ve.value(r.sink)}}))),w=Rt(function(){return d_(t,function(t){var n=S([t]);br(x,W_,{forwardContents:l(n)})})}),S=function(t){var n=d.ui.registry.getAll().buttons,e=w(),o=lt(lt({},n),e.formNavigators),r=iv(d)===bh.scrolling?bh.scrolling:bh.default,i=ft(M(t,function(t){return"contexttoolbar"===t.type?F_(d,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},u,vt.some(["form:"])):(n=p.providers,z_(t,n));var n}));return HC({type:r,uid:Ir("context-toolbar"),initGroups:i,onEscape:vt.none,cyclicKeying:!0,providers:p.providers})},k=vp(function(){d.hasFocus()&&!d.removed&&(Kr(x.element,X_)?k.throttle():f_(w(),d).fold(n,function(t){o(t.toolbars,vt.some(t.elem))}))},17);d.on("init",function(){d.on("remove",n),d.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",e),d.on("click keyup focus SetContent",k.throttle),d.on(H_,n),d.on("contexttoolbar-show",function(n){tt(w().lookupTable,n.toolbarKey).each(function(t){o([t],ot(n.target!==d,n.target)),up.getContent(x).each(Fg.focusIn)})}),d.on("focusout",function(t){lp.setEditorTimeout(d,function(){_a(m.element).isNone()&&_a(x.element).isNone()&&n()},0)}),d.on("SwitchMode",function(){d.mode.isReadOnly()&&n()}),d.on("AfterProgressState",function(t){t.state?n():d.hasFocus()&&k.throttle()}),d.on("NodeChange",function(t){_a(x.element).fold(k.throttle,st)})})}(n1=t1=t1||{})[n1.SemiColon=0]="SemiColon",n1[n1.Space=1]="Space";var g_,p_,h_,v_=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],b_=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],y_={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},x_={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},w_=Object.freeze({__proto__:null,events:function(r,i){function u(e,o){r.updateState.each(function(t){var n=t(e,o);i.set(n)}),r.renderComponents.each(function(t){var n=M(t(o,i.get()),e.getSystem().build);Rs(e,n)})}return eu([Cr(Fi(),function(t,n){var e,o=n;o.universal||(e=r.channel,wt(o.channels,e)&&u(t,o.data))}),ou(function(n,t){r.initialData.each(function(t){u(n,t)})})])}}),S_=Object.freeze({__proto__:null,getState:function(t,n,e){return e}}),k_=xa({fields:[mo("channel"),wo("renderComponents"),wo("updateState"),wo("initialData")],name:"reflecting",active:w_,apis:S_,state:Object.freeze({__proto__:null,init:function(){var t=Po(vt.none());return{readState:function(){return t.get().getOr("none")},get:t.get,set:t.set,clear:function(){return t.set(vt.none())}}}})}),C_=rt([mo("toggleClass"),mo("fetch"),Qu("onExecute"),Eo("getHotspot",vt.some),Eo("getAnchorOverrides",rt({})),Wc(),Qu("onItemExecute"),wo("lazySink"),mo("dom"),Ju("onOpen"),Zs("splitDropdownBehaviours",[$y,Fg,Hg]),Eo("matchWidth",!1),Eo("useMinWidth",!1),Eo("eventOrder",{}),wo("role")].concat(ux())),O_=rt([Jf({factory:fp,schema:[mo("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:nc([Hg.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(yr)},buttonBehaviours:nc([Lg.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),Jf({factory:fp,schema:[mo("dom")],name:"button",defaults:function(){return{buttonBehaviours:nc([Hg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),Qf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[mo("text")],name:"aria-descriptor"}),$f({schema:[Yu()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),Zy()]),__=Cl({name:"SplitDropdown",configFields:C_(),partFields:O_(),factory:function(o,t,n,e){function r(t){cd.getCurrent(t).each(function(t){hd.highlightFirst(t),Fg.focusIn(t)})}function i(t){ex(o,h,t,e,r,Hy.HighlightFirst).get(st)}function u(t){return yr(gl(t,o,"button")),vt.some(!0)}var a,c=lt(lt({},eu([ou(function(e,t){ml(e,o,"aria-descriptor").each(function(t){var n=Ir("aria");on(t.element,"id",n),on(e.element,"aria-describedby",n)})})])),Sm(vt.some(i))),s={repositionMenus:function(t){Lg.isOn(t)&&ix(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:s,eventOrder:lt(lt({},o.eventOrder),((a={})[Ii()]=["disabling","toggling","alloy.base.behaviour"],a)),events:c,behaviours:nl(o.splitDropdownBehaviours,[$y.config({others:{sandbox:function(t){var n=gl(t,o,"arrow");return rx(o,t,{onOpen:function(){Lg.on(n),Lg.on(t)},onClose:function(){Lg.off(n),Lg.off(t)}})}}}),Fg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(t){return i(t),vt.some(!0)}}),Hg.config({}),Lg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),T_=Ir("focus-button"),E_=function(t,n,e){return KO(t,{toolbarButtonBehaviours:[].concat(0<e.length?[mm("toolbarButtonWith",e)]:[]),getApi:GO,onSetup:t.onSetup},n)},D_=function(t,n,e){return Yo(KO(t,{toolbarButtonBehaviours:[Rg.config({}),Lg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[mm("toolbarToggleButtonWith",e)]:[]),getApi:XO,onSetup:t.onSetup},n))},B_=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],M_={button:JO(Op,function(t,n){return e=n.backstage.shared.providers,E_(t,e,[]);var e}),togglebutton:JO(_p,function(t,n){return e=n.backstage.shared.providers,D_(t,e,[]);var e}),menubutton:JO(rC,function(t,n){return G0(t,"tox-tbtn",n.backstage,vt.none())}),splitbutton:JO(function(t){return ao("SplitButton",Q1,t)},function(t,n){return o=t,r=n.backstage.shared,s=Ir("channel-update-split-dropdown-display"),l=Po(st),f={getApi:e,onSetup:o.onSetup},__.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:lt({"aria-pressed":!1},YO(o.tooltip,r.providers))},onExecute:function(t){o.onAction(e(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:nc([Yv(r.providers.isDisabled),pv(),mm("split-dropdown-events",[Cr(T_,Hg.focus),vv(f,l),bv(f,l)]),vw.config({})]),eventOrder:((i={})[Gi()]=["alloy.base.behaviour","split-dropdown-events"],i),toggleClass:"tox-tbtn--enabled",lazySink:r.getSink,fetch:(u=e,a=o,c=r.providers,function(n){return jy(function(t){return a.fetch(t)}).map(function(t){return vt.from(Xb(Yo(Ob(Ir("menu-value"),t,function(t){a.onItemAction(u(n),t)},a.columns,a.presets,Sh.CLOSE_ON_EXECUTE,a.select.getOr(T),c),{movement:_b(a.columns,a.presets),menuBehaviours:Kh("auto"!==a.columns?[]:[ou(function(o,t){ah(o,4,Jp(a.presets)).each(function(t){var n=t.numRows,e=t.numColumns;Fg.setGridSize(o,n,e)})})])})))})}),parts:{menu:Zp(0,o.columns,o.presets)},components:[__.parts.button(qO(o.icon,o.text,vt.none(),vt.some(s),vt.some([Lg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),r.providers)),__.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Um("chevron-down",r.providers.icons)},buttonBehaviours:nc([Yv(r.providers.isDisabled),pv(),jm()])}),__.parts["aria-descriptor"]({text:r.providers.translate("To open the popup, press Shift+Enter")})]});function e(e){return{isDisabled:function(){return gd.isDisabled(e)},setDisabled:function(t){return gd.set(e,t)},setIconFill:function(t,n){Iu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){on(t,"fill",n)})},setIconStroke:function(t,n){Iu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){on(t,"stroke",n)})},setActive:function(n){on(e.element,"aria-pressed",n),Iu(e.element,"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return Lg.set(t,n)})})},isActive:function(){return Iu(e.element,"span").exists(function(t){return e.getSystem().getByDom(t).exists(Lg.isOn)})}}}var o,r,i,u,a,c,s,l,f}),grouptoolbarbutton:JO(function(t){return ao("GroupToolbarButton",K1,t)},function(t,n,e){var o,r,i,u,a,c,s=e.ui.registry.getAll().buttons,l=((o={})[es]=n.backstage.shared.header.isPositionedAtTop()?Oc.TopToBottom:Oc.BottomToTop,o);if(iv(e)!==bh.floating)throw new Error("Toolbar groups are only supported when using floating toolbar mode");return i=n.backstage,u=function(t){return F_(e,{buttons:s,toolbar:t,allowToolbarGroups:!1},n,vt.none())},a=l,c=i.shared,bO.sketch({lazySink:c.getSink,fetch:function(){return jy(function(t){t(M(u(r.items),RC))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:qO((r=t).icon,r.text,r.tooltip,vt.none(),vt.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}})}),styleSelectButton:function(t,n){return KC(t,e=n.backstage,jO(t,lt({type:"advanced"},e.styleselect)));var e},fontsizeSelectButton:function(t,n){return KC(t,n.backstage,LO(t))},fontSelectButton:function(t,n){return KC(t,n.backstage,NO(t))},formatButton:function(t,n){return KC(t,n.backstage,UO(t))},alignMenuButton:function(t,n){return KC(t,n.backstage,HO(t))}},A_={styleselect:M_.styleSelectButton,fontsizeselect:M_.fontsizeSelectButton,fontselect:M_.fontSelectButton,formatselect:M_.formatButton,align:M_.alignMenuButton},F_=function(o,c,s,l){var e,t,n,r,i=(n=c.toolbar,r=c.buttons,!1===n?[]:void 0===n||!0===n?(e=r,t=M(B_,function(t){var n=F(t.items,function(t){return Tt(e,t)||Tt(A_,t)});return{name:t.name,items:n}}),F(t,function(t){return 0<t.items.length})):y(n)?M(n.split("|"),function(t){return{items:t.trim().split(" ")}}):f(n,function(t){return Tt(t,"name")&&Tt(t,"items")})?n:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[]));return F(M(i,function(t){var n=z(t.items,function(t){return 0===t.trim().length?[]:(r=o,n=c.buttons,i=t,u=c.allowToolbarGroups,a=s,e=l,tt(n,i.toLowerCase()).orThunk(function(){return e.bind(function(t){return K(t,function(t){return tt(n,t+i.toLowerCase())})})}).fold(function(){return tt(A_,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return vt.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(e=a,o=r,tt(M_,(n=t).type).fold(function(){return console.error("skipping button defined by",n),vt.none()},function(t){return vt.some(t(n,e,o))})):(console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),vt.none());var n,e,o}).toArray());var r,n,i,u,a,e});return{title:vt.from(o.translate(t.name)),items:n}}),function(t){return 0<t.items.length})},I_=le(),R_=I_.os.isiOS()&&I_.os.version.major<=12,V_=Object.freeze({__proto__:null,render:function(e,n,t,o,r){var i=Po(0),u=n.outerContainer;RO(e);var a=At.fromDom(r.targetNode),c=tn(ge(a)),s=n.mothership;pf(a,s,Bn),Ns(c,n.uiMothership),e.on("PostRender",function(){$O(e,n,t,o),i.set(e.getWin().innerWidth),FO.setMenubar(u,NC(e,t)),FO.setSidebar(u,t.sidebar),function(e,t){function n(){var t=c.get();t.left===u.innerWidth&&t.top===u.innerHeight||(c.set(ke(u.innerWidth,u.innerHeight)),Wv(e))}function o(){var t=e.getDoc().documentElement,n=s.get();n.left===t.offsetWidth&&n.top===t.offsetHeight||(s.set(ke(t.offsetWidth,t.offsetHeight)),Wv(e))}function r(t){return e.fire("ScrollContent",t)}var i=e.dom,u=e.getWin(),a=e.getDoc().documentElement,c=Po(ke(u.innerWidth,u.innerHeight)),s=Po(ke(a.offsetWidth,a.offsetHeight));i.bind(u,"resize",n),i.bind(u,"scroll",r);var l=hc(At.fromDom(e.getBody()),"load",o),f=t.uiMothership.element;e.on("hide",function(){dn(f,"display","none")}),e.on("show",function(){bn(f,"display")}),e.on("NodeChange",o),e.on("remove",function(){l.unbind(),i.unbind(u,"resize",n),i.unbind(u,"scroll",r),u=null})}(e,n)});var l,f,d,m,g=FO.getSocket(u).getOrDie("Could not find expected socket element");R_&&(mn(g.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"}),f=function(){e.fire("ScrollContent")},d=null,m=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];b(d)&&(d=setTimeout(function(){d=null,f.apply(null,t)},20))},l=pc(g.element,"scroll",m),e.on("remove",l.unbind)),gv(e,n),e.addCommand("ToggleSidebar",function(t,n){FO.toggleSidebar(u,n),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return FO.whichSidebar(u)});var p=iv(e);p!==bh.sliding&&p!==bh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=e.getWin().innerWidth;t!==i.get()&&(FO.refreshToolbar(n.outerContainer),i.set(t))});var h={enable:function(){mv(n,!1)},disable:function(){mv(n,!0)},isDisabled:function(){return gd.isDisabled(u)}};return{iframeContainer:g.element.dom,editorContainer:u.element.dom,api:h}}}),P_=Object.freeze({__proto__:null,render:function(n,e,o,r,t){var i=e.mothership,u=e.uiMothership,a=e.outerContainer,c=Po(null),s=At.fromDom(t.targetNode),l=e_(n,s,e,r,c),f=n.getParam("toolbar_persist",!1,"boolean");function d(){var t;c.get()?l.show():(c.set(FO.getHeader(a).getOrDie()),Ns(t=lv(n),i),Ns(t,u),$O(n,e,o,r),FO.setMenubar(a,NC(n,o)),l.show(),function(c,s,l,t){function n(t){var n=o_(s,l.isPositionedAtTop()),e=n.pos,o=n.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:e,bounds:o}),a&&Wv(c,t),l.isVisible()&&(i!==e?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))}var f=Po(o_(s,l.isPositionedAtTop()));t||(c.on("activate",l.show),c.on("deactivate",l.hide)),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){lp.requestAnimationFrame(function(){return n(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var e=mc();e.set(hc(At.fromDom(c.getBody()),"load",n)),c.on("remove",function(){e.clear()})}(n,s,l,f),n.nodeChanged())}function m(){return lp.setEditorTimeout(n,d,0)}VO(n),n.on("show",d),n.on("hide",l.hide),f||(n.on("focus",m),n.on("blur",l.hide)),n.on("init",function(){(n.hasFocus()||f)&&m()}),gv(n,e);var g={show:function(){l.show()},hide:function(){l.hide()},enable:function(){mv(e,!1)},disable:function(){mv(e,!0)},isDisabled:function(){return gd.isDisabled(a)}};return{editorContainer:a.element.dom,api:g}}}),H_="contexttoolbar-hide",z_=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=zm(Dy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:nc([Fg.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return yr(t),!0})},onLeft:function(t,n){return n.cut(),vt.none()},onRight:function(t,n){return n.cut(),vt.none()}})])})),c=(e=a,o=t.commands,r=n,i=M(o,function(t){return zm(("contextformtogglebutton"===t.type?function(t,n,e){var o=n.original;o.primary;var r=B(o,["primary"]),i=co(_p(lt(lt({},r),{type:"togglebutton",onAction:st})));return D_(i,e.backstage.shared.providers,[r_(t,n)])}:function(t,n,e){var o=n.original;o.primary;var r=B(o,["primary"]),i=co(Op(lt(lt({},r),{type:"button",onAction:st})));return E_(i,e.backstage.shared.providers,[r_(t,n)])})(e,t,{backstage:{shared:{providers:r}}}))}),{asSpecs:function(){return M(i,function(t){return t.asSpec()})},findPrimary:function(e){return K(o,function(t,n){return t.primary?vt.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(O(gd.isDisabled)):vt.none()})}});return[{title:vt.none(),items:[a.asSpec()]},{title:vt.none(),items:c.asSpecs()}]},N_={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},L_={maxHeightFunction:Vc(),maxWidthFunction:pO()},W_=Ir("forward-slide"),U_=Ir("backward-slide"),j_=Ir("change-slide-event"),G_="tox-pop--resizing",X_="tox-pop--transition",Y_={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},q_=(h_=["Infinity",(g_="[0-9]+")+"\\."+K_(g_)+K_(p_="[eE][+-]?[0-9]+"),"\\."+g_+K_(p_),g_+K_(p_)].join("|"),new RegExp("^([+-]?(?:"+h_+"))(.*)$"));function K_(t){return"(?:"+t+")?"}function J_(u,a){function n(){var t=a.getOptions(u),r=a.getCurrent(u).map(a.hash),i=gc();return M(t,function(o){return{type:"togglemenuitem",text:a.display(o),onSetup:function(n){function t(t){t&&(i.on(function(t){return t.setActive(!1)}),i.set(n)),n.setActive(t)}t(mt(r,a.hash(o)));var e=a.watcher(u,o,t);return function(){i.clear(),e()}},onAction:function(){return a.setCurrent(u,o)}}})}u.ui.registry.addMenuButton(a.name,{tooltip:a.text,icon:a.icon,fetch:function(t){return t(n())},onSetup:a.onToolbarSetup}),u.ui.registry.addNestedMenuItem(a.name,{type:"nestedmenuitem",text:a.text,getSubmenuItems:n,onSetup:a.onMenuSetup})}function $_(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}}function Q_(t){var n,e;!function(e){fS.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,n){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:jC(e,t.name),onAction:$_(e,t.name)})});for(var t=1;t<=6;t++){var n="h"+t;e.ui.registry.addToggleButton(n,{text:n.toUpperCase(),tooltip:"Heading "+t,onSetup:jC(e,n),onAction:$_(e,n)})}}(t),n=t,fS.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:YC(n,t.action)})}),e=t,fS.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:YC(e,t.action),onSetup:jC(e,t.name)})})}function Z_(n,e){return GC(n,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(t){t.setDisabled(n.mode.isReadOnly()||!n.undoManager[e]())})}function tT(t){var n;t.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:YC(t,"mceToggleVisualAid")}),t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:GC(n=t,"VisualAid",function(t){t.setActive(n.hasVisual)}),onAction:YC(t,"mceToggleVisualAid")})}function nT(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T=t;St([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){T.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:YC(T,t.cmd),onSetup:jC(T,t.name)})}),T.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:YC(T,"JustifyNone")}),Q_(O=t),_=O,fS.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){_.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:YC(_,t.action)})}),_.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:$_(_,"code")}),C=qC(0,d=l=n,HO(f=s=t)),f.ui.registry.addNestedMenuItem("align",{text:d.shared.providers.translate("Align"),getSubmenuItems:function(){return C.items.validateItems(C.getStyleItems())}}),p=qC(0,g=l,NO(m=s)),m.ui.registry.addNestedMenuItem("fontformats",{text:g.shared.providers.translate("Fonts"),getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}}),h=s,b=lt({type:"advanced"},(v=l).styleselect),y=qC(0,v,jO(h,b)),h.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return y.items.validateItems(y.getStyleItems())}}),w=qC(0,l,UO(x=s)),x.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return w.items.validateItems(w.getStyleItems())}}),k=qC(0,l,LO(S=s)),S.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return k.items.validateItems(k.getStyleItems())}}),(a=u=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:Z_(a,"hasUndo"),onAction:YC(a,"undo")}),a.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:Z_(a,"hasRedo"),onAction:YC(a,"redo")}),(c=u).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",disabled:!0,onSetup:Z_(c,"hasUndo"),onAction:YC(c,"undo")}),c.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",disabled:!0,onSetup:Z_(c,"hasRedo"),onAction:YC(c,"redo")}),function(t){var i;(i=t).addCommand("mceApplyTextcolor",function(t,n){var e,o=t,r=n;(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e=t;(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})});var n=Po(Fb),e=Po(Fb);kb(t,"forecolor","forecolor","Text color",n),kb(t,"backcolor","hilitecolor","Background color",e),Cb(t,"forecolor","forecolor","Text color"),Cb(t,"backcolor","hilitecolor","Background color")}(t),tT(t),(r=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:GC(i=r,"NodeChange",function(t){t.setDisabled(!i.queryCommandState("outdent"))}),onAction:YC(r,"outdent")}),r.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:YC(r,"indent")}),J_(e=t,NT),o=e,vt.from(o.getParam("content_langs",void 0,"array")).map(function(t){return{name:"language",text:"Language",icon:"language",getOptions:rt(t),hash:function(t){return E(t.customCode)?t.code:t.code+"/"+t.customCode},display:function(t){return t.title},watcher:function(t,n,e){return t.formatter.formatChanged("lang",e,!1,{value:n.code,customValue:n.customCode}).unbind},getCurrent:function(t){return mr(At.fromDom(t.selection.getNode()),function(t){return vt.some(t).filter(Xn).bind(function(n){return an(n,"lang").map(function(t){return{code:t,customCode:an(n,"data-mce-lang").getOrUndefined(),title:""}})})})},setCurrent:function(t,n){return t.execCommand("Lang",!1,n)},onToolbarSetup:function(t){var n=mc();return t.setActive(o.formatter.match("lang",{},void 0,!0)),n.set(o.formatter.formatChanged("lang",t.setActive,!0)),n.clear}}}).each(function(t){return J_(e,t)})}function eT(t,n){return{type:"makeshift",x:t,y:n}}function oT(t){return"longpress"===t.type||0===t.type.indexOf("touch")}function rT(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?function(t){if(oT(t)){var n=t.touches[0];return eT(n.pageX,n.pageY)}return eT(t.pageX,t.pageY)}(n):(e=t.getContentAreaContainer(),o=function(t){if(oT(t)){var n=t.touches[0];return eT(n.clientX,n.clientY)}return eT(t.clientX,t.clientY)}(n),r=ev.DOM.getPos(e),i=r.x,u=r.y,eT(o.x+i,o.y+u)):LT(t);var e,o,r,i,u}function iT(t,n,e){switch(e){case"node":return{type:"node",node:vt.some(At.fromDom((o=t).selection.getNode())),root:At.fromDom(o.getBody())};case"point":return rT(t,n);case"selection":return LT(t)}var o}function uT(f,d,m,g,p,h){function t(){var n,e,t,o,r,i,u,a,c,s,l=m();t=l,o=g,r=p,u=!(y||v||b&&x),c=iT(n=f,e=d,a=i=h),s=lt({bubble:Sc(0,"point"===a?12:0,UT),layouts:WT,overrides:{maxWidthFunction:pO(),maxHeightFunction:Vc()}},c),U0(t,Sh.CLOSE_ON_EXECUTE,o,!0).map(function(t){e.preventDefault(),up.showMenuWithinBounds(r,{anchor:s},{menu:{markers:Qp("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return vt.some(a_(n,o.shared,"node"===i?"node":"selection"))}),n.fire(H_)})}var n,e=le(),v=e.os.isiOS(),b=e.os.isOSX(),y=e.os.isAndroid(),x=e.deviceType.isTouch();(b||v)&&"node"!==h?(n=function(){(function(t){function n(){lp.setEditorTimeout(t,function(){t.selection.setRng(e)},10),i()}var e=t.selection.getRng();function o(t){t.preventDefault(),t.stopImmediatePropagation()}function r(){return i()}t.once("touchend",n),t.on("mousedown",o,!0),t.once("longpresscancel",r);var i=function(){t.off("touchend",n),t.off("longpresscancel",r),t.off("mousedown",o)}})(f),t()},function(t,n){var e=t.selection;if(!(e.isCollapsed()||n.touches.length<1)){var o=n.touches[0],r=e.getRng();return xs(t.getWin(),ms.domRange(r)).exists(function(t){return t.left<=o.clientX&&t.right>=o.clientX&&t.top<=o.clientY&&t.bottom>=o.clientY})}}(f,d)?n():(f.once("selectionchange",n),f.once("touchend",function(){return f.off("selectionchange",n)}))):t()}function aT(t){return"string"==typeof t?t.split(/[ ,]/):t}function cT(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")}function sT(t){return y(t)?"|"===t:"separator"===t.type}function lT(t,n){if(0===n.length)return t;var e=q(t).filter(function(t){return!sT(t)}).fold(function(){return[]},function(t){return[jT]});return t.concat(e).concat(n).concat([jT])}function fT(t,n){return"longpress"!==n.type&&(2!==n.button||n.target===t.getBody()&&""===n.pointerType)}function dT(t,n){return fT(t,n)?t.selection.getStart(!0):n.target}function mT(s,t,n){function e(t){return up.hide(i)}function o(c){var t;cT(s)&&c.preventDefault(),c.ctrlKey&&!cT(s)||!1===s.getParam("contextmenu")||(t=function(t,n){var e=t.getParam("contextmenu_avoid_overlap","","string"),o=fT(t,n)?"selection":"point";if(at(e)){var r=dT(t,n);return Yb(At.fromDom(r),e)?"node":o}return o}(s,c),(r()?uT:function(t,n,e,o,r,i){var u=e(),a=iT(t,n,i);U0(u,Sh.CLOSE_ON_EXECUTE,o,!1).map(function(t){n.preventDefault(),up.showMenuAt(r,{anchor:a},{menu:{markers:Qp("normal")},data:t})})})(s,c,function(){var t,n,e,o=dT(s,c),r=s.ui.registry.getAll(),i=(e=(n=s).ui.registry.getAll().contextMenus,vt.from(n.getParam("contextmenu")).map(aT).getOrThunk(function(){return F(aT("link linkchecker image imagetools table spellchecker configurepermanentpen"),function(t){return Tt(e,t)})})),u=r.contextMenus,a=o;return 0<(t=R(i,function(o,t){return tt(u,t.toLowerCase()).map(function(t){var n=t.update(a);if(y(n))return lT(o,n.split(" "));if(0<n.length){var e=M(n,GT);return lT(o,e)}return o}).getOrThunk(function(){return o.concat([t])})},[])).length&&sT(t[t.length-1])&&t.pop(),t},n,i,t))}var r=le().deviceType.isTouch,i=Tu(up.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return s.focus()},onShow:function(){return n.setContextMenuState(!0)},onHide:function(){return n.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:nc([mm("dismissContextMenu",[Cr(Yi(),function(t,n){bf.close(t),s.focus()})])])}));s.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(r()?"":" ResizeWindow");s.on(t,e),s.on("longpress contextmenu",o)})}function gT(n){return function(t){return t.translate(-n.left,-n.top)}}function pT(n){return function(t){return t.translate(n.left,n.top)}}function hT(e){return function(t,n){return R(e,function(t,n){return n(t)},ke(t,n))}}function vT(t,n,e){return t.fold(hT([pT(e),gT(n)]),hT([gT(n)]),hT([]))}function bT(t,n,e){return t.fold(hT([pT(e)]),hT([]),hT([pT(n)]))}function yT(t,n,e){return t.fold(hT([]),hT([gT(e)]),hT([pT(n),gT(e)]))}function xT(t,n,e){var o=t.fold(function(t,n){return{position:vt.some("absolute"),left:vt.some(t+"px"),top:vt.some(n+"px")}},function(t,n){return{position:vt.some("absolute"),left:vt.some(t-e.left+"px"),top:vt.some(n-e.top+"px")}},function(t,n){return{position:vt.some("fixed"),left:vt.some(t+"px"),top:vt.some(n+"px")}});return lt({right:vt.none(),bottom:vt.none()},o)}function wT(t,i,u,a){function n(o,r){return function(t,n){var e=o(i,u,a);return r(t.getOr(e.left),n.getOr(e.top))}}return t.fold(n(yT,YT),n(bT,qT),n(vT,KT))}function ST(t,n){var e=un(t,n);return E(e)?NaN:parseInt(e,10)}function kT(t,n,e,o,r,i){var u,a,c,s,l,f,d=(u=e,a=o,l=ST(s=t.element,(c=n).leftAttr),f=ST(s,c.topAttr),(isNaN(l)||isNaN(f)?vt.none():vt.some(ke(l,f))).fold(function(){return u},function(t){return KT(t.left+a.left,t.top+a.top)})),m=(n.mustSnap?JT:$T)(t,n,d,r,i),g=vT(d,r,i),p=n,h=g,v=t.element;return on(v,p.leftAttr,h.left+"px"),on(v,p.topAttr,h.top+"px"),m.fold(function(){return{coord:KT(g.left,g.top),extra:vt.none()}},function(t){return{coord:t.output,extra:t.extra}})}function CT(t,c,s,l){return K(t,function(t){var n,e,o=t.sensor,r=t.range.left,i=t.range.top,u=bT(c,n=s,e=l),a=bT(o,n,e);return Math.abs(u.left-a.left)<=r&&Math.abs(u.top-a.top)<=i?vt.some({output:wT(t.output,c,s,l),extra:t.extra}):vt.none()})}function OT(t,n){var e;t.getSystem().addToGui(n),Yt((e=n).element).filter(Xn).each(function(n){hn(n,"z-index").each(function(t){on(n,ZT,t)}),dn(n,"z-index",pn(e.element,"z-index"))})}function _T(t){Yt(t.element).filter(Xn).each(function(n){an(n,ZT).fold(function(){return bn(n,"z-index")},function(t){return dn(n,"z-index",t)}),sn(n,ZT)}),t.getSystem().removeFromGui(t)}function TT(t,n,e){return t.getSystem().build(ly.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))}function ET(t,n){return{bounds:t.getBounds(),height:Sn(n.element),width:_n(n.element)}}function DT(B,M,t,n,e){var o=t.update(n,e),A=t.getStartData().getOrThunk(function(){return ET(M,B)});o.each(function(t){var n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_=B,T=A,E=t,D=(n=M).getTarget(_.element);n.repositionTarget&&(e=Rn(Ut(_.element)),o=Wk(D),S=hn(w=D,"left"),k=hn(w,"top"),C=hn(w,"position"),O=function(t,n,e){return("fixed"===e?KT:YT)(parseInt(t,10),parseInt(n,10))},r=(S.isSome()&&k.isSome()&&C.isSome()?vt.some(O(S.getOrDie(),k.getOrDie(),C.getOrDie())):vt.none()).getOrThunk(function(){var t=Cn(w);return qT(t.left,t.top)}),i=_,u=n.snaps,a=r,c=e,s=o,l=E,f=T,m=u.fold(function(){var e,o,t=vT((e=l.left,o=l.top,a.fold(function(t,n){return YT(t+e,n+o)},function(t,n){return qT(t+e,n+o)},function(t,n){return KT(t+e,n+o)})),c,s);return KT(t.left,t.top)},function(n){var t=kT(i,n,a,l,c,s);return t.extra.each(function(t){n.onSensor(i,t)}),t.coord}),g=c,p=s,h=(d=f).bounds,v=bT(m,g,p),b=ja(v.left,h.x,h.x+h.width-d.width),y=ja(v.top,h.y,h.y+h.height-d.height),x=qT(b,y),gn(D,xT(m.fold(function(){var t=yT(x,g,p);return YT(t.left,t.top)},rt(x),function(){var t=vT(x,g,p);return KT(t.left,t.top)}),0,o))),n.onDrag(_,D,E)})}function BT(o,t,n,e){t.each(_T),n.snaps.each(function(t){var n,e;n=t,sn(e=o.element,n.leftAttr),sn(e,n.topAttr)});var r=n.getTarget(o.element);e.reset(),n.onDrop(o,r)}function MT(t){return function(n,e){function o(t){e.setStartData(ET(n,t))}return eu(H([Cr(Ui(),function(t){e.getStartData().each(function(){return o(t)})})],t(n,e,o),!0))}}function AT(a,c,s){return[Cr(mi(),function(n,t){var e,o,r,i,u;0===t.event.raw.button&&(t.stop(),r={drop:e=function(){return BT(n,vt.some(i),a,c)},delayDrop:(o=qb(e,200)).schedule,forceDrop:e,move:function(t){o.cancel(),DT(n,a,c,eE,t)}},i=TT(n,a.blockerClass,(u=r,eu([Cr(mi(),u.forceDrop),Cr(hi(),u.drop),Cr(gi(),function(t,n){u.move(n.event)}),Cr(pi(),u.delayDrop)]))),s(n),OT(n,i))})]}function FT(a,c,s){function l(t){BT(t,f.get(),a,c),f.clear()}var f=gc();return[Cr(si(),function(n,t){function e(){return l(n)}t.stop();var o,r,i,u=TT(n,a.blockerClass,(r=o=e,i=function(t){DT(n,a,c,rE,t)},eu([Cr(si(),r),Cr(fi(),o),Cr(di(),o),Cr(li(),function(t,n){i(n.event)})])));f.set(u),s(n),OT(n,u)}),Cr(li(),function(t,n){n.stop(),DT(t,a,c,rE,n.event)}),Cr(fi(),function(t,n){n.stop(),l(t)}),Cr(di(),l)]}function IT(t,r,i,u,n,e){return t.fold(function(){return aE.snap({sensor:qT(i-20,u-20),range:ke(n,e),output:qT(vt.some(i),vt.some(u)),extra:{td:r}})},function(t){var n=i-20,e=u-20,o=t.element.dom.getBoundingClientRect();return aE.snap({sensor:qT(n,e),range:ke(40,40),output:qT(vt.some(i-o.width/2),vt.some(u-o.height/2)),extra:{td:r}})})}function RT(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,n){var e=n.td,o=i.get(),r=e;o.exists(function(t){return Lt(t,r)})||(i.set(e),u(e))},mustSnap:!0}}function VT(t){return zm(fp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:nc([aE.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),vw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}function PT(a,e){function n(t){var n=Nn(t);return IT(g.getOpt(e),t,n.x,n.y,n.width,n.height)}function o(t){var n=Nn(t);return IT(p.getOpt(e),t,n.right,n.bottom,n.width,n.height)}function r(t,i,n,u){var e=n(i);aE.snapTo(t,e),function(t){var n=i.dom.getBoundingClientRect();bn(t.element,"display");var e=Xt(At.fromDom(a.getBody())).dom.innerHeight,o=n[u]<0,r=n[u]>e;(o||r)&&dn(t.element,"display","none")}(t)}function i(t){return r(h,t,n,"top")}function u(t){return r(v,t,o,"bottom")}var c=Po([]),s=Po([]),t=Po(!1),l=gc(),f=gc(),d=RT(function(){return M(c.get(),n)},l,function(n){f.get().each(function(t){a.fire("TableSelectorChange",{start:n,finish:t})})}),m=RT(function(){return M(s.get(),o)},f,function(n){l.get().each(function(t){a.fire("TableSelectorChange",{start:t,finish:n})})}),g=VT(d),p=VT(m),h=Tu(g.asSpec()),v=Tu(p.asSpec());le().deviceType.isTouch()&&(a.on("TableSelectionChange",function(n){t.get()||(Vs(e,h),Vs(e,v),t.set(!0)),l.set(n.start),f.set(n.finish),n.otherCells.each(function(t){c.set(t.upOrLeftCells),s.set(t.downOrRightCells),i(n.start),u(n.finish)})}),a.on("ResizeEditor ResizeWindow ScrollContent",function(){l.get().each(i),f.get().each(u)}),a.on("TableSelectionClear",function(){t.get()&&(Hs(h),Hs(v),t.set(!1)),l.clear(),f.clear()}))}var HT,zT,NT={name:"lineheight",text:"Line height",icon:"line-height",getOptions:function(t){return t.getParam("lineheight_formats","1 1.1 1.2 1.3 1.4 1.5 2","string").split(" ")},hash:function(t){return r=["fixed","relative","empty"],vt.from(q_.exec(t)).bind(function(t){var n=Number(t[1]),e=t[2],o=e;return d(r,function(t){return d(Y_[t],function(t){return o===t})})?vt.some({value:n,unit:e}):vt.none()}).map(function(t){return t.value+t.unit}).getOr(t);var r},display:h,watcher:function(t,n,e){return t.formatter.formatChanged("lineheight",e,!1,{value:n}).unbind},getCurrent:function(t){return vt.from(t.queryCommandValue("LineHeight"))},setCurrent:function(t,n){return t.execCommand("LineHeight",!1,n)}},LT=function(t){return{type:"selection",root:At.fromDom(t.selection.getNode())}},WT={onLtr:function(){return[$a,Xa,Ya,qa,Ka,Ja,cp,sp,Am,Bm,Mm,Dm]},onRtl:function(){return[$a,Ya,Xa,Ka,qa,Ja,cp,sp,Mm,Dm,Am,Bm]}},UT={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},jT={type:"separator"},GT=function(n){function t(t){return{text:t.text,icon:t.icon,disabled:t.disabled,shortcut:t.shortcut}}var e;if(y(n))return n;switch(n.type){case"separator":return jT;case"submenu":return lt(lt({type:"nestedmenuitem"},t(n)),{getSubmenuItems:function(){var t=n.getSubmenuItems();return y(t)?t:M(t,GT)}});default:return lt(lt({type:"menuitem"},t(n)),{onAction:(e=n.onAction,function(){return e()})})}},XT=Ho([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),YT=XT.offset,qT=XT.absolute,KT=XT.fixed,JT=function(t,n,l,f,d){var e=n.getSnapPoints(t);return CT(e,l,f,d).orThunk(function(){return R(e,function(n,e){var t,o,r,i,u,a,c,s=(t=e.sensor,e.range.left,e.range.top,i=bT(l,o=f,r=d),u=bT(t,o,r),a=Math.abs(i.left-u.left),c=Math.abs(i.top-u.top),ke(a,c));return n.deltas.fold(function(){return{deltas:vt.some(s),snap:vt.some(e)}},function(t){return(s.left+s.top)/2<=(t.left+t.top)/2?{deltas:vt.some(s),snap:vt.some(e)}:n})},{deltas:vt.none(),snap:vt.none()}).snap.map(function(t){return{output:wT(t.output,l,f,d),extra:t.extra}})})},$T=function(t,n,e,o,r){return CT(n.getSnapPoints(t),e,o,r)},QT=Object.freeze({__proto__:null,snapTo:function(t,n,e,o){var r,i,u,a=n.getTarget(t.element);n.repositionTarget&&(r=Rn(Ut(t.element)),i=Wk(a),gn(a,xT({coord:wT((u=o).output,u.output,r,i),extra:u.extra}.coord,0,i)))}}),ZT="data-initial-z-index",tE=To("snaps",[mo("getSnapPoints"),Ju("onSensor"),mo("leftAttr"),mo("topAttr"),Eo("lazyViewport",Fe),Eo("mustSnap",!1)]),nE=[Eo("useFixed",T),mo("blockerClass"),Eo("getTarget",h),Eo("onDrag",st),Eo("repositionTarget",!0),Eo("onDrop",st),Io("getBounds",Fe),tE],eE=Object.freeze({__proto__:null,getData:function(t){return vt.from(ke(t.x,t.y))},getDelta:function(t,n){return ke(n.left-t.left,n.top-t.top)}}),oE=H(H([],nE,!0),[ta("dragger",{handlers:MT(AT)})],!1),rE=Object.freeze({__proto__:null,getData:function(t){var n,e=t.raw.touches;return 1===e.length?(n=e[0],vt.some(ke(n.clientX,n.clientY))):vt.none()},getDelta:function(t,n){return ke(n.left-t.left,n.top-t.top)}}),iE=H(H([],nE,!0),[ta("dragger",{handlers:MT(FT)})],!1),uE=H(H([],nE,!0),[ta("dragger",{handlers:MT(function(t,n,e){return H(H([],AT(t,n,e),!0),FT(t,n,e),!0)})})],!1),aE=wa({branchKey:"mode",branches:Object.freeze({__proto__:null,mouse:oE,touch:iE,mouseOrTouch:uE}),name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:vt.from(t.extra)}}},state:Object.freeze({__proto__:null,init:function(){var i=vt.none(),n=vt.none(),t=rt({});return wu({readState:t,reset:function(){i=vt.none(),n=vt.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=vt.some(e),o;var n,e,o})},getStartData:function(){return n},setStartData:function(t){n=vt.some(t)}})}}),apis:QT});function cE(t,n,e){var o,r,i,u,a,c,s=At.fromDom(t.getContainer());J((o=t,r=n,i=e,u=wn(s),a=On(s),(c={}).height=t_(u+r.top,dh(o),gh(o)),i===HT.Both&&(c.width=t_(a+r.left,fh(o),mh(o))),c),function(t,n){return dn(s,n,ZO(t)),0}),t.fire("ResizeEditor")}function sE(t,n,e,o){return cE(t,ke(20*e,20*o),n),vt.some(!0)}function lE(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(v=[],t.getParam("elementpath",!0,"boolean")&&v.push((g=t,h=n,(p={}).delimiter||(p.delimiter="\xbb"),{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:nc([Fg.config({mode:"flow",selector:"div[role=button]"}),gd.config({disabled:h.isDisabled}),pv(),py.config({}),Rg.config({}),mm("elementPathEvents",[ou(function(r,t){g.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return Fg.focusIn(r)}),g.on("NodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return 1;if("bookmark"===t.getAttribute("data-mce-type"))return 1}}(o)){var r=g.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length?Rg.set(r,(n=M(e||[],function(n,t){return fp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){g.focus(),g.selection.select(n.element),g.nodeChanged()},buttonBehaviours:nc([$v(h.isDisabled),pv()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+p.delimiter+" "}},R(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]]))):Rg.set(r,[])})})])]),components:[]})),t.hasPlugin("wordcount")&&v.push((f=t,d=n,fp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:nc([$v(d.isDisabled),pv(),py.config({}),Rg.config({}),Df.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),mm("wordcount-events",[uu(function(t){var n=Df.getValue(t),e="words"===n.mode?"characters":"words";Df.setValue(t,{mode:e,count:n.count}),b(t,n.count,e)}),ou(function(e){f.on("wordCountUpdate",function(t){var n=Df.getValue(e).mode;Df.setValue(e,{mode:n,count:t.wordCount}),b(e,t.wordCount,n)})})])]),eventOrder:((m={})[Ii()]=["disabling","alloy.base.behaviour","wordcount-events"],m)}))),t.getParam("branding",!0,"boolean")&&v.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(l=dp.translate(["Powered by {0}","Tiny"]))+'">'+l+"</a>"}}),e=0<v.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:v}]:[],i=n,a=!(u=r=t).hasPlugin("autoresize"),o=(s=!1===(c=u.getParam("resize",a))?HT.None:"both"===c?HT.Both:HT.Vertical)===HT.None?vt.none():vt.some(Xm("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:i.translate("Resize")},behaviours:[aE.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){return cE(r,e,s)},blockerClass:"tox-blocker"}),Fg.config({mode:"special",onLeft:function(){return sE(r,s,-1,0)},onRight:function(){return sE(r,s,1,0)},onUp:function(){return sE(r,s,0,-1)},onDown:function(){return sE(r,s,0,1)}}),py.config({}),Hg.config({})]},i.icons)),e.concat(o.toArray()))};function b(t,n,e){return Rg.set(t,[ri(d.translate(["{0} "+e,n[e]]))])}}function fE(d){function m(){return i.bind(FO.getHeader)}function g(){return Ve.value(y)}function p(){return i.bind(function(t){return FO.getThrobber(t)}).getOrDie("Could not find throbber element")}var t,n,e,o,r=d.inline,h=r?P_:V_,v=dv(d)?q1:H1,i=vt.none(),u=le(),a=u.browser.isIE()?["tox-platform-ie"]:[],c=u.deviceType.isTouch()?["tox-platform-touch"]:[],s=av(d),l=lv(d),f=dp.isRtl()?{attributes:{dir:"rtl"}}:{},b={attributes:((t={})[es]=s?Oc.BottomToTop:Oc.TopToBottom,t)},y=Tu((n=Lt(be(),l)&&"grid"===pn(l,"display"),e={dom:lt({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(a).concat(c)},f),behaviours:nc([ff.config({useFixed:function(){return v.isDocked(m)}})])},o={dom:{styles:{width:document.body.clientWidth+"px"}},events:eu([Cr(ji(),function(){dn(J.element,"width",document.body.clientWidth+"px")})])},Yo(e,n?o:{}))),x=zm({dom:{tag:"div",classes:["tox-anchorbar"]}}),w=Nk(y,d,function(){return i.bind(function(t){return x.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),S=FO.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:w,onEscape:function(){d.focus()}}),k=iv(d),C=FO.parts.toolbar(lt({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:w.shared.providers,onEscape:function(){d.focus()},type:k,lazyToolbar:function(){return i.bind(function(t){return FO.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return m().getOrDie("Could not find header element")}},b)),O=FO.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:w.shared.providers,onEscape:function(){d.focus()},type:k}),_=FO.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),T=FO.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),E=FO.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:w}),D=d.getParam("statusbar",!0,"boolean")&&!r?vt.some(lE(d,w.shared.providers)):vt.none(),B={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[_,T]},M=rv(d),A=hh(d),F=ph(d),I=FO.parts.header({dom:lt({tag:"div",classes:["tox-editor-header"]},b),components:ft([F?[S]:[],M?[O]:A?[C]:[],sv(d)?[]:[x.asSpec()]]),sticky:dv(d),editor:d,sharedBackstage:w.shared}),R=ft([s?[]:[I],r?[]:[B],s?[I]:[]]),V=ft([[{dom:{tag:"div",classes:["tox-editor-container"]},components:R}],r?[]:D.toArray(),[E]]),P=fv(d),H=lt(lt({role:"application"},dp.isRtl()?{dir:"rtl"}:{}),P?{"aria-hidden":"true"}:{}),z=Tu(FO.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(r?["tox-tinymce-inline"]:[]).concat(s?["tox-tinymce--toolbar-bottom"]:[]).concat(c).concat(a),styles:lt({visibility:"hidden"},P?{opacity:"0",border:"0"}:{}),attributes:H},components:V,behaviours:nc([pv(),gd.config({disableClass:"tox-tinymce--disabled"}),Fg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),i=vt.some(z);d.shortcuts.add("alt+F9","focus menubar",function(){FO.focusMenubar(z)}),d.shortcuts.add("alt+F10","focus toolbar",function(){FO.focusToolbar(z)}),d.addCommand("ToggleToolbarDrawer",function(){FO.toggleToolbarDrawer(z)}),d.addQueryStateHandler("ToggleToolbarDrawer",function(){return FO.isToolbarDrawerToggled(z)});var N,L,W,U,j,G,X,Y,q,K=ny(z),J=ny(y);function $(){var t,n,e,o,r,i,u=ZO((o=sh(e=n=d),r=dh(e),i=gh(e),QO(o).map(function(t){return t_(t,r,i)}).getOr(sh(n)))),a=ZO(n_(t=d).getOr(lh(t)));return d.inline||(vn("div","width",a)&&dn(z.element,"width",a),vn("div","height",u)?dn(z.element,"height",u):dn(z.element,"height","200px")),u}return N=d,L=K,W=J,U=Sa(),j=pc(U,"touchstart",tt),G=pc(U,"touchmove",function(t){return Q(Li(),t)}),X=pc(U,"touchend",function(t){return Q(Wi(),t)}),Y=pc(U,"mousedown",tt),q=pc(U,"mouseup",function(t){0===t.raw.button&&Z(wf(),{target:t.target})}),N.on("PostRender",function(){N.on("click",nt),N.on("tap",nt),N.on("mouseup",et),N.on("mousedown",ot),N.on("ScrollWindow",rt),N.on("ResizeWindow",it),N.on("ResizeEditor",ut),N.on("AfterProgressState",at),N.on("DismissPopups",ct)}),N.on("remove",function(){N.off("click",nt),N.off("tap",nt),N.off("mouseup",et),N.off("mousedown",ot),N.off("ScrollWindow",rt),N.off("ResizeWindow",it),N.off("ResizeEditor",ut),N.off("AfterProgressState",at),N.off("DismissPopups",ct),Y.unbind(),j.unbind(),G.unbind(),X.unbind(),q.unbind()}),N.on("detach",function(){Ls(L),Ls(W),L.destroy(),W.destroy()}),{mothership:K,uiMothership:J,backstage:w,renderUI:function(){var o,r;v.setup(d,w.shared,m),nT(d,w),mT(d,g,w),r=(o=d).ui.registry.getAll().sidebars,St(Ct(r),function(n){function e(){return mt(vt.from(o.queryCommandValue("ToggleSidebar")),n)}var t=r[n];o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){function n(){return t.setActive(e())}return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})}),SC(d,p,w.shared),dt(d.getParam("toolbar_groups",{},"object"),function(t,n){d.ui.registry.addGroupToolbarButton(n,t)});var t,n=d.ui.registry.getAll(),e=n.buttons,i=n.menuItems,u=n.contextToolbars,a=n.sidebars,c=vh(d),s={menuItems:i,menus:(t=d.getParam("menu"))?dt(t,function(t){return lt(lt({},t),{items:t.items})}):{},menubar:d.getParam("menubar"),toolbar:c.getOrThunk(function(){return d.getParam("toolbar",!0)}),allowToolbarGroups:k===bh.floating,buttons:e,sidebar:a};m_(d,u,y,{backstage:w}),PT(d,y);var l=d.getElement(),f=$();return h.render(d,{mothership:K,uiMothership:J,outerContainer:z},s,w,{targetNode:l,height:f})},getUi:function(){return{channels:{broadcastAll:J.broadcast,broadcastOn:J.broadcastOn,register:st}}}};function Q(n,e){St([L,W],function(t){t.broadcastEvent(n,e)})}function Z(n,e){St([L,W],function(t){t.broadcastOn([n],e)})}function tt(t){return Z(yf(),{target:t.target})}function nt(t){return Z(yf(),{target:At.fromDom(t.target)})}function et(t){0===t.button&&Z(wf(),{target:At.fromDom(t.target)})}function ot(){St(N.editorManager.get(),function(t){N!==t&&t.fire("DismissPopups",{relatedTarget:N})})}function rt(t){return Q(Ui(),Ac(t))}function it(t){Z(xf(),{}),Q(ji(),Ac(t))}function ut(){return Z(xf(),{})}function at(t){t.state&&Z(yf(),{target:At.fromDom(N.getContainer())})}function ct(t){Z(yf(),{target:At.fromDom(t.relatedTarget.getContainer())})}}function dE(n){return ar("items","items",Ue(),no(io(function(t){return ao("Checking item of "+n,nB,t).fold(function(t){return Ve.error(ur(t))},function(t){return Ve.value(t)})})))}function mE(t){return y(t.type)&&y(t.name)}function gE(t){return{internalDialog:co(ao("dialog",sB,t)),dataValidator:(n=z(F(dB(t),mE),function(n){return vt.from(mB[n.type]).fold(function(){return[]},function(t){return[go(n.name,t)]})}),$o(n)),initialData:t.initialData};var n}function pE(t){var e=[],o={};return J(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?Ve.error(e):Ve.value(o)}function hE(t,n){dn(t,"height",n+"px"),le().browser.isIE()?bn(t,"flex-basis"):dn(t,"flex-basis",n+"px")}function vE(t,d,n){Fu(t,'[role="dialog"]').each(function(f){Iu(f,'[role="tablist"]').each(function(l){n.get().map(function(t){return dn(d,"height","0"),dn(d,"flex-basis","0"),Math.min(t,(e=d,o=l,r=Gt(n=f).dom,i="fixed"===pn(Fu(n,".tox-dialog-wrap").getOr(n),"position")?Math.max(r.clientHeight,window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight),u=wn(e),a=e.dom.offsetLeft>=o.dom.offsetLeft+On(o)?Math.max(wn(o),u):u,c=parseInt(pn(n,"margin-top"),10)||0,s=parseInt(pn(n,"margin-bottom"),10)||0,i-(wn(n)+c+s-a)));var n,e,o,r,i,u,a,c,s}).each(function(t){hE(d,t)})})})}function bE(t){return Iu(t,'[role="tabpanel"]')}function yE(t,e){function o(t){var n=pE(Df.getValue(t)).getOr({}),e=i.get(),o=Yo(e,n);i.set(o)}function r(t){var n=i.get();Df.setValue(t,n)}var u,a,i=Po({}),c=Po(null),n=M(t.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(t.title)},view:function(){return[eS.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:M(t.items,function(t){return hk(n,t,e)}),formBehaviours:nc([Fg.config({mode:"acyclic",useTabstopAt:O(p0)}),mm("TabView.form.events",[ou(r),ru(o)]),ic.config({channels:lr([{key:kB,value:{onReceive:o}},{key:CB,value:{onReceive:r}}])})])}})]}}}),s=(u=n,a=gc(),{extraEvents:[ou(function(t){var o=t.element;bE(o).each(function(n){var e;dn(n,"visibility","hidden"),t.getSystem().getByDom(n).toOptional().each(function(t){var o,r,i;Y(G((r=n,i=t,M(o=u,function(t,n){Rg.set(i,o[n].view());var e=r.dom.getBoundingClientRect();return Rg.set(i,[]),e.height})),function(t,n){return n<t?-1:t<n?1:0})).fold(a.clear,a.set)}),vE(o,n,a),bn(n,"visibility"),e=t,Y(u).each(function(t){return SB.showTab(e,t.value)}),lp.requestAnimationFrame(function(){vE(o,n,a)})})}),Cr(ji(),function(t){var n=t.element;bE(n).each(function(t){vE(n,t,a)})}),Cr(Ty,function(t,n){var r=t.element;bE(r).each(function(n){var t=Oa(ge(n));dn(n,"visibility","hidden");var e=hn(n,"height").map(function(t){return parseInt(t,10)});bn(n,"height"),bn(n,"flex-basis");var o=n.dom.getBoundingClientRect().height;e.forall(function(t){return t<o})?(a.set(o),vE(r,n,a)):e.each(function(t){hE(n,t)}),bn(n,"visibility"),t.each(ka)})})],selectFirst:!1});return SB.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=Df.getValue(n);br(t,_y,{name:o,oldName:c.get()}),c.set(o)},tabs:n,components:[SB.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[bB.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:nc([py.config({})])}),SB.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:s.selectFirst,tabSectionBehaviours:nc([mm("tabpanel",s.extraEvents),Fg.config({mode:"acyclic"}),cd.config({find:function(t){return Y(SB.getViewItems(t))}}),Df.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([kB],{}),i.get()},setValue:function(t,n){i.set(n),t.getSystem().broadcastOn([CB],{})}}})])})}function xE(t,n,r,e){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:lt(lt({},n.map(function(t){return{id:t}}).getOr({})),e?{"aria-live":"polite"}:{})},components:[],behaviours:nc([cS(0),k_.config({channel:TB,updateState:function(t,n){return vt.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){return"tabpanel"!==t.body.type?[(e=t.body,o=r,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(n=zm(eS.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:M(e.items,function(t){return hk(n,t,o)})}}))).asSpec()]}],behaviours:nc([Fg.config({mode:"acyclic",useTabstopAt:O(p0)}),aS(n),mS(n,{postprocess:function(t){return pE(t).fold(function(t){return console.error(t),{}},h)}})])})]:[yE(t.body,r)];var e,o,n},initialData:t})])}}function wE(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}}function SE(t,n){return JE.parts.close(fp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:nc([py.config({})])}))}function kE(){return JE.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function CE(t,n){return JE.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:xC("<p>"+n.translate(t)+"</p>")}]}]})}function OE(t){return JE.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})}function _E(t,n){return[ly.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),ly.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]}function TE(n){var t,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return JE.sketch({lazySink:n.lazySink,onEscape:function(t){return n.onEscape(t),vt.some(!0)},useTabstopAt:function(t){return!p0(t)},dom:{tag:"div",classes:[e].concat(n.extraClasses),styles:lt({position:"relative"},n.extraStyles)},components:H([n.header,n.body],n.footer.toArray(),!0),parts:{blocker:{dom:xC('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:BB?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:nc(H([Hg.config({}),mm("dialog-events",n.dialogEvents.concat([Br(bi(),function(t,n){Fg.focusIn(t)})])),mm("scroll-lock",[ou(function(){Yr(be(),i)}),ru(function(){qr(be(),i)})])],n.extraBehaviours,!0)),eventOrder:lt(((t={})[Ii()]=["dialog-events"],t[Gi()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Xi()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),n.eventOrder)})}function EE(t){return fp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[Xm("close",{tag:"div",classes:["tox-icon"]},t.icons)],action:function(t){vr(t,wy)}})}function DE(t,n,e){function o(t){return[ri(e.translate(t.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:lt({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:nc([k_.config({channel:_B,renderComponents:o})])}}function BE(){return{dom:xC('<div class="tox-dialog__draghandle"></div>')}}function ME(t,n){return e={title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},o=n.shared.providers,r=JE.parts.title(DE(e,vt.none(),o)),i=JE.parts.draghandle(BE()),u=JE.parts.close(EE(o)),a=[r].concat(e.draggable?[i]:[]).concat([u]),ly.sketch({dom:xC('<div class="tox-dialog__header"></div>'),components:a});var e,o,r,i,u,a}function AE(t,n,e){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.translate(t)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:xC('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}}function FE(t,o,n){return{onClose:function(){return n.closeWindow()},onBlock:function(e){JE.setBusy(t(),function(t,n){return AE(e.message,n,o)})},onUnblock:function(){JE.setIdle(t())}}}function IE(t,n,e,o){var r;return Tu(TE(lt(lt({},t),{lazySink:o.shared.getSink,extraBehaviours:H([k_.config({channel:OB,updateState:function(t,n){return vt.some(n)},initialData:n}),pS({})],t.extraBehaviours,!0),onEscape:function(t){vr(t,wy)},dialogEvents:e,eventOrder:((r={})[Fi()]=[k_.name(),ic.name()],r[Gi()]=["scroll-lock",k_.name(),"messages","dialog-events","alloy.base.behaviour"],r[Xi()]=["alloy.base.behaviour","dialog-events","messages",k_.name(),"scroll-lock"],r)})))}function RE(t){return M(t,function(t){return"menu"===t.type?(e=M((n=t).items,function(t){var n=Po(!1);return lt(lt({},t),{storage:n})}),lt(lt({},n),{items:e})):t;var n,e})}function VE(t){return R(t,function(t,n){return"menu"!==n.type?t:R(n.items,function(t,n){return t[n.name]=n.storage,t},t)},{})}function PE(t,e){return[Er(bi(),g0),t(xy,function(t,n){e.onClose(),n.onClose()}),t(wy,function(t,n,e,o){n.onCancel(t),vr(o,xy)}),Cr(Oy,function(t,n){return e.onUnblock()}),Cr(Cy,function(t,n){return e.onBlock(n.event)})]}function HE(t,n){function e(t,n){return ly.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:M(n,function(t){return t.memento.asSpec()})})}var o=A(n.map(function(t){return t.footerButtons}).getOr([]),function(t){return"start"===t.align});return[e("start",o.pass),e("end",o.fail)]}function zE(t,e){return{dom:xC('<div class="tox-dialog__footer"></div>'),components:[],behaviours:nc([k_.config({channel:EB,initialData:t,updateState:function(t,n){var r=M(n.buttons,function(t){var n=zm(Q0(t,t.type,e));return{name:t.name,align:t.align,memento:n}});return vt.some({lookupByName:function(t,n){return e=t,o=n,V(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:HE})])}}function NE(t,n){return JE.parts.footer(zE(t,n))}function LE(n,e){if(n.getRoot().getSystem().isConnected()){var o=cd.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return eS.getField(o,e).fold(function(){var t=n.getFooter();return k_.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return vt.some(t)})}return vt.none()}function WE(c,o,s){function t(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)}var l={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t,e=Df.getValue(n),o=dt(s,function(t){return t.get()});return lt(lt({},e),o)},setData:function(a){t(function(t){var n,e,o=l.getData(),r=lt(lt({},o),a),i=(n=r,e=c.getRoot(),k_.getState(e).get().map(function(t){return co(ao("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();Df.setValue(u,i),J(s,function(t,n){Tt(r,n)&&t.set(r[n])})})},disable:function(t){LE(c,t).each(gd.disable)},enable:function(t){LE(c,t).each(gd.enable)},focus:function(t){LE(c,t).each(Hg.focus)},block:function(n){if(!y(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){br(t,Cy,{message:n})})},unblock:function(){t(function(t){vr(t,Oy)})},showTab:function(e){t(function(t){var n=c.getBody();k_.getState(n).get().exists(function(t){return t.isTabPanel()})&&cd.getCurrent(n).each(function(t){SB.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([OB],n),t.getSystem().broadcastOn([_B],n.internalDialog),t.getSystem().broadcastOn([TB],n.internalDialog),t.getSystem().broadcastOn([EB],n.internalDialog),l.setData(n.initialData)})},close:function(){t(function(t){vr(t,xy)})}};return l}function UE(t){return x(t)&&-1!==RB.indexOf(t.mceAction)}function jE(o,t,r,n){var e,i,u,a=ME(o.title,n),c=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[d0({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:nc([py.config({}),Hg.config({})])})]}],behaviours:nc([Fg.config({mode:"acyclic",useTabstopAt:O(p0)})])},JE.parts.body(i)),s=o.buttons.bind(function(t){return 0===t.length?vt.none():vt.some(NE({buttons:t},n))}),l=MB(function(){return v},FE(function(){return h},n.shared.providers,t)),f=lt(lt({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),d=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],m=new IB(o.url,{base_uri:new IB(window.location.href)}),g=m.protocol+"://"+m.host+(m.port?":"+m.port:""),p=mc(),h=IE({header:a,body:c,footer:s,extraClasses:d,extraBehaviours:[mm("messages",[ou(function(){var t=pc(At.fromDom(window),"message",function(t){var n,e;m.isSameOrigin(new IB(t.raw.origin))&&(UE(n=t.raw.data)?function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!w(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock()}}(r,v,n):!UE(e=n)&&x(e)&&Tt(e,"mceAction")&&o.onMessage(v,n))});p.set(t)}),ru(p.clear)]),ic.config({channels:((e={})[DB]={onReceive:function(t,n){Iu(t.element,"iframe").each(function(t){t.dom.contentWindow.postMessage(n,g)})}},e)})],extraStyles:f},o,l,n),v={block:function(n){if(!y(n))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");b(function(t){br(t,Cy,{message:n})})},unblock:function(){b(function(t){vr(t,Oy)})},close:function(){b(function(t){vr(t,xy)})},sendMessage:function(n){b(function(t){t.getSystem().broadcastOn([DB],n)})}};function b(t){u.getSystem().isConnected()&&t(u)}return{dialog:u=h,instanceApi:v}}function GE(t){function o(t,y){return gB.open(function(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p,h,v=n,b=(r={redial:gB.redial,closeWindow:function(){JE.hide(b.dialog),y(b.instanceApi)}},i=R,c=ME((o={dataValidator:e,initialData:v,internalDialog:t}).internalDialog.title,i),u=i,a=xE({body:o.internalDialog.body},vt.none(),u,!1),s=JE.parts.body(a),l=RE(o.internalDialog.buttons),f=VE(l),d=NE({buttons:l},i),m=AB(function(){return h},FE(function(){return p},i.shared.providers,r),i.shared.getSink),g=function(){switch(o.internalDialog.size){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}}(),p=IE({header:c,body:s,footer:vt.some(d),extraClasses:g,extraBehaviours:[],extraStyles:{}},o,m,i),h=WE({getRoot:rt(p),getBody:function(){return JE.getBody(p)},getFooter:function(){return JE.getFooter(p)},getFormWrapper:function(){var t=JE.getBody(p);return cd.getCurrent(t).getOr(t)}},r.redial,f),{dialog:p,instanceApi:h});return JE.show(b.dialog),b.instanceApi.setData(v),b.instanceApi},t)}function r(t,A,F,I){return gB.open(function(t,n,e){function o(){return E.on(function(t){up.reposition(t),U1.refresh(t)})}var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T=co(ao("data",e,n)),E=gc(),D=R.shared.header.isPositionedAtTop(),B=(i={dataValidator:e,initialData:T,internalDialog:t},u={redial:gB.redial,closeWindow:function(){E.on(up.hide),V.off("ResizeEditor",o),E.clear(),F(B.instanceApi)}},a=R,c=I,v=Ir("dialog-label"),b=Ir("dialog-content"),y=zm((f={title:i.internalDialog.title,draggable:!0},d=v,m=a.shared.providers,ly.sketch({dom:xC('<div class="tox-dialog__header"></div>'),components:[DE(f,vt.some(d),m),BE(),EE(m)],containerBehaviours:nc([aE.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Ru(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),x=zm((g={body:i.internalDialog.body},p=a,h=c,xE(g,vt.some(b),p,h))),w=RE(i.internalDialog.buttons),S=VE(w),k=zm(FB({buttons:w},a)),C=AB(function(){return _},{onBlock:function(e){fO.block(O,function(t,n){return AE(e.message,n,a.shared.providers)})},onUnblock:function(){fO.unblock(O)},onClose:function(){return u.closeWindow()}},a.shared.getSink),O=Tu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((s={role:"dialog"})["aria-labelledby"]=v,s["aria-describedby"]=b,s)},eventOrder:((l={})[Fi()]=[k_.name(),ic.name()],l[Ii()]=["execute-on-form"],l[Gi()]=["reflecting","execute-on-form"],l),behaviours:nc([Fg.config({mode:"cyclic",onEscape:function(t){return vr(t,xy),vt.some(!0)},useTabstopAt:function(t){return!p0(t)&&("button"!==Ft(t)||"disabled"!==un(t,"disabled"))}}),k_.config({channel:OB,updateState:function(t,n){return vt.some(n)},initialData:i}),Hg.config({}),mm("execute-on-form",C.concat([Br(bi(),function(t,n){Fg.focusIn(t)})])),fO.config({getRoot:function(){return vt.some(O)}}),Rg.config({}),pS({})]),components:[y.asSpec(),x.asSpec(),k.asSpec()]}),_=WE({getRoot:rt(O),getFooter:function(){return k.get(O)},getBody:function(){return x.get(O)},getFormWrapper:function(){var t=x.get(O);return cd.getCurrent(t).getOr(t)}},u.redial,S),{dialog:O,instanceApi:_}),M=Tu(up.sketch(lt(lt({lazySink:R.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},D?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:nc(H([mm("window-manager-inline-events",[Cr(Yi(),function(t,n){vr(B.dialog,wy)})])],(r=V,P&&D?[]:[U1.config({contextual:{lazyContext:function(){return vt.some(Ae(At.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]),!0)),isExtraPart:function(t,n){return Yb(e=n,".tox-alert-dialog")||Yb(e,".tox-confirm-dialog");var e}})));return E.set(M),up.showWithin(M,Eu(B.dialog),{anchor:A},vt.some(be())),P&&D||(U1.refresh(M),V.on("ResizeEditor",o)),B.instanceApi.setData(T),Fg.focusIn(B.dialog),B.instanceApi},t)}var c,s,l,f,R=t.backstage,V=t.editor,P=dv(V),e=(s=(c=t).backstage.shared,{open:function(t,n){function e(){JE.hide(u),n()}var o=zm(Q0({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:vt.none()},"cancel",c.backstage)),r=kE(),i=SE(e,s.providers),u=Tu(TE({lazySink:function(){return s.getSink()},header:wE(r,i),body:CE(t,s.providers),footer:vt.some(OE(_E([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Cr(wy,e)],eventOrder:{}}));JE.show(u);var a=o.get(u);Hg.focus(a)}}),i=(f=(l=t).backstage.shared,{open:function(t,n){function e(t){JE.hide(a),n(t)}var o=zm(Q0({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:vt.none()},"submit",l.backstage)),r=Q0({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:vt.none()},"cancel",l.backstage),i=kE(),u=SE(function(){return e(!1)},f.providers),a=Tu(TE({lazySink:function(){return f.getSink()},header:wE(i,u),body:CE(t,f.providers),footer:vt.some(OE(_E([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Cr(wy,function(){return e(!1)}),Cr(ky,function(){return e(!0)})],eventOrder:{}}));JE.show(a);var c=o.get(a);Hg.focus(c)}});return{open:function(t,n,e){return void 0!==n&&"toolbar"===n.inline?r(t,R.shared.anchors.inlineDialog(),e,n.ariaAttrs):void 0!==n&&"cursor"===n.inline?r(t,R.shared.anchors.cursor(),e,n.ariaAttrs):o(t,e)},openUrl:function(t,n){return e=n,gB.openUrl(function(t){var n=jE(t,{closeWindow:function(){JE.hide(n.dialog),e(n.instanceApi)}},V,R);return JE.show(n.dialog),n.instanceApi},t);var e},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){i.open(t,function(t){n(t)})}}}(zT=HT=HT||{})[zT.None=0]="None",zT[zT.Both=1]="Both",zT[zT.Vertical=2]="Vertical";var XE,YE=rt([mo("lazySink"),wo("dragBlockClass"),Io("getBounds",Fe),Eo("useTabstopAt",D),Eo("eventOrder",{}),Zs("modalBehaviours",[Fg]),$u("onExecute"),Zu("onEscape")]),qE={sketch:h},KE=rt([Qf({name:"draghandle",overrides:function(t,n){return{behaviours:nc([aE.config({mode:"mouse",getTarget:function(t){return Fu(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),Jf({schema:[mo("dom")],name:"title"}),Jf({factory:qE,schema:[mo("dom")],name:"close"}),Jf({factory:qE,schema:[mo("dom")],name:"body"}),Qf({factory:qE,schema:[mo("dom")],name:"footer"}),$f({factory:{sketch:function(t,n){return lt(lt({},t),{dom:n.dom,components:n.components})}},schema:[Eo("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Eo("components",[])],name:"blocker"})]),JE=Cl({name:"ModalDialog",configFields:YE(),partFields:KE(),factory:function(a,t,n,r){var e,i=gc(),o=Ir("modal-events"),u=lt(lt({},a.eventOrder),((e={})[Gi()]=[o].concat(a.eventOrder["alloy.system.attached"]||[]),e));return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(t){i.set(t);var n=a.lazySink(t).getOrDie(),e=r.blocker(),o=n.getSystem().build(lt(lt({},e),{components:e.components.concat([Eu(t)]),behaviours:nc([Hg.config({}),mm("dialog-blocker-events",[Br(bi(),function(){Fg.focusIn(t)})])])}));Vs(n,o),Fg.focusIn(t)},hide:function(n){i.clear(),Yt(n.element).each(function(t){n.getSystem().getByDom(t).each(function(t){Hs(t)})})},getBody:function(t){return gl(t,a,"body")},getFooter:function(t){return gl(t,a,"footer")},setIdle:function(t){fO.unblock(t)},setBusy:function(t,n){fO.block(t,n)}},eventOrder:u,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:nl(a.modalBehaviours,[Rg.config({}),Fg.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),fO.config({getRoot:i.get}),mm(o,[ou(function(t){var n,e,o,r=t.element,i=gl(t,a,"title").element,u=an(r,"id").fold(function(){var t=Ir("dialog-label");return on(i,"id",t),t},h);on(r,"aria-labelledby",u),n=t.element,e=gl(t,a,"body").element,o=vt.from(un(n,"id")).fold(function(){var t=Ir("dialog-describe");return on(e,"id",t),t},h),on(n,"aria-describedby",o)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),$E=$o([po("type"),po("name")].concat(Lh)),QE=er,ZE=[ar("name","name",je(function(){return Ir("button-name")}),nr),Co("icon"),Ao("align","end",["start","end"]),Fo("primary",!1),Fo("disabled",!1)],tD=H(H([],ZE,!0),[po("text")],!1),nD=H([ho("type",["submit","cancel","custom"])],tD,!0),eD=lo("type",{submit:nD,cancel:nD,custom:nD,menu:H([ho("type",["menu"]),Co("text"),Co("tooltip"),Co("icon"),xo("items",$E)],ZE,!0)}),oD=[po("type"),po("text"),ho("level",["info","warn","error","success"]),po("icon"),Eo("url","")],rD=$o(oD),iD=[po("type"),po("text"),Fo("disabled",!1),Fo("primary",!1),ar("name","name",je(function(){return Ir("button-name")}),nr),Co("icon"),Fo("borderless",!1)],uD=$o(iD),aD=[po("type"),po("name"),po("label"),Fo("disabled",!1)],cD=$o(aD),sD=er,lD=[po("type"),po("name")],fD=lD.concat([Co("label")]),dD=fD.concat([Eo("columns","auto")]),mD=$o(dD),gD=Qo([po("value"),po("text"),po("icon")]),pD=$o(fD),hD=nr,vD=$o(fD),bD=nr,yD=lD.concat([Mo("tag","textarea"),po("scriptId"),po("scriptUrl"),Do("settings",void 0,ir)]),xD=lD.concat([Mo("tag","textarea"),vo("init")]),wD=io(function(t){return ao("customeditor.old",to(xD),t).orThunk(function(){return ao("customeditor.new",to(yD),t)})}),SD=nr,kD=$o(fD),CD=no(Jo),OD=[po("type"),po("html"),Ao("presets","presentation",["presentation","document"])],_D=$o(OD),TD=fD.concat([Fo("sandboxed",!0)]),ED=$o(TD),DD=nr,BD=fD.concat([go("currentState",$o([mo("blob"),po("url")]))]),MD=$o(BD),AD=fD.concat([Co("inputMode"),Co("placeholder"),Fo("maximized",!1),Fo("disabled",!1)]),FD=$o(AD),ID=nr,RD=[po("text"),po("value")],VD=[po("text"),xo("items",(XE=Rt(function(){return PD}),{extract:function(t,n){return XE().extract(t,n)},toString:function(){return XE().toString()}}))],PD=eo([$o(RD),$o(VD)]),HD=fD.concat([xo("items",PD),Fo("disabled",!1)]),zD=$o(HD),ND=nr,LD=fD.concat([yo("items",[po("text"),po("value")]),Bo("size",1),Fo("disabled",!1)]),WD=$o(LD),UD=nr,jD=fD.concat([Fo("constrain",!0),Fo("disabled",!1)]),GD=$o(jD),XD=$o([po("width"),po("height")]),YD=[po("type"),xo("header",nr),xo("cells",no(nr))],qD=$o(YD),KD=fD.concat([Co("placeholder"),Fo("maximized",!1),Fo("disabled",!1)]),JD=$o(KD),$D=nr,QD=fD.concat([Ao("filetype","file",["image","media","file"]),Eo("disabled",!1)]),ZD=$o(QD),tB=$o([po("value"),Eo("meta",{})]),nB=Ze(function(){return ro("type",{alertbanner:rD,bar:$o((e=dE("bar"),[po("type"),e])),button:uD,checkbox:cD,colorinput:pD,colorpicker:vD,dropzone:kD,grid:$o((t=dE("grid"),[po("type"),go("columns",tr),t])),iframe:ED,input:FD,listbox:zD,selectbox:WD,sizeinput:GD,textarea:JD,urlinput:ZD,customeditor:wD,htmlpanel:_D,imagetools:MD,collection:mD,label:$o((n=dE("label"),[po("type"),po("label"),n])),table:qD,panel:oB});var t,n,e}),eB=[po("type"),Eo("classes",[]),xo("items",nB)],oB=$o(eB),rB=[ar("name","name",je(function(){return Ir("tab-name")}),nr),po("title"),xo("items",nB)],iB=[po("type"),yo("tabs",rB)],uB=$o(iB),aB=tD,cB=eD,sB=$o([po("title"),go("body",ro("type",{panel:oB,tabpanel:uB})),Mo("size","normal"),xo("buttons",cB),Eo("initialData",{}),Io("onAction",st),Io("onChange",st),Io("onSubmit",st),Io("onClose",st),Io("onCancel",st),Eo("onTabChange",st)]),lB=$o(H([ho("type",["cancel","custom"])],aB,!0)),fB=$o([po("title"),po("url"),ko("height"),ko("width"),_o("buttons",lB),Io("onAction",st),Io("onCancel",st),Io("onClose",st),Io("onMessage",st)]),dB=function(t){return x(t)?[t].concat(z(Z(t),dB)):c(t)?z(t,dB):[]},mB={checkbox:sD,colorinput:hD,colorpicker:bD,dropzone:CD,input:ID,iframe:DD,sizeinput:XD,selectbox:UD,listbox:ND,size:XD,textarea:$D,urlinput:tB,customeditor:SD,collection:gD,togglemenuitem:QE},gB={open:function(t,n){var e=gE(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){return t(co(ao("dialog",fB,n)))},redial:gE},pB=kl({name:"TabButton",configFields:[Eo("uid",void 0),mo("value"),ar("dom","dom",Ye(function(){return{attributes:{role:"tab",id:Ir("aria"),"aria-selected":"false"}}}),Zo()),wo("action"),Eo("domModification",{}),Zs("tabButtonBehaviours",[Hg,Fg,Df]),mo("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:Sm(t.action),behaviours:nl(t.tabButtonBehaviours,[Hg.config({}),Fg.config({mode:"execution",useSpace:!0,useEnter:!0}),Df.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),hB=rt([mo("tabs"),mo("dom"),Eo("clickToDismiss",!1),Zs("tabbarBehaviours",[hd,Fg]),qu(["tabClass","selectedClass"])]),vB=rt([Zf({factory:pB,name:"tabs",unit:"tab",overrides:function(o){return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=hd.isHighlighted(n,t);(e&&o.clickToDismiss?function(t,n){hd.dehighlight(t,n),br(t,Qi(),{tabbar:t,button:n})}:e?st:function(t,n){hd.highlight(t,n),br(t,$i(),{tabbar:t,button:n})})(n,t)},domModification:{classes:[o.markers.tabClass]}}}})]),bB=Cl({name:"Tabbar",configFields:hB(),partFields:vB(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:nl(t.tabbarBehaviours,[hd.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){on(n.element,"aria-selected","true")},onDehighlight:function(t,n){on(n.element,"aria-selected","false")}}),Fg.config({mode:"flow",getInitial:function(t){return hd.getHighlighted(t).map(function(t){return t.element})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),yB=kl({name:"Tabview",configFields:[Zs("tabviewBehaviours",[Rg])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:nl(t.tabviewBehaviours,[Rg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),xB=rt([Eo("selectFirst",!0),Ju("onChangeTab"),Ju("onDismissTab"),Eo("tabs",[]),Zs("tabSectionBehaviours",[])]),wB=rt([Jf({factory:bB,schema:[mo("dom"),bo("markers",[mo("tabClass"),mo("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),Jf({factory:yB,name:"tabview"})]),SB=Cl({name:"TabSection",configFields:xB(),partFields:wB(),factory:function(i,t,n,e){function o(t,n){ml(t,i,"tabbar").each(function(t){n(t).each(yr)})}return{uid:i.uid,dom:i.dom,components:t,behaviours:tl(i.tabSectionBehaviours),events:eu(ft([i.selectFirst?[ou(function(t,n){o(t,hd.getFirst)})]:[],[Cr($i(),function(t,n){var o=n.event.button,r=Df.getValue(o);ml(o,i,"tabview").each(function(e){V(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();an(o.element,"id").each(function(t){on(e.element,"aria-labelledby",t)}),Rg.set(e,n),i.onChangeTab(e,o,n)})})}),Cr(Qi(),function(t,n){var e=n.event.button;i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return ml(t,i,"tabview").map(function(t){return Rg.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){return V(hd.getCandidates(n),function(t){return Df.getValue(t)===e}).filter(function(t){return!hd.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),kB="send-data-to-section",CB="send-data-to-view",OB=Ir("update-dialog"),_B=Ir("update-title"),TB=Ir("update-body"),EB=Ir("update-footer"),DB=Ir("body-send-message"),BB=nb.deviceType.isTouch(),MB=function(u,t){function n(t,i){return Cr(t,function(e,o){var n,r;n=e,r=function(t,n){i(u(),t,o.event,e)},k_.getState(n).get().each(function(t){r(t,n)})})}return H(H([],PE(n,t),!0),[n(Sy,function(t,n,e){n.onAction(t,{name:e.name})})],!1)},AB=function(u,t,c){function n(t,i){return Cr(t,function(e,o){var n,r;n=e,r=function(t,n){i(u(),t,o.event,e)},k_.getState(n).get().each(function(t){r(t.internalDialog,n)})})}return H(H([],PE(n,t),!0),[n(ky,function(t,n){return n.onSubmit(t)}),n(yy,function(t,n,e){n.onChange(t,{name:e.name})}),n(Sy,function(t,n,e,o){function r(){return Fg.focusIn(o)}function i(t){return cn(t,"disabled")||an(t,"aria-disabled").exists(function(t){return"true"===t})}var u=ge(o.element),a=Oa(u);n.onAction(t,{name:e.name,value:e.value}),Oa(u).fold(r,function(n){i(n)||a.exists(function(t){return Wt(n,t)&&i(t)})?r():c().toOptional().filter(function(t){return!Wt(t.element,n)}).each(r)})}),n(_y,function(t,n,e){n.onTabChange(t,{newTabName:e.name,oldTabName:e.oldName})}),ru(function(t){var n=u();Df.setValue(t,n.getData())})],!1)},FB=zE,IB=tinymce.util.Tools.resolve("tinymce.util.URI"),RB=["insertContent","setContent","execCommand","close","block","unblock"];o.add("silver",function(t){var n=fE(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;return uy(t,o.shared),{renderUI:r,getWindowManagerImpl:rt(GE({editor:t,backstage:o})),getNotificationManagerImpl:function(){return u=t,r=e,l={backstage:o}.backstage.shared,{open:function(t,n){function e(){n(),up.hide(c)}var o=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),a=Tu(hp.sketch({text:t.text,level:wt(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:vt.from(t.icon),closeButton:!o,onAction:e,iconProvider:l.providers.icons,translationProvider:l.providers.translate})),c=Tu(up.sketch(lt({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:l.getSink,fireDismissalEventInstead:{}},l.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));function s(){var t=Ae(At.fromDom(u.getContentAreaContainer())),n=Fe(),e=ja(n.x,t.x,t.right),o=ja(n.y,t.y,t.bottom),r=Math.max(t.right,n.right),i=Math.max(t.bottom,n.bottom);return vt.some(Me(e,o,r-e,i-o))}return r.add(c),0<t.timeout&&lp.setTimeout(function(){e()},t.timeout),{close:e,moveTo:function(t,n){up.showAt(c,Eu(a),{anchor:{type:"makeshift",x:t,y:n}})},moveRel:function(t,n){var e,o,r,i=Eu(a),u={maxHeightFunction:Vc()};"banner"!==n&&k(t)?(e=function(){switch(n){case"bc-bc":return sp;case"tc-tc":return cp;case"tc-bc":return Ja;default:return $a}}(),o={type:"node",root:be(),node:vt.some(At.fromDom(t)),overrides:u,layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}},up.showWithinBounds(c,i,{anchor:o},s)):(r=lt(lt({},l.anchors.banner()),{overrides:u}),up.showWithinBounds(c,i,{anchor:r},s))},text:function(t){hp.updateText(a,t)},settings:t,getEl:function(){return a.element.dom},progressBar:{value:function(t){hp.updateProgress(a,t)}}}},close:function(t){t.close()},reposition:function(e){0<e.length&&St(e,function(t,n){0===n?t.moveRel(null,"banner"):t.moveRel(e[n-1].getEl(),"bc-tc")})},getArgs:function(t){return t.settings}};var u,r,l},ui:i()}})}();