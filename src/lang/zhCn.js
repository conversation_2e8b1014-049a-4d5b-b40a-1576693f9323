const zhCn = {
  language: '简体中文',
  pleaseCompleteTheSecurityVerification: '请完成安全验证',
  swipeRightToCompleteVerification: '向右滑动完成验证',
  verificationCodeHasExpired: '验证码已失效，请重新获取',
  verifySuccessfully: '验证成功',
  verificationFailure: '验证失败',
  passwordVerification: '密码由字母、数字和特殊符号三种字符组成8-20位半角字符，区分大小写',
  common: {
    language: '语言',
    condition: '条件',
    display: '显示',
    hide: '隐藏',
    back: '返回',
    confirm: '确定',
    setAsDefault: '设为默认',
    undefault: '取消默认'
  },
  express: {
    sf: '顺丰速运',
    jd: '京东快递',
    yto: '圆通速递',
    yunDa: '韵达快递',
    zto: '中通快递',
    sto: '申通快递',
    best: '百世快递',
    ems: 'EMS',
    monthlyClosingNumber: '月结账号',
    merchantCoding: '商家编码',
    merchantCode: '商家代码',
    merchantKey: '商家密钥',
    yundaWhiteHorseAccount: '韵达白马账号',
    jointCipher: '联调密码',
    partnerCode: '合作方代码',
    partnerKey: '合作方密钥',
    dotCoding: '网点编码',
    customerName: '客户名称',
    customerPassword: '客户密码',
    network: '网点',
    operationCoding: '操作编码',
    secretKey: '密钥',
    ProtocolClientNumber: '协议客户号',
    eCommerceCustomerLogos: '电商客户标识'
  },
  tip: {
    select: '请选择',
    input: '请输入'
  },
  time: {
    start: '开始日期',
    end: '结束日期',
    startTime: '开始时间',
    endTime: '结束时间',
    tip: '至',
    t: '今日',
    y: '昨日',
    n: '近7天',
    m: '近30天',
    a: '全部',
    selectWeek: '选择周',
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日',
    tips1: '结束时间不能少于当前时间',
    tips2: '开始时间不能少于当前时间',
    tips3: '开始时间不能大于或等于结束时间',
    tips4: '结束时间不能小于或等于开始时间'
  },
  home: {
    all: '全部',
    yamiB: '亚米后台',
    company: '广州蓝海创新科技有限公司',
    nowDate: '实时概况',
    platformPay: '平台支付金额',
    // businessPay: '商家支付金额',
    businessPay: '交易金额',
    updateTime: '更新时间',
    payAmount: '支付金额',
    yestAllUpdate: '昨天整天,更新中',
    displays: '整体看板',
    realTimeData: '实时数据',
    dataToday: '今日数据',
    dataYesterday: '昨日数据',
    dataTodayActualTotal: '今日支付金额',
    dataYesterdayActualTotal: '昨日支付金额',
    statistics: '数据统计',
    refundDisplays: '退款看板',
    payCustomers: '支付客户数',
    payCount: '支付订单数',
    pendingOrders: '待付款订单',
    refundAmount: '成功退款金额',
    successfulRefundAmountToday: '成功退款金额（元）',
    successfulRefundNum: '成功退款数量',
    refundRateToday: '退款率(%)',
    reboundRate: '反弹率',
    sameAsYesterday: '同比昨日',
    number: '次数',
    transactionAmount: '交易金额',
    moneyRatio: '金额占比',
    customerPrice: '客单价',
    dayAmount: '当天支付金额(元)',
    monthAmount: '本月已完成(元)',
    storeAmount: '全店支付金额',
    todays: '今天',
    yesterdays: '昨天',
    totals: '总量',
    platformName: '商家端管理平台',
    verificationCode: '验证码',
    refundRate: '退款率',
    refundProRank: '退款商品排行',
    refundReasonRank: '退款原因排行',
    rank: '排名',
    product: '商品',
    reason: '原因',
    ratio: '占比',
    amount: '金额',
    accNoNull: '账号不能为空',
    userNameNoNull: '用户名不能为空',
    pawNoNull: '密码不能为空',
    capNoNull: '验证码不能为空',
    loginTip: '请输入正确的手机号/用户名',
    customerService: '客服',
    notification: '通知',
    haveANewUnreadMessage: '你有新的未读消息',
    msgBox: '消息盒子',
    authTip: '您暂时没有权限访问'
  },
  number: '序号',
  temp: {
    sequence: '顺序',
    area: '区/县',
    prodInfo: '商品信息',
    rotaImg: '轮播图片',
    modify: '修改',
    actLabel: '活动标签',
    setNull: '清空',
    proImg: '商品图片',
    productMainImage: '商品主图',
    m: '近30天'
  },
  crud: {
    filter: {
      addBtn: '新增条件',
      clearBtn: '清空数据',
      resetBtn: '清空条件',
      cancelBtn: '取消',
      submitBtn: '确定',
      modify: '修改'
    },
    rotaImg: '轮播图片',
    tipStartTitle: '当前表格已选择',
    tipEndTitle: '项',
    editTitle: '编辑',
    addTitle: '新增',
    viewTitle: '查看',
    filterTitle: '过滤条件',
    menu: '操作',
    addBtn: '新增',
    showBtn: '显隐',
    filterBtn: '过滤',
    refreshBtn: '刷新',
    printBtn: '打印',
    excelBtn: '导出',
    updateBtn: '修改',
    cancelBtn: '取消',
    columnBtn: '多选',
    searchBtn: '搜索',
    emptyBtn: '清空',
    menuBtn: '功能',
    saveBtn: '保存',
    viewBtn: '查看',
    editBtn: '编辑',
    UpperShelf: '上架',
    LowerShelf: '下架',
    batchDelete: '批量删除',
    delBtn: '删除',
    importe: '导入',
    remark: '备注',
    noAbleTabErr: '找不到可用的标签',
    setNull: '清空'
  },
  text: {
    editBtn: '编辑',
    menu: '操作',
    delBtn: '删除',
    updateBtn: '修改',
    tips: '提示',
    endTime: '结束日期',
    startTime: '开始日期',
    to: '至',
    upload: '点击上传',
    uploadTips: '上传头像图片大小不能超过',
    isRemove: '确定移除'
  },
  withdrawal: {
    appliedAmount: '申请金额',
    toTheBank: '到账银行',
    merchantNotes: '商家备注',
    reviewStatus: '审核状态',
    payingAccount: '付款户名',
    payingCardNo: '付款账号',
    payingTime: '付款日期',
    platformNote: '平台备注'
  },
  takeStock: {
    inputName: '输入名称',
    inputPartyCode: '输入编码',
    productFilter: '商品筛选',
    InventoryNo: '盘点单号',
    InventoryStatus: '盘点状态',
    voided: '已作废',
    taking: '盘点中',
    complete: '已完成',
    maker: '制单人',
    createTime: '盘点日期',
    regionName: '盘点区域名称',
    skuCount: 'sku个数',
    voidInventory: '作废',
    voidTip: '作废后无法编辑，确认作废？',
    exportTip: '若该盘点已有商品保存，则无法使用批量导入功能。',
    exportTip1: '若导入盘点数量为空则默认为0',
    exportProdFileName: '盘点商品导入模板.xlsx',
    prodDetailFileName: '盘点商品信息.xlsx',
    exportInventoryFileName: '盘点列表信息.xlsx',
    importErrorTip: '已添加商品，不支持批量导入',
    infoText1: '盘点商品',
    infoText2: '种，实盘库存',
    infoText3: '件，盘盈',
    infoText4: '件，盘亏',
    infoText5: '件',
    bookStock: '账面库存',
    profitLossCount: '盈亏数量',
    profitLossTip: '此处的盈亏差异值仅做参考，请以最后“完成盘点”时盈亏计算的结果为准。',
    totalStock: '实盘库存',
    profit: '盘盈',
    loss: '盘亏',
    equal: '盘平',
    exception: '异常',
    inventoryDetail: '盘点单详情',
    exportProdDetail: '导出商品明细',
    editInventory: '实盘录入',
    regionNameTip: '自定义该盘点区域名称，例如冷藏区。',
    saveDraft: '保存草稿',
    finishInventory: '完成盘点',
    prodNotNull: '盘点商品不能为空',
    prodStockNotNull: '商品实盘库存不能为空',
    newInventory: '新建盘点',
    createTimeNotNull: '盘点时间不能为空'
  },
  product: {
    activeProd: '组合选品',
    viewProduct: '查看商品',
    cannotBeRemovedOrRemovedFromTheChassis: '不能进行上下架操作',
    voucher: '代金券',
    freTempl: '运费模板',
    fixedFreight: '固定运费',
    pleaseEnterTheAmount: '请输入金额',
    unifiedPackageMail: '统一包邮',
    uniformDeliveredPricing: '统一运费',
    doNotEnterSpecialCharacters: '请勿输入特殊字符',
    ExpressDistribution: '快递配送',
    isTop: '是否置顶',
    seq: '排序号',
    brandId: '品牌id',
    brandName: '品牌名称',
    basicInformation: '基本信息',
    productCategories: '商品分类',
    priceAndInventory: '价格及库存',
    brandImg: '品牌图片',
    prodName: '商品名称',
    prodStatus: '商品状态',
    prodNameCn: '商品sku中文名称',
    prodNameEn: '商品sku英文名称',
    prodInfo: '商品信息',
    oriPrice: '商品原价',
    price: '商品现价',
    prices: '价格',
    pic: '商品图片',
    status: '状态',
    totalStocks: '商品库存',
    prodType: '商品类型',
    prodMold: '商品类别',
    ordProd: '普通商品',
    groupProd: '团购商品',
    limitedTimeProd: '秒杀商品',
    pointsProduct: '积分商品',
    comProd: '组合商品',
    combinedProducts: '组合选品',
    delType: '配送方式',
    sameCityDelivery: '同城配送',
    shopDelivery: '店铺配送',
    userPickUp: '用户自提',
    pleaseSelectTheProduct: '请先选择商品分类再填写以下商品信息',
    pleaseSelectCategoryAgainSelectBrand: '请先选择商品分类，再选择品牌',
    deletes: '你确定要删除此商品吗？',
    saveTip: '该商品的库存为0，确定继续发布吗？',
    select: '选择商品',
    violation: '违规下架',
    batchUp: '批量上架',
    batchDown: '批量下架',
    pendingReview: '待审核',
    violationPendingReview: '违规下架待审核',
    auditHistory: '审核历史',
    reviewStatus: '审核状态',
    prod: '商品',
    atributeID: '属性ID',
    attributeName: '规格名称',
    attributeNameEn: '规格英文名称',
    attributeNameNoNull: '规格名称不能为空',
    attributeNameEnNoNull: '规格英文名称不能为空',
    attributeValue: '规格值',
    attributeValueEn: '英文规格值',
    attributeValueEnTips: '英文规格值，为空时默认使用中文规格值',
    attributeValueEditTips: '点击以修改对应规格值',
    attributeValueNoNull: '规格值不能为空',
    content: '请输入内容',
    same: '不能添加相同的规格值',
    voucherName: '卡券名称',
    relatedProducts: '关联商品',
    expired: '已失效',
    startUsing: '已发放',
    notEnabled: '未发放',
    notEnabledTips: '显示未发放且未过期卡券',
    voucherNameNoNull: '卡券名称不能为空',
    appliedAmount: '申请金额',
    debitCard: '到账银行卡',
    uploadTips: '请选择操作,同时只能上传一个文件',
    selectFile: '选取文件',
    downloadTemplate: '下载模板',
    fileSuccess: '文件导入成功',
    fileFail: '文件导入失败',
    fileUploadFail: '文件上传失败！',
    downloadTemplateTips1: '上传模板只能是 xls、xlsx格式!',
    downloadTemplateTips2: '上传模板大小不能超过 10MB!',
    fileName: '商品信息模板.xlsx',
    templateName: '模板.xlsx',
    merAddProdTime: '商家添加商品时间',
    meSubRevTime: '商家提交审核时间',
    proApprTime: '商品审核通过时间',
    uploadProdTip: '1.上传的规格编码若为空或与已有的编码重复，系统将会自动生成新的编码值。',
    uploadProdTip2: '2.不建议保留空白行或在商品sku名称中填写冒号，容易导致数据错误并不能成功导入。',
    maxNum: '限购数量',
    maxCancelTime: '取消订单时间',
    offViol: '违规下线',
    pleaEntBraName: '请输入品牌名称',
    branls: '品牌首字母',
    pletials: '请输入品牌首字母',
    pleaemarks: '请输入备注内容',
    brandNaBeEmpty: '品牌名称不能为空',
    brandNameErrorTips: '品牌名称长度在1-50字之间',
    brandInitBeEmpty: '品牌首字母不能为空',
    firstLetterErrorTips: '请输入一位大写字母',
    brandLogoNotEmpty: '请上传品牌logo',
    sortValmpty: '顺序值不能为空',
    shippinngs: '运费设置',
    parameterSetting: '参数设置',
    parameter: '参数',
    shippingtBeEmpty: '运费模板不能为空',
    deliveryArea: '配送区域',
    freeShipping: '包邮',
    further: '且',
    freeShiullAmount: '满金额包邮',
    freeippingR: '元金额包邮',
    pwvFree: '件/重量/体积包邮',
    import: '导入',
    productVideo: '商品视频',
    draggableSort: '可拖动排序，首图为主图，最多上传',
    platforation: '平台分类',
    shopCategory: '本店分类',
    thisPlatformEmpty: '平台分类不能为空',
    thisShopeEmpty: '本店分类不能为空',
    thisProduCategroy: '请选择商品分类',
    thisShopCategroy: '请选择本店分类',
    thisProductImg: '请选择至少一张图片作为商品主图',
    brand: '所属品牌',
    stockWarning: '库存预警',
    whetPreSale: '是否开启预售',
    isBindElectronicCard: '是否关联卡券',
    electronicCardTips: '卡券在【商品-卡券管理】进行设置，若不关联卡券，需要主动联系用户发放卡券内容',
    chooseABrand: '选择品牌',
    preSaleTime: '预售发货时间',
    thePreSaleDtBeEmpty: '预售发货时间不能为空',
    choosengDate: '选择发货日期',
    userMention: '用户自提',
    chinenInput: '中文信息',
    prodellingPoint: '产品卖点',
    plePictureToUpload: '请选择图片上传',
    pleeliveryMethod: '请选择配送方式',
    pleShgTlate: '请选择运费模板',
    zhpleComAndEnName: '请完善中文名称名称',
    violatingGoods: '违规商品',
    refuseToPass: '拒绝通过',
    other: '其他',
    importGoods: '导入商品',
    offlineManagement: '下线管理',
    reportsToCond: '导出报表只根据搜索条件的状态导出',
    exportProduct: '导出筛选出的商品信息',
    oneInARow: '一列一个',
    twoInARow: '一列两个',
    threeInARow: '一列三个',
    pleaseEnterALabelName: '请输入标签名称',
    addNew: '新增',
    singleProductBar: '单品条码',
    noBarcode: '如无条形码系统将自动生成',
    usedToQuickItem: '用于快速识别该单品',
    item: '单品',
    itemName: '单品名称',
    singleProdPic: '单品图片',
    singleProdSpe: '单品规格',
    specificationStock: '规格库存',
    singleProdClas: '单品分类',
    singleProdUnit: '单品单位',
    singleProdInve: '单品库存',
    supplier: '首选供应商',
    supplierStatus: '供应商状态',
    chooseSupplier: '选择供应商',
    itemNameCanEmpty: '单品名称不能为空',
    pleaseUploadApicture: '请上传单品图片',
    chooseProdCateg: '选择商品分类',
    productSecondaryClassification: '商品二级分类',
    productThreeClassification: '商品三级分类',
    currCho: '你当前的选择是',
    isItAComtionPro: '是否为组合商品',
    combinationGoods: '组合选品',
    quantityInCom: '组合内数量',
    singleProd: '单品成本',
    total: '合计',
    skuPictures: 'sku图片',
    sellingPrice: '销售价(元)',
    marketPrice: '市场价(元)',
    eventPrice: '活动售价',
    commodityCode: '商品编码',
    productBarcode: '商品条形码',
    haveReadFol: '我已阅读以下规则，现在发布',
    releaseNotice: '发布须知',
    commodityWeight: '商品重量(kg)',
    commodityVolume: '商品体积(m³)',
    productSpecifi: '商品规格',
    addSpecifications: '添加商品规格',
    specificationValue: '规格值',
    pleaseerTheSpName: '请输入规格名',
    pleaseEntValue: '请输入规格值',
    rule: '规则',
    merchantDelivery: '商家配送',
    comInfoSorXls: '商品信息整理.xlsx',
    voucherItemExcel: '卡券明细.xlsx',
    voucherItemExcelTips: '确定要导出卡券明细吗？',
    exportVoucherDetails: '导出卡券明细',
    exportVoucherOfSearch: '导出筛选出的卡券明细',
    exportVoucherOfSelect: '导出选中的卡券明细',
    pleaseSelectAUnit: '请选择单位',
    isExistsPleaReEn: '已存在，请重新输入',
    specificationy: '规格项不能为空',
    banInf: '禁止发布侵犯他人知识产权的商品，请确认商品符合知识产权保护的规定',
    userMustFollRule: '用户应遵守国家法律、行政法规、部门规章等规范性文件。对任何涉嫌违反国家法律、行政法规、部门规章等规范性文件的行为， 本平台有权酌情处理。但平台对用户的处理不免除其应尽的法律责任。 用户在平台的任何行为，应同时遵守与平台及其关联公司所签订的各项协议。 平台有权随时变更本规则并在网站上予以公告。 若用户不同意相关变更，应立即停止使用平台的相关服务或产品。平台有权对用户行为及应适用的规则进行单方认定，并据此处理。',
    relatedItems: '关联单品',
    chooSingPro: '选择单品',
    specTip: '规格值 第 N 行有空值',
    selOnlineCustomer: '选择在线客服',
    customersNotInYourCharge: '不是你负责的客户',
    pendingPayment: '待付款',
    pendingDelivery: '待发货',
    pendingReceipt: '待收货',
    pendinEvaluation: '待评价',
    evaluated: '已评价',
    shipped: '已发货',
    completed: '已完成',
    canceled: '已取消',
    grouping: '拼团中',
    priceTip: '销售价不得高于市场价',
    noContractedCategories: '暂无已签约分类，请前往',
    noShopCategories: '暂无店铺分类，请前往',
    applyContracting: '申请签约分类',
    createShopCategory: '创建',
    shopInfo: '店铺信息',
    propEditingDelTips: '当前正在修改该属性，',
    propDelTips: '确定要删除该属性值吗？',
    productSort: '商品排序',
    selectFirstCategory: '请先选择第一级类目',
    selectSecondCategory: '请先选择第二级类目',
    platformTopping: '平台置顶',

    // 发布商品
    selectProductCategory: '选择商品类目',
    editProductInfo: '编辑商品信息',
    editProductDetails: '编辑商品详情',
    physicalGoods: '实物商品',
    logisticsDelivery: '（物流发货）',
    virtualGoods: '虚拟商品',
    electronicCard: '电子卡券',
    bindCard: '关联卡券',
    noLogisticsRequired: '（无需物流）',
    comboGoods: '组合商品',
    productTemplates: '商品模板',
    templateImport: '（模板导入）',
    selectPlatformCategory: '选择平台分类',
    selectPlatformCategoryOnly: '选择平台分类（仅展示已签约分类）',
    selectShopCategory: '选择店铺分类',
    selectFirstLevelCategory: '选择第一级分类',
    selectSecondLevelCategory: '选择第二级分类',
    selectThirdLevelCategory: '选择第三级分类',
    selectedPlatformCategories: '已选平台分类',
    selectedShopCategories: '已选店铺分类',
    nextStep1: '选好了，下一步',
    nextStep2: '下一步',
    prevStep: '上一步',
    saveBtn1: '保存',
    saveBtn2: '保存并查看',
    selectedCategories: '已选分类',
    platformCategories: '平台分类',
    shopCategories: '店铺分类',
    selectLanguage: '选择语言',
    productSellingPoints: '商品卖点',
    productBrands: '商品品牌',
    selectText: '选择',
    specStock: '规格库存',
    totalInventory: '库存总和',
    chineseDetails: '中文详情',
    englishDetails: '英文详情',
    detailPagePreviewImage: '详情页预览图',
    parameteNameAndParameterValue: '参数名和参数值显示在商品详情开头，参数名不超过10字，参数值不超过20字',
    productDetailsContentLimitTxt: '商品详情内容字数过多，请重新输入！',
    // 虚拟商品
    otherSettings: '其他设置',
    instructions: '使用说明',
    enterInstructionsTip: '请输入使用说明',
    noWriteOffRequired: '无需核销',
    singleWriteOff: '单次核销',
    multipleWriteOffs: '多次核销',
    numberOfWriteOffs: '核销次数',
    expiryNumberOfWriteOffs: '有效核销次数',
    userMessage: '用户留言',
    expiryDate: '有效期',
    longTermValidity: '长期有效',
    unlimitedTime: '无限次',
    mostWriteOffs: '最多核销',
    validOnTheSameDay: '购买后当天有效',
    beforeTime: '（每天24点前可以使用）',
    afterPurchase: '购买后',
    validDays: '天内有效',
    validFrom: '在',
    to: '至',
    startDate: '开始日期',
    endDate: '结束日期',
    requiredField: '必填',
    addField: '添加字段',
    msgFieldTips: '购买商品时让买家输入留言，最多可设置 10 条',
    afterSalesService: '售后服务',
    supportApplyRefund: '支持买家申请退款',
    doNotSupportApplyRefund: '不支持买家申请退款',
    afterSalesServiceTips: '商品详情页将展示“不支持申请退款”的说明，买家如需退款只能主动联系商家处理。',
    msgCannotBePlainSpace: '不能为纯空格，请重新输入',
    // 规格
    addSpecPic: '添加规格图片',
    specValueCharacterLength: '规格值长度不超过20个字符',
    specNameNotNull: '规格名不能为空',
    selectSpecFirst: '请先选择规格',
    specName: '规格名',
    specValue: '规格值',
    specialWordSymbolTips: '不允许包含特殊字符',
    currentlyAdded: '当前已添加',
    productSpecCount: '组商品规格',
    groups: '组',
    cannotAddUnderThisSpec: '该规格名下不可添加',
    createNewSpecNameManually: '支持手动输入创建新规格名，鼠标点击或回车均可添加，规格名称不超过10个字符',
    // 提示
    postProductTips1: '选择准确分类，方便用户搜索，提高下单率',
    postProductTips2: '选择多语言需编辑多个商品卖点和商品详情，未设置的语言将显示默认语言',
    postProductTips3: '商品名称建议：商品描述+属性，长度不超过60个字符',
    postProductTips4: '商品卖点展示在商品详情标题下面，长度不超过100个字符',
    postProductTips5: '建议尺寸800*800像素，可拖动排序，最多上传9张，首图为主图',
    postProductTips6: '主图视频建议时长9-30秒，视频宽高和商品图保持一致',
    postProductTips7: '每个规格库存的总和',
    postProductTips8: '“同城配送”需在配送管理设置后才能生效',
    postProductTips9: '运费模板支持按地区、购买件数、按重量计算运费等',
    postProductTips10: '需要核销的商品会生成核销码，可提供给商家线下核销',
    postProductTips11: '规格名称不超过10个字符',
    postProductTips12: '仅支持为第一组规格设置规格值图片，建议尺寸：800 x 800像素，规格值长度不超过20个字符',
    postProductTips13: '第一个规格的规格值可以添加图片',
    postProductTips14: '选择内容进行批量填充',
    postProductTips15: '如无编码系统将自动生成',
    postProductTips16: '商品编码不能重复',
    postProductTips17: '商品中文名称不能为空',
    postProductTips18: '商品英文名称不能为空',
    postProductTips19: '商品中文卖点不能为空',
    postProductTips20: '商品英文卖点不能为空',
    postProductTips21: '请上传商品图片',
    postProductTips22: '商品发布成功后不可更改',
    postProductTips23: '商品发布后平台分类不可修改',
    postProductTips24: '商品发布后平台分类不可修改，请选择准确的分类',
    selectPlatformCategoryTips: '请选择平台分类',
    selectShopCategoryTips: '请选择店铺分类',
    enableSpec: '至少要启用一种商品规格',
    specValueCannotBeEmpty: '规格值不能为空',
    completeTheAddedSpec: '请先完善已添加的规格',
    dateErrTips1: '开始时间不能大于或等于结束时间',
    dateErrTips2: '结束时间不能少于或等于当前时间',
    dateErrTips3: '结束时间不能少于或等于开始时间',
    msgMaxLength: '最多可设置10条',
    validDaysEmptyTips: '请填写核销有效天数',
    validDateEmptyTips: '请选择核销有效日期',
    msgEmptyTips: '请填写已添加的留言',
    cannotModifyProdType: '已发布的商品不允许修改商品类型',
    uploadDetailPicTips: '为了保证图片清晰，请上传宽度大于940px的图',
    parameterTips: '请输入正确的参数',
    notAvailableSeparatePurchase: '组合选品不可单买且不会被买家搜索到，仅用于套餐和赠品的非主商品以及组合商品的关联商品',
    fillInTheNecessaryInfo: '请先完善商品必填信息！',
    chooseLanguage: '选择语言',
    skuLangTips: '选择多语言需编辑多个规格名称和规格值',
    attributeTips: '规格名称长度不超过10个字',
    attributeValueTips: '规格值不超过20个字',
    availableInventory: '现有库存',
    attributeNotDel: '仅剩一条规格值不能删除',
    warehouseStock: '仓库库存',
    stationStock: '门店库存'
  },
  productComm: {
    content: '评论内容',
    pics: '评论图片',
    recTime: '记录时间',
    no: '无',
    replyTime: '回复时间',
    postip: 'IP来源',
    score: '评价得分',
    isAnonymous: '是否匿名',
    replyContent: '掌柜回复',
    platformAudit: '平台审核',
    status: '审核',
    noPass: '审核未通过',
    pass: '审核通过',
    waitPass: '未审核',
    anonymousuUser: '匿名用户'
  },
  users: {
    name: '用户昵称'
  },
  prodTag: {
    prodManage: '管理分组商品',
    tips: '只能输入整数',
    scoreTip2: '排序号不能为空',
    prodTagSeq: '分组排序号',
    isDele: '】商品从分组中删除吗?',
    updateprodTagSeq: '修改排序号',
    returnProdManage: '返回分组管理',
    insertProd: '新增分组商品',
    oneStyle: '每行一种样式',
    twoStyle: '每行两种样式',
    threeStyle: '每行三种样式'
  },
  transport: {
    name: '模板名称',
    type: '模板类型',
    buyerPrice: '买家承担运费',
    shopPrice: '卖家包邮',
    setShopPrice: '设置包邮条件',
    chargePrice: '收费方式',
    byCount: '按件数',
    byWeight: '按重量',
    byVolume: '按体积',
    fullCount: '满件',
    fullWeight: '满重量',
    fullVolume: '满体积',
    distributableArea: '可配送区域',
    allRegions: '所有地区',
    addPrice: '点击添加地区和运费',
    freePostage: '指定条件免邮费',
    selArea: '指定区域',
    selCity: '请选择指定包邮城市',
    fullPieces: '满件/重量/体积',
    fullAmount: '满金额',
    fullAmount1: '且满金额',
    firstPiece: '首件(个)',
    transportationCost: '运费(元)',
    continuationPiece: '续件(个)',
    continuationCost: '续费(元)',
    firstWeight: '首重(kg)',
    continuedWeight: '续重(kg)',
    firstVolume: '首体积(m³)',
    continuedVolume: '续体积(m³)',
    freeShippCondi: '包邮条件',
    pwv: '件/重量/体积',
    pieces: '件',
    shippingIncluded: '包邮',
    yuan: '元',
    andFull: '且满',
    areaMenu: '区域操作'
  },
  address: {
    receiverTelephone: '公司座机',
    postalCode: '邮政编码',
    province: '省份',
    city: '城市',
    area: '区/县',
    detailed: '详细地址',
    addr: '地址',
    defaultAddr: '默认',
    defaultRefundAddr: '默认退货地址'
  },
  refund: {
    plat: '平台',
    platInter: '平台介入中',
    buyerWithdrawn: '买家已撤销',
    interTips: '用户申请平台介入售后，请在time前补充凭证',
    refundLog: '退款记录',
    platRejectRefund: '平台拒绝退款',
    platAgreeRefundSucceed: '平台同意退款成功',
    applyPlatInter: '申请平台介入',
    addProof: '添加凭证',
    areplenishProof: '补充凭证',
    uploadProof: '上传凭证',
    uploadProofTips: '请上传凭证',
    uploadProofLimit: '最多上传3张',
    proofDesc: '凭证说明',
    proofDescEmptyTips: '凭证说明不能为空',
    repealApplyInter: '撤销平台介入申请',
    platNote: '平台留言',
    closing: '交易结束',
    applyInter: '申请介入',
    interHandle: '介入处理',
    buyerApply: '买家已申请',
    merchantHasAgree: '卖家已同意退款，等待买家发货',
    buyerHasShipped: '买家已发货，等待快递拣货',
    receivedGoods: '卖家已收货',
    refundMoney: '卖家已退款'
  },
  order: {
    selectAddrTips: '请选择发货地址',
    notLogisticsTips: '未开通相关物流配置',
    selectPrinterTips: '请选择打印机',
    userlogoutTips: '该用户已注销',
    weixin: '微信',
    alipay: '支付宝',
    balance: '余额',
    payPal: '贝宝',
    wait: '等待',
    refund: '进行退款',
    orderDetail: '订单详情',
    lookRefunds: '查看退款',
    orderPickupS: '订单提货',
    contactBuyer: '联系买家',
    orderWriteOff: '订单核销',
    number: '订单编号',
    createTime: '下单时间',
    statusMsg: '请选择订单状态',
    checkDvyId: '请输入正确的快递单号！',
    checkDvyIdMsg: '请输入正确的物流单号！',
    query: '查询',
    exportOrder: '导出待发货订单',
    exportSales: '导出销售记录',
    pendingPayment: '待付款',
    EstimatedDeliveryTime: '预计发货时间:',
    shopPreferentialAmount: '商家优惠金额',
    platformPreferentialAmount: '平台优惠金额',
    uploadTips1: '请选择操作,同时只能上传一个文件',
    uploadTips2: '批量发货流程为：导出待发货订单，添加快递公司和单号后，选取该文件进而导入数据。',
    uploadTips3: '注意：',
    uploadTips4: '1.若存在正在退款或者已经退款或者已经部分发货的订单则无法批量发货。',
    uploadTips5: '2.导入期间请不要对导出的订单做发货操作，否则该订单将无法批量发货。',
    SelectFile: '选取文件',
    ImportingFiles: '导入',
    ExportingFiles: '导出',
    pleExpOrderFirst: '请先选择导出订单的时间',
    downloadTemplateTips1: '上传模板只能是 xls、xlsx格式!',
    downloadTemplateTips2: '上传模板大小不能超过 2MB!',
    fileUploadFail: '文件上传失败！',
    fileSuccess: '文件导入成功',
    sureToExport: '确定进行导出操作?',
    BulkShipping: '批量发货',
    confirm: '确定',
    cancel: '取消',
    addressee: '收货人',
    toBeShipped: '待发货',
    delivery: '发货',
    deliveryMsg: '买家付款后才可以发货',
    comprador: '买家',
    compradorMsg: '买家备注',
    invoiceMsg: '不开发票',
    pendingReceipt: '待收货',
    product: '商品',
    purchaseQuantity: '购买数量',
    transactionPrice: '成交单价',
    receivedGoods: '买家已收货',
    totalPrice: '总价',
    actualAmount: '实付金额',
    status: '订单状态',
    operation: '操作',
    paymentMethod: '支付方式',
    amountDue: '应付金额',
    createOrder: '创建订单（成功）',
    payment: '订单付款（成功）',
    deliverys: '订单发货（成功）',
    completed: '完成订单（成功）',
    cancelOrder: '取消订单（成功）',
    logs: '订单日志',
    toBeEvaluated: '待评价',
    success: '成功',
    fail: '失败',
    refundId: '退款编号',
    applicationType: '申请方式',
    onlyRefund: '仅退款',
    refundAndMoney: '退款退货',
    orderAmount: '订单金额',
    refundAmount: '退款金额',
    applicationTime: '申请时间',
    refundStatus: '退款状态',
    processingRefunds: '处理退款',
    shipped: '买家已发货',
    waitingRefund: '等待退款',
    refundsuccessfully: '退款成功',
    refunding: '退款中',
    refundFailed: '退款失败',
    withdrawsApplication: '买家撤销申请',
    refusedRefund: '商家拒绝退款',
    refundClosed: '退款关闭',
    exportForm: '导出报表',
    viewOrder: '查看订单',
    recordId: '记录ID',
    shopId: '店铺ID',
    orderId: '订单ID',
    refundAll: '订单项ID 全部退款是0',
    orderPayment: '订单支付流水号',
    thirdParty: '第三方退款单号(微信退款单号)',
    orderPaymentMethod: '订单支付方式: 1 微信支付 2 支付宝',
    orderPaymentName: '订单支付名称',
    buyerId: '买家ID',
    returnQuantity: '退货数量',
    returnAmount: '退款金额',
    applicationMethod: '申请类型:1,仅退款,2退款退货',
    processingStatus: '处理状态:1为待审核,2为同意,3为不同意',
    processingRefundStatus: '处理退款状态: 0:退款处理中 1:退款成功 -1:退款失败',
    sellerProcessingTime: '卖家处理时间',
    refundTime: '退款时间',
    fileCredentialjson: '文件凭证json',
    reasonForApplication: '申请原因',
    sellerNote: '卖家备注',
    logisticsCompanyName: '物流公司名称',
    logisticsNumber: '物流单号',
    deliveryTime: '发货时间',
    timeOfReceipt: '收货时间',
    commentsAfterReceipt: '收货备注',
    pleEntShipInfo: '请输入发货信息',
    senderName: '发件人姓名',
    shiPhoNum: '发货人手机号',
    deliveryAddress: '发货地址',
    orderType: '订单类型',
    orderMold: '订单类别',
    pleaseSelectOrderType: '请选择订单类型',
    pleaseSelectOrderMold: '请选择订单类别',
    theRecipientSName: '收件人姓名',
    pleaseEnRecipName: '请输入收件人姓名',
    pleaseEnterNumber: '请输入收件人手机号',
    pleaseSePayt: '请选择支付方式',
    afterSalesStatus: '售后状态',
    pleSelAfterSalesSta: '请选择售后状态',
    logisticsType: '物流类型',
    pleSelTypeOfLog: '请选择物流类型',
    transaQuantity: '成交单价(元)/数量',
    actualPaymentAmount: '实付金额(元)',
    buyerConsignee: '买家/收货人',
    afterSale: '售后',
    afterSaleTips: '该数据包含买家申请、卖家接受、买家发货、申请介入、介入处理列表数据',
    buyerApplication: '买家申请',
    refundApplicationInProgress: '退款申请中',
    sellerAccepts: '卖家接受',
    buyReceiving: '买家收货',
    selShipment: '卖家发货',
    buyShipment: '买家发货',
    sellerReceipt: '卖家收货',
    refundSuccessfully: '退款成功',
    successfulTransaction: '交易成功',
    transactionFailed: '交易失败',
    sameCityDelivery: '同城配送',
    selfMention: '自提',
    onlineDelivery: '线上发货',
    selfDelivery: '线下发货',
    toOpen: '去开通',
    deliveryPrintType: '发货类型',
    printInfo: '打印面单',
    printer: '打印机',
    selfMentio: '自提',
    integral: '积分',
    includingFreight: '含运费',
    total: '共',
    modifyTheAmount: '修改金额',
    unpaid: '未付款',
    pointsPayment: '积分支付',
    wecProPay: '微信小程序支付',
    alipayPCPayment: '支付宝PC端支付',
    wechatScanCodePayment: '微信扫码支付',
    wechatH5Payment: '微信H5支付',
    weclAccountPay: '微信公众号支付',
    alipayH5Payment: '支付宝H5支付',
    alipayAPPPayment: '支付宝APP支付',
    wechatAPPPayment: '微信APP支付',
    balancePayment: '用户余额支付',
    payPalPayment: 'PayPal支付',
    refundApplication: '退款申请中',
    partialRefundSucc: '部分退款成功',
    noAfterSales: '暂无售后',
    seeDetails: '查看详情',
    modifyLogistics: '修改物流',
    pickUp: '提货',
    Writeoffs: '核销',
    refundInformation: '退款信息',
    noData: '暂无数据',
    ifModifyTheLog: '若修改物流信息，请仔细填写并核对',
    package: '包裹',
    deliveryMethod: '发货方式',
    needLogistics: '需要物流',
    courierCompany: '快递公司',
    trackingNumber: '快递单号',
    packageName: '包裹名称',
    amountOfGoods: '商品数量',
    logisticsCompany: '物流公司',
    save: '保存',
    backToModify: '返回修改',
    confirmTheChanges: '确认修改',
    normalOrder: '普通订单',
    groupPurchaseOrder: '团购订单',
    spikeOrder: '秒杀订单',
    virtualOrder: '虚拟商品订单',
    physicalOrder: '实物商品订单',
    comboOrder: '组合商品订单',
    electronicCardOrder: '电子卡券订单',
    giveawayPord: '赠品',
    combo: '组合',
    expressDelivery: '快递',
    noNeedRequired: '无需快递',
    distribution: '快递配送',
    logEmpty: '物流单号不能为空',
    logistics: '物流配送',
    noLogIsChanged: '未更改任何物流信息',
    exportReport: '导出报表只根据选择的下单时间内导出',
    orderInfCollationXls: '订单信息整理.xlsx',
    pendingOrderInformation: '待发货订单信息.xlsx',
    orderShipping: '订单发货',
    productNumber: '商品编号',
    reimburse: '退款',
    canShipQuantity: '可发货数量',
    shipmentCompleted: '发货完成',
    waitWriteOff: '待核销',
    waitForDelivery: '等待发货',
    numberOfShipments: '发货数量',
    delType: '配送方式',
    selfConOrd: '快递配送',
    seleOrd: '请选择需要发货的商品',
    oderNumNo: '订单可发货数量不足',
    numNotZero: '发货数量不能为0',
    fhNumNotZero: '发货数量不能为空',
    seleCouCom: '请选择快递公司',
    entCouNum: '请输入快递单号',
    pledSeleMet: '请选择发货方式',
    requestARefund: '退款申请中',

    partialDelivery: '部分发货',
    pendingPickUp: '待提货',
    shippingInformation: '配送信息',
    virtualInfo: '虚拟商品信息',
    virtualMsg: '全部留言',
    selfMentionCode: '核销码',
    pleEntPickupCode: '请输入核销码',
    doNotEntPickupCode1: '不输入核销码，默认为无需校验，直接提货',
    doNotEntPickupCode2: '不输入核销码，默认为无需校验，当前订单全部核销',
    pickupCodeError: '提货码错误，请输入正确的提货码',

    modifyOrderAmount: '修改订单金额',
    modificatioAmount: '修改用户支付金额，在降价时，平台优惠、分类抽佣金额都按照比例降低,在升价时订单抽佣按金额按比例提升,而平台优惠金额不会超过用户下单时平台优惠金额。请谨慎更改',
    unitPrice: '单价',
    quantity: '数量',
    price: '价',
    totalDiscount: '优惠总额',
    userPayunt: '用户支付金额',
    platforlowance: '平台津贴',
    estimancome: '预计收入',
    reducedAnt: '减少金额',
    shippingAmunt: '运费金额',
    decreaslowance: '平台津贴减少',
    theOraterThan0: '订单金额必须大于0',
    waitingFosPayment: '等待买家付款',
    waitiooShip: '等待商家发货',
    waitingFeGoods: '等待买家收货',
    waitingtion: '等待买家评价',
    commoditful: '商品交易成功',
    commodityFailed: '商品交易失败',
    commodited: '商品待成团',
    buyerDidNTime: '买家在规定时间未付款，订单将自动取消。',

    buyPleF: '买家已付款，请商家尽快发货。',
    shelF: '商家已发货，等待确认收货。',
    buyA: '买家已自提，等待确认收货。',
    buyB: '订单已完成，商品交易成功。',
    orderCanc: '订单已取消，商品交易失败。',
    outTimeCanOrd: '规定时间内未成团，订单将自动取消。',
    submitOrders: '提交订单',
    theBuyerHasPaid: '买家已付款',
    buyerHasMentioned: '买家已自提',
    shippedBySeller: '卖家已发货',
    shippedByBuyer: '买家 已发货',
    buyerHasReceived: '买家已收货',
    waybillNumber: '运单号',
    logisticsStatus: '物流状态',
    noRecord: '没有记录',
    collected: '已揽收',
    delivering: '运输途中',
    haveBeenReceived: '已签收',
    reachTheDestinationCity: '达到目的城市',
    problemPiece: '问题件',
    logisticsInformation: '物流信息',
    noLogisticsInformation: '暂无物流信息，请您稍后再试',
    merchantHasShippedWa: '商家已发货，等待快递拣货',
    buyerHasPaidWa: '买家已支付完成，等待发货',
    buyerSubmittedAnOrder: '买家提交了订单，等待系统确认',
    recipientInformation: '收货人信息',
    deliveryPerson: '提货人',
    paymentInformation: '付款信息',
    paymentTime: '付款时间',
    no: '暂无',
    buyerInformation: '买家信息',
    buyerSNickname: '买家昵称',
    buyerPhone: '买家电话',
    buyerMessage: '买家留言',
    orderRemarks: '订单备注',
    shippingFees: '配送费用',
    orderLog: '订单日志',
    orderUpdate: '订单更新（成功）',
    orderPickup: '订单自提',
    typeOfRefund: '退款类型',
    pleaseChooseHowToApply: '请选择申请方式',
    withdrawApplication: '撤回申请',
    addCategory: '增加分类',
    addBrand: '增加品牌',
    withdraw: '撤回',
    commodityUnitPriceYuanQuantity: '商品单价(元)/数量',
    commodityCondition: '商品状况',
    refunds: '退货退款',
    goodsReceived: '已收到商品',
    goodsNotReceived: '未收到商品',
    wholeGoodsRefund: '整单退款',
    singleItemRefund: '单个物品退款',
    wholeOrderRefund: '整单退款',
    orderSerialNumber: '订单流水号',
    pleaseSelectTheDeliveryAddress: '请选择收货地址',
    deliveryAddressCannotBeEmpty: '收货地址不能为空',
    refundProcessing: '退款处理',
    requestARefundT: '申请退货退款',
    agreeToReturnAndRefund: '同意退货退款',
    buyer: '买家',
    merchant: '商家',
    confirmReceipt: '确认收货',
    successfulProcessing: '处理成功',
    issueARefund: '发放退款',
    orderActuallyPaid: '实付金额',
    reasonForReturn: '退款原因',
    refundInstructions: '退款说明',
    logisticsDetails: '物流详情',
    logisticsName: '物流名称',
    returnCertificate: '退款凭证',
    logisticsCertificate: '物流凭证',
    refundGoods: '退款商品',
    applyForApproval: '申请审批',
    agree: '同意',
    disagree: '不同意',
    returnStatus: '退货状态',
    received: '已收货',
    unreceived: '未收货',
    returnRequest: '退货申请',
    denialReason: '拒绝原因',
    merchantNotes: '商家备注',
    confirmTreatment: '确定处理',
    refundLog: '退款日志',
    submitARefundRequestWa: '提交退款申请, 等待商家处理',
    merchantHasProcessedWaBuy: '商家已处理, 等待买家发货',
    merchantHasProcessedWaSh: '商家已处理, 等待商家退款',
    buyerShipmentWaPro: '买家发货, 等待商家收货',
    merchantHasReceivedWaShGr: '商家已收货,等待商家同意退款',
    agreeToRefund: '同意退款,等待发放退款',
    buyerHasWithdrawn: '买家已撤销（或超时）',
    merchantRejected: '商家已拒绝',

    ordinaryExpress: '普通快递',
    buyerMention: '买家自提',
    prodTotalPrice: '商品总价',
    returnType: '退款类型',
    returnMethod: '退款方式',
    returnDetails: '退款详情',
    discount: '优惠',
    agreeToRefundA: '同意退款',
    refusalToRefund: '拒绝退款',
    modifyAmountSuccess: '修改金额成功',
    num: '数量',

    invoiceStatus: '发票状态',
    applicationInProgress: '申请中',
    invoiceIssued: '已开票',
    headerType: '发票抬头',
    headerName: '发票名称',
    uploadTime: '上传时间',
    unit: '单位',
    personal: '个人',
    invoiceType: '发票类型',
    normalInvoiceType: '电子普通发票',
    invoiceTaxNumber: '发票税号',
    invoiceUpload: '发票上传',
    refundTip: '该订单已上传发票，确认退款后请查看并处理已上传发票的内容。',
    uploadInvoiceTip: '请选择文件上传',
    uploadInvoiceTip1: '该订单未确认收货, 是否继续上传发票?',
    uploadInvoiceTip2: '用户可能变更了发票申请信息，是否继续提交?',
    uploadInvoiceTip4: '请选择文件并上传',
    lastProdRefundTip: '该退款申请项是订单中最后一件商品，同意退款后运费￥[d]会一并退还给用户',
    lastProdRefundTipPlatform: '该退款申请项是订单中最后一件商品，同意退款后平台包邮运费￥[d]会返还平台',
    freeFreight: '商家包邮减免',
    platformFreeFreightAmount: '平台包邮减免',
    salesPrice: '销售价',
    score: '积分'
  },
  hotSearch: {
    contentEs: '西语内容',
    contentEn: '英语内容',
    titleEs: '西语标题',
    titleEn: '英语标题',
    seq: '排序号',
    length50: '长度在1到50个字符内',
    length250: '长度在1到255个字符内'
  },
  homes: {
    home: '首页',
    names: '亚米商城-商家端',
    shortName: '亚米',
    updatePwd: '修改密码',
    exit: '退出',
    noPermission: '401',
    notFound: '404',
    login: '登录',
    verifyTip: '向右拖动滑块填充拼图',
    isExit: '确定进行[退出]操作?',
    layout: '主入口整体布局',
    failed: '请求菜单列表和权限失败，跳转至登录页！！',
    productDetails: '产品详情',
    oldPwd: '原密码',
    newPwd: '新密码',
    confirmPassword: '确定密码',
    userName: '账号',
    istrue: '确认密码与新密码不一致',
    yes: '确定',
    no: '取消',
    loading: '拼命加载中',
    cloCurrTab: '关闭当前标签页',
    cloOtherTab: '关闭其它标签页',
    cloAllTab: '关闭全部标签页',
    refreCurrTab: '刷新当前标签页',
    sorry: '抱歉！您访问的页面',
    miss: '失联',
    la: '啦',
    goBack: '返回上一页',
    enterMain: '进入首页',
    noPermissionAccessPage: '您没有权限访问此页面',
    register: '注册',
    readConsent: '我已阅读同意',
    readFirst: '请先阅读',
    registerProtocol: '商家注册协议',
    shopProtocol: '商家入驻协议',
    alreadyAccount: '已有账户',
    goToLogin: '前去登录',
    registerSuccess: '注册成功！即将跳转到登录页...',
    registerSuccess2: '注册成功！即将跳转到申请开店页...',
    InputCorrectPhone: '请输入正确的手机号',
    InputCorrectUsername: '用户名为非纯数字的4-16位字母、数字或下划线',
    InputCorrectPassword: '密码长度在6-20个字符之间',
    registrationTips: '免费开店，商户入驻',
    registration: '立即注册',
    notFoundTips: '抱歉！您访问的页面失联啦...',
    backPrePage: '返回上一页',
    toHome: '进入首页'
  },
  category: {
    categoryPicture: '分类图片',
    recPicSize: '建议图片尺寸为',
    categoryIcon: '分类图标',
    categoryName: '分类名称',
    categoryParent: '上级分类',
    deductionRate: '分类扣率',
    categoryPicNull: '分类图片不能为空',
    categoryIconNull: '分类图标不能为空',
    categoryNameNull: '分类名称不能为空',
    categorySelector: '分类选择',
    chooseProdCateg: '选择商品分类',
    currCho: '你当前的选择是',
    isItAComtionPro: '是否为组合商品',
    generalMerchandise: '普通商品',
    combinationGoods: '组合选品',
    haveReadFol: '确认选择',
    exportXls: '店铺分类.xlsx'
  },
  publics: {
    seq: '排序',
    seqError: '顺序值不能小于0',
    category: '分类名称',
    categoryInputTips: '请输入分类名称',
    categoryCn: '分类中文名称',
    categoryEn: '分类英文名称',
    categoryNoNull: '分类名称不能为空',
    image: '图片',
    imageNoNull: '图片不能为空',
    videoNoNull: '视频不能为空',
    status: '状态',
    serial: '序列号',
    serialNoEnter: '请输入排序值',
    isTop: '置顶',
    cancelTop: '取消置顶',
    title: '标题名称',
    titleNoEnter: '请输入标题名称',
    addressee: '收货人',
    mobilePhone: '手机号码',
    deliveryAddr: '收货地址',
    freight: '运费',
    type: '类型',
    defaultType: '默认类型',
    customType: '自定义类型',
    list: '列表样式',
    noNull: '不能为空',
    chooseStore: '选择店铺',
    selectActivity: '选择活动',
    normal: '正常',
    stop: '暂 停',
    restore: '恢复',
    executeImmediately: '立即执行',
    disable: '禁用',
    able: '可用',
    label: '标签名称',
    releaseTime: '发布时间',
    updateTime: '更新时间',
    yes: '是',
    no: '否',
    content: '内容',
    deletes: '确定要进行删除操作吗？',
    operation: '操作成功',
    selectOne: '请至少选择一个发送类型！',
    cancel: '撤销',
    publicar: '公布',
    des: '描述',
    default: '是否默认',
    remark: '备注',
    name: '名称',
    nameEg: '名, 例：',
    profilePicture: '用户头像',
    batchStopte: '批量暂停',
    and: '并且',
    batchRestore: '批量恢复',
    batchExecuteImmediately: '批量立即执行',
    batchDelete: '批量删除',
    operating: '操作',
    parentCa: '上级',
    UpperShelf: '上架',
    metUpperShelf: '商家上架',
    LowerShelf: '下架',
    metLowerShelf: '商家下架',
    pendingReview: '待审核',
    violationShelf: '违规下架',
    importe: '导 入',
    noAbleTabErr: '未能找到可用标签页',
    setNull: '清空',
    relateSingleProduct: '关联单品',
    selectAll: '全选',
    selected: '已选',
    batchSetting: '批量设置',
    saveBatchSetting: '保存批量设置',
    hadLowerShelf: '已下架',
    code: '编码',
    maxIndexImgNumOfPlatform: '每个用户端的最大轮播图数量为10',
    currentSelectAll: '当页全选',
    newUnreadMessages: '您有新的未读消息'
  },
  addr: {},
  sys: {
    userName: '用户名',
    nickName: '昵称',
    mobile: '手机号',
    beanName: 'bean名称',
    beanNameEg: 'spring bean 名称, 例如: testTask',
    beanNameNoNull: 'beanName不能为空',
    userAction: '用户操作',
    requestMerthod: '请求方法',
    requestParameter: '请求参数',
    executionTime: '执行时长(毫秒)',
    consumingTime: '耗时(毫秒)',
    ipAddress: 'IP地址',
    creationTime: '创建时间',
    parameteName: '参数名',
    parameteNameNoNull: '参数名不能为空',
    parameterValue: '参数值',
    parameterValuenoNull: '参数值不能为空',
    parameteNameAndParameterValue: '参数名和参数值显示在商品详情开头，参数名不超过10字，参数值不超过20字',
    parentMenu: '上级菜单',
    menuUrl: '菜单Url',
    menuUrlNoNull: '菜单URL不能为空',
    authorization: '授权标识',
    menuIcon: '菜单图标',
    menu: '菜单',
    menuNameNoNull: '菜单名称不能为空',
    catalog: '目录',
    button: '按钮',
    separated: '多个用逗号分隔,如:',
    menuIconName: '菜单图标名称',
    icon: '图标',
    roles: '角色',
    des: '描述',
    operation: '操作',
    deletes: '你确定要删除此商品吗？',
    batchDelete: '批量删除',
    password: '密码',
    confirmPassword: '确认密码',
    confirmUpdate: '确定修改',
    email: '邮箱',
    connInfo: '联系方式',
    roleName: '角色名称',
    roleNameNoNull: '角色名称不能为空',
    authorize: '授权',
    methodName: '方法名称',
    methodNameNoNull: '方法名称不能为空',
    params: '参 数',
    cronExpression: 'cron 表达式',
    cronExpressionEg: '如: 0 0 12 * * ?',
    cronExpressionNoNull: 'cron表达式不能为空',
    areaName: '地区',
    superiorAreaL: '上级地区',
    usernameNotEmpty: '用户名不能为空',
    makeSure: '确定进行',
    makeSureDelete: '删除图片可能会影响到页面展示，确定进行',
    passwordNoNull: '密码不能为空',
    oldPwdNotNull: '原密码不能为空',
    newPwdNotNull: '新密码不能为空',
    confirmPassNoNull: '确认密码不能为空',
    passworldContrast: '确认密码与密码输入不一致',
    retrievePassword: '找回密码',
    emailaError: '邮箱格式错误',
    emailaNoNull: '邮箱不能为空',
    mobilePhoneError: '手机号格式错误',
    mobilePhoneNoNull: '手机号不能为空',
    userNameNoNull: '用户名不能为空',
    nickNameBetween: '昵称长度要在2-20之间',
    nickNameNoNull: '昵称不能为空',
    userLength: '用户名长度要在2-20之间',
    content: '全站推荐使用SVG Sprite, 详细请参考:icons/index.js 描述',
    regionalKeyword: '地区关键词',
    jobId: '任务ID',
    superAdmin: '（超级管理员）',
    pleaseSelectAuth: '请选择授权项！'
  },
  platform: {
    platform: '用户端',
    applets: '小程序',
    mobile: '移动端',
    pc: 'pc端'
  },
  station: {
    stationAdd: '新增门店',
    stationEdit: '编辑门店',
    stationLogo: '门店logo',
    stationLogoTips: '建议图片尺寸为 80*80像素',
    stationImg: '门店图片',
    stationImgTips: '最多上传9张',
    stationUse: '门店用途',
    stationUseSelTips: '请选择门店用途',
    selfPickup: '到店自提/核销',
    sameCityDelivery: '同城配送',
    stationTips: '门店都将提供虚拟商品核销服务',
    deliveryAreaNullTips: '请点击地图配置门店配送区域',
    prodStation: '商品门店',
    stationName: '门店',
    stationNames: '门店名称',
    stationAddr: '门店地址',
    addr: '地址',
    number: '号码',
    account: '账号',
    close: '关闭',
    closeAndUpdate: '关闭并返回刷新',
    open: '开启',
    business: '营业',
    platformClosed: '平台关闭',
    // underReview: '审核中',
    underReview: '上线待审核',
    auditFailure: '审核失败',
    successfulAudit: '审核成功',
    businessStatus: '营业状态',
    withdrawCash: '提现待审核',
    pendingReview: '待审核'
  },
  form: {
    formName: '报表名称',
    day: '天',
    week: '周',
    month: '月',
    reportType: '报表类型'
  },
  group: {
    actName: '活动名称',
    groupType: '成团人数',
    groupOrderCount: '成团订单数',
    actStartTime: '活动开始时间',
    actTime: '活动时间',
    actEndTime: '活动结束时间',
    actStatus: '活动状态',
    prodInfo: '商品信息',
    groupNum: '参团人数',
    totalOrderAmount: '订单总金额',
    groupTime: '开团时间',
    orderStatus: '订单状态',
    actProName: '活动商品名称',
    proImg: '商品图片',
    proActStatus: '商品活动状态',
    groupSkuId: '拼团活动商品规格id',
    groupProdId: '拼团活动商品id',
    skuId: '商品规格id',
    actPrice: '活动价格',
    leaderPrice: '团长价格',
    sellNum: '已售数量',
    waitGroup: '待成团',
    notOpenGroup: '未开团',
    waitGroupUnpay: '待付款',
    inAGroup: '拼团中',
    succ: '拼团成功',
    failGroup: '拼团失败',
    groupOrdId: '团单ID',
    groupId: '拼团ID',
    actProId: '活动商品ID',
    skuName: '规格名',
    expired: '已失效',
    startUsing: '已启用',
    notEnabled: '未启用',
    offlineViolation: '违规下架',
    moderated: '等待审核',
    over: '已结束',
    groupOrdStatus: '拼单状态',
    groupsNum: '成团人数',
    groupStratTime: '拼团开始时间',
    groupEndTime: '拼团结束时间',
    actLabel: '活动标签'
  },
  live: {
    view: '查看',
    addNewLiveRoom: '新增直播间',
    viewLRoomInfo: '直播间信息',
    liveTime: '直播的时间',
    liveName: '直播间名称',
    anchorName: '主播昵称',
    anchorMobile: '主播手机号',
    liveStatus: '直播间状态',
    living: '直播中',
    offline: '下线',
    noStart: '未开始',
    finished: '已结束',
    stop: '已暂停',
    numberLimit: '请输入一个小于10000000000000的数',
    over: '已结束',
    liveStartTime: '直播开始时间',
    liveEndTime: '直播结束时间',
    pleaseEnteThan0: '请输入大于0的整数或者保留两位小数的正数',
    productBeEmpty: '商品封面不能为空',
    startTime: '直播时间',
    notPinned: '未置顶',
    pinned: '已置顶',
    recommendedSize: '建议尺寸',
    pixel: '像素',
    prod: '直播商品',
    chooseStartDate: '选择开始日期',
    chooseEndDate: '选择结束日期',
    liveBackgroundImage: '直播背景图',
    liveRoomNameCannotBeEmpty: '直播间名称不能为空',
    anchoBeEmp: '主播昵称不能为空',
    anchorMobileBeEmp: '主播手机号不能为空',
    backEmpty: '背景图不能为空',
    hostSnPict: '主播分享图不能为空',
    liveEmpty: '直播频道封面不能为空',
    liveBy: '直播时间不能为空',
    liveProductCover: '直播商品封面',
    returnment: '返回直播间管理',
    addLicts: '新增直播间商品',
    edit: '编辑',
    recomImaSizeIs: '图片建议大小为',
    anchorSharingPicture: '主播分享图',
    liveCoverImage: '直播封面图',
    liveTip1: '填写主播在商城所使用的账号昵称',
    liveAnchorNotExist: '该主播已经注销账号',
    liveProdTip: '请选择直播商品',
    liveAnchorError: '该主播账号已被平台禁用',
    liveTimeTip: '开始和结束时间间隔必须大于30分钟，不得超过24小时'
  },
  coupon: {
    couponName: '优惠券名',
    couponType: '优惠券类型',
    couponTypeTips: '请选择优惠券类型',
    voucher: '代金券',
    yuan: '元',
    discountCoupon: '折扣券',
    excCerti: '兑换券',
    timeToMarket: '投放时间',
    chooseLaunchTime: '选择投放时间',
    launchTimeTip: '投放时间不得小于当前时间',
    launchTimeTip1: '投放时间不能为空',
    launchTimeTip2: '失效时间不能早于等于投放时间',
    startTime: '生效时间',
    expire: '有效期',
    endTime: '失效时间',
    effectiveType: '生效类型',
    expStatus: '状态',
    notExp: '未过期',
    exp: '过期',
    editStatusErrorTips: '优惠券处于违规下线或待审核状态，不能设置投放状态',
    launchStatus: '投放状态',
    launched: '投放',
    waitLaunch: '暂不投放',
    waitAutoLaunch: '自动投放',
    launchTip: '系统会根据优惠券投放时间进行自动投放，投放后状态为投放',
    launchTip1: '当优惠券过期，优惠券状态为取消投放',
    cancelLaunch: '取消投放',
    illOff: '违规下架',
    waitReview: '等待审核',
    stock: '库存',
    receiveDirectly: '直接领取',
    exchangeOrSystemIssue: '商家发放',
    getWay: '获取方式',
    pleaseSelectCoupon: '请选择优惠券',
    discountVoucher: '折扣券'
  },
  distribution: {
    commAmount: '佣金数额',
    distributor: '分销员',
    phoneNum: '手机号',
    distriStatus: '分销状态',
    waitPayment: '待付款',
    waitSettle: '待结算',
    settled: '已结算',
    invOrder: '订单失效',
    orderNumber: '订单号'
  },
  distributionProdLog: {
    lapseCase: '失效原因',
    lapse0: '正常',
    lapse1: '分销佣金大于或者等于订单项实付金额',
    lapse2: '订单项售后成功',
    lapse3: '分销佣金小于0.01'
  },
  shop: {
    polygonPathOverNum: '最多只能选择12个坐标点哦!',
    resetMap: '重置',
    hotTitle: '热搜标题',
    hotContent: '热搜内容',
    recDate: '录入时间',
    enableStatus: '启用状态',
    notEna: '未启用',
    ena: '启用',
    noticeContent: '公告内容',
    isTop: '是否置顶',
    ioType: '收支类型',
    pay: '支出',
    income: '收入',
    amountType: '金额类型',
    amoSett: '待结算金额',
    notThrough: '未通过',
    notAudit: '未审核',
    withdrawalDetail: '详情',
    avaStoAmo: '可用店铺金额',
    unavaBalance: '不可用余额',
    summAmo: '总结算金额',
    beforeAmount: '改变前金额',
    afterAmount: '改变后金额',
    changeAomunt: '改变金额',
    reason: '资金变化原因',
    userPay: '用户支付',
    userRece: '用户确认收货',
    userRefund: '用户退款申请',
    withApply: '提现申请',
    withNoPass: '提现申请被拒绝',
    withPass: '提现申请通过',
    unreviewed: '未审核',
    succIss: '发放成功',
    failIss: '发放失败',
    withdrawalAmount: '提现金额',
    withdrawalTime: '提现时间',
    withdrawalStatus: '提现状态',
    dedAmo: '扣除订单分销金额',
    supplierName: '供应商名称',
    supplierProdImportTip: '已添加的商品不为空，不支持导入',
    tel: '座机号',
    contactName: '联系人',
    contactTel: '联系电话',
    phoneNumber: '手机号码',
    supplierCategoryName: '供应商分类',
    exportTip: '是否导出供应商信息？',
    exportProdTip: '导出的商品信息根据已保存的内容导出，是否导出供应商商品信息？',
    exportProdTip_store: '导出的商品信息根据已保存的内容导出，是否导出盘点商品信息？',
    exportProdTip_physical: '导出的商品信息根据已保存的内容导出，是否导出实物盘点信息？',
    importProdTip: '只能导入未添加的商品噢~',
    disableCategoryTip: '确认禁用此供应商分类？禁用后不可新增此类供应商',
    fileNullTip: '请选择要上传的文件',
    exportWarn: '供应商地区为下拉框联动选项，故无法使用导入功能，请于导入供应商后手动添加。',
    comInfoSorXls: '供应商信息整理.xlsx',
    fileName: '供应商信息模板.xlsx',
    prodFileName: '供应商商品模板.xlsx',
    exProdFileName: '供应商商品信息整理.xlsx',
    supplierProdCount: '供应商商品数量',
    editSupplierProd: '编辑供应商商品',
    exportProdTip1: '导入根据商品编码进行导入，即以一个商品规格为单位，若该商品规格无商品编码，请补充后再进行导入。',
    exportProdTip2: '最小订货量:下采购订单时，需要按照最小订货数量进行采购，不填写默认为1。',
    exportProdTip3: '采购价:不填写时默认为0.01。',
    exportProdTip4: '导入默认保存，若导入相同的商品编码，只保存第一条数据。',
    supplierProd: '供应商商品',
    addProd: '添加商品',
    addItem: '添加单品',
    editProd: '编辑商品',
    minOrderQuantity: '最小订货量',
    purchasePrice: '采购价(元)',
    addBankCard: '添加银行卡',
    bankName: '银行名称',
    pleEntTheBankName: '请输入银行名称',
    bankAccBranch: '银行卡开户行',
    plnterThntBh: '请输入银行开户支行',
    bankCaccount: '银行卡账号',
    pleaseEarNuber: '请输入银行卡账号',
    bankCardOwner: '银行卡账户名',
    pleaEnTheN: '请输入银行卡账号持有人姓名',
    withdraw: '提现',
    storeName: '店铺名称',
    withdraBala: '可提现余额',
    withdraBalaless: '提现金额不能少于1.00元',
    withdr: '本次提现',
    pleEnWithAmo: '请输入提现金额',
    withdrawAll: '全部提现',
    unToTime: '可提现金额不足1元，本次无法提现',
    debitCard: '到账银行卡',
    defaultBankCard: '默认银行卡',
    cannotBankCard: '不能删除默认银行卡',
    setAsDefault: '设为默认',
    noData: '暂无数据',
    smsVerification: '短信验证',
    getVerificationCode: '获取验证码',
    verifySMS: '验证短信将发送到商家的接收通知手机号',
    maximumInput: '最多可输入60字，特殊字符会被过滤',
    pleaseCheck: '请注意查收',
    withdrawaEmpty: '可提现余额不能为空',
    pleaseEnteCode: '请输入验证码',
    pleSelTheBankCard: '请选择到账银行卡',
    passed: '已通过',
    notPass: '未通过',
    platformOffline: '违规下线',
    failed: '未通过',
    title: '标题',
    content: '内容',
    announcementTitle: '公告标题',
    titCanNoBlank: '标题不能为空',
    cityCannotBeEmpty: '城市不能为空',
    provinceCannotBeEmpty: '省份不能为空',
    districtCounEmpty: '区/县不能为空',
    newShipAdd: '新增收货地址',
    modifyShipAdd: '修改收货地址',
    consigneeName: '收货人姓名',
    companyLandline: '公司座机',
    coneeNameCanEmpty: '收货人姓名不能为空',
    addressCannotBeEmpty: '地址不能为空',
    pleeNormaeNumF: '请输入正常的手机号格式',
    pleaseInputNumber: '请输入正确格式的号码',

    sameCityDelFun: '同城配送功能',
    deliSerProByYou: '启用后，买家下单可以选择同城配送，由你提供上门配送服务。',
    shopAdress: '店铺地址',
    startingFeeY: '起送费(元)',
    startingFee: '起送费',
    phone: '电话',
    charges: '收费标准',
    chaDelFeeByReg: '按区域收取配送费',
    delFeeChaDis: '按距离收取配送费',
    chaFixDelFeeByRe: '配送区域内商品会不计算距离，按区域收取固定配送费。',
    byWalkDis: '配送费按地图直线距离计算。',
    deliveryFee: '配送费',
    deliveryFeeY: '配送费(元)',
    costAllocation: '费用配置',
    withinKm: 'km内按',
    yuanToChaDelFee: '元来收取配送费，每超出',
    incInDelFees: '配送费增加',
    storeLocation: '店铺所在位置',
    renewalCharge: '续重收费',
    commodityWeight: '商品重量',
    pleaseEnterTheWeight: '请输入重量',
    noExtraCharge: 'kg内不额外收费，每超出',
    renewalFeeIncrease: '续重费增加',
    stargFeeCannoEmp: '起送费不能为空',
    delFeeCannoEmp: '配送费不能为空',
    costCannotBeEmpty: '费用不能为空',
    excDisFeeCanBeEm: '超出距离费用不能为空!',
    distanceCannotBeEmpty: '距离不能为空!',
    weightCannotBeEmpty: '重量不能为空!',
    myShop: '我的店铺',
    storeStatus: '店铺状态',
    nonactivated: '未开通',
    closed: '停业中',
    inOperation: '营业中',
    bindMobilePhoneNumber: '绑定手机号',
    shopAddress: '店铺详细地址',
    shopDescription: '店铺描述',
    shopLogo: '店铺logo',
    businessLicense: '营业执照',
    frontOfIDCard: '身份证正面',
    idCardReverse: '身份证反面',
    submitChanges: '提交修改',
    storeBalance: '店铺余额',
    availableStoreBalance: '可用店铺余额(元)',
    incAvaStoBal: '用户确认收货15天后，增加可用店铺余额，可用于店铺提现',
    pendingSettlement: '待结算金额(元)',
    pendingSettlementTip: '用户购买商品之后，等待用户确认收货15天后，将会变成可用店铺余额',
    unusableBalanceYuan: '冻结余额(元)',
    unusableBalanceYuanTip: '商城进行提现申请的时候，将会把余额冻结，增加冻结余额',
    totalSettlementAmount: '总结算金额(元)',
    totalSettlementAmountTip: '用户确认收货15天后结算的所有金额，当用户发生退款的时候，总结算金额会减少',
    totalSettlementAmountTip2: '用户确认收货15天后结算的所有金额',
    recentTransactions: '交易记录',
    withdrawalRecord: '提现记录',
    qq: 'QQ',
    weChatNumber: '微信号',
    mailbox: '邮箱',
    fax: '传真',
    pleaseWriteInfo: '请填写模板信息',
    chooseDeliveryArea: '选择配送区域',
    templateNameCannotBeEmpty: '模板名称不能为空',
    pleaseSelectADeliveryArea: '请选择可配送区域',
    addAreaAndShipping: '添加可配送的区域',
    freeShippingOnSpecifiedConditions: '指定条件包邮',
    pleaseSelectTheDesignatedShippingCity: '请选择指定包邮城市',
    clickToAddTheSpecifiedShippingConditions: '添加指定包邮条件',
    setRegionalConditions: '请设置指定区域包邮条件',
    changeAmountTip1: '由于地址变更，运费应',
    undercharged: '少',
    overcharged: '多',
    by: '收',
    yuan: '元',
    changeAmountTip2: '，此操作不会变更订单金额，是否确认直接修改订单地址？',
    setArea: '请设置可配送区域',
    PlatformOff: '平台下线',
    reapplyReason: '请输入200字以内的申请理由',
    applicationHistory: '申请历史',
    applicationTips: '请填写申请理由',
    submit: '确认提交',
    unbundlingQuestions: '是否解绑法人账号?',
    UnbindingSuccessful: '解绑成功'
  },
  admin: {
    carouselImg: '轮播图片',
    recommImgSize: '建议图片尺寸为',
    carouselImgNoNull: '轮播图片不能为空',
    sortNONull: '排序值不能为空',
    publicMsg: '公开留言',
    cancelPublic: '取消公开',
    seleData: '选择日期',
    msgConten: '留言内容',
    msgReply: '留言回复',
    msgRemSet: '消息提醒设置',
    msgSet: '短信设置',
    msgType: '消息类型',
    notifyConten: '通知内容',
    notifyType: '通知类型',
    msgNotify: '短信通知（计费发送）',
    msgFreeNotify: '短信通知（免费）',
    msgNotifyFree: '短信通知',
    publicNotify: '公众号通知',
    appletNotify: '小程序通知',
    appletMessage: '站内信通知',
    seleMsgType: '请选择消息类型',
    notifyContenNoNull: '通知内容不能为空',
    notifyTempNoNull: '短信模板code不能为空',
    orderPay: '订单催付',
    paySuccNotify: '付款成功通知',
    merAgreeRefund: '商家已处理',
    merAgreeReturn: '商家同意退货',
    merRefuseRefund: '商家拒绝退款',
    wriOffRem: '核销提醒',
    shipRem: '发货提醒',
    groupFailRem: '拼团失败提醒',
    groupSuccRem: '拼团成功提醒',
    startGroupRem: '拼团开团提醒',
    memUpRem: '会员升级提醒',
    timeoutRem: '退款临近超时提醒',
    confirmReceRem: '确认收货提醒',
    buyerRefundRem: '买家发起退款提醒',
    buyerReturnRem: '买家已退货提醒',
    buyerPaySuccessRem: '用户支付成功提醒',
    mallTranRem: '商城交易提醒',
    sendMer: '发送给商家',
    free: '免费',
    isDeleOper: '确定进行删除操作',
    smsRecord: '短信充值记录',
    rechargeNum: '充值条数',
    amountSpent: '花费金额（元）',
    rechargeSource: '充值来源',
    rechargeTime: '充值时间',
    unpay: '未支付',
    weChatPay: '微信支付',
    aliPay: '支付宝支付',
    balancePay: '余额支付',
    payPal: 'payPal支付',
    selePaySetMeal: '请选择套餐购买',
    proFeat: '产品特性',
    eveMsm: '每条短信 ￥',
    num: '条',
    weCharBuy: '微信购买',
    weCharScanPay: '微信扫一扫支付',
    aliBuy: '支付宝购买',
    payCom: '已完成支付',
    amoAndMon: '元／24个月',
    commLevelReward: '佣金等级奖励',
    signAmoRange: '签到金额区间',
    redProv: '红包发放',
    minWithAmo: '最小提现金额',
    add: '添加',
    remove: '移除',
    dollar: '元',
    autoReview: '自动审核',
    manReview: '手动审核',
    maxLvNoNull: '等级最大值不能为空',
    minLvNoNull: '等级最小值不能小于0',
    minNoMax: '等级最小值不能大于最大值',
    commNoLZero: '佣金比例不能小于0',
    remaSms: '剩余短信',
    expiredSms: '过期短信',
    refresh: '刷新',
    newConstruction: '新建',
    smsRecharge: '短信充值',
    rechargeRecord: '充值记录',
    modiReceNum: '修改接收号码',
    smsSendRecord: '短信发送记录',
    sendTime: '发送时间',
    smsContent: '短信内容',
    senduserName: '发送用户昵称',
    sendPhoNum: '发送手机号',
    sendStatus: '发送状态',
    resetPwd: '重置密码',
    createAcc: '创建账号',
    applyBus: '申请营业',
    modifyAcc: '修改账户',
    accIsExist: '此账号已存在，请重新输入',
    twoPwdIncon: '两次输入的密码不一致',
    stationAccNoNull: '门店账号不能为空',
    stationImg: '门店图片',
    phoNumber: '电话号码',
    areaCode: '区号',
    businTime: '营业时间',
    businStartTime: '营业开始时间',
    businEndTime: '营业结束时间',
    seleTimeRange: '选择时间范围',
    timeInter: '时段间隔',
    pleaseChoiceTimeInter: '请选择时段间隔',
    morAndEve: '上下午晚上',
    dempoint: '12:00和18:00为分界点',
    hour: '小时',
    halfHour: '半小时',
    pickupTime: '提货时间',
    inputDayNum: '请输入天数',
    startPickup: '天后开始提货',
    inPickupEndTime: '请输入自提结束时间',
    endPickup: '天后结束提货',
    pcd: '省市区',
    location: '定位',
    stationLocal: '门店所在位置',
    seleProv: '请选择省份',
    seleCity: '请选择城市',
    seleDC: '请选择区/县',
    seleBusTime: '请选择营业时间',
    stationA: '自提时间只能为大于或等于0的整数',
    inStatrPupTime: '请输入开始自提时间',
    startTimeis: '开始自提时间只能为',
    endTimeis: '自提结束时间只能为',
    pickUpTimeError: '自提开始时间不能晚于自提结束时间',
    stationNameNoNull: '门店名称不能为空',
    addrNoNull: '地址不能为空',
    number: '手机/电话号码',
    numberNoNull: '手机/电话号码不能为空',
    onlyInNum: '只能为数字',
    mapPosi: '地图定位中,请在定位完毕后再次点击',
    busiTimeErr: '营业开始时间不能大于或等于营业结束时间',
    selePCD: '请先选择省市区',
    modifySmsReceNum: '修改短信接收号码',
    noSet: '暂未设置',
    oldReceNum: '原接收号码',
    newReceNum: '新接收号码',
    newReceNumNoNull: '新的接收号码不能为空',
    phoNumFormaErr: '手机号格式不正确'
  },
  dataAnaly: {
    statisticsTime: '统计时间',
    selectMonth: '选择月',
    realTimeToday: '今日实时',
    natureDay: '自然日',
    naturalMoon: '自然月',
    nearly7Days: '近 7 天',
    nearly30Days: '近 30 天',
    keyInVolumeYes: '卡卷昨日关键指标',
    numberOfRe: '领取次数',
    microMallUsage: '微商城使用次数',
    cardVolumeTrendChart: '卡卷趋势图',
    detailedData: '详细数据',
    pickupTime: '领取时间',
    date: '日期',
    ovelProduvervi: '商品整体概况',
    orderTip1: '订单相关指标（如下单人数）以下单时间统计，支付相关指标（如支付人数）以支付成功时间统计（拼团订单成团算支付）。订单查询页面根据下单时间查询对应订单。',
    orderTip2: '若顾客当日下单、次日支付，则下单与支付数据会有一天时间差，请注意区分。',
    commodityTrendAnalysis: '商品趋势分析',
    commodityOverview: '商品概况',
    numberOfNewProducts: '新增商品数',
    numberOfProductsVisited: '被访问商品数',
    numberOfProductsForSale: '动销商品数',
    commodityFlow: '商品流量',
    commodityExposure: '商品曝光数',
    shareVisits: '分享访问数',
    productViews: '商品浏览量',
    commodityVisitors: '商品访客数',
    commodityConversion: '商品转化',
    numberOfCases: '加购件数',
    orderNumber: '下单件数',
    numberOfPayment: '支付件数',
    mostChoices: '最多选择',
    item: '项',
    chosen: '已选择',
    selectTheIndicatorToDisplay: '选择显示的指标',
    commodityRanking: '商品排行榜',
    paymentAmountTOP: '支付金额TOP',
    paymentAmount: '支付金额(元)',
    numberOfVisitorsTOP: '访客数TOP',
    numberOfVisitors: '访客数',
    visitToPayConversionRate: '访问-支付转化率',
    productEffect: '商品效果',
    commodityInsight: '商品洞察',
    pleasToSearch: '请输入商品名称搜索',
    grouping: '分组',
    activityArea: '活动区域',
    groupAll: '全部分组',
    hiddenFromList: '列表中隐藏',
    allStatus: '全部状态',
    onSale: '出售中',
    inTheWarehouse: '仓库中',
    soldOut: '已售罄',
    salesIndex: '销售指标:',
    impressions: '曝光次数',
    exposure: '曝光人数',
    pageviews: '浏览量',
    numberOfAdditionalPurchases: '加购人数',
    payers: '支付人数',
    singleProductConversionRate: '单品转化率',
    numberOfGoodsPaid: '支付件数',
    placeOrderPerson: '下单人数',
    placeOrderNum: '下单件数',
    placeOrderAmount: '下单金额',
    commodityPaymentAmount: '支付金额',
    serviceIndex: '服务指标:',
    numberOfOrdersRequestedForRefund: '申请退款订单数',
    numberOfPeopleApplyingForRefund: '申请退款人数',
    numberOfSuccessfullyRefundedOrders: '成功退款订单数',
    numberOfSuccessfulRefunds: '成功退款人数',
    successfulRefundAmount: '成功退款金额',
    refundRate: '退款率',
    indicators: '个指标',
    chooseUpTo8Items: '最多选择8项',
    orderTip3: '1.订单相关指标（如下单人数）以下单时间统计，支付相关指标（如支付人数）以支付成功时间统计（拼团订单成团算支付）。订单查询页面根据下单时间查询对应订单。',
    orderTip4: '若顾客当日下单、次日支付，则下单与支付数据会有一天时间差，请注意区分。',
    orderTip5: '2. 选择自然周、自然月、近7天、近30天时，人数在所选时间段内去重。',
    prodHid: '已隐藏无数据的商品',
    prodTip1: '统计时间内，商品详情页被访问的去重人数，一个人在统计时间范围内访问多次记为一人。注意，若顾客浏览商品列表并直接加购、下单，未进入商详页，则不会记入该商品的访客数，此时，会出现商品的访客数小于支付人数的情况',
    trendAnalysis: '趋势图分析',
    naturalDailyTrendChart: '自然日趋势图',
    numberOfPayments: '付款人数',
    numberOfPaymentProducts: '付款商品件数',
    couponAnalysis: '卡券分析',
    transactionIndicator: '成交指标',
    numberOfTransactionMembers: '成交会员数',
    proportionOfTradingMembers: '成交会员占比',
    proportionOfPaymentAmount: '支付金额占比',
    perCapitaConsumptionFrequency: '人均消费频次',
    customerType: '客户类型',
    addProdDescribe: '统计时间内，发布的商品数量',
    prodTip2: '统计时间内，商品详情页浏览次数大于 0 的商品数',
    timeTip0: '统计时间内，销量不为 0 的商品数量',
    // timeTip1: '统计时间内，店铺所有商品在店铺首页、列表页、商品分组页、 微页面、搜索结果页以及商品页底部关联商品区域中的展示次数之和。（直接进入详情页或微页面中通过图片链接到商品不会统计',
    timeTip1: '统计时间内，通过其他用户分享的链接访问商品的次数之和',
    timeTip2: '统计时间内，所有商品详情页被访问的次数，一个人在统计时间内访问多次记为多次',
    timeTip3: '统计时间内，访问任何商品详情页的人数，一个人在统计时间范围内访问多次只记为一个',
    timeTip4: '统计时间内，添加商品进入购物车的商品件数',
    timeTip5: '统计时间内，成功下单的商品件数之和（不剔除退款订单）',
    timeTip6: '统计时间内， 成功付款订单的商品件数之和（不剔除退款订单）',
    days7Before: '较前 7 天 ',
    days30Before: '较前 30 天 ',
    fromThePreviousDay: '较前一日 ',
    monthBefore: '较前一月 '
  },
  user: {
    averageDiscount: '平均折扣',
    actuallypaid: '实付金额',
    consumptionAmount: '消费金额',
    consumptionTimes: '消费次数',
    totalOrderTimes: '下单次数',
    refundTimes: '退款次数',
    clientImport: '导入客户',
    clientExport: '导出客户',
    importClientMode: '客户导入模板.xlsx',
    edit: '编辑',
    userInfo: '用户信息',
    tradeDetails: '交易明细',
    payTime: '支付时间',
    prodTotalPrice: '商品总额',
    orderFreight: '订单运费',
    preferentialAmount: '优惠金额',
    details: '详情',
    payScore: '支付积分',
    sendCoupons: '送优惠券',
    selectCoupons: '选择优惠券',
    sendCouponTips: '提示：发放优惠券时，库存不足或者达到用户领券上限则用户无法收到该优惠券。',
    couponTip1: '请输入优惠券名称搜索',
    couponTip2: '不超过每人限领，不超过剩余库存',
    couponTip3: '不统计用户手动删除和超过30天的被系统删除过期优惠券的部分',
    cancel: '取消',
    stockNum: '库存数量',
    couponUpperLimit: '领券上限',
    perRecevies: '每人发放',
    success: '操作成功',
    clientInformationForm: '客户信息表',
    noData: '暂无数据',
    confirm: '确定',
    merchandiseManagement: '商品管理',
    salesRecord: '销售记录',
    walletManagement: '钱包管理',
    walletFlow: '钱包流水',
    performanceStatistics: '业绩统计',
    promotionalEffect: '推广效果',
    withdrawalRecords: '提现管理',
    withdrawalManagement: '提现管理',
    distributorManagement: '分销员管理',
    distributorAudit: '分销员审核',
    relationshipSearch: '关系查询',
    blockingRecords: '封禁记录',
    growthSetting: '成长值获取设置',
    growthSwitch: '成长值开关:',
    perPurchase: '购买商品每消费',
    getOneGrowth: '获取一点成长值',
    completedOrderGet: '每完成一笔订单获取:',
    growth: '成长值',
    pleaseEnterCorrectValue: '请输入正确的值',
    growthLog: '成长值记录',
    changeGrowthValue: '变更成长值',
    affiliateBusinessId: '关联业务ID',
    consumptionGetGrowthCannotEmpty: '消费金额获取成长值不能为空',
    orderAcquisitionCannotEmpty: '订单获取不能为空',
    levelPageSet: '等级页展示设置',
    memberBenefitsPageDisplaySet: '会员权益页展示设置',
    displayContent: '展示内容：',
    levelPageConfiguration: '等级页展示配置',
    pointsSet: '积分获取设置',
    pointsEarnOne: '连续签到1天可获取积分:',
    pointsEarnTwo: '连续签到2天可获取积分:',
    pointsEarnThree: '连续签到3天可获取积分:',
    pointsEarnFour: '连续签到4天可获取积分:',
    pointsEarnFive: '连续签到5天可获取积分:',
    pointsEarnSix: '连续签到6天可获取积分:',
    pointsEarnSeven: '连续签到7天可获取积分:',
    pointsForRegistration: '注册可获取的积分:',
    pointsSwitch: '购物使用积分开关:',
    earnOnePoint: '可获取1积分',
    pointsPurchase: '积分购买商品时每',
    pointsDeducted: '积分可抵扣1元',
    pointsEarnLimit: '积分获取上限',
    platformPercent: '全平台比例',
    categoryPercent: '商品分类比例',
    pointsUseLimit: '积分使用上限',
    getPercentLimit: '获取比例上限(%)',
    percent: '比例',
    usePercentLimit: '使用比例上限(%)',
    pointsOfRegistrationCannotEmpty: '注册积分获取不能为空',
    pointsOfShoppingCannotEmpty: '购物积分获取不能为空',
    shoppingPointsDeductedCannotEmpty: '购物积分抵扣不能为空',
    usePointsCannotEmpty: '使用积分上限百分比不能为零或者空',
    getPointsCannotEmpty: '获取积分上限百分比不能为零或者空',
    pointsExpirationSetting: '积分过期设置',
    expirationSwitch: '积分过期开关：',
    expiration: '积分过期时间：',
    year: '年',
    expirationDes: '配置的为最长的过期时间，即从获取积分开始，自最长的n-1年的年底会将积分清空。',
    expirationDes1: '（设置为1时，积分会在获取积分当年的年底过期）',
    expirationConfiguration: '积分过期配置',
    enterExpiration: '请输入过期时间',
    pointsDesSetting: '积分说明设置',
    pointsDes: '积分说明',
    pointsEarnSetting: '积分获取设置',
    pointSettings: '积分设置',
    userConsumptionAmount: '用户消费金额的',
    RedeemableForPoints: '%可以兑换获取积分',
    CanBeOffsetWithPoints: '%可以用积分抵消掉',
    pointSettingsTip: '(积分无法抵扣运费)',
    growthEarnSetting: '成长值获取设置',
    growthValueObtainedFromOrderConfirmationOfReceipt: '订单确认收货获取的成长值',
    systemModificationOfUserGrowthValue: '系统修改用户成长值',
    growthValueObtainedFromUserTopUpBalance: '用户充值余额获取的成长值',
    RefundOfGrowthValueForOrderRefund: '订单退款退回成长值',
    otherConfiguration: '其他配置',
    growthValueQuestionSetting: '成长值常见问题设置',
    pointsQusetion: '常见问题：',
    ordinaryMember: '普通会员',
    paidMembership: '付费会员',
    ordinaryMemberSet: '普通会员设置',
    paidMembershipSet: '付费会员设置',
    generalConfiguration: '常规配置',
    enterGradeName: '请输入等级名称',
    bgImg: '背景图片',
    displayEquitybgImg: '权益展示页背景图片',
    levelStyle: '等级样式',
    bronze: '青铜',
    silver: '白银',
    gold: '黄金',
    enterLevelStyle: '请选择等级样式',
    pleaseSelectDisplayEquitybgImg: '请选择会员权益页背景',
    paidMemberPrice: '付费会员价格',
    enterInteger: '请输入整数',
    enterPrice: '请输入价格',
    userInformationForm: '会员信息表',
    timeType: '时间类型',
    maxTimeTip: '最大不得超过10年，以此类推其他时间类型',
    levelConfig: '等级配置',
    day: '天',
    week: '周',
    month: '月',
    season: '季',
    time: '时间',
    requiredGrowthValue: '所需成长值',
    requiredGrowthValueMinimum: '所需成长值不能低于上一级会员所需的成长值',
    requiredGrowthValueMaximum: '所需成长值不能超过下一级会员所需的成长值',
    equityAllocation: '权益配置',
    selfFreeShipping: '自营店包邮',
    pointFeedbackRate: '积分回馈倍率',
    enterMagnification: '请输入倍率数',
    coupons: '优惠券',
    etcCoupons: '等优惠券',
    bonusPoints: '赠送积分',
    discount: '折扣',
    discountRange: '请输入折扣范围',
    platform: '全平台',
    selfShop: '自营店',
    allProducts: '全部商品',
    categoryGoods: '分类下的商品',
    otherRights: '其他权益',
    bgImgCannotEmpty: '背景图片不能为空',
    rightBgImgCannotEmpty: '会员权益页背景图片不能为空',
    selectCategory: '请选择分类',
    gradeNmaeCannotEmpty: '等级名称不能为空',
    feedback1: '积分回馈倍率必须大于1',
    bonus0: '赠送积分必须大于0',
    discountRangeValue: '折扣的取值范围为0.01-9.99',
    addLevel: '添加等级',
    updateUserData: '更新用户数据',
    addLevelTip: '如果最高等级会员的成长值等于或超过1000000000，将无法继续新增',
    updateSoon: '用户等级更新后仅对新用户有效，老用户未生效，请尽快更新',
    growthRange: '等级成长值范围',
    memberDiscount: '会员折扣',
    someProducts: '部分商品',
    remove: '移除',
    atMost: '最多只能添加50个等级',
    saveFirst: '先保存上个等级才能创建新的等级',
    membershipLevel: '会员等级',
    regularMembershipLevel: '普通会员等级',
    payingMembershipLevel: '付费会员等级',
    level: '等级',
    levelName: '等级名称',
    noPic: '暂无图片',
    needGrowth: '购买VIP所需成长值',
    within: '内',
    vipLevel: 'VIP等级',
    memberType: '会员类型',
    userScore: '用户积分',
    rightsName: '权益名称',
    rightsType: '权益类型',
    rightsIcon: '权益图标',
    rightsDescription: '权益简介',
    sysRights: '系统权益',
    selfRights: '自定义权益',
    source: '来源',
    order: '订单',
    levelUp: '等级提升',
    signIn: '签到',
    allLabels: '全部标签',
    conditionLabel: '条件标签',
    addLabel: '新增标签',
    updateLabelData: '更新标签数据',
    editLabel: '编辑标签',
    customConditionLabel: '自定义条件标签',
    customConditionLabelTips: '最多添加20个',
    peopleNum: '人数',
    creationTime: '创建时间',
    updateTime: '更新时间',
    updateBtn: '更新',
    editBtn: '编辑',
    deleteBtn: '删除',
    manualLabel: '手动标签',
    labelName: '标签名称',
    labelType: '标签类型',
    conditionSetting: '条件设置',
    conditionSettingTips: '选择多个条件时，需全部满足',
    basicCondition: '基础条件',
    becomeCustomerTime: '用户注册时间',
    pleaseSelect: '请选择',
    startDate: '开始时间',
    endDate: '结束时间',
    followTime: '关注时间',
    becomeMemberTime: '成为付费会员时间',
    nearConsuTime: '最近消费时间',
    consuNum: '消费次数',
    bout: '次',
    consuAmount: '消费金额',
    yuan: '元',
    averPri: '订单均价',
    rechargeConditions: '充值条件',
    rechargeAmount: '充值金额',
    rechargeNumber: '充值次数',
    tradingConditions: '交易条件',
    calcel: '取消',
    preservation: '保存',
    today: '今天',
    yesterday: '昨天',
    lastSevenDay: '最近7天',
    lastFifteenDay: '最近15天',
    lastThirtyDay: '最近30天',
    lastFortyFiveDay: '最近45天',
    lastSixtyDay: '最近60天',
    lastNinetyDay: '最近90天',
    lastOneHundredEightyDay: '最近180天',
    thisMonth: '这个月',
    lastMonth: '上个月',
    customRange: '自定义范围',
    unlimited: '不限',
    updateSucceeded: '更新成功',
    deletionSucceeded: '删除成功',
    labelNameNullTips: '标签名称为空',
    becomeCustomerTimeNullTips: '用户注册时间为空',
    followTimeNullTips: '关注时间为空',
    becomeMemberTimeNullTips: '成为付费会员时间为空',
    memberTime: '会员时间',
    consuNumNullTips: '消费次数为空',
    consuAmountNullTips: '消费金额为空',
    averPriNullTips: '订单均价为空',
    rechargeAmountNullTips: '充值金额为空',
    rechargeNumberNullTips: '充值次数为空',
    requireOne: '至少添加一项条件',
    enterRechargeAmount: '请输入充值金额',
    rewardAmount: '赠送金额',
    enterRewardAmount: '请输入赠送金额',
    reward: '赠送',
    rewardScore: '赠送积分',
    enterRewardScore: '请输入赠送积分',
    rewardGrowthValue: '赠送成长值',
    enterRewardGrowthValue: '请输入赠送成长值',
    rewardCoupon: '赠送优惠券',
    updateGrowth: '修改成长值',
    tagging: '打标签',
    updateScore: '修改积分',
    customerLabel: '客户标签',
    memberTags: '会员标签',
    export: '导出会员',
    registrationTime: '注册时间',
    openingTime: '开通时间',
    lastConsumptionTime: '最近消费时间',
    modifyBalance: '修改余额',
    cumulativeScore: '累计积分',
    currentBalance: '当前余额',
    cumulativeBalances: '累计余额',
    tagTip1: '请输入标签名称搜索',
    growthTip1: '正数代表增加，负数代表减少，只能输入整数，根据修改后的成长值重新计算会员等级',
    growthTip2: '成长值数量',
    growthTip3: '只能是正整数或者负整数',
    growthTip4: '成长值数量不能为空',
    updateUserScore: '修改用户积分',
    scoresChange: '积分数量(增减)',
    scoreTip1: '正数代表增加，负数代表减少，只能输入整数',
    scoreTip2: '改变数值不能为空',
    couponDscription: '优惠券描述',
    couponType: '优惠类型',
    balanceUpdate: '修改用户余额',
    balanceRecharge: '余额充值',
    changeQuantity: '修改数量',
    balanceTip1: '正数代表增加，负数代表减少，只能输入数字，最多两位小数',
    balanceTip2: '修改数量不能为空',
    balanceTip3: '请输入不等于0或者保留两位小数的数值',
    balanceTip4: '批量给用户充值余额，只能批量给用户增加余额，请输入大于零的数',
    balanceTip5: '请输入大于0的数值，最多保留2位小数',
    balanceTip6: '用户当前余额：',
    balanceTip7: '用户余额最大不能超过*********.99，最小不能小于0',
    balanceTip8: '若用户余额加修改的余额大于最大值，则取最大值作为用户余额',
    accountAssets: '账户资产',
    accountWallet: '储值账户',
    currentScore: '当前积分',
    notUsed: '未使用',
    used: '已使用',
    invalid: '已失效',
    otherInfo: '其他信息',
    scoreDetails: '积分明细',
    balanceDetails: '余额明细',
    couponDetails: '优惠券明细',
    scorePay: '积分支付',
    scoreOrder: '积分订单',
    changeTime: '变动时间',
    changeReason: '变动原因',
    registerScore: '注册送积分',
    lockScore: '(积分锁定中)',
    shopping: '购物',
    shoppingDeducteScore: '购物抵扣积分',
    scoreExpired: '积分过期',
    rechargeBalance: '余额充值',
    sysChangeScore: '系统更改积分',
    changeScoreNum: '变动积分',
    changeType: '变动类型',
    recharge: '充值',
    payment: '支付',
    refund: '退款',
    platformChange: '平台手动修改',
    rechargeMember: '充值会员',
    changeBalance: '变动余额',
    getCouponTime: '领券时间',
    effectiveTime: '有效时间',
    couponStatus: '优惠券状态',
    userRules: '使用规则',
    discountMsg: '满PRICE元打FOLD折',
    tipError2: '最多选择NUM个标签',
    userImport: '导入会员',
    refundRetrunScore: '商品退款退回抵现积分',
    integerGreaterThanZero: '请输入大于0的整数',
    add: '添加',
    reset: '重置',
    invitees: '邀请人',
    status: '状态',
    ban: '暂时封禁',
    normal: '正常',
    cleared: '已清退',
    succeeded: '操作成功',
    selectTag: '选择标签',
    selectTagError: '请选择标签',
    disableRemark: '禁用原因',
    disableRemarkNoNull: '禁用原因不能为空',
    grade: '等级',
    save: '保存',
    imgsTip: '建议尺寸',
    px: '像素',
    rightsInterests: '会员权益',
    couponNumber: '优惠券数量',
    recruitmentSituation: '招募情况',
    stopRecruitment: '停止招募',
    beginRecruitment: '开始招募',
    freeShipping: '包邮',
    isDeleOper: '确定进行删除操作',
    buyLevel: '购买会员',
    buyTime: '购买时间',
    payAmount: '支付金额',
    revise: '修改',
    delete: '删除',
    isBeginRecruitment: '确定继续招募“',
    isStopRecruitment: '确定停止招募“',
    isBeginRecruitment2: '”会员吗',
    isDelete: '确定进行[删除]操作?',
    cashCoupon: '代金券',
    discountVoucher: '折扣券',
    coinCertificate: '兑换券',
    custom: '自定义',
    suspendRecruitment: '暂停招募',
    recruiting: '正在招募',
    importMemberMode: '导入会员信息模板.xlsx',
    isMember: '是否会员',
    userTag: '会员标签',
    userTagDefaultTxt: '请选择会员标签',
    levelNameCn: '中文等级名称',
    levelNameEn: '英文等级名称',
    enterGradeNameCn: '请输入中文等级名称',
    enterGradeNameEn: '请输入英文等级名称',
    enterContentCn: '请输入内容，长度不超过五个字符',
    enterContentEn: '请输入内容，长度不超过二十个字符',
    gradeNmaeCannotEmptyCn: '中文等级名称不能为空',
    noUpdate: '暂无更新',
    successfullyFileImport: '此文件导入成功',
    failedFileImport: '此文件导入失败'
  },
  userRights: {
    customBenefits: '自定义权益',
    rightsNameCn: '中文权益名称',
    rightsNameEn: '英文权益名称',
    rightsIntroduceCn: '中文权益简介',
    rightsIntroduceEn: '英文权益简介',
    nameCanNotEmptyCn: '中文权益名称不能为空',
    intrCanNotEmptyCn: '中文权益简介不能为空',
    systemRights: '系统权益',
    rightsName: '权益名称',
    enterContent: '请输入内容，长度不超过五个字符',
    rightsIcon: '权益图标',
    rightsIntroduce: '权益简介',
    nameCanNotEmpty: '权益名称不能为空',
    iconCanNotEmpty: '权益图标不能为空',
    intrCanNotEmpty: '权益简介不能为空',
    serialNumber: '序号',
    sequence: '顺序',
    errorIntegerTip1: '请输入整数'
  },
  formData: {
    export: '导出',
    exportIng: '导出中',
    stockLimit: '请输入一个小于2147483600的数',
    shop: '店铺',
    natureDay: '自然日',
    natureWeek: '自然周',
    naturalMoon: '自然月',
    customTime: '自定义时间',
    specifyTimeRange: '指定时间范围',
    near: '近',
    reportName: '报表名称',
    pleaseEnterTheReportName: '请输入报表名称',
    typeOfData: '数据类型',
    timePeriod: '时间周期',
    accordingToTheSelectedType: '根据选择的类型，生成对应时间周期的报表',
    timeFormat: '时间格式',
    timeLimit: '时间范围',
    pleaseEnterTheTime: '请输入时间',
    day: '天',
    date: '日',
    daterange: '日期范围',
    monthrange: '月份范围',
    week: '周',
    month: '月',
    serialNumber: '序号',
    selectIndicator: '选择指标',
    lastWeek: '最近一周',
    lastMonth: '最近一个月',
    lastThreeMonths: '最近三个月',
    theReportNameCannotBeEmpty: '报表名称不能为空',
    startTimeCannotBeEmpty: '开始时间不能为空',
    endTimeCannotBeEmpty: '结束时间不能为空',
    timeRangeCannotBeEmpty: '时间范围不能为空',
    timeCannotBeEmpty: '时间不能为空',
    pleaseSelectAnIndicator: '请选择指标',
    timeTip: '如选择近1天，则该报表会自动更新为 当前天 的数据，近2天，则该报表会自动更新为 当前天+昨天 的数据',
    pleaseThan0: '请输入大于0的整数',
    numberOfIndicators: '指标数量',
    reportIndicators: '报表指标',
    addMyReport: '加入我的报表',
    natural: '自然',
    noTimeZero: '时间范围不能为0'
  },
  groups: {
    tip10: '发布团购后的商品不可更换，请选择准确的商品',
    selectProd: '选择商品',
    newJoinGroupActivity: '新建拼团活动',
    GroupActivityInfo: '拼团活动详情',
    startUsing: '启用',
    editEvent: '编辑',
    manageEvent: '管理',
    sku: '规格',
    manageEventProducts: '管理商品',
    invalidActivity: '失效活动',
    applyForListing: '申请上架',
    ifNotDealtWith: '如不处理，拼团活动的商品将无法重新开启',
    groupOfPeople: '人团',
    notEnabled: '未启用',
    notOpen: '未开启',
    hasNotStarted: '未开始',
    processing: '进行中',
    over: '已结束',
    expired: '已失效',
    offlineViolation: '违规下线',
    moderated: '等待审核',
    confirmDelete: '确定删除【',
    active: '】活动吗?',
    determinedToFail: '确定失效【',
    actTip: '】活动吗？失效后如果活动开启了模拟成团，拼团中的团单仍需要处理',
    editGroupActivities: '编辑拼团活动',
    eventName: '活动名称',
    enterEventName: '输入活动名称',
    relatedProducts: '关联商品',
    edit: '编辑',
    activityStartTime: '活动开始时间',
    selectEventStartTime: '选择活动开始时间',
    eventEndTime: '活动结束时间',

    selectEventEndTime: '选择活动结束时间',
    numberOfParticipants: '参团人数',
    people: '人',
    groupValidityPeriod: '成团有效期时间',
    minute: '分钟',
    limitPurchase: '限制购买',

    turnOn: '开启',
    limitedPurchaseQuantity: '限购数量',
    simulation: '模拟成团',
    groupLeaderOffer: '团长优惠',
    groupMode: '凑团模式',
    activityWarmUp: '活动预热',

    cannotrOpen: '开启后，商品详情页展示未开始的拼团活动，但活动开始前用户无法拼团购买',
    submit: '提交',
    submitAndProducts: '提交并管理活动商品',
    activityTimeTime: '活动时间不能少于当前时间',
    alterActivityTime: '修改活动后，活动开始时间不能少于当前时间',
    startTime: '活动开始时间不能少于当前时间',
    endTime: '活动结束时间不能少于当前时间',
    deliveryTime: '发货时间不能少于当前时间',
    endTimeIsTooSmall: '结束时间不能小于或等于开始时间',
    extendedEndTime: '结束时间只能延长，不能小于原本设定值',
    pleaseEnterEventName: '请输入活动名称',
    pleaseEnterEventNum: '请输入限购数量',

    pleaseEnteNumGrp: '请输入成团人数',
    pleaseEnterTimeGroup: '请输入成团有效时间',
    startTimeCannotBeEmpty: '开始时间不能为空',
    endTimeCannotBeEmpty: '结束时间不能为空',

    invalidate: '“使失效”即立即结束且不可再编辑，未成团订单将自动关闭并退款，已成团订单仍需及时处理。失效后的商品活动可删除。',
    tip1: '成团人数不能小于2人',
    groupNumberLimit: '成团人数不能多于1000000000人',
    tip2: '成团有效时间不能小于15分钟',
    tip3: '成团有效时间不能大于一天(1440分钟)',
    tip4: '若设置30分钟，用户开团后，需要在30分钟内成团，超时则拼团失败',
    memberPeople: '件/人',
    tip5: '开启模拟成团后，拼团有效期内人数未满的团，系统将会以“虚拟用户”凑满人数，使该团拼团成功。你只需要对已付款参团的真实买家发货。建议合理开启，以提高成团率。',
    tip6: '开启团长(开团人)优惠后，团长将享受更优惠价格，有助于提高开团率和成团率。',
    tip7: '注意：开启“模拟成团”时，团长也能享受团长优惠，请谨慎设置，避免资金损失。',
    tip8: '开启凑团后，对于未参团的买家，活动商品详情页会显示未成团的团列表，买家可以直接任选一个参团，提升成团率。',
    tip9: '开启后，商品详情页与优惠团购列表等页面展示即将开始的拼团活动，但活动开始前用户无法拼团购买',
    groupStatus: '拼团状态(0:待拼，1:成功，2:失败)',

    returnToJoinGroupActivities: '返回拼团活动',
    newEventProduct: '新增活动商品',
    activitySpecifications: '活动规格',
    expiredGoods: '失效商品',
    determine: '确定',
    cancel: '取消',
    successfulOperation: '操作成功',
    failureTip: '“失效活动商品”即立即结束且不可再编辑，未成团订单将自动关闭并退款，已成团订单仍需及时处理。失效后的商品活动可删除。',
    isSuer: '确定对【',
    isDele: '】活动商品进行删除吗?',

    activityId: '活动id',
    proId: '商品id',
    commSpenMan: '商品规格管理',
    costOfProduction: '原价',
    headPrice: '团长价',
    stock: '库存',
    groupPrice: '拼团价',
    skuName: 'sku名称',
    groupPriceYuan: '拼团价(元)',
    headPriceYuan: '团长价(元)',
    enterPrice: '输入价格',
    batchSettings: '批量设置',

    activityPriceMus: '活动价格必须大于0元',
    theLeaderPriceMu: '团长价格必须大于0元',
    canTPlateauPrice: '活动价格不能高于商品原价',
    notTPY: '团长价格不能高于商品原价',
    notTAY: '团长价格不能高于活动价格',
    pleaseSetTGP: '请先批量设置拼团价，或者取消批量设置操作',
    pleaseSetFirst: '请先批量设置团长价，或者取消批量设置操作',
    groupPriceMusThan0: '拼团价格必须为大于0',
    viewGroupOrders: '查看同团订单',
    viewGroupActivityInfo: '查看活动详情',
    groupOrderList: '同团订单列表',
    actProductAmount: '活动商品金额',
    membership: '团员身份',
    head: '团长',
    member: '团员',
    robot: '机器人',
    invalidatePrductPre: '确定对活动商品【',
    invalidatePrductSuf: '】进行失效吗？'
  },
  shopWallet: {
    operationAmount: '操作金额',
    platformAmount: '平台补贴金额',
    unusableBalance: '冻结余额',
    shopAmount: '商家补贴金额',
    platformCommission: '平台佣金',
    amountChangeReason: '资金变化原因',
    orderSettlement: '订单结算',
    applyRefund: '用户申请退款',
    rejectApplyRefund: '拒绝用户退款申请',
    exportTips: '导出筛选出的交易记录',
    excelName: '店铺结算表.xlsx'
  },
  marketing: {
    castTime: '生效时间',
    modifyCoupon: '修改优惠券',
    newCoupon: '新增优惠券',
    couponName: '优惠券名称',
    couponSubtitle: '优惠券副标题',
    conditionsOfUse: '使用条件',
    fullConsumption: '消费金额满',
    reductionAmount: '减免金额',
    discountAmount: '折扣额度',
    discountTip1: '此处的折扣权益可以自定义适用范围',
    discountTip2: '勾选的其他权益会显示在用户等级权益列表中，仅用于图标展示没有对应功能',
    discountNoZero: '优惠金额不能为0',
    pleaseEnterDiscount: '请输入折扣额度 例：9.6为9.6折',
    fold: '折',
    effectiveTime: '生效时间',
    fixedTime: '固定时间',
    effectiveAfterReceipt: '领取后生效',
    chooseStartTime: '选择开始时间',
    chooseEndTime: '选择结束时间',
    afterReceivingTheCoupon: '领券以后',
    effectiveDays: '天生效',
    maxTimeTip: '最大不得超过10年，即3652天',
    validDate: '有效天数',
    perPerson: '每人限领',
    piece: '张',
    inventory: '库存',
    activeInventory: '活动库存',
    activeStationInventory: '活动门店库存',
    applicableGoods: '适用商品',
    allProdsPar: '全部商品参与',
    participateInD: '指定商品参与',
    specifiedProduct: '指定商品不参与',
    pleaseaTo0: '请输入大于等于0的整数',
    pleaseaTo1: '请输入大于等于1的整数',
    pleaseTo10: '请输入10以内的整数或者保留两位小数的正数',
    timeCanThanOrEqualTo: '开始时间不能小于或等于结束时间',
    couponNameCannotBeEmpty: '优惠券名称不能为空',
    theDedEmpty: '减免金额不能为空',
    theLimitetBeEmpty: '限领数量不能为空',
    collectioeEmpty: '领取后天数不能为空',
    effectiveEmpty: '有效天数不能为空',
    theDiscouBeEmpty: '折扣额度不能为空 例：9.6为9.6折',
    conditionBeEmpty: '使用条件不能为空',
    effectiveotBeEmpty: '生效时间不能为空',
    invenEmpty: '库存不能为空',
    applicabltBeEmpty: '适用商品类型不能为空',
    quantitssThan0: '数量不能小于0',
    amounnCannotBe: '减免金额不能大于或等于使用金额',
    mobiltyDiagram: '移动端活动图',
    suggest1: '建议：移动端活动图尽量使用深色的图片',
    pcActivityListChart: 'PC活动列表图',
    pcActiviroundMap: 'PC活动背景图',
    activTime: '活动时间',
    typeOfActivity: '活动类型',
    fullMoneoney: '满钱减钱',
    fullMoneyDiscount: '满钱打折',
    typeOfExemption: '减免类型',
    decreaseOestLevel: '按满足最高层级减一次',
    decreaeryTime: '每满一次减一次',
    offerContent: '优惠内容',
    activityLevel: '活动层级',
    everyFull: '每满',
    conditionsOfUseIn: '使用条件需要随活动层级的递增而递增',
    discountedPrice: '优惠金额',
    reducea: '减',
    dozen: '打',
    pleaseThan0: '请输入大于0的数字',
    pleaseEnterTheNumber010: '请输入大于0小于10的数字',
    discountTip: '活动中的秒杀商品不参与满减折',
    maximumDiscountAmount: '优惠金额上限',
    applicableProductType: '适用商品类型',
    pleaseUploadAPicture: '请上传图片',
    item: '件',
    theConditierThan: '的使用条件需要大于',
    theOfferAmountThan: '的优惠金额需要大于',
    theOfferDiscountThan: '的优惠折扣需要低于',
    activimount: '活动条件金额必须大于优惠金额',
    activitytBeEmpty: '活动的使用条件不能为空',
    promotioBeEmpty: '活动的优惠金额不能为空',
    maximumDiscountAmountBig: '优惠金额上限必须大于或等于最小的优惠金额',
    pleaseSelectAProduct: '请选择商品',
    eventEndanStartTime: '活动结束时间应该大于开始时间',
    subHeadings: '上级分类',
    newDisProducts: '新增分销商品',
    modifyDisnProducts: '修改分销商品',
    defaultReward: '系统设置',
    proporteward: '按比例奖励',
    rewardByFixedValue: '按固定数值奖励',
    notBasedOnGrade: '不根据等级奖励',
    rewardBasedOnLevel: '根据等级奖励',
    inviterReward: '间推奖励',
    amountSetting: '奖励设置',
    inviterRewardAmount: '间推奖励',
    name: '等级名称',
    rewardAmount: '直推奖励',
    theProduList: '该商品已经在分销商品列表中',
    valueSetBeEmpty: '数值设置不能为空',
    valueSetting: '数值设置只能输入大于0的整数或两位小数',
    couponTypeCannotBeEmpty: '优惠券类型不能为空',
    fullDiscount: '满件打折',
    full: '满',
    noUse: '不使用',
    use: '使用',
    rewardRatio: '奖励比例',
    rewardSet: '奖励方式',
    tips: '商品实际付款金额(剔除运费)需大于奖励金额，否则分销判定为失效',
    newDiscount: '新增限时折扣活动',
    viewDiscount: '编辑限时折扣活动',
    totalAwardOverTips: '要小于100%',
    maxActivityLevelsTips: '活动层级最多不超过5级',
    stockReplenishment: '库存补充',
    stockReplenishmentTips: '开启后未秒杀完的商品库存将会补充到对应库存点中'
  },
  seckill: {
    unlimited: '不限',
    makeSurateTheAct: '确定使活动失效?',
    purcPerPerson: '每人限购',
    cancelTheOrder: '订单取消',
    unpaidnutes: '分钟未支付订单，订单取消',
    warning: '警告：秒杀商品采用独立库存请合理安排',
    price: '商品sku原价(元): ',
    exisocks: '商品sku库存: ',
    pleaheyTime: '请选择活动时间',
    timeCanEmpty: '时间不可为空',
    evenBeEmpty: '活动名称不可为空',
    theOrionEmpty: '订单取消时间不可为空',
    commoBeEmpty: '商品价格不能为空',
    commodiBeEmpty: '商品库存不能为空',
    enableMustOne: '请至少启用一个sku',
    pleaSenProc: '请选择活动商品',
    openPurchaseLimit: '开启限购',
    canBePurcPer: '每人可购买',
    newSeckill: '新增秒杀活动',
    viewSeckill: '查看秒杀活动',
    productHasBeenDeleted: '商品已被删除',
    skuStockNotZero: '商品库存不能为0'
  },
  retailProd: {
    number: '序号',
    pic: '单品图片',
    barCode: '单品条码',
    name: '单品名称',
    spec: '单品规格',
    categoryName: '单品分类',
    costPrice: '单品成本',
    stocks: '单品库存',
    unit: '单品单位',
    supplier: '供应商',
    chooseSupplier: '选择供应商',
    export: '导出',
    import: '导入',
    chooseFile: '选取文件',
    DownloadTemplate: '下载模板',
    confirmImport: '确认导入',
    batchDelete: '批量删除',
    barCodePlaceholder: '如无条形码系统将自动生成',
    barCodeAppend: '用于快速识别该单品',
    unitPlaceholder: '请选择单位',
    tipError: '请输入不小于0的整数',
    tip2Error: '请至少选择一个单品',
    upLoadRetailProd: '单品导入',
    uploadRetailProdTip: '导入单品时如选择了供应商，将以最低采购价0.01元，最小订货量1存入供应商商品中。'
  },
  menuList: {
    prodManage: '产品管理',
    releaseGoods: '发布商品',
    itemManagement: '单品管理',
    classificationManage: '分类管理',
    groupManage: '分组管理',
    commentManage: '评论管理',
    specManage: '规格管理',
    brandManage: '品牌管理',
    storeManage: '店铺管理',
    announcementManage: '公告管理',
    storeAddressManage: '店铺地址管理',
    freightTemplate: '运费模板',
    carouselManage: '轮播图管理',
    newsPush: '消息推送',
    hotSearchManage: '热搜管理',
    pickupPointManage: '门店管理',
    cityDistributionManage: '同城配送管理',
    storeManagement: '店铺设置',
    storeBalance: '店铺余额',
    supplierManage: '供应商管理',
    groupManagement: '拼团管理',
    groupActivities: '拼团活动',
    groupOrder: '拼团订单',
    marketingManage: '营销管理',
    spikeManage: '秒杀管理',
    fullDiscount: '限时折扣',
    couponManage: '优惠券管理',
    distrProManage: '分销商品管理',
    distrReco: '分销商品记录',
    orderManage: '订单管理',
    refundManage: '退款管理',
    dataReport: '数据报表',
    myReport: '我的报表',
    recommendedReport: '推荐报表',
    dataAnalysis: '数据分析',
    commodityOverview: '商品概况',
    commodityInsight: '商品洞察',
    distributionManage: '配送管理',
    freTempl: '运费模板',
    liveBroadManage: '直播管理',
    liveRoomMerchand: '直播间商品',
    liveUser: '成员管理',
    liveRoomManage: '直播间管理',
    shopFeature: '店铺装修',
    shopPcFeature: '店铺装修PC'
  },
  components: {
    pictureManager: '图片管理器',
    selectImage: '选择图片',
    pictureName: '图片名称',
    uploadImage: '上传图片',
    pleSeleLocImaUpload: '请选择本地图片上传',
    confirmUpload: '确定上传',
    originalName: '原名称',
    editName: '修改名称',
    pleEntNewPicName: '请输入新的图片名称',
    sizeCannotExceed: '大小不能超过',
    imageExc300Px: '图片宽度或高度不能超过300px',
    numHasReaLimit: '可选择照片数量已达上限',
    onlyUploadPictures: '只能上传图片,其他文件已清除',
    onlySupportsPictures: '仅支持 jpg/png/gif 图片',
    theSizeCannotExceed2M: '大小不能超过2M',
    already: '已有',
    pictures: '张图片',
    willUploadSoon: '即将上传',
    youCanAlsoChoose: '还可以选择',
    imageUpload: '张图片上传',
    theServerTookANap: '服务器打了个盹^_^',
    youCanOnlySelectAtMost: '当前最多只能选择',
    avaExc2MB: '上传头像图片大小不能超过 2MB!',
    maxiNumPicture: '图片最大数量为',
    violationManagement: '违规管理',
    handler: '处理人',
    processingStatus: '处理状态',
    platformOffline: '违规下线',
    auditNotPassed: '审核不通过',
    reasonForOffline: '下线原因',
    offlineTime: '下线时间',
    reasonForApply: '申请理由',
    applicationLog: '申请日志',
    applicationTime: '申请时间',
    reviewTime: '审核时间',
    denialReason: '拒绝原因',
    submitApplication: '提交申请',
    retryAfterRefresh: '商品不在审核状态，请刷新后重试',
    stationRetryAfterRefresh: '门店不在审核状态，请刷新后重试',
    applicaEmpty: '申请理由不能为空',
    areYouSureAtion: '确定提交申请吗？',
    modifyReceivingInfo: '修改收货信息',
    consultWithUsers: '请与用户协商一致后，再修改信息',
    userComments: '用户备注',
    pleaseEnterRemarks: '请输入备注信息',
    commodityPrice: '商品价格',
    groupWhetherToContinue: '此操作将部分在限时特惠中的部分商品提交到拼团活动中, 是否继续?',
    seckillWhetherToContinue: '此操作将部分在限时特惠中的部分商品提交到秒杀活动中, 是否继续?',
    fileSizeIsTooLarge: '文件体积过大',
    somethhTheSer: '服务器出了点小差',
    editPictureName: '修改图片名称',
    widthNoExc300Px: '图片宽度不能超过300px',
    heightNoExc300Px: '图片高度不能超过300px',
    upImaSizeNotExc: '上传图片大小不能超过 2MB!',
    loading: '加载中...',
    noDistribution: '分销未开启',
    selecTips: '不支持选择当前的类型，请切换到符合的类型',
    maxChoice: '最多只能选择',
    templateRestrictionTips: '模板名称不能为"包邮"或"固定运费"',
    chosenTip: '个区域'
  },
  utils: {
    requestErr: 'http请求方式有误',
    serverErr: '服务器出了点小差，请稍后再试',
    serverNoSupp: '服务器不支持当前请求所需要的某个功能'
  },
  resource: {
    newGroup: '新建分组',
    Delete: '删除',
    Move: '移动',
    selectAll: '全选',
    selectGroup: '选择分组',
    group: '分组',
    modifyName: '修改分组名称',
    update: '修改',
    groupName: '分组名称',
    cancel: '取消',
    confirm: '确定',
    mobileGroup: '移动分组',
    CannotBeEmpty: '分组名称不能为空',
    successTips: '新建分组成功',
    successTips1: '修改分组成功',
    tips: '提示',
    uploadSuccess: '上传成功',
    updateSuccess: '修改成功'
  },
  pictureManager: {
    picManager: '图片管理器',
    choosePic: '选择图片',
    picName: '图片名称',
    query: '查询',
    tips1: '请选择图片',
    tips2: '确定进行[删除]操作?',
    tips3: '该资源可能已用于活动或商品，确定进行[删除]操作？',
    uploadPic: '上传图片',
    selectLocalPic: '请选择本地图片上传：',
    confirmUpload: '确定上传',
    revisePicName: '修改图片名称',
    revisePicNameAndGroup: '修改图片名称和分组',
    oldName: '原名称',
    revName: '修改名称',
    inputNewName: '请输入新的图片名称',
    superiorLimit: '可选择照片数量已达上限',
    onlyPictures: '只支持图片格式、大小不能超过2m，其他文件已清除',
    onlySupported: '仅支持',
    pic: '图片',
    imageSize: '图片尺寸为',
    notExceed: '大小不能超过',
    alreadyExist: '已有',
    soonUpload: '即将上传',
    unit: '张图片',
    allPictures: '全部图片',
    upload: '上传',
    remainder: '还可以选择',
    maxSelect: '当前最多只能选择',
    requestError: '服务器打了个盹^_^',
    uploadPDF: '只能上传一个PDF文件！',
    pdfTypeErrorTips: '请上传PDF文件！',
    pdfUploadErrorTips: '请上传正确的PDF文件！'
  },
  videoManager: {
    picManager: '视频管理器',
    chooseVideo: '视频管理',
    videoName: '视频名称',
    AllVideos: '全部视频',
    query: '查询',
    tips1: '请选择视频',
    tips2: '请上传正确的视频格式',
    tips3: '上传视频大小不能超过20MB!',
    uploadVideo: '上传视频',
    selectLocalVideo: '请选择本地视频上传：',
    confirmUpload: '确定上传',
    revisePicName: '修改视频名称',
    revisePicNameAndGroup: '修改视频名称和分组',
    oldName: '原名称',
    revName: '修改名称',
    inputNewName: '请输入新的视频名称',
    superiorLimit: '可选择视频数量已达上限',
    onlyPictures: '只支持视频、大小不能超过20m，其他文件已清除',
    onlySupported: '仅支持',
    video: '视频',
    notExceed: '大小不能超过',
    alreadyExist: '已有',
    soonUpload: '即将上传',
    unit: '个视频',
    upload: '上传',
    remainder: '还可以选择',
    maxSelect: '当前最多只能选择',
    requestError: '服务器打了个盹^_^'
  },
  notice: {
    plaNotice: '平台公告',
    moreNotice: '更多公告',
    noticeList: '平台公告列表',
    publishTime: '发布时间',
    msgContent: '消息内容',
    msgType: '消息类型',
    msgStatus: '消息状态',
    selectedItem: '已选信息',
    allRead: '全部已读',
    batchRead: '批量已读',
    productRemovalReminder: '商品下架提醒',
    commodityAuditResultReminder: '商品审核结果提醒',
    userConfirmsReceiptNotification: '用户确认收货通知',
    reminderOfPendingShipment: '待发货提醒',
    reminderOfPendingRefund: '待退款提醒',
    reminderToNeReceived: '待收货提醒',
    menu: '所属菜单',
    pushNode: '推送节点',
    enableReminder: '开启提醒',
    turnOffReminder: '关闭提醒',
    msgList: '消息列表',
    msgSettings: '消息设置',
    bulletinBoard: '公告栏',
    messageNotification: '消息通知',
    more: '更多',
    ReminderChangeConsignment: '代销商品变更提醒',
    ReminderPurchased: '待采购提醒',
    ReminderMarketingActivityOffShelf: '营销活动下架提醒',
    ActivityAuditResultReminder: '活动审核结果提醒',
    ReminderSuccessfulPayment: '用户支付成功提醒',
    buyerReturnedGoodsReminder: '买家已退货提醒',
    contentLimitTxt: '公告内容字数过多，请重新输入！'
  },
  noGetUserInfor: '无法获取用户信息，跳转到登陆页面',
  idIsLoginInElse: '账户已在别处登录',
  reLogin: '重新登陆',
  otherParIsNoOL: '对方不在线',
  activityDetails: '活动详情',
  addAct: '添加活动',
  shopFeature: {
    editMiniPage: '编辑微页面',
    chooseFeature: {
      choosePageTips: '请选择微页面',
      pageTitle: '页面标题',
      createTime: '创建时间'
    },
    template: {
      blank: '空白自建',
      newTemplate: '新增模板',
      refresh: '刷新',
      edit: '编辑',
      delete: '删除',
      copy: '复制',
      copySuccess: '复制成功',
      selectTemplate: '选择模板'
    },
    allCanUse: {
      basicComponents: '基础组件',
      config: '配置',
      businessSigns: '商家招牌',
      titleText: '标题文本',
      notice: '公告',
      goodsList: '商品列表',
      imgAd: '图片广告',
      navigationBar: '导航栏',
      activities: '促销活动',
      searchBar: '搜索栏',
      productMarketing: '商品营销',
      extendComponent: '扩展组件',
      goodsModule1: '商品模块1',
      goodsModule2: '商品模块2',
      goodsModule3: '商品模块3',
      goodsModule4: '商品模块4',
      goodsModule5: '商品模块5',
      goodsWaterfall: '瀑布流'
    },
    goods: {
      prod: '商品',
      prodName: '商品名称',
      prodDesc: '商品卖点',
      prodPrice: '商品价格',
      pleaseAddProd: '请添加商品',
      listStyle: '列表样式',
      showContent: '显示内容',
      oneLineItem1: '一行一个',
      oneLineItem2: '一行两个',
      oneLineItem3: '一行三个'
    },
    goodsWaterfall: {
      sortType: '排序方式',
      timeType: '时间范围',
      timeStatus: '排序升降序',
      shelfTime: '上架时间',
      salesVolume: '销量',
      commentCount: '评论数',
      lastYear: '最近一年',
      threeMonths: '最近三月',
      lastMonth: '最近一月',
      lastWeek: '最近一周',
      ascendingOrder: '升序',
      descendingOrder: '降序',
      tip1: '瀑布流组件只能显示在底部！',
      tip2: '瀑布流只能添加一个！'
    },
    header: {
      microTitle: '微页面标题',
      pageName: '页面名称',
      pageNameCanntEmpty: '页面名称不能为空',
      pageNamePlaceholder: '请输入页面名称'
    },
    headerAd: {
      pageTitle: '[页面标题]',
      pageHeader: '页面头部',
      pageFooter: '页面底部',
      microPage: '微页面',
      prodDetail: '商品详情',
      showPosition: '展示位置:',
      showPages: '出现的页面:'
    },
    imageAd: {
      carouselPoster: '轮播海报',
      lateralSliding: '横向滑动',
      hotArea: '绘制热区',
      prodDetPage: '商品详情页',
      actPage: '活动页',
      widthSuggest: '建议图片尺寸为750*300',
      imageAd: '图片广告',
      selModel: '选择模板',
      changeModelTips: '注意：切换模板时会清空热区',
      isShowDots: '指示点显隐',
      dotsColor: '指示点颜色',
      reset: '重置',
      actDotColor: '激活指示点',
      actDotLoc: '指示点位置',
      addPic: '添加图片',
      max10Ads: '注：最多添加10个广告',
      imgSizeTip: '如作为顶部轮播图，建议图片尺寸750*300',
      imgHeight: '图片高度',
      picTit: '图片标题',
      addHotArea: '添加热区',
      setRoute: '设置跳转',
      addBgImg: '添加背景图',
      save: '保存',
      hotAreaTit: '万能热区',
      setHotAreaRoute: '请设置热区跳转'
    },
    notice: {
      announcement: '轮播公告',
      announcementTips1: '以轮播形式展示店铺公告',
      announcementTips2: '请在客户端查看效果'
    },
    promotionalActivities: {
      addProd: '请添加商品',
      groupPurchase: '团购',
      spike: '秒杀',
      more: '更多',
      promotionalActivity: '促销活动',
      addActivityProds: '添加活动商品',
      addActivityProdsTip: '最多添加3个商品',
      activityType: '活动类型',
      promotionPrice: '促销商品价格',
      tipsContent: '检测到组件内有失效的活动商品，是否立即刷新数据？',
      tips: '提示',
      max5Prods: '最多展示5个'
    },
    tabNav: {
      picNav: '图片导航',
      picTextNav: '图文导航',
      tetxNav: '文字导航',
      line4item: '一行四个',
      line5item: '一行五个',
      cart: '购物车',
      personalCenter: '个人中心',
      customPath: '自定义路径',
      fullFillTips: '请填写完整信息',
      pleaseAddNav: '请添加导航',
      pleaseAddPic: '请添加图片',
      pleaseFillNavTitle: '请填写导航标题',
      pleaseChooseRouteLink: '请选择跳转路径',
      pleaseFillThePath: '请输入自定义路径',
      navBar: '导航栏',
      countInLine: '每行数量',
      changePic: '更改图片',
      addPic: '添加图片',
      tit: '标题',
      routeLink: '跳转路径',
      choose: '请选择',
      addNav: '新增导航',
      microPage: '微页面',
      confirm: '确定',
      cancel: '取消',
      link: '链接'
    },
    goodsModule: {
      mainTitCon: '热卖推荐',
      snappedUpImmediately: '立即抢购',
      subTitTwo: '选择对应的列数，进行详细设置',
      labelSubTit: '自定义副标题',
      conTwo1: '团购价更优惠',
      conTwo2: '今日上新72件',
      conTwo3: '发现好店',
      conOne1: '今日爆款',
      conOne2: '美好新生活',
      conOne3: '新品首发',
      conOne4: '大家都在买',
      toPay: '去抢购',
      maxAdd: '最多添加',
      addBigImg: '添加大图',
      piece: '个商品',
      suggestChoose: '建议选择',
      sameScalePic: '或者同等比例的图片',
      goodsModuleTip1: '自定义标题不能为空',
      goodsModuleTip2: '自定义副标题不能为空',
      goodsModuleTip3: '立即抢购跳转链接不能为空',
      goodsModuleTip4: '图片不能为空',
      goodsModuleTip5: '跳转链接不能为空',
      goodsModuleTip6: '商品不能为空',
      goodsModuleTip7: '主标题文字不能为空',
      goodsModuleTip8: '副标题文字不能为空',
      goodsModuleTip9: '大图不能为空',
      goodsModuleTip10: '大图跳转链接不能为空'
    },
    titText: {
      linkText: '查看更多',
      prodDet: '商品详情',
      pleaseFillTit: '请填写标题内容',
      pleaseChoosePord: '请选择商品',
      titText: '标题文本',
      titContent: '标题',
      decsContent: '描述内容',
      decsContentPlaceholder: '请输入描述内容，最多100字',
      showPos: '显示位置',
      left: '居左',
      center: '居中',
      titSize: '标题大小',
      titWeight: '标题粗细',
      descWeight: '描述粗细',
      normal: '普通',
      bold: '加粗',
      more: '查看更多',
      style: '样式',
      textContent: '文本内容',
      pleaseFillTextContent: '请填写文本内容'
    },
    businessSigns: {
      prodNum: '商品数量',
      saleGoods: '在售商品',
      selfEmployed: '自营',
      preferred: '优选',
      fans: '粉丝',
      follow: '关注',
      fen: '分',
      wan: '万',
      tip1: '商家招牌组件只能显示在头部！',
      tip2: '商家招牌只能添加一个！'
    },
    edit: {
      save: '保存',
      saveAndCon: '保存并继续',
      moreOper: '更多操作',
      noneUseableCom: '没有可用的组件',
      saveSuccess: '保存成功',
      deleteConfirm: '确定删除该组件？',
      cancel: '取消',
      confirm: '确定',
      componentManagement: '组件管理',
      emptyTemplate: '页面组件为空',
      removeAll: '清空组件',
      removeComponent: '删除组件',
      back: '返回',
      componentSorting: '组件排序'
    },
    list: {
      newMicroPage: '新增页面',
      pageName: '页面名称',
      shopHomePage: '店铺主页',
      createTime: '创建时间',
      updateTime: '更新时间',
      oper: '操作',
      edit: '编辑',
      modify: '修改',
      view: '查看',
      delete: '删除',
      setHomePage: '设为主页',
      cancelHomePage: '取消主页',
      deleteTips: '确认删除吗？',
      tips: '提示',
      deleteSuccess: '删除成功！',
      operSuccess: '操作成功'
    },
    searchBar: {
      searchBar: '搜索栏',
      search: '搜索',
      normal: '正常',
      topThenLock: '滚至顶部固定',
      square: '方形',
      fillet: '圆角',
      left: '居左',
      center: '居中',
      showPos: '显示位置',
      textPos: '文本位置',
      boxStyle: '框体样式',
      boxHeight: '框体高度',
      bgColor: '背景颜色',
      boxColor: '框体颜色',
      textColor: '文字颜色'
    }
  },
  settlementDetail: {
    tip1: '结算明细',
    tip2: '所有订单在订单完成并且超过退款时效的次日入账，如存在未处理完的退单，则延迟到退单处理完成（对应退单状态为：拒绝收货、拒绝退款、已作废、已完成）的次日入账；',
    tip3: '名词解释：',
    tip4: '商品单价：商品原价，包括设价后的金额；',
    tip5: '数量(不含退)：结算时订单中商品数量需排除掉退款完成的数量，结算时满减优惠金额、店铺优惠券优惠金额、平台优惠券优惠金额、使用积分、订单改价差额、商品实付金额、供货金额都以不含退的数量来计算；',
    tip6: '满减优惠金额：商品参加满减、满折活动减免的金额，成本由商家承担；',
    tip7: '店铺优惠券优惠金额：商品使用了店铺优惠券减免的金额，成本由商家承担；',
    tip8: '平台优惠券优惠金额：商品使用了通用优惠券减免的金额，成本由平台承担，在结算时补偿给商家；',
    tip9: '订单改价差额：商家修改订单金额产生的差价，差价可正可负；',
    tip10: '使用积分：用户在支付时使用使用积分的部分，成本由平台承担，在结算时补偿给商家；',
    tip11: '商品实付金额：商品在进行了各类优惠活动、虚拟资产抵扣、订单改价后的实付金额；',
    tip12: '未改价订单商品实付金额=商品原价-满减优惠-满折优惠-店铺优惠券优惠金额-平台优惠券优惠金额-使用积分',
    tip13: '有改价订单商品实付金额=改价后金额',
    tip14: '供货金额：代销供应商商品需要结算给供应商的金额；',
    tip15: '分账比例：商品所属类目产生销售时需支付给平台的佣金，结算比例以用户下单时为准；',
    tip16: '平台佣金：平台应收的佣金，每件商品平台佣金=（商品实付金额+平台优惠）*类目扣率，商品实付金额不包含商品运费；',
    tip17: '退单改价差额：订单产生退款，商家若修改退单金额导致商品实退金额小于实付金额，则在结算时将差额补偿给商家；',
    tip18: '运费：用户支付的运费，由平台代收，在结算时需返还给商家；',
    tip19: '店铺实收金额：每笔订单店铺实收金额=商品实付金额+退单改价差额+运费+平台优惠券优惠金额+使用积分-平台佣金-分销佣金；',
    tip20: '店铺实收总额：店铺实收总额=每笔订单店铺实收金额之和',
    tip21: '商品实付总额：每笔订单商品实付金额之和；',
    tip22: '供货总额：每笔订单需结算给供货商的供货金额总和；',
    tip23: '平台佣金总额：每笔订单平台佣金之和；',
    tip24: '分销佣金比例 ：商家设置的分销员代销商品可获得的佣金比例，结算比例以用户下单时为准；',
    tip25: '分销佣金：商家支付给分销员的佣金，每款商品平台佣金=商品实付金额 * 佣金比例；',
    tip26: '分销佣金总额：每笔订单分销佣金之和；',
    tip27: '结算说明',
    tip2_1: '结算明细',
    tip2_2: '如果一笔订单进行整单退款或每个订单项都已经退款完成，那么会立即结算给商家。其他情况皆为确认收货15天后进行结算；',
    tip2_3: '名词解释：',
    tip2_4: '- 购买数量：用户购买商品数量，不包含售后数量；',
    tip2_5: '- 商品总金额：商品原价*购买数量，不包含售后金额；',
    tip2_6: '- 用户支付金额：扣除平台优惠(平台优惠券优惠、会员折扣优惠、积分抵扣金额)、店铺优惠(优惠券、满减折、秒杀优惠、团购优惠、优惠套餐、订单改价)，商品实际支付的金额；',
    tip2_7: '- 商家优惠金额：商品使用店铺优惠总和，包含商家改价金额；',
    tip2_8: '- 平台优惠金额：商品使用平台优惠总和，成本由平台承担，在结算时补偿给商家；',
    tip2_9: '商家未改价：平台优惠金额=平台优惠券优惠+会员折扣优惠+积分抵扣金额',
    tip2_10: '商家改价：平台优惠金额=原平台优惠-(商品减少金额/商品原价*(平台优惠券优惠+会员折扣优惠+积分抵扣金额))',
    tip2_11: '- 平台津贴减少金额：商家改价减少的平台优惠金额；',
    tip2_12: '商家改价：平台津贴减少金额=商品减少金额/商品原价*(平台优惠券优惠+会员折扣优惠+积分抵扣金额)',
    tip2_13: '- 分销金额：商家支付给分销员的佣金；',
    tip2_14: '按比例奖励：分销金额=商品扣除平台优惠支付金额*分销奖励比例',
    tip2_15: '按固定数值奖励：分销金额=固定数值奖励，商品扣除平台优惠支付金额如果小于奖励数值，分销金额为0',
    tip2_16: '- 使用积分：用户在支付时使用的积分；',
    tip2_17: '- 类目扣率：该商品签约分类的对应扣率；',
    tip2_18: '- 平台佣金：商品所属类目产生销售时需支付给平台的佣金；',
    tip2_19: '每件商品的平台佣金=（商品实付金额+平台优惠券优惠+会员折扣优惠+积分抵扣金额）*类目扣率；',
    tip2_20: '- 积分抵扣金额：用户使用积分抵扣的金额，根据平台端的使用比例和抵扣比例计算；',
    tip2_21: '- 会员折扣金额：会员等级特权中折扣产生的优惠金额，成本由平台承担，在结算时补偿给商家；',
    tip2_22: '- 平台优惠券优惠金额：商品使用了平台优惠券减免的金额，成本由平台承担，在结算时补偿给商家；',
    tip2_23: '- 商家优惠券优惠金额：商品使用了店铺优惠券减免的金额，成本由商家承担；',
    tip2_24: '- 满减优惠金额：商品参加满减、满折活动减免的金额，成本由商家承担；',
    tip2_25: '- 拼团优惠金额：商品参加拼团活动减免的金额，成本由商家承担；',
    tip2_26: '- 秒杀优惠金额：商品参加秒杀活动减免的金额，成本由商家承担；',
    tip2_27: '- 套餐优惠金额：商品参加优惠套餐活动减免的金额，成本由商家承担；',
    tip2_28: '- 店铺改价金额：用户提交订单后商家更改商品的金额；',
    tip2_29: '- 退款金额：订单发生售后，用户申请的退款金额；',
    tip2_30: '- 退款数量：订单发生售后，用户申请的退款数量；',
    tip2_31: '- 运费：计算优惠时，不包含运费；',
    tip2_32: '商家改运费：用户提交订单后商家更改订单运费金额',
    tip2_33: '直营店包邮：用户等级特权中包邮产生的优惠金额，成本由平台承担，在结算时补偿给商家',

    prodTotalAmount: '商品总金额',
    multishopReduce: '商家优惠金额',
    platformShareReduce: '平台优惠金额',
    useScore: '使用积分',
    scoreAmount: '积分抵扣金额',
    distributionAmount: '分销金额',
    platMemberAmount: '平台会员折扣',
    shopMemberAmount: '店铺会员折扣',
    platformCouponAmount: '平台优惠券优惠金额',
    shopCouponAmount: '商家优惠券优惠金额',
    discountAmount: '满减优惠金额',
    comboAmount: '套餐优惠金额',
    seckillAmount: '秒杀优惠金额',
    groupAmount: '拼团优惠金额',
    shopChangeFreeAmount: '店铺改价金额',
    platformShopChangeAmount: '平台优惠减少金额',
    refundAmount: '退款金额',
    refundCount: '退款数量',
    freeFreightAmount: '运费变化金额：',
    freeFreightAmountTips: '订单管理中修改的订单运费金额',
    platformFreeFreightAmount: '平台运费减免金额',
    platformFreeFreightAmountTips: '用户等级特权中包邮产生的优惠金额',
    rate: '分账比例'
  },
  chat: {
    userName: '用户名',
    official: '官方',
    stores: '店铺',
    clickToLoadMore: '点击加载更多',
    mallCustomerService: '商城客服',
    sendLink: '发送链接',
    send: '发送',
    inquiring: '正在查询',
    myOrders: '我的订单',
    recentlyViewed: '最近浏览',
    orderNumber: '订单号',
    loading: '加载中',
    noMore: '没有更多了',
    read: '已读',
    unRead: '未读',
    noRecord: '暂无记录',
    chatRecordTips1: '以上是',
    chatRecordTips2: '客服聊天记录',
    customerOffline: '当前客服不在线',
    loginOtherSide: '用户在别处登陆,请刷新页面',
    reLogin: '无法获取用户信息,请重新登陆',
    sendOrderNumber: '发送订单号',
    pleaseLoginAgain: '账号已下线，请重新登录',
    paymentAmount: '支付金额',
    onlineCustomerService: '在线客服',
    selectOnlineCustomerService: '选择在线的其他客服',
    productLinks: '商品链接',
    orderLink: '订单链接',
    none: '无',
    pleaseSelectAcustomerService: '请选择一个客服转接',
    storeGoneOffline: '商家',
    offline: '已下线',
    resumeGoingLive: '请恢复上线',
    Closed: '已停业',
    notEnabled: '未开通',
    messageBox: '消息盒子',
    transferred: '已转接',
    pendingPayment: '待付款',
    pendingDelivery: '待发货',
    pendingReceipt: '待收货',
    pendinEvaluation: '待评价',
    grouping: '拼团中',
    evaluated: '已评价',
    shipped: '已发货',
    completed: '已完成',
    canceled: '已取消',
    sorryYouHaveBeenDisconnected: '对不起您已断开连接，正在重连',
    notYourResponsibility: '不是你负责的客户',
    noContactReceivedMessage: '无接收消息的联系人',
    transferToCustomerService: '转接客服',
    transferTips: '可设置多个客服，在后台[系统一员工列表]添加有客服角色权限的账号，有其他客服账号在线时可进行转接。',
    connetBroken: '连接已断开，请刷新页面重试',
    tips: '提示',
    confirm: '确认',
    cancel: '取消',
    noContacts: '暂无联系人',
    cannotSendBlankMessage: '不能发送空白消息哦',
    justNow: '刚刚',
    yesterday: '昨天',
    twoDaysAgo: '前天',
    Sunday: '周日',
    Monday: '星期一',
    Tuesday: '星期二',
    Wednesday: '星期三',
    Thursday: '星期四',
    Friday: '星期五',
    Saturday: '星期六',
    PleaseSelectAContactPerson: '请选择联系人',
    AccountCancelled: '账户已注销'
  },
  shopProcess: {
    auditTip1: '提交工商信息审核成功',
    auditTip2: '撤回修改申请成功',
    notAudit: '未审核',
    passed: '已通过',
    notPass: '未通过',
    viewModify: '查看修改信息',
    denialReason: '拒绝原因',
    businessTerm: '营业期限',
    merchantDetail: '商家详情',
    basicInfo: '基本信息',
    businessInfo: '工商信息',
    theContractIsNotValid: '未到签约有效期',
    validityPeriodOfTheContractHasExpired: '签约有效期已过',
    signUpInfo: '签约信息',
    financeInfo: '财务信息',
    disable: '禁用',
    enable: '启用',
    delete: '删除',
    deleted: '已删除',
    closed: '停业中',
    inOperation: '营业中',
    onlinePendingReview: '上线待审核',
    shopApplication: '开店申请中',
    storeOpenPendingReview: '开店待审核',
    onlineAuditFailure: '上线审核失败',
    storeAccount: '店铺账号',
    merchantSelfIncreasing: '商家自增',
    validityPeriodOfContract: '签约有效期',
    validPeriod: '商家店铺有效期',
    shopType: '店铺类型',
    ordinaryShop: '普通店铺',
    preferredGoodShop: '优选好店',
    masterAccount: '主账户',
    yes: '是',
    no: '否',
    group: '组',
    audit: '审核',
    reject: '驳回',
    rejectRemarks: '请填写200字以内的驳回原因',
    rejectInputTips: '请填写驳回原因',
    merchantReview: '商家审核',
    addSigning: '请补充签约信息',
    submit: '提交',
    customRate: '自定义类目扣率',
    saveSubmit: '保存并提交审核',
    changeBusinessInfor: '工商信息变更申请',
    reAmendmentRequest: '撤销修改申请',
    close: '关闭',
    noYet: '暂无',
    detailed: '详细地址',

    openAShop: '申请开店',
    auditStatus: '审核状态',
    accountStatus: '账号状态',
    shopStatus: '店铺状态',
    notSubmitApplyTips: '您还没有开店，请提交您的开店申请！',
    applyShop: '申请开店',
    applyAuditingTips: '您的开店申请正在审核中！请耐心等待~',
    applyAuditing: '开店申请审核中',
    applyFailTips: '您的开店申请未通过！原因是：',
    applyFail: '开店申请审核未通过',
    previousStep: '上一步',
    seePreviousStep: '查看上一步',
    nextStep: '下一步',
    submitAndNextStep: '提交，下一步',
    seeNextStep: '查看下一步',
    merchantName: '商家名称',
    shopName: '店铺名称',
    detailAddr: '详细地址',
    email: '联系邮箱',
    tel: '联系方式',
    receiveMobile: '接收通知手机号',
    receiveMobileTips: '接收短信通知手机号',
    addr: '所在地区',
    intro: '店铺简介',
    logo: '店铺logo',
    logoTips: '仅限jpg、gif、png，仅限上传1张',
    shopLogoPicTips: '建议尺寸80*80像素, ',
    backgroundPic: '店铺背景图',
    mobileBackgroundPic: '移动端背景图',
    mobilePicTips: '(建议尺寸750*380)',
    pcBackgroundPic: 'pc端背景图',
    pcPicTips: '(建议尺寸590*220)',
    businessLicense: '营业执照电子版',
    example: '示例',
    corporateIdentityCard: '法人身份证',
    identityCardTips: '请上传身份证正反面照片，仅限jpg、gif、png，仅限上传2张',
    identityCardFront: '人像面示例',
    identityCardLater: '国徽面示例',
    merchantNameNotEmpty: '商家名称不能为空',
    merchantNameErrorTips: '商家名称长度在2-10个字符之间',
    merchantNameInputTips: '请输入2-10字以内的商家名称',
    shopNameNotEmpty: '店铺名称不能为空',
    shopNameInputTips: '请输入2-20字以内的店铺名称',
    emailNotEmpty: '联系邮箱不能为空',
    emailInputTips: '请输入常用联系邮箱',
    emailErrorTips: '请输入正确的邮箱',
    inputAllSpace: '内容不能全为空格',
    reasonAllSpace: '申请理由不能全为空格',
    telNotEmpty: '请填写联系方式',
    receiveMobileNotEmpty: '请填写接收通知手机号',
    telErrorTips: '请输入正确的联系方式',
    telInputTips: '请输入常用联系人11位手机号码',
    addrNotEmpty: '请先选择省市区',
    addrInputTips: '请选择所在地区',
    detailAddrNotEmpty: '详细地址不能为空',
    detailAddrInputTips: '请输入50字以内的详细地址',
    introInput: '请输入200字以内店铺简介',
    logoNotEmpty: '请上传店铺logo',
    mobilePicNotEmpty: '请上传移动背景图',
    pcPicNotEmpty: '请上传pc端背景图',
    businessLicenseNotEmpty: '营业执照需要上传',
    identityCardFrontNotEmpty: '请上传身份证人像面',
    identityCardLaterNotEmpty: '请上传身份证国徽面',
    baseSaveSuccess: '基本信息保存成功',
    businessSaveSuccess: '工商信息保存成功',
    categorySaveSuccess: '类目保存成功',
    categoryAddSuccess: '新增签约类目成功',
    categoryApplySuccess: '已提交签约类目申请',
    brandSaveSuccess: '品牌保存成功',
    brandAddSuccess: '新增品牌成功',
    brandApplySuccess: '已提交签约品牌申请',
    creditCode: '统一社会信用代码',
    creditCodeErrorTips: '请输入正确的统一社会信用代码',
    creditCodeInputTips: '请输入统一信用代码',
    creditCodeNotEmpty: '社会信用代码不能为空',
    firmName: '企业名称',
    firmNameInputTips: '请输入50字以内的企业名称',
    firmNameNotEmpty: '企业名称不能为空',
    residence: '住所',
    residenceInputTips: '请输入50字以内的住所',
    representative: '法定代表人',
    representativeInputTips: '请输入20字以内的法定代表人',
    representativeNotEmpty: '法定代表人不能为空',
    capital: '注册资本',
    tenThousandYuan: '万元',
    fountTime: '成立日期',
    startTime: '开始日期',
    endTime: '结束日期',
    businessTime: '营业日期',
    businessTimeNotEmpty: '营业日期不能为空',
    businessScope: '经营范围',
    noFixedTerm: '无固定期限',
    businessScopeInputTips: '请输入500字以内的经营范围',
    businessScopeNotEmpty: '经营范围不能为空',
    signingCategory: '签约类目',
    category: '类目',
    categoryAddMaxLimitTips: '最多可选择200个类目',
    categoryName: '类目名称',
    parentCategoryName: '上级类目',
    categoryRate: '类目扣率',
    categoryQualifications: '经营资质',
    brandQualifications: '授权文件',
    brandQualificationsNotEmpty: '请上传品牌授权文件',
    brandMaxLimitTips: '您最多只能签约50个品牌',
    categoryStatus: '类目状态',
    signingBrand: '签约品牌',
    brandStatus: '品牌状态',
    customBrand: '自定义品牌',
    brand: '品牌',
    brandName: '品牌名称',
    brandNameRepeatTip: '品牌名称重复',
    firstLetter: '检索首字母',
    brandLogo: '品牌logo',
    editSigningCategory: '新增签约类目',
    editSigningBrand: '新增签约品牌',
    signingCategoryTips: '请选择末级类目签约',
    signing: '签约',
    preSigned: '已签约',
    application: '申请中',
    chosen: '已选择',
    piece: '个',
    mostSigning: '最多可签约',
    categorySigningNotEmpty: '请选择至少一种分类',
    add: '添加',
    added: '已添加',
    mostAdd: '最多可添加',
    settlementAccountEditTips: '如需修改结算账户请在店铺信息模块操作',
    settlementAccount: '结算账户',
    addSettlementAccount: '新增账户',
    bankName: '银行名称',
    brandNameInputTips: '请输入银行名称',
    brandNameNotEmpty: '银行名称不能为空',
    brandNameErrorTips: '银行卡长度在2-20个字符',
    accountErrorTips: '请输入正确的账户',
    cardNo: '账号',
    account: '账户',
    cardNoInputTips: '请输入银行卡号',
    cardNoNotEmpty: '账号不能为空',
    accountNotEmpty: '账户不能为空',
    recipientName: '账户名',
    recipientNameInputTips: '账户名长度在2到20个字符',
    bankCardInputTips: '账户名长度在8到30个字符',
    recipientNameNotEmpty: '账户名不能为空',
    cardNoErrorTips: '请输入正确银行卡号',
    openingBank: '银行开户行',
    openingBankNotEmpty: '开户行不能为空',
    openingBankErrorTips: '开户行名称长度在2到20个字符',
    branchInputTips: '例如：南京市雨花区雨花支行',
    branchNameNotEmpty: '支行名称不能为空',
    cardMaxLimitTips: '最多可添加5个结算账户',
    submitApply: '提交申请',
    submitApplySuccessTips: '店铺申请提交成功',

    addSigningCategory: '新增签约类目',
    applySigningCategory: '申请签约类目',
    applySigningBrand: '新增签约品牌',
    seeApplySigningCategory: '查看签约类目申请',
    seeApplySigningBrand: '查看签约品牌申请',
    setMainAccount: '设为主账户',
    setNotMainAccount: '取消主账户',
    readShopProtocolTips: '请先阅读并同意下方《商家入驻协议》',
    incompleteInformation: '信息不完整',
    applyOnline: '申请上线',
    saveBasicInfo: '保存信息',
    saveCompanyInfo: '保存信息',
    saveCompanyInfoSuccess: '工商信息保存成功',
    shopBankCardDeletePre: '是否确认删除尾号',
    shopBankCardDeleteAfter: '的结算账户?',
    setSuccess: '设置成功',
    cancelMainAccountSuccess: '取消主账号成功',
    platformBrand: '平台品牌',
    merchantCustomBrand: '商家自定义品牌',
    cancel: '取消',
    confirm: '确定',
    noBrandSelected: '未选择品牌！',
    topNavbarTitle: 'Mall4j宇宙版 商家端',
    saveSuccessfully: '保存成功！',
    deleteSuccessfully: '删除成功！',
    modify: '修改',
    businessStartEndTime: '营业起始日期不能大于营业终止时间',
    saveSuccess: '保存成功',
    deleteSuccess: '删除成功',
    yourStoreIsOfflineBecauseOf: '您的店铺已下线，原因是：',
    onlineShopTips: '如有问题可在店铺信息页重新申请上线',
    shopStopReason: '您的店铺已停业，原因是',
    shopStopTips: '如有问题请联系平台',
    applyingOnline: '您的店铺正在申请上线',
    shopIsUnderReview: '店铺待审核中',
    withdrawalApplicationOfContract: '确定要撤回本次签约申请吗？',
    contractApplicationWithdrawn: '签约申请已撤销',
    addShopErrorTip7: '密码只能为数字或字母',
    withdrawalTips1: '确定要撤回',
    withdrawalTips2: '的签约信息吗？'
  },
  purchase: {
    order: {
      purchaseNumber: '采购编号',
      dvyFlowId: '物流单号',
      supplier: '供应商',
      supplierProd: '商品',
      deliverTime: '送达日期',
      deliverTimeNotEmpty: '送达日期不能为空',
      remark: '备注',
      deliveryMethod: '配送方式',
      newPurchaseOrder: '新建采购订单',
      viewPurchaseOrder: '查看采购订单',
      purchaseOrderInbound: '采购订单入库',
      newPurchaseInbound: '新建采购库存',
      viewPurchaseInbound: '查看采购库存',
      purchaseNum: '采购数量',
      purchasePrice: '采购单价(元)',
      purchaseNumThanMinimum: '采购数量必须大于供应商商品的最小订货量',
      purchasePriceMustThen0: '采购价格需要大于供应商采购单价',
      purchaseAmount: '采购金额',
      purchaseProdUploadTip: '导入自采供应商的商品采购价默认为商品原价',
      selectSupplierProduct: '请选择供应商商品',
      selectSupplier: '请选择供应商',
      inbound: '入库',
      nullify: '作废',
      finish: '完成',
      estimatedIncomingQuantity: '预计入库量',
      numberInStock: '已入库量',
      remainingIncomingQuantity: '剩余入库量',
      actualIncomingQuantity: '实际入库量',
      voided: '已作废',
      warehoused: '待入库',
      partiallyComplete: '部分入库',
      complete: '已完成',
      stockNumber: '库存编号',
      purchaseProdTemplate: '采购商品.xlsx',
      purchaseOrderTemplate: '采购订单.xlsx',
      determineCancellationPurchaseOrder: '确定作废该采购订单',
      finalizePurchaseOrder: '确定完成该采购订单',
      exportInboundProd: '导出入库商品',
      importInboundProd: '导入入库商品',
      deleted: '已删除'
    }
  },
  stock: {
    exportRadioEmptyTips: '请选择要导出的方式',
    exportStockLogSearchEmptyTips: '当前筛选出的数据列表为空',
    exportStockLogSelectEmptyTips: '请选择要导出的数据',
    skuStockInfo: '商品库存信息',
    skuStockXls: '商品库存信息.xlsx',
    stockFlowXls: '库存流水信息.xlsx',
    stockBillLogXls: '入库明细信息.xlsx',
    stockBillLogOutXls: '出库明细信息.xlsx',
    newOutboundDetails: '新建出库明细',
    newWarehousingDetails: '新建入库明细',
    confirmDelivery: '确认出库',
    confirmWarehousing: '确认入库',
    deliveryCount: '出库数量',
    warehousingCount: '入库数量',
    saveDraft: '保存草稿',
    exportSuccess: '导出成功',
    deliveryDate: '出库日期',
    deliveryDateNotEmpty: '出库时间不能为空',
    warehousingDate: '入库日期',
    warehousingDateNotEmpty: '入库时间不能为空',
    deliveryReason: '出库原因',
    deliveryReasonNotEmpty: '出库原因不能为空',
    warehousingReason: '入库原因',
    warehousingReasonNotEmpty: '入库原因不能为空',
    appendix: '附件',
    deliveryCertificate: '出库凭证',
    warehousingCertificate: '入库凭证',
    businessInQualTips: '请上传入库凭证等资质文件，支持 png、jpg、jpeg 文件，最多上传4张',
    businessOutQualTips: '请上传出库凭证等资质文件，支持 png、jpg、jpeg 文件，最多上传4张',
    batchImport: '批量导入',
    batchExport: '批量导出',
    prodCountTips: '种商品，合计入库量',
    prodCountOutTips: '种商品，合计出库量',
    prodNotEmpty: '商品明细不能为空，请选择商品',
    unit: '单位',
    pieces: '件',
    outInStock: '出入库',
    afterStock: '剩余量',
    availableStock: '可用库存',
    newlyBuildOtherStorage: '新建其他入库',
    exportStorageDetails: '导出入库明细',
    export: '导出',
    exportOfSearchStorage: '导出筛选出的入库明细',
    exportOfSelectStorage: '导出选中的入库明细',
    exportOfSearchFlow: '导出筛选出的库存流水',
    exportOfSelectFlow: '导出选中的库存流水',
    exportOfSearchInquire: '导出筛选出的库存信息',
    exportOfSelectInquire: '导出选中的库存信息',
    newlyBuildOtherOutbound: '新建其他出库',
    exportOutboundDetails: '导出出库明细',
    retailInfo: '单品信息',
    exportOfSearchOutbound: '导出筛选出的出库明细',
    exportOfSelectOutbound: '导出选中的出库明细',
    all: '全部',
    waitSubmit: '待提交',
    inStorage: '已入库',
    inOutbound: '已出库',
    voided: '已作废',
    spec: '规格',
    outProdFileName: '其他出库excel模板.xlsx',
    inProdFileName: '其他入库excel模板.xlsx',
    downloadSuccessful: '下载成功',
    stockBillReason: '出入库原因',
    stockBillCnReason: '出入库中文原因',
    stockBillReasonNotEmpty: '出入库原因不能为空',
    stockBillEnReason: '出入库英文原因',
    stockBillReasonInputTips: '请输入出入库原因',
    stockType: '出入库类别',
    stockTypeNotEmpty: '出入库类别不能为空',
    sysSet: '系统内置',
    sendStock: '出库',
    receiveStock: '入库',
    viewRemark: '查看备注',
    remark: '备注',
    remarkCn: '中文备注',
    remarkCnNotEmpty: '中文备注不能为空',
    remarkEn: '英文备注',
    price: '销售价（元）',
    queryCondition: '查看同款',
    stockBillNo: '单据编号',
    sourceOrderNo: '关联单号',
    pleaseStockBillNo: '请输入单据编号',
    pleaseSourceOrderNo: '请输入关联单号',
    stockBillType: '单据类型',
    billStatus: '单据状态',
    createOrderTime: '制单时间',
    purchaseInStock: '采购入库',
    returnToStorage: '退款入库',
    editStorage: '编辑入库',
    otherEntries: '其他入库',
    sellOut: '销售出库',
    otherOutbound: '其他出库',
    editOutBound: '编辑出库',
    profitStorage: '盘盈入库',
    lossOutBound: '盘亏出库',
    inventoryInitialization: '库存初始化',
    orderCancelled: '订单取消',
    outStockTime: '出库时间',
    inStockTime: '入库时间',
    outStockOrderNo: '出库单号',
    inStockOrderNo: '入库单号',
    actualOutCount: '实际出库量',
    actualInCount: '实际入库量',
    totalOutAmount: '合计出库金额（元）',
    totalOutAmount2: '合计出库金额',
    totalInAmount: '合计入库金额（元）',
    totalInAmountTips: '合计入库金额=入库金额*实际入库数量；入库金额：采购入库使用的是成本价，其他单据类型使用的是发布商品时设置的销售价',
    totalInAmount2: '合计入库金额',
    costAmount: '成本单价（元）',
    costTotalAmount: '成本金额（元）',
    voidInventoryTips: '作废后将无法继续编辑，确定作废吗？',
    uploadNotEmptyTips: '已添加的商品不为空，不支持批量导入',
    countNotEmptyOr0: '数量不能为空或者等于0',
    createOrderRemark: '制单备注',
    retailName: '单品名称',
    retailSearch: '单品筛选',
    retailNo: '单品编码',
    retailStockXls: '单品库存信息.xlsx',
    retailUnit: '单品单位',
    deleteFailureProd: '删除失效商品',
    include: '包含',
    failureCountTips: '种已失效的商品',
    viewTransferOrder: '查看调拨订单',
    transferNumber: '调拨编号',
    outStockPoints: '调出库存点',
    inStockPoints: '调入库存点',
    warehouse: '仓库',
    station: '门店',
    transferType: '调拨类型',
    transferQuantity: '调拨数量',
    transferDate: '调拨日期',
    partiallyComplete: '部分入库',
    complete: '已完成',
    voidInventory: '作废',
    details: '详情',
    warehoused: '待入库',
    transferVoidInventoryTip: '确定作废该调拨订单?',
    tip: '提示',
    confirm: '确认',
    cancel: '取消',
    success: '操作成功',
    createTransferOrder: '新建调拨订单',
    warehouseTransferOrder: '调拨订单入库',
    pleaseSelect: '请选择',
    pleaseSelectOutPoint: '请选择调出库存点',
    pleaseSelectInPoint: '请选择调入库存点',
    prodInfo: '商品信息',
    transferOutStorage: '调出仓库存',
    transferInStorage: '调入仓库存',
    product: '商品',
    prodName: '商品名称',
    estimatedNumberIncomingStorage: '预计入库数',
    actualNumberStorage: '实际入库数',
    defaultWarehouseAddress: '请先将默认仓库的地址信息填写完整',
    outStockPointEmptyTip: '调出库存点不能为空',
    inPointEmptyTip: '调入库存点不能为空',
    pleaseSelectPointTip: '请先选择调出库存点与调入库存点',
    pleaseSelectTransferProdect: '请选择调拨商品',
    transferNumberTip: '调拨数量不能为0',
    transferOrderProductTemplate: '调拨订单商品模板',
    uploadTransferOrderProd: '导入调拨订单商品',
    uploadSuccess: '导入成功',
    commodityPrice: '商品价格',
    warehouseName: '仓库名称',
    inputWareHouseName: '输入仓库名称',
    warehouseNameEmpty: '仓库名称不能为空',
    warehouseType: '仓库类型',
    regionalWarehouse: '区域仓库',
    defaultWarehouse: '默认仓库',
    custodian: '管理人',
    inputCustodian: '输入管理人',
    custodianPhone: '管理人电话',
    search: '搜索',
    reset: '重置',
    newlyIncreased: '新增',
    warehouseAddress: '仓库地址',
    edit: '编辑',
    merchandiseInventory: '商品库存',
    delete: '删除',
    isDeleOper: '确定进行删除操作',
    supplyArea: '供货区域',
    setArea: '设置区域',
    InputCorrectPhone: '请输入正确的手机号',
    custodianEmptyTip: '管理人不能为空',
    warehouseNameTip: '请填写仓库名称',
    custodianPhoneTip: '请填写管理人电话',
    custodianTip: '请填写管理人',
    supplyAreaTip: '请选择供货区域',
    warehouseAddressTip: '请填写仓库地址',
    newWarehouse: '新建仓库',
    editWarehouse: '编辑仓库',
    inventory: '库存',
    currentWarehouseInventory: '当前仓库存',
    stockPointName: '库存点名称',
    pleaseSelectStockPoint: '请选择库存点',
    pleaseSelectStockPointFirst: '请先选择库存点',
    pleaseSelectStockPointType: '请选择库存点类型',
    viewStock: '查看库存',
    warehouseInventory: '仓库库存',
    storeInventory: '门店库存',
    stationNames: '门店名称',
    stockPointType: '库存点类型',
    stockMold: '库存模式',
    sharedHeadquartersInventory: '共享总部库存',
    stockMoldTip1: '门店产生销售时，扣减默认仓库的商品库存',
    independentSellingInventory: '独立销售库存',
    stockMoldTip2: '门店产生销售时，扣减该门店的商品库存',
    switchTo: '切换成',
    sharedHeadquartersInventoryTip1: '1.切换后，门店商品可售库存将会同步为默认仓库（总部）商品库存，商品销售后对默认仓库（总部）的库存进行扣减；',
    sharedHeadquartersInventoryTip2: '2.切换后将不能单独对该门店的商品库存进行修改；',
    sharedHeadquartersInventoryTip3: '3.切换库存模式对正在进行秒杀的商品不生效；',
    sharedHeadquartersInventoryTip4: '4.请合理选择门店原先库存的处理方式，退回总部或将原先门店库存清零。',
    independentSellingInventoryTip1: '1.切换后，门店商品可售库存将会由门店实际库存决定，商品销售后对门店库存进行扣减；',
    independentSellingInventoryTip2: '2.切换后可单独对该门店的商品库存进行修改；',
    independentSellingInventoryTip3: '3.切换库存模式对正在进行秒杀的商品不生效；',
    originalStock: '原先库存',
    originalStockSelect1: '门店库存同步回默认仓库（总部）',
    originalStockSelect2: '门店库存清零',
    transferWarehouse: '调拨入库',
    transferOutWarehouse: '调拨出库',
    inventoryModeSwitchesOutInventory: '库存模式切换出库',
    inventoryModeSwitchesInInventory: '库存模式切换入库',
    secondKillReplenishmentStock: '秒杀补充库存',
    voucherExpire: '卡券过期',
    code: '编码',
    inquire: '查询',
    prodStatus: '商品状态',
    LowerShelf: '下架',
    UpperShelf: '上架',
    platformFrame: '平台下架',
    moderated: '等待审核',
    noData: '暂无数据',
    switchInventoryMode: '切换库存模式',
    numericOrAlphabetic: '账号只能为数字或字母',
    landlineNumberNoNUll: '请输入正确的座机号',
    QQNumberNoNull: '请输入正确的QQ号',
    microSignalNoNull: '请输入正确的微信号',
    productAvailable: '当前商品已失效',
    hellow: '你好',
    defaultWarehouseTip: '默认仓库默认全国可供货，即区域仓库外的所有区域均可供货，无需设置供货区域',
    regionalWarehouseTip: '区域仓库供货区域不重叠，且区域内的快递订单都将由该仓库进行供货，请合理分配仓库库存'
  },
  combo: {
    name: '套餐名称',
    comboStatus: '活动状态',
    type: '套餐类型',
    totalAmount: '套餐销售价',
    addCombo: '新增套餐',
    nameNotEmpty: '套餐名称不能为空',
    editCombo: '编辑套餐',
    viewCombo: '查看套餐',
    fixedCombo: '固定套餐',
    close: '关闭',
    open: '开启',
    fixedComboTips: '套装内所有商品打包销售，消费者需成套购买整个套餐',
    matchingCombo: '搭配套餐',
    matchingComboTips: '套餐内主商品必选，搭配商品可设置可选或不可选',
    comboAmount: '套餐金额',
    comboProd: '套餐商品',
    comboPic: '套餐主图',
    comboPicNotEmpty: '套餐主图不能为空',
    mainProdErrorTips: '主商品不能为空',
    matchingProdErrorTips: '搭配商品数量最多4件',
    comboMainProd: '套餐主商品',
    comboMatchingProd: '套餐搭配商品',
    isRequired: '是否必选',
    soldNum: '套餐销量',
    yes: '是',
    no: '否',
    matchingPrice: '搭配单价',
    mainProdTips: '主商品为必买商品，此商品详情页展示搭配套餐',
    matchingProdTips: '搭配商品用户可选择购买，最多设置4件',
    participateSpec: '参与规格',
    leastNum: '起搭数量',
    maxChose: '最多可以选择',
    editSpec: '选择规格',
    countProd: '件商品',
    delete: '删除',
    back: '返回',
    mainProd: '主商品名称',
    matchingProd: '搭配商品名称',
    mainAndMatchProdNotDvyTips: '该商品与主商品或必选商品没有相同的配送方式，不可购买'
  },
  giveaway: {
    name: '赠品名称',
    mainProdName: '主商品名称',
    mainProdInfo: '主商品信息',
    giveawayStatus: '活动状态',
    type: '赠品类型',
    totalAmount: '销售金额',
    addGiveaway: '新增赠品',
    nameNotEmpty: '赠品名称不能为空',
    editGiveaway: '编辑赠品',
    viewGiveaway: '查看赠品',
    close: '关闭',
    open: '开启',
    matchingGiveaway: '搭配赠品',
    giveawayAmount: '赠品金额',
    giveawayProd: '赠品商品',
    giveawayPicNotEmpty: '赠品主图不能为空',
    mainProdErrorTips: '主商品不能为空',
    giveawayProdErrorTips: '赠送商品不能为空',
    giveawayMainProd: '主商品',
    giveawayMatchingProd: '赠送商品',
    isRequired: '是否必选',
    soldNum: '赠品销量',
    yes: '是',
    no: '否',
    refundPrice: '售后价',
    mainProdTips: '主商品为必买商品，此商品详情页展示搭配赠品',
    matchingProdTips: '搭配商品用户可选择购买，最多设置4件',
    participateSpec: '参与规格',
    giveawayNum: '赠送数量',
    maxChose: '最多可以选择',
    countProd: '件商品',
    countSku: '件sku',
    delete: '删除',
    addInKindProd: '添加实物商品',
    giveawayLimitTips: '赠送商品最多设置15个sku',
    refundPriceTips: '当赠品订单发生退货时，“售后价”将作为赠品的退款金额从用户退款金额中扣除。',
    buyNum: '购买数量',
    buyNumTips: '购买数量达到了对应值才赠送赠品',
    mainAndGiveProdNotDvyTips: '该商品与主商品没有相同的配送方式，将不会在活动页显示'
  },
  pcdecorate: {
    decorateNavbar: {
      setTemplate: '设为模板',
      preview: '预览',
      templateInfo: '模板信息',
      addComponent: '添加组件',
      templateTip: '请输入您的模板描述，文案不超过100个字。',
      templateImg: '模板缩略图',
      templateRemark: '模板备注',
      templateNameNotNull: '模板名称不能为空',
      moveTip: '提示：长按微页面中已添加的组件可移动位置'
    },
    componentTitle: {
      businessSigns: '商家招牌',
      pictureBy: '图片/轮播',
      AuxiliaryInterval: '辅助间隔',
      floorTitle: '楼层标题',
      goodsList: '商品列表',
      UniversalHotspot: '万能热区',
      storeList: '店铺列表',
      subTitleCon: '口碑好店，无限回购',
      marketing: '营销活动',
      limitedKill: '限时秒杀',
      discountCoupon: '优惠团购',
      goodsModule1: '商品模块1',
      goodsModule2: '商品模块2',
      goodsModule3: '商品模块3',
      goodsModule4: '商品模块4',
      goodsModule5: '商品模块5',
      component: '组件管理',
      pageName: '页面名称',
      pageBackground: '页面背景',
      componentSort: '组件排序',
      clearComponent: '清空组件',
      pageTitle: '页面名称不能为空',
      businessSignsTip1: '商城招牌只能添加一个',
      businessSignsTip2: '商城招牌组件只能显示在头部',
      componentTip: '当前配置组件为空，不能进行保存'
    },
    baseComponent: {
      base: '基础组件',
      businessSigns: '商家招牌',
      pictureBy: '图片/轮播',
      AuxiliaryInterval: '辅助间隔',
      floorTitle: '楼层标题',
      goodsList: '商品列表',
      UniversalHotspot: '万能热区',
      storeList: '店铺列表'
    },
    marketingActive: {
      marketing: '营销活动',
      limitedKill: '限时秒杀',
      discountCoupon: '优惠团购'
    },
    extendComponent: {
      extend: '扩展组件',
      goodsModule1: '商品模块1',
      goodsModule2: '商品模块2',
      goodsModule3: '商品模块3',
      goodsModule4: '商品模块4',
      goodsModule5: '商品模块5'
    },
    commonModal: {
      smallTitle: '微页面标题',
      pageName: '页面名称',
      search: '搜索',
      cancel: '取消',
      sure: '确定',
      delText: '确定删除该组件',
      component: '组件管理',
      storeName: '店铺名称',
      categoryOne: '选择商品分类',
      categoryTwo: '商品二级分类',
      categoryThree: '商品三级分类',
      goodsType: {
        normalgoods: '普通商品',
        spellgroup: '团购商品',
        secondskill: '秒杀商品',
        integral: '积分',
        package: '套餐',
        activitiy: '活动商品'
      },
      pageComponent: {
        index: '店铺首页',
        cart: '购物车',
        order: '订单中心',
        profile: '个人中心',
        collection: '我的收藏',
        recommand: '新品推荐',
        limitTime: '限时折扣',
        discount: '优惠团购',
        skill: '秒杀专场',
        coupon: '领劵中心',
        IntegralMall: '积分商城',
        memberCenter: '会员中心',
        modalDistribution: '分销中心',
        modalLive: '直播中心',
        memberIndex: '积分签到'
      },
      pageTitle: '页面标题',
      createTime: '创建时间',
      goods: '商品',
      category: '分类',
      store: '店铺',
      page: '页面',
      smallPage: '微页面',
      customLink: '自定义链接',
      customLinkOpen: '是否在浏览器新标签页打开',
      customLinkTips: '请输入自定义链接',
      customLinkTips1: '网站外部链接需填写完整地址，网站内部链接可写为：\'/\' + 页面路径',
      selectTypeTips: '请选择相应的选项'
    },
    shopMessage: {
      storeMessage: '店铺信息',
      shopName: '店铺名称',
      shopType: '店铺类型',
      shopStatus: '店铺状态',
      ordinaryShops: '普通店铺',
      preferShops: '优先好店',
      goods: '商品',
      goodsPrice: '商品价格',
      inventory: '库存',
      goodsType: '商品类型',
      goodsCategory: '商品分类',
      prodNameTips: '请输入商品名称',
      shopNameTips: '请输入店铺名称'
    },
    placeholder: {
      selectGoods: '请选择商品类型',
      selectCategory: '请选择商品分类',
      link: '请选择跳转路径',
      pageName: '请输入页面名称'
    },
    storeSignate: {
      businessSigns: '商家招牌',
      customerService: '联系客服',
      collectionShops: '收藏店铺',
      qualifications: '资质证照',
      searchPlaceholder: '搜索一下试试看',
      search: '搜索',
      cart: '购物车',
      style1: '样式一',
      style2: '样式二',
      goodsCategory: '全部分类',
      logo: '平台logo',
      show: '显示',
      hide: '不显示',
      category: '全部商品分类',
      searchBar: '搜索栏',
      navsBar: '导航栏',
      navsBarTip: '导航栏最多添加7个',
      navsBarTip1: '输入导航名称',
      addNavs: '添加导航栏',
      navBarsArea: '导航栏区域',
      signboardBack: '招牌背景图',
      signTips: '展示在店铺信息下方，建议上传图片尺寸为1920*100像素',
      addImg: '添加图片',
      navs: '导航',
      navsBack: '导航背景',
      path: '跳转路径',
      warning1: '导航栏最多添加7个',
      warning2: '商家招牌导航栏不能为空',
      warning3: '商家招牌配置信息不能为空'
    },
    pictureBy: {
      picsize: '图片尺寸',
      picTips: '如果作为顶部轮播图建议上传尺寸1920*500比例的图片',
      highly: '轮播高度',
      pagenation: '分页器',
      show: '显示',
      hide: '不显示',
      updatePic: '更改图片',
      link: '链接',
      addImg: '添加图片',
      suggest: '建议图片尺寸1920*500',
      warning1: '图片轮播的配置信息不能为空',
      warning2: '图片轮播的轮播高度不能为0',
      warning3: '图片轮播的图片不能为空',
      warning4: '图片轮播的图片链接不能为空！'
    },
    axinterval: {
      highly: '间隔高度',
      background: '背景颜色',
      reset: '重置',
      warning1: '辅助间隔的间隔高度不能小于5'
    },
    floorTitle: {
      mainTitCon: '热卖推荐',
      subTitCon: '精选商品，商城热销',
      mainTitle: '主标题',
      mainTitleLabel: '主标题文字',
      subTitle: '副标题',
      subTitleLabel: '副标题文字',
      more: '查看更多',
      show: '显示',
      hide: '不显示',
      mainTitleColor: '主标题文字颜色',
      subTitleColor: '副标题文字颜色',
      moreTitleColor: '查看更多文字颜色',
      titleBgColor: '标题背景色',
      titleSize: '标题字号',
      subTitleSize: '副标字号',
      marginTop: '上边距',
      marginBottom: '下边距',
      warning1: '楼层标题的配置信息不能为空',
      warning2: '楼层标题的主标题不能为空',
      warning3: '楼层标题的副标题不能为空',
      warning4: '楼层标题的查看更多跳转路径不能为空'
    },
    goodsList: {
      listStyle: '列表样式',
      three: '一行三个',
      four: '一行四个',
      five: '一行五个',
      showContent: '显示内容',
      goodsName: '商品名称',
      goodsDescription: '商品卖点',
      goodsPrice: '商品价格',
      goods: '商品',
      addgoods: '添加商品',
      price: '价格',
      warning1: '商品列表配置信息不能为空',
      warning2: '商品列表商品不能为空'
    },
    univerHot: {
      picSize: '图片尺寸',
      one: '1920全屏',
      two: '1200居中',
      addImage: '添加图片',
      jumpLinks: '跳转链接',
      imageTips: '最多添加10个热区广告',
      addHot: '添加热区',
      updateImage: '更改图片',
      warning1: '万能热区配置信息不能为空',
      warning2: '万能热区背景图片不能为空',
      warning3: '热区跳转链接不能为空！'
    },
    storeList: {
      storeTitle: '优先好店',
      storeSubTitle: '副标题',
      showSubTitle: '显示副标题',
      show: '显示',
      hide: '不显示',
      mainTileColor: '标题文字颜色',
      subTitleColor: '副标题文字颜色',
      titleBgColor: '标题背景颜色',
      titleSize: '标题字号',
      subTitleSize: '副标字号',
      marginTop: '上边距',
      marginBottom: '下边距',
      addStore: '添加店铺',
      storeIn: '进店看看',
      storeName: '店铺名称',
      storeAttention: '人关注',
      warning1: '店铺列表配置信息不能为空',
      warning2: '店铺列表副标题不能为空',
      warning3: '店铺列表店铺不能为空'
    },
    limitedSkill: {
      mainTitle: '秒杀专区',
      subTitle: '精选商品，限时秒杀',
      warning1: '限制秒杀配置信息不能为空',
      warning2: '限时秒杀的副标题不能为空',
      warning3: '限时秒杀的商品不能为空'
    },
    disCountForm: {
      mainTitle: '优惠团购',
      subTitle: '好物推荐，一起拼团',
      warning1: '优惠团购配置信息不能为空',
      warning2: '优惠团购的标题不能为空',
      warning3: '优惠团购的商品不能为空'
    },
    goodsModule1: {
      select: '请选择对应的列数，进行详情设置',
      mainTitle: '主标题文字',
      subTitle: '副标题文字',
      mainTitleCon: '热卖推荐',
      subTitleCon: '销量引领购物新风向',
      titleLink: '标题链接',
      warning1: '商品模块1的配置信息不能为空',
      warning2: '商品模块1的主标题不能为空',
      warning3: '商品模块1的副标题不能为空',
      warning4: '商品模块1的标题跳转链接不能为空',
      warning5: '商品模块1的商品不能为空',
      addOtherGoods: '最多添加3个'
    },
    goodsModule2: {
      customTitle: '自定义标题',
      placeholder: '请输入自定义标题',
      titleLink: '标题链接',
      addMainGoods: '添加主商品(最多添加1个)',
      addOtherGoods: '添加其他商品(最多添加6个)',
      warning1: '商品模块2的配置信息不能为空',
      warning2: '商品模块2的标题不能为空',
      warning3: '商品模块2的标题链接不能为空',
      warning4: '商品模块2的主商品不能为空！',
      warning5: '商品模块2的其他商品不能为空！'
    },
    goodsModule3: {
      addOtherGoods: '添加商品(最多添加3个)',
      warning1: '商品模块3的配置信息不能为空',
      warning2: '商品模块3的标题不能为空',
      warning3: '商品模块3的标题链接不能为空',
      warning4: '商品模块3的商品不能为空'
    },
    goodsModule4: {
      addBg: '添加背景图',
      addTips: '建议上传尺寸1200*300像素的图片',
      picLink: '图片跳转路径',
      addOtherGoods: '添加商品(最多添加4个)',
      warning1: '商品模块4的配置信息不能为空',
      warning2: '商品模块4的图片链接不能为空',
      warning3: '商品模块4的商品不能为空',
      warning4: '商品模块4的背景图不能为空'
    },
    goodsModule5: {
      addMain: '添加主图(建议选择232*320px图片)',
      addImage: '添加图片',
      link: '链接',
      addGoods: '添加商品(最多添加4个)',
      warning1: '商品模块5的配置信息不能为空',
      warning2: '商品模块5的主图不能为空',
      warning3: '商品模块5的主图链接不能为空',
      warning4: '商品模块5的商品不能为空'
    }
  },
  imMsgBizSkills: {
    noNull: '不能为空',
    keyword: '关键字',
    content: '回复内容',
    automaticReply: '自动回复',
    enablingStatus: '启用状态:',
    enablingStatusTips: '启用后，当天买家用户发送第一条消息或者发消息时无客服在线，系统会自动进行回复',
    enablingStatusTips2: '执行一次自动回复后，人工进行了回复，用户再次咨询时无客服在线才会执行下一次的自动回复',
    sendTextMessage: '发送文字信息',
    sendCommonQuestions: '发送常见问题',
    addQuestion: '添加问题',
    question: '问题',
    answer: '答案:',
    addUpToTenItems: '最多添加10条',
    save: '保存',
    cancel: '取消',
    edit: '编辑',
    maximumwordCount: '问题不超过20个字',
    maximumwordCount2: '这里是问题答案不超过500个字',
    up: '上移',
    down: '下移',
    delete: '删除',
    canAlsoInput: '还可以输入',
    words: '字',
    checkOneOptions: '请勾选其中一个自动回复选项',
    questionEmpty: '自动回复问题为空',
    messageEmpty: '自动回复文字信息为空',
    replyContentEmpty: '自动回复内容不能为空',
    confirm: '确定'
  },
  allinpay: {
    allinpayBusinessTip1: '平台开启通联支付，请重新提交工商信息与财务信息',
    allinpayBusinessTip2: '已保存工商信息，请确认并提交财务信息',
    allinpayBusinessTip3: '当前影印件状态异常，可在工商信息中刷新影印件状态',
    photocopyUpload: '影印件上传',
    auditFailureTip1: '审核未通过，请重新上传',
    auditFailureTip2: '法人身份证审核失败，请重新上传',
    auditFailureTip3: '工商营业执照审核失败，请重新上传',
    legalPhone: '法人手机号',
    legalPhoneNotEmpty: '法人手机号不能为空',
    updateTime: '数据更新',
    notSufficientFundsTip1: '店铺余额不足',
    notSufficientFundsTip2: '元，本次无法提现',
    corporateAccount: '法人账户',
    companyAccount: '对公账户',
    captchaPrompt: '验证码将发送到银行卡预留手机号，发送验证码后将会冻结本次提现金额且不可撤回，请谨慎操作',
    selectBankTip: '请先选择银行卡！',
    missingInfoTip1: '是否已签约对公户提现协议',
    missingInfoTip2: '是否已签约法人提现协议',
    missingInfoTip3: '是否已绑定手机',
    missingInfoTip4: '是否已创建店铺会员',
    missingInfoTip5: '企业信息审核状态是否正常',
    missingInfoTip6: '影印件状态是否正常',
    confirmTip: '请确定',
    mobile: '手机号',
    untapePhone: '解绑手机号',
    beTiedPhone: '绑定手机号',
    pleaseEnterPhone: '请输入手机号',
    getCode: '获取验证码',
    code: '验证码',
    pleaseEnterCode: '请输入验证码',
    cancel: '取消',
    confirm: '确定',
    unbinding: '解绑',
    binding: '绑定',
    InputCorrectPhone: '请输入正确的手机号',
    mobilePhoneNoNull: '手机号不能为空',
    capNoNull: '验证码不能为空',
    saveSuccess: '保存成功',
    accountType: '账户类型',
    bankName: '银行名称',
    pleaseSelect: '请选择',
    branchName: '开户支行名称',
    forExample: '例如：',
    businessPublicAccounts: '企业对公账户',
    pleaseEntePublicAccount: '请输入企业对公账户',
    paymentLineNumber: '支付行号',
    enterLineNumber: '请输入支付行号',
    enterLineNumber2: '请输入正确的支付行号',
    identityCard: '身份证',
    pleaseEnterIdentityCard: '请输入身份证号',
    pleaseEnterCardPhone: '请输入银行预存手机号',
    PleaseEnterCorporateIDCard: '请输入正确的法人身份证',
    IDCardNumberEmpty: '身份证号码不能为空',
    pleaseEnterDigits: '请输入12位数字',
    pleaseEnterBankName: '请填写开户银行名称',
    companyNotEmpty: '企业对公账户不能为空',
    corporateIdentityCard: '法人身份证',
    corporateIdentityCardNotEmpty: '法人身份证不能为空',
    pleaseEnterIDCard: '请输入法人身份证号',
    pleaseEnterLegalPhone: '请输入法人手机号',
    added: '已添加',
    added2: '个结算账户，最多可添加',
    added3: '个结算账户',
    added4: '个法人账户',
    addedPhone: '，绑定手机号：',
    signAgreement: '签约协议',
    clearingBankAccount: '结算银行账户',
    pleaseAddPublicAccount: '请添加1个对公账户',
    submit: '提交',
    branchTip: '请填写开户支行名称',
    branchTip2: '开户支行名称长度在2到20个字符',
    accNoNull: '账号不能为空',
    signFail: '签约失败',
    signSuccess: '签约成功',
    submitSuccess: '提交成功',
    applicationSuccess: '提交申请成功！',
    signType: '协议类别：',
    pleaseSelectCategory: '请选择签约类别',
    privateContract: '对私签约',
    contractWithPublic: '对公签约',
    pleaseBindPhone: '请先绑定手机号',
    pleaseBindLegalAccNo: '请先绑定法人账户',
    processBindAccNoTip: '开店时最多可添加1个对公账户',
    pleaseEnterDigitLineNumber: '请输入12位的支付行号',
    allinpayWithdrawalUnpaid: '待支付',
    allinpayWithdrawalApproved: '申请成功',
    allinpayWithdrawalSuccessful: '提现成功',
    allinpayWithdrawalFailure: '提现失败',
    dateloading: '数据处理中...'
  },
  outletConfig: {
    addrManage: '管理发货地址',
    addrEmptyTips: '发货地址不能为空',
    shipperEmptyTips: '发货人不能为空',
    deliveryCompanyTypeEmptyTips: '快递公司不能为空',
    oneSheet: '一联面单',
    twoSheet: '二联面单',
    deliveryCompanyType: '快递公司类型',
    shipper: '发货人',
    mobile: '发货人电话',
    partnerId: '电子面单客户账户或月结账号',
    partnerKey: '电子面单密码',
    net: '收件网点名称',
    paperSize: '纸张规格',
    isDefault: '是否默认'
  },
  printer: {
    printerId: '打印机id',
    shopId: '店铺id',
    printerName: '打印机名称',
    siid: '设备码',
    printerRemark: '打印机备注',
    isDefault: '是否默认',
    deviceInfo: '设备连接说明',
    siidEmptyTips: '设备码不能为空',
    siidRuleTips: '设备码仅支持英文及数字',
    printerNameEmptyTips: '请输入打印机名称',
    printerNameEmptyTips2: '打印机名称不能为空',
    defaultTips: '平台尚未开启电子面单功能，如需使用请到平台后台配置',
    specialDevices1: '1、完成打印机安装，接上电源，打开开关；<br/>',
    specialDevices2: '2、确保手机蓝牙为开启状态；<br/>',
    specialDevices3: '3、打开微信扫描云打印机底部二维码，进入‘云打印机管家’小程序进行联网操作。'
  },
  popupAd: {
    popupAd: '弹窗广告',
    addPopup: '新增弹窗',
    popupName: '弹窗名称',
    popupState: '弹窗状态',
    notStarted: '未开始',
    release: '投放中',
    finished: '已结束',
    triggerPage: '触发页面',
    platHome: '平台首页',
    memberCenter: '会员中心',
    paySuccess: '支付成功',
    shopHome: '店铺首页',
    prodDetail: '商品详情',
    selectProd: '选择商品',
    redirect: '跳转页面',
    redirectTips: '跳转路径为优惠券时，将显示系统配置的优惠券领取页面',
    selectRedirect: '请选择跳转路径',
    popupImg: '弹窗图片',
    pushUser: '推送用户',
    allUsers: '所有用户',
    FreeMember: '免费会员',
    paidMember: '付费会员',
    shopCustomer: '店铺客户',
    shopMember: '店铺会员',
    memberLevel: '会员等级',
    pushTime: '推送时间',
    pushFrequency: '推送频次',
    pushFrequencyTips: '跳转路径为优惠券时，如用户已领取过该优惠券则不再推送，如用户未领取，则按照设定的推送频次进行推送',
    onceForAll: '永久一次',
    perEntry: '每次进入',
    customFrequency: '自定义频次',
    every: '每',
    pushOnce: '推送一次',
    frequencyDayTips: '请填写正确的频次天数',
    frequencyWeekTips: '请选择频次周数',
    invalidConfirmTips: '确定进行失效操作?',
    noNull: '不能为空'
  },
  data: {
    integralPrice: '积分价'
  },
  fitment: {
    maxCountTips: '当前添加的商品个数已达到最大个数',
    failImgTip: '图片加载失败'
  },
  feature: {
    CategoryList: '商品分类列表',
    category: '分类',
    headerImage: '分类头图',
    addImg: '添加一个背景图',
    changeImg: '更换图片',
    jump: '跳转',
    chooseJump: '选择跳转到的页面',
    subcategory: '子分类',
    title: '标题',
    addSubcategory: '添加一个子分类',
    addMainCategory: '添加一个主分类',
    widthPxTip1: '建议宽度 765*282 像素',
    widthPxTip2: '建议宽度 150*150 像素',
    addshopTips: '添加商品(最多添加2个)'
  },
  prod: {
    stockpilesTips: '提示：当前关联商品无共同供货仓库',
    OriginalSalesPrice: '原销售价',
    stockpilesTotal: '合计共同仓库库存',
    stocksEditContent: '组合商品库存是由所关联商品的库存及搭配数共同决定，不能单独编辑',
    voucherStocksTip: '商品关联卡券后，库存由该卡券的未发放数量决定，不能单独编辑',
    setStockQuantity: '共享总部库存的门店无需额外设置商品库存数量',
    prodNameTip: '请完善商品名称',
    correlationTip: '组合商品规格至少需要关联一个商品！',
    bindVoucherTip: '存在未关联卡券的规格',
    bankName: '中国工商银行股份有限公司北京樱桃园支行'
  },
  // 主动退款组件
  proactiveRefund: {
    proactiveRefund: '主动退款',
    currentlyRefundable: '当前可退',
    refundAmount: '退款金额',
    enterRefundAmount: '请输入退款金额',
    fullRefund: '全部退款',
    enterCorrectAmount: '请输入正确的金额',
    tipOne: '当前已超过订单售后申请时间但商品仍在核销有效期内',
    tipTwo: '如用户需要售后，订单还未结算时可与买家协商一致进行主动退款',
    tipThree: '订单已结算后请与买家自行协商退款方式',
    endWriteOff: '结束核销',
    WriteOffHasEnded: '已结束核销'
  },
  // 电子卡券相关
  voucher: {
    add: '新增卡券',
    update: '修改卡券',
    inputTips: '请填写卡券名称',
    exportDate: '导入日期',
    cardNum: '卡号',
    cardPWD: '卡密',
    cardCode: '优惠码',
    uploadText: '上传卡券说明',
    uploadTipsOne: '1、请先下载卡券导入模板，并根据模板填写信息，未按要求填写将会导致卡券导入失败',
    uploadTipsTwo: '2、请选择.xlsx或.xls文件，每次只能导入一个文件，建议每次导入不超过5000条数据',
    uploadTipsThree: '3、不建议保留空白行，容易导致数据错误并不能成功导入',
    excelModel: '卡券明细导入模板',
    voucherDetail: '卡券明细',
    chooseVoucher: '选择卡券',
    voucherList: '卡券列表',
    preBindVoucher: '商品原先绑定的卡券'
  },
  virtualLog: {
    code: '核销券码',
    time: '核销时间',
    station: '核销门店',
    virtualLogXls: '核销记录.xlsx'
  }
}

export default zhCn
