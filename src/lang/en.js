const en = {
  language: 'English',
  pleaseCompleteTheSecurityVerification: 'Please complete the security verification',
  swipeRightToCompleteVerification: 'Swipe right to complete verification',
  verificationCodeHasExpired: 'The verification code has expired, please get it again',
  verifySuccessfully: 'Verify successfully',
  verificationFailure: 'Verification failure',
  passwordVerification: 'Passwords are composed of three types of characters: letters, numbers, and special symbols. They are 8-20 half width characters and are case sensitive',
  common: {
    language: 'Language',
    condition: 'condition',
    display: 'display',
    hide: 'hide',
    back: 'back',
    confirm: 'confirm',
    setAsDefault: 'Set As Default',
    undefault: 'undefault'
  },
  express: {
    sf: 'SF',
    jd: 'Jingdong',
    yto: 'YTO',
    yunDa: 'YunDa',
    zto: 'ZTO',
    sto: 'STO',
    best: 'Best',
    ems: 'EMS',
    monthlyClosingNumber: 'Monthly closing number',
    merchantCoding: 'merchant coding',
    merchantCode: 'merchant code',
    merchantKey: 'Merchant key',
    yunda<PERSON>hiteHorseAccount: 'Yunda white horse account',
    jointCipher: 'Joint cipher',
    partnerCode: 'Partner code',
    partner<PERSON>ey: 'Partner key',
    dotCoding: 'Dot coding',
    customerName: 'customer name',
    customerPassword: 'Customer password',
    network: 'Network',
    operationCoding: 'Operation coding',
    secretKey: 'secret key',
    ProtocolClientNumber: 'Protocol client number',
    eCommerceCustomerLogos: 'E-commerce customer identification'
  },
  tip: {
    select: 'please select prod',
    input: 'please input'
  },
  time: {
    start: 'Start date',
    end: 'End date',
    startTime: 'Start time',
    endTime: 'End time',
    tip: 'to',
    t: 'today',
    y: 'yesterday',
    n: 'nearly 7',
    m: 'nearly 30',
    a: 'whole',
    selectWeek: 'select Week',
    monday: 'monday',
    tuesday: 'tuesday',
    wednesday: 'wednesday',
    thursday: 'thursday',
    friday: 'friday',
    saturday: 'saturday',
    sunday: 'sunday',
    tips1: 'The end time cannot be less than the current time',
    tips2: 'The start time cannot be less than the current time',
    tips3: 'The start time cannot be greater than or equal to the end time',
    tips4: 'The end time cannot be younger than or equal to the start time'
  },
  home: {
    all: 'All',
    yamiB: 'Yami Backstage',
    company: 'Guangzhou Blue Ocean Innovation Technology Co., Ltd.',
    nowDate: 'Real-time survey',
    updateTime: 'Update time',
    payAmount: 'Payment amount',
    yestAllUpdate: 'Yesterday all day,updating',
    displays: 'Overall details',
    statistics: 'Statistics',
    refundDisplays: 'Refund details',
    realTimeData: 'Real time data',
    dataToday: 'Data today',
    transactionAmount: 'Transaction amount',
    dataYesterday: 'Data yesterday',
    dataTodayActualTotal: 'Amount paid today',
    dataYesterdayActualTotal: 'Amount paid yesterday',
    platformPay: 'Platform pay',
    // businessPay: 'Merchant Payment Amount',
    businessPay: 'Amount of transactions',
    pendingOrders: 'Pending orders',
    payCustomers: 'Number of customers paid',
    reboundRate: 'Rebound rate',
    payCount: 'Number of payment orders',
    sameAsYesterday: 'Same as yesterday',
    refundAmount: 'Successful refund amount',
    successfulRefundAmountToday: 'Today\'s successful refund amount(￥)',
    successfulRefundNum: 'Successful refund num',
    refundRateToday: 'Refund Rate(%)',
    number: 'Number',
    moneyRatio: 'Amount share',
    customerPrice: 'Customer unit price',
    dayAmount: 'Amount paid on the day(￥)',
    monthAmount: 'Amount paid on the month(￥)',
    storeAmount: 'Store wide payment',
    todays: 'Today',
    yesterdays: 'Yesterday',
    totals: 'Total',
    customerService: 'Customer service',
    platformName: 'Business-side management platform',
    verificationCode: 'Captcha',
    refundRate: 'Refund rate',
    refundProRank: 'Refund product ranking',

    refundReasonRank: 'Refund reason ranking',
    rank: 'Rank',
    product: 'Product',
    reason: 'Reason',
    ratio: 'Ratio',
    amount: 'Amount',
    accNoNull: 'Account cannot be empty',
    userNameNoNull: 'User name cannot be empty',
    pawNoNull: 'Password cannot be empty',
    capNoNull: 'Captcha cannot be empty',
    loginTip: 'Please enter the correct cell phone number/username',
    notification: 'Notification',
    haveANewUnreadMessage: 'You have a new unread message',
    msgBox: 'Message Box',
    authTip: 'You do not have permission to access'
  },
  number: 'Number',
  temp: {
    sequence: 'Sequence',
    area: 'Area',
    prodInfo: 'Product info',
    rotaImg: 'Rotate pictures',
    modify: 'Modify',
    actLabel: 'Activity label',
    setNull: 'Reset',
    proImg: 'Product picture',
    productMainImage: 'Product main image',
    m: 'nearly 30'
  },
  crud: {
    filter: {
      addBtn: 'Add',
      clearBtn: 'Clear',
      resetBtn: 'Reset',
      cancelBtn: 'Cancel',
      submitBtn: 'Submit',
      modify: 'Modify'
    },
    rotaImg: 'Rotate pictures',
    tipStartTitle: 'Currently selected',
    tipEndTitle: 'Term',
    editTitle: 'Edit',
    addTitle: 'Add',
    viewTitle: 'View',
    filterTitle: 'Filter',
    menu: 'Menu',
    addBtn: 'Add',
    showBtn: 'Show',
    filterBtn: 'Filter',
    refreshBtn: 'Refresh',
    printBtn: 'Print',
    excelBtn: 'Excel',
    updateBtn: 'Update',
    cancelBtn: 'Cancel',
    columnBtn: 'Column',
    searchBtn: 'Search',
    emptyBtn: 'Empty',
    menuBtn: 'Menu',
    saveBtn: 'Save',
    viewBtn: 'View',
    editBtn: 'Edit',
    UpperShelf: 'Upper shelf',
    LowerShelf: 'Lower shelf',
    batchDelete: 'Batch delete',
    delBtn: 'delete',
    importe: 'Import',
    remark: 'Remark',
    noAbleTabErr: 'Could not find available tab',
    setNull: 'Reset'
  },
  text: {
    editBtn: 'Editor',
    menu: ' operate',
    delBtn: 'delete',
    updateBtn: 'update',
    tips: 'Remind',
    endTime: 'End time',
    startTime: 'Start time',
    to: 'To',
    upload: 'Upload',
    uploadTips: 'Upload avatar picture size cannot exceed',
    isRemove: 'Confirm removal'
  },
  withdrawal: {
    appliedAmount: 'Applied Amount',
    toTheBank: 'To The Bank',
    merchantNotes: 'Merchant Notes',
    reviewStatus: 'Review Status',
    payingAccount: 'Paying Account',
    payingCardNo: 'Paying Card No',
    payingTime: 'Paying Time',
    platformNote: 'Platform Note'
  },
  takeStock: {
    inputName: 'Enter name',
    inputPartyCode: 'Enter code',
    productFilter: 'Product Filter',
    InventoryNo: 'Inventory Single Number',
    InventoryStatus: 'Inventory Status',
    voided: 'Voided',
    taking: 'Taking Stock',
    complete: 'Completed',
    maker: 'Order maker',
    createTime: 'Inventory time',
    regionName: 'Inventory area name',
    skuCount: 'Number of sku',
    voidInventory: 'Voided',
    voidTip: 'I can\'t continue to edit after the invalidation of the purchase order.',
    exportTip: 'If the inventory already has items saved, you cannot use the bulk import function.',
    exportTip1: 'Defaults to 0 if the number of inventories imported is empty',
    exportProdFileName: 'Inventory goods import template.xlsx',
    prodDetailFileName: 'Inventory commodity information.xlsx',
    exportInventoryFileName: 'Inventory list information.xlsx',
    importErrorTip: 'Added products, do not support batch import',
    infoText1: 'Inventory Products',
    infoText2: 'species, live inventory',
    infoText3: 'Pieces, inventory surplus',
    infoText4: 'pieces, inventory loss',
    infoText5: 'pieces',
    bookStock: 'Book Inventory',
    profitLossCount: 'Profit & Loss Number',
    profitLossTip: 'The profit and loss variance values here are for reference only, please refer to the final "completion of inventory" profit and loss calculation results.',
    totalStock: 'Live Inventory',
    profit: 'Profit',
    loss: 'Loss',
    equal: 'Equal',
    exception: 'Exception',
    inventoryDetail: 'Inventory details',
    exportProdDetail: 'Export Product Detail',
    editInventory: 'Live Entry',
    regionNameTip: 'Customize the name of this inventory area, e.g. refrigerated area.',
    saveDraft: 'Save draft',
    finishInventory: 'Complete Inventory',
    prodNotNull: 'Inventory items cannot be empty',
    prodStockNotNull: 'The actual inventory of the product cannot be empty',
    newInventory: 'New Construction Inventory',
    createTimeNotNull: 'Inventory time cannot be empty'
  },
  product: {
    activeProd: 'Active Prod',
    viewProduct: 'View product',
    isTop: 'isTop',
    cannotBeRemovedOrRemovedFromTheChassis: 'Cannot be removed or removed from the chassis',
    voucher: 'voucher',
    freTempl: 'fre templ',
    fixedFreight: 'fixed freight',
    pleaseEnterTheAmount: 'Please enter the amount',
    unifiedPackageMail: 'Unified package mail',
    uniformDeliveredPricing: 'uniform delivered pricing',
    doNotEnterSpecialCharacters: 'Do not enter special characters',
    ExpressDistribution: 'Express distribution',
    brandId: 'Brand id',
    brandName: 'Brand name',
    brandImg: 'Brand image',
    prodName: 'Product name',
    priceAndInventory: 'Price and inventory',
    basicInformation: 'Basic information',
    productCategories: 'Product Categories',
    prodNameCn: 'Chinese skuname of commodity',
    prodNameEn: 'English skuname of commodity',
    prodStatus: 'Product Status',
    prodInfo: 'Product information',
    oriPrice: 'Original price',
    price: 'Price',
    prices: 'Price',
    pic: 'Imagen',
    totalStocks: 'Total stocks',
    prodType: 'Product Type',
    prodMold: 'Product Mold',
    ordProd: 'Ordinary goods',
    groupProd: 'Group buy goods',
    limitedTimeProd: 'Limited time offer goods',
    pointsProduct: 'Points goods',
    comProd: 'Combined Products',
    combinedProducts: 'Combined Products',
    status: 'Status',
    deletes: 'Are you sure you want to delete this product?',
    pleaseSelectTheProduct: 'Please select the product category first and then fill in the following product information',
    select: 'Select product',
    saveTip: 'The inventory of this item is 0. Are you sure to continue posting?',
    prod: 'Product',
    violation: 'Illegal off the shelf',
    batchUp: 'Batch launch',
    batchDown: 'Batch off shelves',
    pendingReview: 'Pending review',
    violationPendingReview: 'Downgraded for review',
    auditHistory: 'Audit History',
    reviewStatus: 'Approval status',
    atributeID: 'Attribute ID',
    attributeName: 'Attribute name',
    delType: 'Delivery type',
    sameCityDelivery: 'same city delivery',
    shopDelivery: 'shop delivery',
    userPickUp: 'user pick up',
    attributeNameEn: 'Attribute English name',
    attributeNameNoNull: 'Attribute name cannot be empty',
    attributeNameEnNoNull: 'Attribute English name cannot be empty',
    attributeValue: 'Attribute value',
    attributeValueEn: 'English Attribute Value',
    attributeValueEnTips: 'Default attribute value',
    attributeValueEditTips: 'Click to modify the corresponding attribute value',
    attributeValueNoNull: 'Attribute value cannot be empty',
    content: 'Please enter the content',
    same: 'Cannot add the same attribute value',
    voucherName: 'voucher name',
    relatedProducts: 'Related products',
    expired: 'Invalidated',
    startUsing: 'Enable',
    notEnabled: 'Not enabled',
    notEnabledTips: 'Displays unissued and non-expired passes',
    voucherNameNoNull: 'voucher name cannot be empty',
    uploadTips: 'Please select an operation and only one file can be uploaded at the same time',
    selectFile: 'Select File',
    downloadTemplate: 'Download template',
    fileSuccess: 'File imported successfully',
    fileFail: 'File import failed',
    fileUploadFail: 'File upload failed!',
    downloadTemplateTips1: 'Upload template can only be XLS, xlsx format!',
    downloadTemplateTips2: 'Upload template size cannot exceed 10MB!',
    fileName: 'Product information template.xlsx',
    templateName: 'template.xlsx',
    merAddProdTime: 'Merchant add product time',
    meSubRevTime: 'Merchant submission review time',
    proApprTime: 'Product approval time',
    uploadProdTip: 'If the uploaded specification code is empty or duplicates an existing code, the system will automatically generate a new code value.',
    maxNum: 'Limited purchase quantity',
    maxCancelTime: 'Order cancel time',
    offViol: 'Offline violation',

    pleaEntBraName: 'Please enter the brand name',
    branls: 'Brand initials',
    pletials: 'Please enter the brand initials',
    pleaemarks: 'Please enter the remarks',
    brandNaBeEmpty: 'Brand name cannot be empty',
    brandInitBeEmpty: 'Brand initials cannot be empty',
    firstLetterErrorTips: 'Please enter one capital letter',
    brandLogoNotEmpty: 'Please upload your brand logo',
    sortValmpty: 'Sort value cannot be Empty',
    shippinngs: 'Shipping fee setting',
    parameterSetting: 'Parameter Setting',
    parameter: 'Parameter',
    shippingtBeEmpty: 'Shipping template cannot be empty',
    deliveryArea: 'Delivery area',
    freeShipping: ' free shipping',
    further: ' And ',
    freeShiullAmount: 'Free shipping with full amount',
    freeippingR: '￥ free shipping amount',
    pwvFree: 'Pieces/weight/volume Free shipping',
    import: 'Import',
    productVideo: 'Product video',
    draggableSort: 'Can be dragged to sort, the first image is the main image, the most uploads',
    platforation: 'Platform category',
    shopCategory: 'Store category',
    thisPlatformEmpty: 'This platform category cannot be empty',
    thisShopeEmpty: 'This shop category cannot be empty',
    thisProduCategroy: 'Please select a product category',
    thisShopCategroy: 'Please choose our category',
    thisProductImg: 'Please choose at least one picture as the main picture of the product',
    brand: 'Brand',
    stockWarning: 'Inventory warning',
    whetPreSale: 'Whether it is open Pre-sale',
    isBindElectronicCard: 'Whether to bind electronic card coupons',
    electronicCardTips: 'The electronic card can be set up in [Product - Vouchers]. If it is not bound to an electronic card, the user needs to be actively contacted to issue the electronic card content',
    chooseABrand: 'Select brand',
    preSaleTime: 'Pre-sale delivery time',
    thePreSaleDtBeEmpty: 'Pre-sale delivery time cannot be empty',
    choosengDate: 'Select delivery date',
    userMention: 'User pick up',
    chinenInput: 'Chinese information',
    prodellingPoint: 'Selling point',
    plePictureToUpload: 'Please choose a picture to upload',
    pleeliveryMethod: 'Please choose a delivery method',
    pleShgTlate: 'Please choose a shipping template',
    pleComAndEnName: 'Please complete the Chinese name',
    zhpleComAndEnName: 'Please complete the Chinese name',
    violatingGoods: 'Illegal products',
    refuseToPass: 'Refuse to pass',
    other: 'Other',
    importGoods: 'Import products ',
    offlineManagement: 'offline manage',
    reportsToCond: 'Exporting reports based on the status of the search criteria only',
    exportProduct: 'Export filtered product information',
    oneInARow: 'One column one',
    twoInARow: 'One column two',
    threeInARow: 'One column three',
    pleaseEnterALabelName: 'Please enter the label name',
    addNew: 'Add new',
    singleProductBar: ' Single product barcode',
    noBarcode: 'If there is no barcode, the system will automatically generate it',
    usedToQuickItem: 'Used to quickly identify the single product',
    item: 'Single Product',
    itemName: 'Single product name',
    singleProdPic: 'Single product picture',
    specificationStock: 'Specification stock',
    singleProdSpe: 'Single product specification',
    singleProdClas: 'Single product classification',
    singleProdUnit: ' Single product unit',
    singleProdInve: 'Single product inventory',
    supplier: 'Preferred Supplier',
    supplierStatus: 'Supplier Status',
    chooseSupplier: 'Select supplier',
    itemNameCanEmpty: 'Single product name cannot be empty',
    pleaseUploadApicture: 'Please upload a picture of the single product',
    chooseProdCateg: 'Select product category',
    productSecondaryClassification: 'Product secondary classification',
    productThreeClassification: 'Product three classification',
    currCho: 'Your current choice is',
    isItAComtionPro: 'Is it a combination product ',
    combinationGoods: 'Combined product',
    quantityInCom: 'Quantity in combination',
    singleProd: 'Single product cost',
    total: 'Total',
    skuPictures: 'Sku picture',
    sellingPrice: 'Sale price(￥)',
    marketPrice: 'Market Price(￥)',
    eventPrice: 'event Price',
    commodityCode: 'Commodity code',
    productBarcode: 'Commodity barcode',
    haveReadFol: 'I have read the following rules and publish now',
    releaseNotice: ' Release notice',
    commodityWeight: 'Product weight (kg)',
    commodityVolume: 'Product volume (m³)',
    productSpecifi: 'Product specification',
    addSpecifications: 'Add product specifications',
    specificationValue: 'Sku value',
    pleaseerTheSpName: 'Please input specification name',
    pleaseEntValue: 'Please input specification value',
    rule: ' Rule',
    merchantDelivery: 'Merchant delivery',
    comInfoSorXls: 'Commodity information sorting.xlsx',
    voucherItemExcel: 'Card voucher details.xlsx',
    voucherItemExcelTips: 'Are you sure you want to export the voucher details？',
    exportVoucherDetails: 'Export voucher details',
    exportVoucherOfSearch: 'Export the filtered voucher details',
    exportVoucherOfSelect: 'Export the selected voucher details',
    pleaseSelectAUnit: 'Please select a unit',
    isExistsPleaReEn: 'Already exist, please re-enter',
    specificationy: 'Specification item cannot be empty',
    banInf: 'It is prohibited to publish products that infringe the intellectual property rights of others, please confirm that the products comply with the provisions of intellectual property protection',
    userMustFollRule: 'Users should comply with national laws, administrative regulations, departmental rules and other normative documents. This platform has the right to deal with any acts suspected of violating national laws, administrative regulations, departmental rules and other normative documents. However, the handling of users by the platform does not exempt them from their due legal responsibilities. Any behavior of users on the platform shall also comply with the various agreements signed with the platform and its affiliates. The platform has the right to change these rules at any time and make an announcement on the website. If the user does not agree to the relevant changes, he should immediately stop using the platformrelated services or products. The platform has the right to unilaterally determine user behavior and applicable rules, and deal with it accordingly. ',
    relatedItems: 'Related items',
    chooSingPro: 'Choose single product',
    specTip: 'Attribute Value Row N has a null value',
    priceTip: 'The sales price shall not be higher than the market price',
    noContractedCategories: 'There are no contracted categories, please go to',
    noShopCategories: 'There are no shop categories, please go to',
    applyContracting: 'to apply for contracting classification',
    createShopCategory: 'to create',
    shopInfo: 'Shop Information',
    propEditingDelTips: 'The property is editting,',
    propDelTips: 'Sure to delete this property ?',
    productSort: 'Sort by product',
    selectFirstCategory: 'Please select the first level category first',
    selectSecondCategory: 'Please select the second level category first',
    platformTopping: 'PlatformTopping',

    // Post a product
    selectProductCategory: 'Select Product Category',
    editProductInfo: 'Edit Product Information',
    editProductDetails: 'Edit Product Details',
    physicalGoods: 'Physical goods',
    logisticsDelivery: '( Logistics delivery )',
    virtualGoods: 'Virtual goods',
    noLogisticsRequired: '( No logistics required )',
    comboGoods: 'combo goods',
    electronicCard: 'Voucher Card',
    bindCard: 'Bind card',
    productTemplates: 'Product templates',
    templateImport: '( Template import )',
    selectPlatformCategory: 'select platform category',
    selectPlatformCategoryOnly: 'Select Platform category (show only contracted categories)',
    selectShopCategory: 'select shop category',
    selectFirstLevelCategory: 'select first level category',
    selectSecondLevelCategory: 'select second level category',
    selectThirdLevelCategory: 'select third level category',
    selectedPlatformCategories: 'Selected Platform Categories',
    selectedShopCategories: 'Selected Shop Categories',
    nextStep1: 'Selected, next step',
    nextStep2: 'Next step',
    prevStep: 'Previous step',
    saveBtn1: 'Save',
    saveBtn2: 'Save and view',
    selectedCategories: 'Selected Categories',
    platformCategories: 'Platform Categories',
    shopCategories: 'Shop Categories',
    selectLanguage: 'Select language',
    productSellingPoints: 'Product Selling Points',
    productBrands: 'Product Brands',
    selectText: 'select',
    specStock: 'Specification Stock',
    totalInventory: 'Total inventory',
    chineseDetails: 'Chinese Details',
    englishDetails: 'English Details',
    detailPagePreviewImage: 'Detail page preview image',
    parameteNameAndParameterValue: 'The parameter name and parameter value are displayed at the beginning of commodity details. The parameter name shall not exceed 10 words and the parameter value shall not exceed 20 words',
    productDetailsContentLimitTxt: 'Product details too many words, please re-enter！',
    // Virtual goods
    otherSettings: 'Other settings',
    instructions: 'directions',
    enterInstructionsTip: 'Please enter the directions',
    noWriteOffRequired: 'No write-off required',
    singleWriteOff: 'Single write-off',
    multipleWriteOffs: 'Multiple write-offs',
    numberOfWriteOffs: 'Number of write-offs',
    expiryNumberOfWriteOffs: 'Number of valid write-offs',
    userMessage: 'User message',
    expiryDate: 'Expiry date',
    longTermValidity: 'Long-term validity',
    unlimitedTime: 'Unlimited time',
    mostWriteOffs: 'Most of verification',
    validOnTheSameDay: 'Valid on the same day',
    beforeTime: '(Available until 24:00 daily)',
    afterPurchase: 'Valid for',
    validDays: 'days after purchase',
    validFrom: 'Valid from',
    to: 'to',
    startDate: 'Start date',
    endDate: 'End date',
    requiredField: 'Required Field',
    addField: 'Add Field',
    msgFieldTips: 'Let buyers enter messages when buying products, up to 10 messages can be set',
    afterSalesService: 'After Sales Service',
    supportApplyRefund: 'Support buyers to apply for refund',
    doNotSupportApplyRefund: 'Do not support buyers to apply for refund',
    afterSalesServiceTips: 'The product details page will display the statement "Refunds are not supported", so buyers can only contact the merchant to initiate a refund.',
    msgCannotBePlainSpace: 'Cannot be plain space, please re-enter',
    // Specification
    addSpecPic: 'Add specification image',
    specValueCharacterLength: 'The length of specification value shall not exceed 20 characters',
    specNameNotNull: 'Specification name cannot be empty',
    selectSpecFirst: 'Please select the specification first',
    specName: 'Specification name',
    specValue: 'Specification value',
    specialWordSymbolTips: 'Special characters are not allowed ',
    currentlyAdded: 'Currently ',
    productSpecCount: ' groups of product specifications have been added',
    groups: 'groups',
    cannotAddUnderThisSpec: 'You cannot add under this specification name',
    createNewSpecNameManually: 'Support manual input to create a new specification name, mouse click or enter can be added, specification name no more than 10 characters',
    // tips
    postProductTips1: 'Choose the exact category to make it easier for users to search and increase order placement rates',
    postProductTips2: 'Selecting multiple languages requires editing multiple product selling points and product details. Languages not set will display the default language',
    postProductTips3: 'The suggestion: description + attribute, length cannot exceed 60',
    postProductTips4: 'Displayed under the title, the length cannot exceed 100',
    postProductTips5: 'Recommended size 800*800 pixels, draggable sorting, maximum of 9 uploads, first image as main image',
    postProductTips6: 'The main image video is recommended to be 9-30 seconds in length, with the same width and height as the product image.',
    postProductTips7: 'Total of each specification in stock',
    postProductTips8: '\'Same City Delivery\' is only available after the delivery management has been set up',
    postProductTips9: 'Shipping templates support shipping by region, number of pieces purchased, by weight, etc.',
    postProductTips10: 'Products requiring write-off will generate a write-off code that can be provided to merchants for offline write-off',
    postProductTips11: 'Specification name no more than 10 characters',
    postProductTips12: 'Only the first set of specifications is supported, recommended size: 800 x 800 pixels, specifications up to 20 characters long',
    postProductTips13: 'The first specification value can be added to the image',
    postProductTips14: 'Select content for bulk fill',
    postProductTips15: 'If no code is available, the system will automatically generate it',
    postProductTips16: 'No duplication of product codes',
    postProductTips17: 'The Chinese name of the product cannot be empty',
    postProductTips18: 'The English name of the product cannot be empty',
    postProductTips19: 'Product Chinese selling point can not be empty',
    postProductTips20: 'Product English selling point can not be empty',
    postProductTips21: 'Please upload a picture of the product',
    postProductTips22: 'The product cannot be changed after it is published successfully',
    postProductTips23: 'Platform classification cannot be modified after product release.',
    postProductTips24: 'The platform classification cannot be modified after the product is released. Please select an accurate classification',
    selectPlatformCategoryTips: 'Please select a platform category',
    selectShopCategoryTips: 'Please select a shop category',
    enableSpec: 'At least one product specification must be enabled',
    specValueCannotBeEmpty: 'The specification value cannot be empty',
    completeTheAddedSpec: 'Please complete the added specifications first',
    dateErrTips1: 'The start time cannot be greater than or equal to the end time',
    dateErrTips2: 'The end time cannot be less than or equal to the current time',
    dateErrTips3: 'End time cannot be less than or equal to the start time',
    msgMaxLength: 'Set up to 10 items',
    validDaysEmptyTips: 'Please fill in the number of days the write-off is valid',
    validDateEmptyTips: 'Please select the effective date of the write-off',
    msgEmptyTips: 'Please fill in the added message',
    cannotModifyProdType: 'Published products are not allowed to modify the product type',
    uploadDetailPicTips: 'To ensure that the picture is clear, please upload a picture with a width greater than 940px',
    parameterTips: 'Please enter the correct parameters',
    notAvailableSeparatePurchase: 'Combination items cannot be purchased individually and will not be searched for by buyers, and are only used for non-primary items in packages and giveaways, as well as for related items in combinations.',
    fillInTheNecessaryInfo: 'Please complete the required information for the product first!',
    chooseLanguage: 'Select Language',
    skuLangTips: 'select multiple languages to edit multiple attribute names and attribute values',
    attributeTips: 'The length of the attribute name does not exceed 10 words',
    attributeValueTips: 'Attribute value of no more than 20 words',
    availableInventory: 'Available Inventory',
    attributeNotDel: 'Only one attribute value remains that cannot be deleted',
    warehouseStock: 'Warehouse stock',
    stationStock: 'Store inventory'
  },
  productComm: {
    content: 'Comment content',
    pics: 'Comment on pictures',
    recTime: 'Recording time',
    no: 'No',
    replyTime: 'Recovery time',
    postip: 'IP source',
    score: 'Assess score',
    isAnonymous: 'Is anonymous',
    replyContent: 'Shopkeeper reply',
    platformAudit: 'Platform audit',
    status: 'To examine',
    noPass: 'Audit failed',
    pass: 'Audit pass',
    waitPass: 'Unreviewed',
    anonymousuUser: ' Anonymous use'
  },
  users: {
    name: 'Nickname'
  },
  prodTag: {
    prodManage: 'Group commodity management',
    tips: 'Only integers can be entered',
    scoreTip2: 'Sort number cannot be empty',
    isDele: '】product removed from group?',
    prodTagSeq: 'Group sort number',
    updateprodTagSeq: 'Modify sort number',
    returnProdManage: 'Back to group management',
    insertProd: 'New group goods',
    oneStyle: 'One style per column',
    twoStyle: 'Two style per column ',
    threeStyle: 'Three style per column'
  },
  transport: {
    name: 'Template name',
    type: 'Template Type',
    buyerPrice: 'Buyers bear the freight',
    shopPrice: 'Seller postage',
    chargePrice: 'Way of collecting money',
    byCount: 'By number of pieces',
    byWeight: 'By weight',
    byVolume: 'By volume',
    fullCount: 'full number of pieces',
    fullWeight: 'full weight',
    fullVolume: 'full volume',
    distributableArea: 'Distributable area',
    allRegions: 'All regions',
    addPrice: 'Click to add the area and shipping cost',
    freePostage: 'Designated conditions free postage',
    selArea: 'Designated Area',
    selCity: 'Please select a designated postage free city',
    fullPieces: 'Cumulative number of pieces/weight/volume',
    fullAmount: 'Cantidad de consumo alcanzada',
    fullAmount1: 'and Cantidad de consumo alcanzada',
    setShopPrice: 'Set postage free conditions',
    firstPiece: 'First piece(piece)',
    transportationCost: 'Transportation cost(cost)',
    continuationPiece: 'Continuation piece(piece)',
    continuationCost: 'Costo de continuación(costo)',
    firstWeight: 'Firest weight(kg)',
    continuedWeight: 'Continued weight(kg)',
    firstVolume: 'First volume(m³)',
    continuedVolume: 'Continued volume(m³)',
    freeShippCondi: 'Free shipping conditions',
    pwv: 'Pieces/weight/volume',
    pieces: 'Pieces',
    shippingIncluded: ' shipping included',
    yuan: ' yuan ',
    andFull: 'and full',
    areaMenu: 'Regional Operations'
  },
  address: {
    receiverTelephone: 'Receiver telephone',
    postalCode: 'Postal code',
    province: 'Province',
    city: 'City',
    area: 'Area',
    detailed: 'Detailed address',
    addr: 'Address',
    defaultAddr: 'Default addr',
    defaultRefundAddr: 'Default refund addr'
  },
  refund: {
    plat: 'platform',
    platInter: 'Platform intervention',
    buyerWithdrawn: 'Buyer withdrawn',
    interTips: 'Users apply for the platform to intervene in after-sales, please supplement the voucher before time',
    refundLog: 'Refund record',
    platRejectRefund: 'Platform refuses to refund',
    platAgreeRefundSucceed: 'The platform agrees that the refund is successful',
    applyPlatInter: 'Apply for platform intervention',
    addProof: 'Add certificate',
    areplenishProof: 'Supplementary document',
    uploadProof: 'Upload credentials',
    uploadProofTips: 'Please upload the certificate',
    uploadProofLimit: 'Upload up to 3 photos',
    proofDesc: 'Certificate description',
    proofDescEmptyTips: 'The certificate description cannot be empty',
    repealApplyInter: 'Withdraw the application for platform intervention',
    platNote: 'Platform message',
    closing: 'close of transaction',
    applyInter: 'Application for intervention',
    interHandle: 'Interventional treatment',
    buyerApply: 'Buyer has applied',
    merchantHasAgree: 'The seller has agreed to refund, waiting for the buyer to ship',
    buyerHasShipped: 'Buyer has shipped, waiting for courier picking',
    receivedGoods: 'The seller has received the goods',
    refundMoney: 'The seller has refunded'
  },
  order: {
    selectAddrTips: 'Please select a shipping address',
    notLogisticsTips: 'The relevant logistics configuration has not been opened',
    selectPrinterTips: 'Please select a printer',
    weixin: 'WeChat',
    alipay: 'Alipay',
    balance: 'Balance',
    payPal: 'PayPal',
    wait: 'wait for ',
    refund: ' Refund',
    lookRefunds: 'View Refunds',
    orderDetail: 'Order Details',
    contactBuyer: 'Contact Buyer',
    number: 'Order number',
    orderPickupS: 'Order pickup',
    orderWriteOff: 'Order Write-off',
    createTime: 'Order time',
    statusMsg: 'Select Order Status',
    checkDvyId: 'Please input the correct delivery order number!',
    checkDvyIdMsg: 'Please enter the correct logistics bill number!',
    query: 'Query',
    exportOrder: 'Exportar pedidos pendientes',
    exportSales: 'Export sales records',
    pendingPayment: 'Pending payment',
    EstimatedDeliveryTime: 'Estimated delivery time:',
    shopPreferentialAmount: 'MerchantPreferentialAmount',
    platformPreferentialAmount: 'platformPreferentialAmount',
    uploadTips1: 'Please select the operation, only one file can be uploaded at the same time',
    uploadTips2: 'The batch shipping process is: export the orders to be shipped, add the courier company and single number, then select the file and import the data.',
    uploadTips3: 'Note: ',
    uploadTips4: '1. If there are orders that are being refunded or have been refunded or have been partially shipped, they cannot be shipped in bulk.',
    uploadTips5: '2. Please do not do shipping operation for the exported order during the import period, otherwise the order will not be shipped in bulk.',
    SelectFile: 'Select file',
    ImportingFiles: 'Importing',
    ExportingFiles: 'Export',
    BulkShipping: 'Bulk shipping',
    sureToExport: 'Be sure to export operation?',
    pleExpOrderFirst: 'Please select the time to export the order first',
    downloadTemplateTips1: 'Upload template can only be XLS, xlsx format!',
    downloadTemplateTips2: 'Upload template size cannot exceed 2MB!',
    fileUploadFail: 'File upload failed!',
    confirm: 'Confirm',
    cancel: 'Cancel',
    toBeShipped: 'To be shipped',
    pendingReceipt: 'Goods to be received',
    delivery: 'Delivery',
    deliveryMsg: 'The buyer can only ship after payment.',
    invoiceMsg: 'Do not send invoice',
    fileSuccess: 'File imported successfully',
    addressee: 'Addressee',
    toBeEvaluated: 'To be evaluated',
    success: 'Success',
    product: 'Product',
    totalPrice: 'Total price',
    transactionPrice: 'Transaction price',
    receivedGoods: 'Buyer has received the goods',
    purchaseQuantity: 'Purchase quantity',
    paymentMethod: 'Payment method',
    actualAmount: 'Actual amount paid',
    status: 'Status',
    logs: 'Order log',
    amountDue: 'Amount due',
    createOrder: 'Create order',
    payment: 'Order payment',
    deliverys: 'Order delivery',
    completed: ' Order completed',
    cancelOrder: 'Cancel order',
    operation: ' Operation',
    comprador: 'Comprador',
    compradorMsg: 'Buyer Comments',
    fail: 'Fail',
    refundId: 'Refund ID',
    applicationType: 'Apply type',
    onlyRefund: 'Only refund',
    refundAndMoney: 'Money back and returns',
    orderAmount: 'Order amount',
    refundAmount: 'Refund amount',
    applicationTime: 'Apply time',
    refundStatus: 'Refund status',
    processingRefunds: 'Processing refunds',
    shipped: 'The buyer has shipped',
    waitingRefund: 'Waiting for refund',
    refundsuccessfully: 'Refund success',
    refunding: 'Refund in progress',
    refundFailed: 'Refund failed',
    withdrawsApplication: 'Buyer withdraws application',
    refusedRefund: 'Seller refuses refund',
    refundClosed: 'Refund closed',
    exportForm: 'Export form',
    viewOrder: 'View order',
    recordId: 'Record',
    shopId: 'Shop ID',
    orderId: 'Order ID',
    refundAll: 'Line item ID, full amount refund is 0',
    orderPayment: 'Order payment serial number',
    thirdParty: 'Third party refund number(WeChat refund ticket number)',
    orderPaymentMethod: 'Order payment method: 1 WeChat  2 Alipay',
    orderPaymentName: 'Order payment name',
    buyerId: 'Buyer ID',
    returnQuantity: 'Refund number',
    returnAmount: 'Refund amount',
    applicationMethod: 'Application type: 1,Only refund, 2.Money back and returns',
    processingStatus: 'Processing status: 1.Pending review,2.Agree,3.disagree',
    processingRefundStatus: 'Refund status: 1.Processing refunds,2.Refund successfully,3.Refund failed',
    sellerProcessingTime: 'Seller processing time ',
    refundTime: 'Refund time',
    fileCredentialjson: 'File credential json',
    reasonForApplication: 'Reason for application',
    sellerNote: 'Seller note',
    logisticsCompanyName: 'Logistics company name',
    logisticsNumber: 'Logistics number',
    deliveryTime: 'Delivery time',
    timeOfReceipt: 'Time of receipt',
    commentsAfterReceipt: 'Comments after receipt',
    pleEntShipInfo: 'Please enter shipping information',
    senderName: 'Sender name',
    shiPhoNum: 'Shipper phone number',
    deliveryAddress: 'Shipping address',
    orderType: 'Order type',
    orderMold: 'Order mold',
    pleaseSelectOrderType: 'Please select order type',
    pleaseSelectOrderMold: 'Please select order mold',
    theRecipientSName: ' Recipient',
    pleaseEnRecipName: 'Recipient\'s name',
    pleaseEnterNumber: 'Recipient\'s phone number',
    pleaseSePayt: 'Please select payment method',
    afterSalesStatus: 'After-sales status',
    pleSelAfterSalesSta: 'Please select after-sales status',
    logisticsType: ' Logistics type',
    pleSelTypeOfLog: 'Please select logistics type',
    transaQuantity: 'Transaction price (￥) / quantity',
    actualPaymentAmount: 'Amount actually paid (￥)',
    buyerConsignee: 'Buyer/Consignee',
    afterSale: 'After-sales',
    afterSaleTips: 'The data includes buyer application, seller acceptance, buyer delivery, application intervention, intervention processing list data',
    buyerApplication: 'Buyer apply',
    refundApplicationInProgress: 'Refund application in progress',
    sellerAccepts: ' Seller accepts',
    buyShipment: 'Buyer shipment',
    buyReceiving: 'Buyer receive',
    selShipment: 'Seller shipment',
    sellerReceipt: 'Seller receipt',
    refundSuccessfully: 'Refund successful',
    successfulTransaction: 'Transaction successful',
    transactionFailed: 'Transaction failed',
    sameCityDelivery: 'Same city delivery',
    onlineDelivery: 'Online Shipping',
    selfDelivery: 'Blow-the-line Shipping',
    toOpen: 'To Config',
    deliveryPrintType: 'Shipping Type',
    printInfo: 'Printout',
    printer: 'Printer',
    selfMention: 'Pick up',
    integral: 'Points',
    includingFreight: 'Shipping costs included ',
    total: 'Total',
    modifyTheAmount: 'Modify Amount',
    unpaid: 'Unpaid',
    pointsPayment: 'Point Payment',
    wecProPay: 'WeChat Mini Program Payment',
    alipayPCPayment: 'Alipay PC Payment',
    wechatScanCodePayment: 'WeChat Scan Code Payment',
    wechatH5Payment: 'WeChat H5 Payment',
    weclAccountPay: 'WeChat Official Account Payment',
    alipayH5Payment: 'Alipay H5 Payment',
    alipayAPPPayment: 'Alipay APP Payment',
    wechatAPPPayment: 'WeChat APP Payment',
    balancePayment: 'User balance payment',
    payPalPayment: 'PayPal payment',
    refundApplication: 'Refund Application in Progress',
    partialRefundSucc: 'Partial Refund Successful',
    noAfterSales: 'No After-sales Service',
    seeDetails: 'View Details',
    modifyLogistics: 'Modify Logistics',
    pickUp: 'Pickup',
    Writeoffs: 'Write-offs',
    refundInformation: 'Refund Information',
    noData: 'No Data',
    ifModifyTheLog: 'If you modify the logistics information, please carefully check and fill in and verify',
    package: 'Package',
    deliveryMethod: 'Delivery Method',
    needLogistics: 'Require Logistics',
    courierCompany: 'Express Delivery Company',
    trackingNumber: 'Express Tracking Number',
    packageName: 'Package Name',
    amountOfGoods: 'Product Quantity',
    logisticsCompany: 'Logistic Company',
    save: 'Save',
    backToModify: 'Return to Modify',
    confirmTheChanges: 'Confirm Modification',
    normalOrder: 'Ordinary Order',
    groupPurchaseOrder: 'Group Purchase Order',
    spikeOrder: 'Seckill Order',
    virtualOrder: 'virtual item order',
    physicalOrder: 'Physical product orders',
    comboOrder: 'Combo product orders',
    electronicCardOrder: 'Electronic card order',
    giveawayPord: 'giveaway',
    combo: 'combo',
    expressDelivery: 'Express',
    noNeedRequired: 'No express delivery',
    distribution: 'Express delivery',
    logEmpty: 'Logistics number cannot be empty',
    logistics: 'Logistics delivery',
    noLogIsChanged: 'No logistics information has been changed',
    exportReport: 'Export reports are only exported according to the selected time of order placement',
    orderInfCollationXls: 'Order Information Sorting.xlsx',
    pendingOrderInformation: 'Pending Order Information.xlsx',
    orderShipping: 'Order Shipment',
    productNumber: 'Product No.',
    reimburse: 'Refund',
    canShipQuantity: 'Quantity Available for Shipment',
    shipmentCompleted: 'Shipment Complete',
    waitWriteOff: 'Pending write-offs',
    waitForDelivery: 'Waiting for Shipment',
    numberOfShipments: 'Shipment Quantity',
    delType: 'Delivery type',
    selfConOrd: 'Express Delivery',
    seleOrd: 'Please select the product that needs to be shipped',
    oderNumNo: 'The order can be shipped is not enough',
    numNotZero: 'The shipping quantity cannot be 0',
    fhNumNotZero: 'The shipping quantity cannot be empty',
    seleCouCom: 'Please select the courier company',
    entCouNum: 'Please enter the courier number',
    pledSeleMet: 'Please select the shipping method',
    requestARefund: 'Request a refund',

    partialDelivery: 'Partial delivery',
    pendingPickUp: 'Pending pickup',
    shippingInformation: 'Shipping information',
    virtualInfo: 'virtual product information',
    virtualMsg: 'All messages',
    selfMentionCode: 'Write-off Code',
    pleEntPickupCode: 'Please enter the self-pickup code',
    doNotEntPickupCode1: 'If you do not enter the checkout code, the default is no verification, direct pickup',
    doNotEntPickupCode2: 'If you do not enter the checkout code, the default is no verification and all current orders are checked out',
    pickupCodeError: 'Pickup code error, please enter the correct pickup code',

    modifyOrderAmount: 'Modify the order amount',
    modificatioAmount: 'Modify the user payment amount, in the price reduction, the platform discount, classification commission amount are reduced proportionally, in the price increase order commission according to the amount of proportional increase, and the amount of platform discount will not exceed the amount of the platform discount when the user orders. Please be careful to change',
    unitPrice: 'Unit price',
    quantity: 'Quantity',
    price: 'Price',
    totalDiscount: 'Total discount',
    userPayunt: 'Amount paid by users',
    platforlowance: 'Platform allowance',
    estimancome: 'Estimated income',
    reducedAnt: 'Reduction amount',
    shippingAmunt: 'Shipping amount',
    decreaslowance: 'Platform allowance reduction',
    theOraterThan0: 'Order amount must be greater than 0',
    waitingFosPayment: 'Waiting for buyer  payment',
    waitiooShip: 'Waiting for merchant to ship',
    waitingFeGoods: 'Waiting for purchase Home receipt',
    waitingtion: 'Waiting for buyer  evaluation',
    commoditful: 'Commodity transaction successful',
    commodityFailed: 'Commodity transaction failure',
    commodited: 'Commodity to be grouped',
    buyerDidNTime: 'Buyer fails to pay within the specified time, the order will be automatically cancelled. ',

    buyPleF: 'The buyer has paid, please ship the goods as soon as possible. ',
    shelF: 'The merchant has shipped and is waiting for confirmation of receipt. ',
    buyA: 'The buyer has already picked up and is waiting for confirmation of receipt. ',
    buyB: 'The order has been completed and the commodity transaction is successful. ',
    orderCanc: 'The order has been cancelled and the commodity transaction failed. ',
    outTimeCanOrd: 'If there is no group within the specified time, the order will be automatically cancelled. ',
    submitOrders: 'Submit order',
    theBuyerHasPaid: 'Buyer has paid',
    buyerHasMentioned: 'Buyer has picked up',
    shippedBySeller: 'Seller has shipped',
    buyerHasReceived: 'Buyer has received goods',
    waybillNumber: 'Waybill number',
    logisticsStatus: 'Logistic status',
    noRecord: 'No Record',
    collected: 'Collected',
    delivering: 'In transit',
    haveBeenReceived: 'Signed for',
    reachTheDestinationCity: 'Achieved destination city',
    logisticsInformation: 'Logistics Information',
    problemPiece: 'Problem piece',
    noLogisticsInformation: 'No logistics information, please try again later',
    merchantHasShippedWa: 'Business has shipped, Waiting for courier picking',
    buyerHasPaidWa: 'Buyer has completed payment and waiting for shipment',
    buyerSubmittedAnOrder: 'Buyer submits order and waits for system confirmation',
    recipientInformation: 'Consignee information',
    deliveryPerson: 'Pick-up person',
    paymentInformation: 'Payment information',
    paymentTime: 'Payment time',
    no: 'Nothing',
    buyerInformation: 'Buyer information',
    buyerSNickname: 'Buyer nickname',
    buyerPhone: 'Buyer phone number',
    buyerMessage: 'Buyer message',
    orderRemarks: 'Order remarks',
    shippingFees: 'Delivery cost ',
    orderLog: 'Order log',
    orderUpdate: 'Order update (success)',
    orderPickup: 'Order self-pickup',
    typeOfRefund: 'Refund type',
    pleaseChooseHowToApply: 'Please choose an application method',
    withdrawApplication: 'Revoke apply',
    addCategory: 'Add category',
    addBrand: 'Add brand',
    withdraw: 'revoke',
    commodityUnitPriceYuanQuantity: 'Product price (￥) / Quantity',
    commodityCondition: 'Product status',
    refunds: 'Return goods refund',
    goodsReceived: 'Goods received',
    goodsNotReceived: 'Goods not received',
    wholeGoodsRefund: 'Refund of whole goods',
    singleItemRefund: 'Refund of individual items ',
    wholeOrderRefund: 'Full order refund',
    orderSerialNumber: 'Order serial number',
    pleaseSelectTheDeliveryAddress: 'Please select the delivery address',
    deliveryAddressCannotBeEmpty: 'The delivery address cannot be empty',
    refundProcessing: ' Refund processing',
    requestARefundT: 'Apply for a return refund',
    agreeToReturnAndRefund: 'Agree to return a refund',
    buyer: ' Buyer',
    merchant: 'Merchant',
    confirmReceipt: 'Confirm receipt',
    successfulProcessing: 'Successful processing',
    issueARefund: 'Issuing a refund',
    orderActuallyPaid: 'Order paid total',
    reasonForReturn: 'Refund reason',
    refundInstructions: 'Refund description',
    logisticsDetails: 'Logistics details',
    logisticsName: 'Logistic name',
    returnCertificate: 'Return certificate',
    logisticsCertificate: 'Logistic certificate',
    refundGoods: 'Refund product',
    applyForApproval: 'Apply for approval',
    agree: 'Agree',
    disagree: 'Disagree',
    returnStatus: 'Return status',
    received: 'Received goods',
    unreceived: 'Unreceived goods',
    returnRequest: 'Return application',
    denialReason: 'Reason for rejection',
    merchantNotes: 'Merchant remarks',
    confirmTreatment: 'Confirm processing',
    refundLog: 'Refund log',
    submitARefundRequestWa: 'Submit refund request, waiting for merchant to process',
    merchantHasProcessedWaBuy: 'Merchant processed, waiting for buyer to ship',
    merchantHasProcessedWaSh: 'Merchant processed, waiting for merchant to refund',
    buyerShipmentWaPro: 'Buyer shipped, waiting for merchant to receive goods',
    merchantHasReceivedWaShGr: 'Merchant has received goods, waiting for merchant to agree to refund',
    agreeToRefund: 'Agree Refund, waiting to issue refund',
    buyerHasWithdrawn: 'Buyer has cancelled(or timeout)',
    merchantRejected: 'Merchant rejected',

    ordinaryExpress: 'Ordinary express',
    buyerMention: 'Buyer mention',
    prodTotalPrice: 'Total commodity price',
    returnType: 'Return type',
    returnMethod: 'Return method',
    returnDetails: 'Return details',
    discount: 'Discount',
    agreeToRefundA: 'Agree to refund',
    refusalToRefund: 'Refusal to refund',
    modifyAmountSuccess: 'Modify amount successfully',
    num: 'Num',
    invoiceStatus: 'Invoice Status',
    applicationInProgress: 'Application in progress',
    invoiceIssued: 'Invoice issued',
    headerType: 'Invoice payable to',
    headerName: 'Invoice name',
    uploadTime: 'Upload time',
    unit: 'Unit',
    personal: 'Personal',
    invoiceType: 'Invoice Type',
    normalInvoiceType: 'Electronic General Invoice',
    invoiceTaxNumber: 'Taxpayer identification number',
    invoiceUpload: 'Invoice Upload',
    refundTip: 'An invoice has been uploaded for this order, please review and process the contents of the uploaded invoice after confirming the refund.',
    uploadInvoiceTip: 'Please select file to upload',
    uploadInvoiceTip1: 'The order has not been confirmed, will I continue to upload the invoice?',
    uploadInvoiceTip2: 'Users may have changed their invoice application information, do they continue to submit?',
    uploadInvoiceTip4: 'Please select the file and upload it',
    lastProdRefundTip: 'The refund request item is the last item in the order, and the shipping cost ￥[d] will be refunded to the user after the refund is granted.',
    lastProdRefundTipPlatform: 'The refund request item is the last item in the order, and the shipping cost ￥[d] will be refunded to the platform after the refund is granted.',
    freeFreight: 'Free shipping for merchants',
    platformFreeFreightAmount: 'Platform package reduction',
    salesPrice: 'selling price',
    score: 'Score'
  },
  hotSearch: {
    contentEs: 'Spanish content',
    contentEn: 'English content',
    titleEs: 'Spanish title',
    titleEn: 'English title',
    seq: 'Queue number',
    length50: 'Length from 1 to 50 characters',
    length250: 'Length from 1 to 250 characters'
  },
  homes: {
    home: 'Home',
    names: 'YamiShop-Backstage',
    shortName: 'Yami',
    updatePwd: 'Change Password',
    exit: 'Exit',
    noPermission: '401',
    notFound: '404',
    login: 'Login',
    verifyTip: 'Drag the slider to the right to fill the puzzle',
    isExit: 'Are you sure you want to exit?',
    layout: 'Overall layout of the main entrance',
    productDetails: 'Details',
    oldPwd: 'Old password',
    newPwd: 'New password',
    failed: 'Menu list request and failed permissions, jump to the login page! !',
    confirmPassword: 'Confirm Password',
    userName: 'Username',
    istrue: 'The confirmation password is inconsistent with the new password',
    yes: 'Yes',
    no: 'No',
    loading: 'Loading desperately',
    cloCurrTab: 'Close current tab',
    cloOtherTab: 'Close other tabs',
    cloAllTab: 'Close all tabs',
    refreCurrTab: 'Refresh the current tab',
    sorry: 'Sorry! The page you visited is',
    miss: 'Missing',
    la: 'La.',
    goBack: 'Go back to the last page',
    noPermissionAccessPage: 'You do not have permission to access this page',
    enterMain: 'Enter main page',
    register: 'register',
    readConsent: 'I have read and agree',
    registerProtocol: 'Merchant Registration Agreement',
    shopProtocol: 'Merchant Settlement Agreement',
    alreadyAccount: 'Already have an account',
    readFirst: 'Please read first',
    goToLogin: 'Go to log in',
    registerSuccess: 'Registration is successful! About to jump to the login page...',
    registerSuccess2: 'Registration is successful! About to jump to the apply to open a store page...',
    InputCorrectPhone: 'Please enter the correct phone number',
    InputCorrectUsername: '4-16 letters, numbers or underscores in the user name that are not pure numbers',
    InputCorrectPassword: 'Password length is between 6-20 characters',
    registrationTips: 'Open shop onfree',
    registration: 'Registration',
    notFoundTips: 'Not Found',
    backPrePage: 'Back',
    toHome: 'Home'
  },
  category: {
    categoryPicture: 'Category Pic',
    recPicSize: 'Suggested image size is ',
    categoryIcon: 'Category Icon ',
    categoryName: 'Category Name ',
    categoryParent: 'Parent Category',
    deductionRate: 'DeductionRate',
    categoryPicNull: 'Category pic cannot be empty ',
    categoryIconNull: 'Category icon cannot be empty ',
    categoryNameNull: 'Category name cannot be empty',
    categorySelector: 'Category Selector',
    chooseProdCateg: 'Select merchandise category',
    currCho: 'Your current choice is',
    isItAComtionPro: 'Whether it is a combined good',
    generalMerchandise: 'Ordinary Goods',
    combinationGoods: 'Combination Goods',
    haveReadFol: 'Confirm Selection',
    exportXls: 'Shop classification.xlsx'
  },
  publics: {
    seq: 'Sort',
    category: 'Category',
    categoryCn: 'Category Chinese Name',
    categoryEn: 'Category English Name',
    categoryNoNull: 'Category name cannot be empty',
    categoryInputTips: 'Category name',
    image: 'Image',
    imageNoNull: 'Image cannot be empty',
    videoNoNull: 'Video cannot be empty',
    status: 'Status',
    serial: 'Serial Number',
    serialNoEnter: 'Please enter the serial number',
    isTop: 'IsTop',
    cancelTop: 'Cancel top',
    addressee: 'Receiver name',
    title: 'Title',
    titleNoEnter: 'Please enter a title name',
    type: 'Type',
    selectOne: 'Please select at least one sending type!',
    defaultType: 'Default type',
    customType: 'Custom type',
    list: 'List',
    noAbleTabErr: 'Could not find available tab',
    normal: 'Normal',
    seqError: 'The number can not less than zero',
    setNull: 'Reset',
    stop: 'Stop',
    restore: 'Restore',
    executeImmediately: 'Execute immediately',
    disable: 'Disable',
    able: 'Able',
    label: 'Label',
    releaseTime: 'Release Time',
    updateTime: 'Update Time',
    yes: 'Yes',
    no: 'No',
    mobilePhone: 'Mobile phone',
    deliveryAddr: ' delivery address',
    freight: 'Freight',
    content: 'Content',
    cancel: 'Cancel',
    publicar: 'Publish',
    noNull: ' cannot be empty',
    chooseStore: 'Choose a store',
    selectActivity: 'Select activity',
    des: 'Description',
    deletes: 'Are you sure you want to delete?',
    remark: 'Remark',
    operation: 'Operation success',
    batchDelete: 'Batch delete',
    default: 'Default',
    name: 'Name',
    nameEg: 'Name, E.g ',
    profilePicture: 'Profile Picture',
    batchStopte: 'Batch Stop',
    batchRestore: 'Batch Restore',
    and: 'And',
    batchExecuteImmediately: 'Batch Execute immediately',
    operating: 'Operating',
    importe: 'Import',
    UpperShelf: 'Upper shelf',
    metUpperShelf: 'Merchants upper shelf',
    pendingReview: 'Pending review',
    violationShelf: 'Violation of the shelf',
    LowerShelf: 'Lower shelf',
    metLowerShelf: 'Merchant lower shelf',
    parentCa: 'Superior classification',
    relateSingleProduct: 'Relate single-product',
    selectAll: 'Select all',
    selected: 'Selected',
    batchSetting: 'Batch setting',
    saveBatchSetting: 'Save Batch Settings',
    hadLowerShelf: 'Pull off shelves',
    code: 'code',
    maxIndexImgNumOfPlatform: 'The maximum number of carousels per platform is 10',
    currentSelectAll: 'Select All on the Current Page',
    newUnreadMessages: 'You have new unread messages'
  },
  addr: {},
  sys: {
    userName: 'Username',
    nickName: 'Nickname',
    mobile: 'Mobile',
    beanName: 'Bean Name',
    beanNameEg: 'Spring bean Name, E.g: testTask',
    beanNameNoNull: 'BeanName cannot be empty',
    userAction: 'User Action',
    requestMerthod: 'Request Method',
    requestParameter: 'Request Parameter',
    executionTime: 'Execution Time(millisecond)',
    consumingTime: 'Time consuming(millisecond)',
    ipAddress: 'IP Address',
    creationTime: 'Creation Time',
    parameteName: 'Parameter Name',
    parameteNameNoNull: 'Parameter value cannot be empty',
    parameterValue: 'Parameter Value',
    parameterValuenoNull: 'Parameter Value',
    parameteNameAndParameterValue: 'The parameter name shall not exceed 10 words and the parameter value shall not exceed 20 words',
    parentMenu: 'Parent Menu',
    menuUrl: 'Menu Url',
    menuUrlNoNull: 'Menu URL cannot be empty',
    authorization: 'Authorization ID',
    menuIcon: 'Menu icon',
    menu: 'Menu',
    menuNameNoNull: 'Menu URL cannot be empty',
    catalog: 'Catalog',
    button: 'Button',
    separated: 'Separated by commas, E.g--',
    menuIconName: 'Menu icon name',
    icon: 'Icon',
    roles: 'Roles',
    des: 'Description',
    operation: 'Operation',
    deletes: 'Are you sure you want to delete this product?',
    batchDelete: 'Batch delete',
    password: 'Password',
    confirmPassword: 'Confirm password',
    confirmUpdate: 'Determine the modification',
    email: 'Email',
    connInfo: 'Contact information',
    roleName: 'Role Name',
    roleNameNoNull: 'Role name cannot be empty',
    authorize: 'Autorize',
    methodName: 'Method Name',
    methodNameNoNull: 'Method name cannot be empty',
    params: 'Parameter',
    cronExpression: 'Cron Expression',
    cronExpressionEg: 'E.g : 如: 0 0 12 * * ?',
    cronExpressionNoNull: 'Cron expression cannot be empty',
    areaName: 'Area Name',
    superiorAreaL: 'Superior area',
    usernameNotEmpty: 'Username can not be empty',
    makeSure: 'Are you sure to ',
    makeSureDelete: 'Deleting images may affect the page display, make sure to proceed',
    passwordNoNull: 'Password can not be empty',
    oldPwdNotNull: 'Old password cannot be empty',
    newPwdNotNull: 'New password cannot be empty',
    confirmPassNoNull: 'Confirm password can not be empty',
    passworldContrast: 'Confirm password is inconsistent with password input',
    retrievePassword: 'Retrieve password',
    emailaError: 'Incorrect mailbox format',
    emailaNoNull: 'E-mail can not be empty',
    mobilePhoneError: 'Username can not be empty',
    mobilePhoneNoNull: 'Phone number cannot be empty',
    userNameNoNull: 'Username can not be empty',
    nickNameBetween: 'nickName must be between 2 and 20 characters',
    nickNameNoNull: 'Nickname can not be empty',
    userLength: 'Username length should be between 2-20',
    content: 'Recomendado para SVG Sprite , Please refer to icons/index.js ',
    regionalKeyword: 'Regional keyword',
    jobId: 'JobId',
    superAdmin: ' (Super Administrator)',
    pleaseSelectAuth: 'Please select authorization!'
  },
  platform: {
    platform: 'Cilent',
    applets: 'Applets',
    mobile: 'Mobile',
    pc: 'PC'
  },
  station: {
    stationAdd: 'New Stores',
    stationEdit: 'Editorial Stores',
    stationLogo: 'Stores Logo',
    stationLogoTips: 'Recommended image size is 80*80 pixels',
    stationImg: 'Stores Pictures',
    stationImgTips: 'Upload up to 9 pictures',
    stationUse: 'Shop usage',
    stationUseSelTips: 'Please select shop use',
    selfPickup: 'In-store pick-up/write-off',
    sameCityDelivery: 'home delivery',
    stationTips: 'Shops will all offer virtual goods write-off services',
    deliveryAreaNullTips: 'Please click on the map to configure the shop delivery area',
    prodStation: 'Stores name',
    stationName: 'Stores name',
    stationNames: 'Stores name',
    stationAddr: 'Stores address',
    addr: 'Address',
    number: 'Number',
    account: 'Account',
    close: 'Close',
    closeAndUpdate: 'Close and return to refresh',
    open: 'Open',
    business: 'Business',
    platformClosed: 'Platform closed',
    underReview: 'Under Review',
    auditFailure: 'Audit Failure',
    successfulAudit: 'Successful Audit',
    businessStatus: 'Business Status',
    withdrawCash: 'Withdrawal pending review',
    pendingReview: 'pending review'
  },
  form: {
    formName: 'Report name',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    reportType: 'Report type'
  },
  group: {
    actName: 'Activity name',
    groupType: 'Group type',
    groupOrderCount: 'Group order count',
    actStartTime: 'Activity start time',
    actTime: 'Activity time',
    actEndTime: 'Activity end time',
    actStatus: 'Activity status',
    prodInfo: 'Product information',
    groupNum: 'Group members',
    totalOrderAmount: 'Total order amount',
    groupTime: 'Group time',
    orderStatus: 'Order status',
    actProName: 'Activity product name',
    proImg: 'Product picture',
    proActStatus: 'Product activity status',
    groupSkuId: 'Group sku id',
    groupProdId: 'Group product id',
    skuId: 'Sku id',
    actPrice: 'Activity price',
    leaderPrice: 'Leader price',
    sellNum: 'Quantity sold',
    waitGroup: 'Waiting group',
    notOpenGroup: 'Not open',
    waitGroupUnpay: 'Waiting for payment',
    inAGroup: 'In a group',
    succ: 'Success group',
    failGroup: 'Failed group',
    groupOrdId: 'Group Order ID',
    groupId: 'Group ID',
    actProId: 'Activity product ID',
    expired: 'Invalidated',
    startUsing: 'Enable',
    notEnabled: 'Not enabled',
    offlineViolation: 'Illegal off the shelf',
    moderated: 'Awaiting review',
    over: 'Ended',
    skuName: 'Sku name',
    groupOrdStatus: 'Group order status',
    groupsNum: 'Number of groups',
    groupStratTime: 'Group start time',
    groupEndTime: 'Group end time',
    actLabel: 'Activity label'
  },
  live: {
    view: 'View',
    addNewLiveRoom: 'Add Live Room',
    liveTime: 'Time of live broadcast',
    viewLRoomInfo: 'View live room information',
    liveName: 'Live room name',
    anchorName: 'Anchor name',
    anchorMobile: 'Anchor Mobile',
    numberLimit: 'Please enter a number lower than 10000000000000',
    liveStatus: 'Live room status',
    living: 'Live',
    over: 'Over',
    offline: 'Offline',
    noStart: 'Not started',
    finished: 'Closed',
    stop: 'Stoped',
    liveStartTime: 'Live broadcast start time',
    liveEndTime: 'Live broadcast end time',
    pleaseEnteThan0: 'Please enter an integer greater than 0 or a positive number with two decimal places',
    productBeEmpty: 'Product cover cannot be empty',
    startTime: 'Live time',
    notPinned: 'Not topped',
    pinned: 'Topped',
    recommendedSize: 'Recommended size',
    pixel: 'Pixel',
    prod: 'Live Prod',
    chooseStartDate: 'Choose start date',
    chooseEndDate: 'Choose end date',
    liveBackgroundImage: 'Live background image',
    liveRoomNameCannotBeEmpty: 'Live room name cannot be empty',
    anchoBeEmp: ' Anchor nickname cannot be empty',
    anchorMobileBeEmp: 'Anchor Mobile cannot be empty',
    backEmpty: 'Background image cannot be empty',
    hostSnPict: 'Anchor sharing image cannot be empty',
    liveEmpty: 'Live channel cover cannot be empty',
    liveBy: 'Live broadcast time cannot be empty',
    liveProductCover: 'Live product cover',
    returnment: 'Return to live room management',
    addLicts: 'Add live room products',
    edit: 'Edit',
    recomImaSizeIs: 'The recommended image size is',
    anchorSharingPicture: 'Anchor sharing picture',
    liveCoverImage: 'Live cover image',
    liveTip1: 'Fill in the account nickname used by the anchor in the mall',
    liveAnchorNotExist: 'The anchor has canceled his account',
    liveProdTip: 'Please select a live product',
    liveAnchorError: 'The anchor\'s account has been disabled by the platform',
    liveTimeTip: 'The interval between the start and end time must be greater than 30 minutes and less than 24 hours'
  },
  coupon: {
    couponName: 'Coupon name',
    couponType: 'Coupon type',
    couponTypeTips: 'Please select the coupon type',
    voucher: 'Voucher',
    yuan: '￥',
    discountCoupon: 'Discount coupon',
    editStatusErrorTips: 'Coupons are offline or pending review, and cannot be set to serve status',
    excCerti: 'Exchange certificate',
    timeToMarket: 'Time to market',
    chooseLaunchTime: 'Select drop time',
    launchTimeTip: 'Casting time must not be less than the current time',
    launchTimeTip1: 'Casting time must not be null',
    launchTimeTip2: 'The expiration time cannot be earlier than the time equal to the time of placement',
    waitLaunch: 'Temporarily not put',
    waitAutoLaunch: 'Automatic placement',
    launchTip: 'The system will automatically place the coupon according to the coupon placement time, and the status will be placed after the placement',
    launchTip1: 'When a coupon expires, the coupon status is cancelled',
    startTime: 'Effective time',
    endTime: 'Expiration time',
    effectiveType: 'Effective Type',
    expStatus: 'Expired status',
    notExp: 'Not expired',
    exp: 'Expired',
    launchStatus: 'Launch status',
    launched: 'Launch',
    cancelLaunch: 'Cancel launch',
    illOff: 'Illegal off the shelf',
    getWay: 'get way',
    waitReview: 'Waiting for review',
    receiveDirectly: 'Receive directly',
    exchangeOrSystemIssue: 'System issue',
    pleaseSelectCoupon: 'Please select a coupon',
    stock: 'Stock'
  },
  distribution: {
    commAmount: 'Commission amout',
    distributor: 'Distributor',
    phoneNum: 'Phone number',
    distriStatus: 'Distribution status',
    waitPayment: 'Waiting for payment',
    waitSettle: 'Waiting for settle',
    settled: 'Settled',
    invOrder: 'Invalid order',
    orderNumber: 'order number'
  },
  distributionProdLog: {
    lapseCase: 'Cause of failure',
    lapse0: 'normal',
    lapse1: 'The distribution commission is greater than or equal to the actual amount paid for the line item',
    lapse2: 'Line item after-sales success',
    lapse3: 'The distribution commission is less than 0.01'
  },
  shop: {
    polygonPathOverNum: 'You can only select 12 coordinate points at most!',
    resetMap: 'Reset',
    hotTitle: 'Hot search title',
    hotContent: 'Hot search content',
    recDate: 'Rec Time',
    enableStatus: 'Enabled status',
    notEna: 'Not Enabled',
    ena: 'Enable',
    noticeContent: 'Notice Title',
    isTop: 'isTop',
    ioType: 'Pay Or Income',
    pay: 'Pay',
    income: 'Income',
    amountType: 'Amount type',
    amoSett: 'Amount to be settled',
    withdrawalDetail: 'Detail',
    avaStoAmo: 'Available store amount',
    unavaBalance: 'Unavailable balance',
    summAmo: 'Summary amount',
    beforeAmount: 'Amount before change',
    notThrough: 'NotThrough',
    notAudit: 'NotAudit',
    afterAmount: 'Amount after change',
    changeAomunt: 'Change amount',
    reason: 'Reasons for changes in funds',
    userPay: 'User payment',
    userRece: 'User confirms receipt',
    userRefund: 'User refund request',
    withApply: 'Withdrawal apply',
    withNoPass: 'Withdraw apply no passed',
    withPass: 'Withdraw apply passed',
    succIss: 'Success issued',
    failIss: 'Fail Issuance',
    dedAmo: 'Deduct the order distribution amount',
    supplierName: 'Supplier name',
    supplierProdImportTip: 'Added products are not empty and do not support import',
    tel: 'Work phone',
    unreviewed: 'Unreviewed',
    passed: 'Passed',
    notPass: 'failed to pass',
    contactName: 'Contact person',
    contactTel: 'Contact number',
    phoneNumber: 'mobile phone',
    supplierCategoryName: 'Supplier category name',
    exportTip: 'Whether to export supplier information',
    exportProdTip: 'Does the exported product information export based on what has been saved, and does it export supplier product information?',
    exportProdTip_store: 'Does the exported product information export the inventory product information based on the saved content?',
    exportProdTip_physical: 'Is the exported product information exported based on the saved content, and is the physical inventory information exported?',
    importProdTip: 'You can only import products that have not been added.',
    disableCategoryTip: 'Confirm to disable this supplier category? No new suppliers can be added to this category after it has been disabled',
    fileNullTip: 'Please select the file to be uploaded',
    exportWarn: 'The supplier region is a drop down box linkage option, so the import function is not available, please add it manually after importing the supplier.',
    comInfoSorXls: 'Supplier information collation.xlsx',
    fileName: 'Supplier information templates.xlsx',
    prodFileName: 'Supplier product templates.xlsx',
    exProdFileName: 'Supplier Product Information.xlsx',
    supplierProdCount: 'Number of supplier goods',
    editSupplierProd: 'Edit supplier product',
    exportProdTip1: 'The import is based on the product code, i.e. one product specification as a unit. If there is no product code for that product specification, please add it and then import it.',
    exportProdTip2: 'Minimum order quantity:When placing a purchase order, you need to purchase according to the minimum order quantity, and the default is 1 if not filled.',
    exportProdTip3: 'Purchase price:Default is 0.01 when not filled in.',
    exportProdTip4: 'Import default save, if importing the same product code, only the first data will be saved.',
    supplierProd: 'Supplier product',
    addProd: 'Add product',
    addItem: 'Add single product',
    editProd: 'Edit product',
    minOrderQuantity: 'Minimum order quantity',
    purchasePrice: 'Purchase price (RMB)',
    addBankCard: 'Add bank card',
    bankName: 'Bank name',
    pleEntTheBankName: 'Please enter the bank name',
    bankAccBranch: 'Bank card account opening branch',
    plnterThntBh: 'Please enter the bank account opening branch',
    bankCaccount: 'Bank card account number',
    pleaseEarNuber: 'Please enter bank card account number',
    bankCardOwner: 'Bank card owner ',
    pleaEnTheN: 'Please enter the name of the bank card account holder',
    withdraw: 'Withdrawal',
    storeName: 'Shop name',
    withdraBala: 'Withdrawable balance',
    withdraBalaless: 'Withdrawal amount cannot be less than 1.00 yuan',
    withdr: 'This withdrawal',
    pleEnWithAmo: 'Please enter the amount of withdrawal',
    withdrawAll: 'All withdrawals',
    unToTime: 'Can withdraw cash The amount is less than 1 yuan, this time cannot withdraw cash',
    debitCard: 'Deposit Bank Card',
    defaultBankCard: 'Default Bank Card',
    cannotBankCard: 'Default bank card cannot be deleted',
    setAsDefault: 'Set as Default',
    noData: 'No data available',
    smsVerification: 'SMS Verification',
    getVerificationCode: 'Get Verification Code',
    verifySMS: 'Verification SMS will be sent to the merchant\'s receiving notification cell phone number',
    maximumInput: 'Can enter up to 60 characters, special characters will be filtered',
    pleaseCheck: 'Please check',
    withdrawaEmpty: 'Withdrawable balance cannot be empty',
    pleaseEnteCode: 'Please enter the verification code',
    pleSelTheBankCard: 'Please select the bank card to the account',
    title: 'Title ',
    content: 'Content',
    announcementTitle: 'Notice Title',
    titCanNoBlank: 'Title cannot be empty',
    cityCannotBeEmpty: 'City cannot be empty',
    provinceCannotBeEmpty: 'Province cannot be empty',
    districtCounEmpty: 'District/County cannot be empty',
    newShipAdd: 'Add shipping address',
    modifyShipAdd: ' Modify the delivery address',
    consigneeName: 'Consignee name',
    companyLandline: 'Company landline',
    coneeNameCanEmpty: 'Consignee name cannot be empty',
    addressCannotBeEmpty: 'Address cannot be empty',
    pleeNormaeNumF: 'Please enter normal mobile phone number format',
    pleaseInputNumber: 'Please enter the number in the correct format',
    sameCityDelFun: 'Same city delivery function',
    deliSerProByYou: 'After enabling, buyers can choose same city delivery when placing orders, and you will provide door-to-door delivery services. ',
    shopAdress: 'Store address',
    startingFeeY: 'Start delivery fee (RMB)',
    startingFee: 'Start delivery fee',
    phone: 'Phone number',
    charges: 'Charge standard',
    chaDelFeeByReg: 'Delivery fee charged by area',
    delFeeChaDis: 'Delivery fee charged by distance',
    chaFixDelFeeByRe: 'Delivery area The products within will not calculate the distance, and a fixed delivery fee will be charged according to the area. ',
    byWalkDis: 'Delivery charges are calculated based on straight line distance from the map. ',
    deliveryFee: 'Delivery fee',
    deliveryFeeY: 'Delivery fee (RMB)',
    costAllocation: 'Expense configuration',
    withinKm: 'Press within km',
    yuanToChaDelFee: 'Yuan to charge delivery fee every time it exceeds',
    incInDelFees: 'Delivery fee increases',
    storeLocation: 'Store location',
    renewalCharge: ' Additional weight charge',
    commodityWeight: 'Product weight',
    pleaseEnterTheWeight: 'Please enter the weight',
    noExtraCharge: 'Kg no extra charge, every time excess ',
    renewalFeeIncrease: 'Additional weight fee increase',
    stargFeeCannoEmp: 'Start delivery fee cannot be empty',
    delFeeCannoEmp: ' Delivery fee cannot be empty',
    costCannotBeEmpty: 'Fee cannot be empty',
    excDisFeeCanBeEm: 'Exceeding distance fee cannot be empty!',
    distanceCannotBeEmpty: 'Distance cannot be empty!',
    weightCannotBeEmpty: 'Weight cannot be empty!',
    myShop: 'My shop',
    storeStatus: 'Store status',
    nonactivated: 'Unopened',
    closed: 'Closed business',
    inOperation: 'Opening',
    bindMobilePhoneNumber: 'Binding phone number',
    shopAddress: 'Shop detailed address',
    shopDescription: 'Store description',
    shopLogo: 'Shop logo',
    businessLicense: 'Business license',
    frontOfIDCard: 'Front of ID Card',
    idCardReverse: 'Back of ID Card',
    submitChanges: 'Submit Modification',
    storeBalance: 'Shop Balance',
    availableStoreBalance: 'Available Shop Balance (RMB)',
    incAvaStoBal: 'After 15 days of confirmation of receipt by the user, the available store balance will increase and can be used for store withdrawals',
    pendingSettlement: 'Pending settlement amount(RMB)',
    pendingSettlementTip: 'After the user purchases the product and waits for the user to confirm receipt, it will become the available store balance',
    unusableBalanceYuan: 'Unavailable balance (RMB)',
    unusableBalanceYuanTip: 'When the mall applies for withdrawal, it will The balance will be frozen and the unusable balance will be increased',
    totalSettlementAmount: 'Total settlement amount (RMB)',
    totalSettlementAmountTip: 'All amounts settled 15 days after the user confirms receipt of the goods, when the user refunds, the total settlement amount will decrease',
    totalSettlementAmountTip2: 'All amounts settled 15 days after the user confirms receipt of the goods',
    recentTransactions: 'transaction records',
    withdrawalRecord: 'withdrawalRecord',
    withdrawalAmount: 'withdrawalAmount',
    withdrawalTime: 'withdrawalTime',
    withdrawalStatus: 'withdrawalStatus',
    qq: 'QQ',
    weChatNumber: 'WeChat ID',
    mailbox: 'Email',
    fax: 'Fax',
    pleaseWriteInfo: 'Please fill in the template information',
    chooseDeliveryArea: 'Select delivery area',
    templateNameCannotBeEmpty: 'Template name cannot be empty',
    pleaseSelectADeliveryArea: 'Please select a delivery area',
    addAreaAndShipping: 'Add a delivery area and freight',
    freeShippingOnSpecifiedConditions: 'Designated shipping conditions',
    pleaseSelectTheDesignatedShippingCity: 'Please select a designated shipping city',
    clickToAddTheSpecifiedShippingConditions: 'Add designated shipping conditions',
    setRegionalConditions: 'Please set up shipping conditions in a designated area',
    changeAmountTip1: 'Due to the change of address, the shipping cost should be ',
    undercharged: 'undercharged ',
    overcharged: 'overcharged ',
    by: 'by ',
    yuan: ' yuan',
    changeAmountTip2: ', this operation will not change the order amount, is it confirmed to change the order address directly?',
    setArea: 'Please set the deliverable area',
    PlatformOff: 'Platform offline',
    reapplyReason: 'Please enter a reason for your application in 200 words or less',
    applicationHistory: 'Application History',
    applicationTips: 'Please fill in the reason for application',
    submit: 'Confirmation of submission',
    unbundlingQuestions: 'Whether to unbind the corporate account?',
    UnbindingSuccessful: 'Unbinding successful'

  },
  admin: {
    carouselImg: 'Carousel pictures',
    recommImgSize: 'Carousel pictures',
    carouselImgNoNull: 'Carousel picture cannot be empty',
    sortNONull: 'Sort value cannot be empty',
    publicMsg: 'Sort value cannot be empty',
    cancelPublic: 'Unpublish',
    seleData: 'Select date',
    msgConten: 'Message content',
    msgReply: 'Leave a feedback',
    msgRemSet: 'Message reminder settings',
    msgSet: 'SMS settings',
    msgType: 'Message type',
    notifyConten: 'Notification content',
    notifyType: 'Notification type',
    msgNotify: 'Notification type(billing)',
    msgFreeNotify: 'SMS notification (free)',
    msgNotifyFree: 'SMS notification',
    publicNotify: 'Official account notification',
    appletNotify: 'Official account notification',
    appletMessage: 'Stores News',
    seleMsgType: 'Please select the message type',
    notifyContenNoNull: 'The notification content cannot be empty',
    notifyTempNoNull: 'SMS template code cannot be empty',
    orderPay: 'Order payment',
    paySuccNotify: 'Payment successful notification',
    merAgreeRefund: 'The merchant has processed',
    merAgreeReturn: 'The merchant agreed to return the goods',
    merRefuseRefund: 'Seller refuses refund',
    wriOffRem: 'Write-off reminder',
    shipRem: 'Delivery reminder',
    groupFailRem: 'Reminder of failed group join',
    groupSuccRem: 'Reminder of successful joining',
    startGroupRem: 'Reminder to join the group',
    memUpRem: 'Member upgrade reminder',
    timeoutRem: 'Refund approaching timeout reminder',
    confirmReceRem: 'Confirm receipt reminder',
    buyerRefundRem: 'Buyer initiates refund reminder',
    buyerReturnRem: 'Buyer has returned reminder',
    buyerPaySuccessRem: 'User payment success reminder',
    mallTranRem: 'Mall transaction reminder',
    sendMer: 'Send to merchant',
    free: 'Free',
    isDeleOper: 'Are you sure to delete',
    smsRecord: 'SMS recharge record',
    rechargeNum: 'Recharge number',
    amountSpent: 'Amount spent (￥)',
    rechargeSource: 'Recharge source',
    rechargeTime: 'Recharge time',
    unpay: 'unpay',
    weChatPay: 'WeChat Pay',
    aliPay: 'Pay with Ali-Pay',
    balancePay: 'Balance Pay',
    payPal: 'PayPal Pay',
    selePaySetMeal: 'Please choose package purchase',
    proFeat: 'Product Features',
    eveMsm: 'Every message ￥',
    num: 'number',
    weCharBuy: 'WeChat purchase',
    weCharScanPay: 'WeChat scan payment',
    aliBuy: 'Alipay purchase',
    payCom: 'Completion',
    amoAndMon: '￥／24 months',
    commLevelReward: 'Commission level reward',
    signAmoRange: 'Check-in amount range',
    redProv: 'Red envelope distribution',
    minWithAmo: 'Minimum withdrawal amount',
    add: 'Add',
    remove: 'Remove',
    dollar: '￥',
    autoReview: 'Auto review',
    manReview: 'Manual review',
    maxLvNoNull: 'Maximum level cannot be empty',
    minLvNoNull: 'The minimum level cannot be less than 0',
    minNoMax: 'The minimum level cannot be greater than the maximum',
    commNoLZero: 'The commission ratio cannot be less than 0',
    remaSms: 'Remaining SMS',
    expiredSms: 'Expired SMS',
    refresh: 'Refresh',
    newConstruction: 'New Construction',
    smsRecharge: 'SMS recharge',
    rechargeRecord: 'Recharge record',
    modiReceNum: 'Modify the receiving number',
    smsSendRecord: 'SMS sending record',
    sendTime: 'Send time',
    smsContent: 'Text message content',
    senduserName: 'Send user nickname',
    sendPhoNum: 'Send phone number',
    sendStatus: 'Send status',
    resetPwd: 'Reset Password',
    createAcc: 'Create an account',
    applyBus: 'Apply for business',
    modifyAcc: 'Modify account',
    accIsExist: 'This account already exists, please re-enter',
    twoPwdIncon: 'The two passwords entered are not consistent',
    stationAccNoNull: 'Self-Pickup account cannot be empty',
    stationImg: 'Pick up some pictures',
    phoNumber: 'Telephone number',
    areaCode: 'Area code',
    businTime: 'Business hours',
    businStartTime: 'Business start time',
    businEndTime: 'Business end time',
    seleTimeRange: 'Choose time range',
    timeInter: 'Time interval',
    morAndEve: 'Morning and evening',
    dempoint: '12:00 and 18:00 are the demarcation points',
    hour: 'Hour',
    halfHour: 'Half an hour',
    pickupTime: 'Pickup time',
    inputDayNum: 'Please enter the number of days',
    startPickup: 'Pick up after days, pick up starts',
    inPickupEndTime: 'Please enter the end time',
    endPickup: 'Delivery ends after days',
    pcd: 'Province City District',
    location: 'Position',
    stationLocal: 'Location of self-pickup point',
    seleProv: 'Please select province',
    seleCity: 'Please select city',
    seleDC: 'Please select district/county',
    seleBusTime: 'Please select business hours',
    stationA: 'The self-pickup time can only be an integer greater than or equal to 0',
    inStatrPupTime: 'Please enter the start time',
    startTimeis: 'The starting time for self-reporting can only be',
    endTimeis: 'The end time of self mention can only be',
    pickUpTimeError: 'The pick-up start time cannot be later than the pick-up end time',
    stationNameNoNull: 'Pickup point name cannot be empty',
    addrNoNull: 'Address cannot be empty',
    number: 'Address cannot be empty',
    numberNoNull: 'Mobile/phone number cannot be empty',
    onlyInNum: 'Can only be numbers',
    mapPosi: 'Map positioning, please click again after positioning',
    busiTimeErr: 'Business start time cannot be greater than or equal to business end time',
    selePCD: 'Please select the province',
    modifySmsReceNum: 'Modify SMS receiving number',
    noSet: 'Not set yet',
    oldReceNum: 'Old receiving number',
    newReceNum: 'New receiving number',
    newReceNumNoNull: 'The new receiving number cannot be empty',
    phoNumFormaErr: 'Phone number format is incorrect'
  },
  dataAnaly: {
    statisticsTime: 'Statistics Time',
    selectMonth: '  Select month',
    realTimeToday: 'Real time today  ',
    natureDay: ' Nature day ',
    naturalMoon: ' Natural moon ',
    nearly7Days: ' Nearly 7 days ',
    nearly30Days: ' Nearly 30 days ',
    keyInVolumeYes: ' Key indicators of card volume yesterday ',
    numberOfRe: ' Number of receipts ',
    microMallUsage: ' Micro mall usage ',
    cardVolumeTrendChart: ' Card volume trend chart ',
    detailedData: ' Detailed data ',
    date: ' Date ',
    ovelProduvervi: ' Overall product overview ',
    orderTip1: 'Order-related indicators (such as the number of orders) are counted on the time of the following order, and payment-related indicators (such as the number of payers) are counted as payment success time statistics (group orders are counted and paid in groups). The order inquiry page queries the corresponding order according to the time when the order is placed.',
    orderTip2: 'If the customer places an order on the same day and pays the next day, there will be a one-day time difference between the order and the payment data, please pay attention to the distinction.',
    commodityTrendAnalysis: 'Commodity trend analysis  ',
    commodityOverview: 'Commodity Overview  ',
    numberOfNewProducts: ' Number of new products ',
    numberOfProductsVisited: ' Number of products visited ',
    numberOfProductsForSale: ' Number of products for sale ',
    commodityFlow: ' Commodity flow ',
    commodityExposure: ' Commodity exposure ',
    shareVisits: 'Share Visit Num',
    productViews: ' Product views ',
    commodityVisitors: ' Commodity visitors ',
    commodityConversion: ' Commodity conversion ',
    numberOfCases: 'Number of cases ',
    orderNumber: 'Order number ',
    numberOfPayment: 'Number of payment ',
    mostChoices: ' Most choices ',
    item: ' item ',
    chosen: ' chosen ',
    selectTheIndicatorToDisplay: ' Select the indicator to display ',
    commodityRanking: 'Product ranking',
    paymentAmountTOP: 'Payment amount TOP',
    paymentAmount: 'Payment amount (￥)',
    numberOfVisitorsTOP: 'Number of visitors TOP',
    numberOfVisitors: 'Number of visitors',
    visitToPayConversionRate: 'Visit-payment conversion rate',
    productEffect: 'Product effect',
    commodityInsight: 'Commodity insight',
    pleasToSearch: 'Please enter the product name to search',
    grouping: 'Group',
    activityArea: 'Activity area',
    groupAll: 'All groups',
    hiddenFromList: 'Hide in the list',
    allStatus: 'All status',
    onSale: 'Sale',
    inTheWarehouse: 'Warehouse',
    soldOut: 'Sold out',
    salesIndex: ' Sales indicators:',
    impressions: 'Number of exposures',
    exposure: 'Number of exposures',
    pageviews: 'Number of views',
    numberOfAdditionalPurchases: 'Number of additional purchases',
    payers: 'Number of payers',
    singleProductConversionRate: 'Single product conversion rate',
    numberOfGoodsPaid: ' Payment of the number of items',
    placeOrderPerson: 'Number of orders placed',
    placeOrderNum: 'Quantity of goods ordered',
    placeOrderAmount: 'Place order amount',
    commodityPaymentAmount: 'Product payment amount',
    serviceIndex: 'Service indicators:',
    numberOfOrdersRequestedForRefund: 'Number of orders for refunds',
    numberOfPeopleApplyingForRefund: 'Number of refunds requested',
    numberOfSuccessfullyRefundedOrders: 'Number of orders successfully refunded',
    numberOfSuccessfulRefunds: 'Number of successful refunds',
    successfulRefundAmount: 'Successfully refunded Payment amount',
    refundRate: 'Refund rate',
    indicators: 'Indices',
    chooseUpTo8Items: 'Select up to 8 items',
    orderTip3: '1. Order-related indicators (the number of people in the following order) are counted when the order is placed, and the payment-related indicators (such as the number of people who pay) are counted based on the time of successful payment (group orders are calculated as payment). The order query page queries the corresponding order according to the order time.',
    orderTip4: 'If the customer places an order on the same day and pays the next day, there will be a one-day time difference between the order and the payment data, please pay attention to the distinction.',
    orderTip5: '2. When selecting natural week, natural month, the past 7 days, and the past 30 days, the number of people will be removed during the selected time period.',
    prodHid: ' Products without data have been hidden',
    prodTip1: 'During the statistical time, the number of deduplicated number of visits to the product detail page is counted as one person who visits multiple times within the statistical time range. Note that if the customer browses the product list and directly purchases or places an order without entering the business details page, the number of visitors to the product will not be counted. At this time, the number of visitors to the product will be less than the number of people paying',
    trendAnalysis: 'Trend Graph Analysis',
    naturalDailyTrendChart: 'Natural Daily Trend Graph',
    numberOfPayments: 'Number of Payments',
    numberOfPaymentProducts: 'Number of Payment Products',
    couponAnalysis: 'Card and Coupon Analysis',
    transactionIndicator: 'Transaction Index',
    numberOfTransactionMembers: 'Number of Dealing Members',
    proportionOfTradingMembers: 'Proportion of Dealing Members',
    proportionOfPaymentAmount: 'Payment Proportion of amount',
    perCapitaConsumptionFrequency: 'Per capita consumption frequency',
    customerType: 'Customer type',
    addProdDescribe: 'Count the quantity of goods released within the time',
    prodTip2: 'The number of products whose product detail page views are greater than 0 during the statistical period',
    timeTip0: 'The number of products whose sales are not 0 during the statistical period',
    // timeTip1: ' During the statistical period, the sum of the number of impressions of all products in the store on the store homepage, list page, product group page, micro page, search result page, and the associated product area at the bottom of the product page. (Directly entering the detail page or micropage to link to the product through the picture will not be counted.',
    timeTip1: 'During the statistical period, the sum of the number of visits to the product through the link shared by other users.',
    timeTip2: 'The number of times that all product detail pages have been visited during the statistical period. Multiple visits by a person during the statistical period are counted as multiple times',
    timeTip3: 'Statistics During the period, the number of people who visit any product detail page, a person who visits multiple times within the statistical time range is only counted as one',
    timeTip4: 'The number of items added to the shopping cart during the statistical period',
    timeTip5: 'Successfully placed an order during the statistical period The sum of the number of merchandise (not excluding refund orders)',
    timeTip6: 'The sum of the number of merchandises for successfully paid orders during the statistical period (not excluding refund orders)',
    days7Before: '7 days ago ',
    days30Before: '30 days ago ',
    fromThePreviousDay: 'Compared to the previous day',
    monthBefore: 'Compared to the previous month'
  },
  user: {
    averageDiscount: 'Average discount',
    actuallypaid: 'Amount actually paid',
    consumptionAmount: 'Consumption amount (yuan)',
    consumptionTimes: 'Consumption times',
    totalOrderTimes: 'total order times',
    refundTimes: 'Refund times',
    clientExport: 'Export client',
    importClientMode: 'Import the customer information template.xlsx',
    clientImport: 'Import client',
    edit: 'Edit',
    userInfo: 'User Information',
    tradeDetails: 'Trade details',
    payTime: 'Payment time',
    prodTotalPrice: 'All commodity prices',
    orderFreight: 'Order freight',
    preferentialAmount: 'Preferential amount',
    payScore: 'Pay score',
    confirm: 'Confirm',
    sendCoupons: 'Send coupons',
    selectCoupons: 'Select coupons',
    couponTip1: 'Please enter the coupon name to search',
    couponTip2: 'No more than the limit per person, not more than the remaining inventory',
    couponTip3: 'Does not count user manual deletion and the part of expired coupons deleted by the system over 30 days',
    cancel: 'Cancel',
    stockNumber: 'Stock Number',
    couponUpperLimit: 'Coupon upper limit',
    perRecevies: 'Give out Each person',
    success: 'Success',
    clientInformationForm: 'Customer Information sheet',
    noData: 'No Data',
    details: 'details',
    merchandiseManagement: 'Merchandise management',
    salesRecord: 'Sales record',
    walletManagement: 'Wallet management',
    walletFlow: 'Wallet flow',
    withdrawalRecords: 'Withdrawal records',
    withdrawalManagement: 'Withdrawal management',
    performanceStatistics: 'Performance statistics',
    promotionalEffect: 'Promotional effect',
    distributorManagement: 'Distributor management',
    distributorAudit: 'Distributor audit',
    relationshipSearch: 'Relationship search',
    blockingRecords: 'Blocking records',
    growthSetting: 'Growth value acquisition settings',
    growthSwitch: 'Growth value switch:',
    perPurchase: 'Per purchase',
    getOneGrowth: 'Gain one growth value',
    completedOrderGet: 'Get every order completed',
    growth: 'Growth value',
    growthLog: 'Growth log',
    pleaseEnterCorrectValue: 'Please enter the correct value',
    changeGrowthValue: 'Change growth value',
    affiliateBusinessId: 'Affiliate business id',
    consumptionGetGrowthCannotEmpty: 'The growth value obtained by the consumption amount cannot be empty',
    orderAcquisitionCannotEmpty: 'Order acquisition cannot be empty',
    levelPageSet: 'Level page display settings',
    memberBenefitsPageDisplaySet: 'Member benefits page display settings',
    displayContent: 'Display content',
    levelPageConfiguration: 'Level page display configuration',
    pointsSet: 'Points earning settings',
    pointSettings: 'Point settings',
    userConsumptionAmount: 'User consumption amount',
    RedeemableForPoints: '%Redeemable for points',
    CanBeOffsetWithPoints: '%Can be offset with points',
    pointSettingsTip: '(Points cannot be used to offset shipping costs)',
    pointsEarnOne: 'Points can be earned by signing in for one consecutive day:',
    pointsEarnTwo: 'Points can be earned by signing in for two consecutive day:',
    pointsEarnThree: 'Points can be earned by signing in for three consecutive day:',
    pointsEarnFour: 'Points can be earned by signing in for four consecutive day:',
    pointsEarnFive: 'Points can be earned by signing in for five consecutive day:',
    pointsEarnSix: 'Points can be earned by signing in for six consecutive day:',
    pointsEarnSeven: 'Points can be earned by signing in for seven consecutive day:',
    pointsForRegistration: 'Points for registration:',
    pointsSwitch: 'Shopping use points switch',
    earnOnePoint: ' can earn one point',
    pointsPurchase: 'When purchasing goods with points per',
    pointsDeducted: 'points can be deducted 1 yuan',
    pointsEarnLimit: 'Upper limit of points acquisition',
    platformPercent: 'Proportion of the whole platform',
    categoryPercent: 'Proportion of commodity categories',
    pointsUseLimit: 'Upper limit of points',
    getPercentLimit: 'Get percentage of upper limit of proportion',
    percent: 'percent',
    usePercentLimit: 'Percentage of upper limit used',
    pointsOfRegistrationCannotEmpty: 'Registration point acquisition cannot be empty',
    pointsOfShoppingCannotEmpty: 'Shopping points cannot be empty',
    shoppingPointsDeductedCannotEmpty: 'Shopping points deduction cannot be empty',
    usePointsCannotEmpty: 'The percentage of use points cannot be zero or empty',
    getPointsCannotEmpty: 'Get the upper limit percentage of points cannot be zero or empty',
    pointsExpirationSetting: 'Point expiration settings',
    expirationSwitch: 'Points expiration switch:',
    expiration: 'Points expiration time:',
    year: 'year',
    expirationDes: 'The longest expiration time is configured, that is, starting from the point acquisition, the points will be emptied at the end of the longest n-1 year.',
    expirationDes1: '(When set to 1, points will expire at the end of the year in which they were earned)',
    expirationConfiguration: 'Points expiration configuration',
    enterExpiration: 'Please enter expiration time',
    pointsDesSetting: 'Points description settings',
    pointsDes: 'Points description',
    pointsEarnSetting: 'Points earning settings',
    growthEarnSetting: 'Growth value acquisition settings',
    growthValueObtainedFromOrderConfirmationOfReceipt: 'Growth value obtained from order confirmation of receipt',
    systemModificationOfUserGrowthValue: 'System modification of user growth value',
    growthValueObtainedFromUserTopUpBalance: 'Growth value obtained from user top-up balance',
    RefundOfGrowthValueForOrderRefund: 'Refund of growth value for order refund',
    otherConfiguration: 'Other configuration',
    growthValueQuestionSetting: 'Growth value FAQ settings',
    pointsQusetion: 'FAQ',
    ordinaryMember: 'Ordinary member',
    paidMembership: 'Paid membership',
    ordinaryMemberSet: 'Ordinary member settings',
    paidMembershipSet: 'Paid membership settings',
    generalConfiguration: 'General configuration',
    enterGradeName: 'Please enter the grade name',
    bgImg: 'Background picture',
    displayEquitybgImg: 'Equity display page background image',
    levelStyle: 'Level style',
    bronze: 'bronze',
    silver: 'silver',
    gold: 'gold',
    enterLevelStyle: 'Please select level style',
    pleaseSelectDisplayEquitybgImg: 'Please select the background of the Membership Benefits page',
    paidMemberPrice: 'Paid member price',
    enterInteger: 'Please enter an integer',
    enterPrice: 'Please enter the price',
    userInformationForm: 'User Information Form',
    timeType: 'Time type',
    maxTimeTip: 'The maximum may not exceed 10 years, and so on for other time types',
    levelConfig: 'Grade configuration',
    week: 'week',
    month: 'month',
    season: 'season',
    time: 'time',
    requiredGrowthValue: 'Required growth value',
    requiredGrowthValueMaximum: 'Required growth value can not large than the value of next level',
    requiredGrowthValueMinimum: 'Required growth value can not less than the value of pre level',
    equityAllocation: 'Equity allocation',
    selfFreeShipping: 'Free shipping from self-operated store',
    pointFeedbackRate: 'Point feedback rate',
    enterMagnification: 'Please enter the magnification',
    coupons: 'coupons',
    etcCoupons: 'Etc. coupons',
    bonusPoints: 'Bonus points',
    discount: 'Discount',
    discountRange: 'Please enter discount range',
    platform: 'platform',
    selfShop: 'Self-operated shop',
    allProducts: 'all products',
    categoryGoods: 'Commodities under category',
    otherRights: 'Other rights',
    bgImgCannotEmpty: 'Background image cannot be empty',
    rightBgImgCannotEmpty: 'The background image of the membership benefits page cannot be empty',
    selectCategory: 'Please select a category',
    gradeNmaeCannotEmpty: 'Grade name cannot be empty',
    feedback1: 'The integral feedback rate must be greater than 1',
    bonus0: 'Bonus points must be greater than 0',
    discountRangeValue: 'The value range of discount is 0.01-9.99',
    addLevel: 'Add level',
    updateUserData: 'Update user data',
    addLevelTip: 'If the growth value of the highest level member equals or exceeds 1,000,000,000, it will not be able to continue to add',
    updateSoon: 'After the user level is updated, it is only valid for new users, and it is not valid for old users. Please update as soon as possible',
    growthRange: 'Level growth value range',
    memberDiscount: 'Member discount',
    someProducts: 'Some products',
    remove: 'Remove',
    atMost: 'You can only add 50 levels at most',
    saveFirst: 'Save the previous level before you can create a new level',
    membershipLevel: 'membership level',
    regularMembershipLevel: 'regular membership level',
    payingMembershipLevel: 'paying membership level',
    level: 'Level',
    levelName: 'Level name',
    noPic: 'No picture',
    needGrowth: 'Growth value required to purchase VIP',
    within: 'Within',
    vipLevel: 'VIP level',
    memberType: 'Membership type',
    userScore: 'User points',
    rightsName: 'Rights name',
    rightsType: 'Rights type',
    rightsIcon: 'Rights icon',
    rightsDescription: 'Rights description',
    sysRights: 'System rights',
    selfRights: 'Custom rights',
    source: 'source',
    order: 'order',
    levelUp: 'Level up',
    signIn: 'Sign in',
    allLabels: 'All labels',
    conditionLabel: 'Condition label',
    addLabel: 'Add Label',
    updateLabelData: 'Update label data',
    editLabel: 'Edit label',
    customConditionLabel: 'Custom condition label',
    customConditionLabelTips: 'Up to 20 can be added',
    peopleNum: 'Number of people',
    creationTime: 'Creation time',
    updateTime: 'Update time',
    updateBtn: 'Update',
    editBtn: 'Edit',
    deleteBtn: 'Delete',
    manualLabel: 'Manual label',
    labelName: 'Label name',
    labelType: 'Label type',
    conditionSetting: 'Condition setting',
    conditionSettingTips: 'When multiple conditions are selected, all must be met',
    basicCondition: 'basic condition',
    becomeCustomerTime: 'User registration time',
    pleaseSelect: 'Please select',
    startDate: 'Start date',
    endDate: 'End date',
    followTime: 'Time of concern',
    becomeMemberTime: 'Become a paid member time',
    transactionTerms: 'transaction terms',
    nearConsuTime: 'Latest consumption time',
    consuNum: 'Number of consumption',
    bout: 'bout',
    consuAmount: 'Amount of consumption',
    yuan: 'yuan',
    averPri: 'Average order price',
    rechargeConditions: 'Recharge conditions',
    rechargeAmount: 'Recharge amount',
    rechargeNumber: 'Number of top-ups',
    tradingConditions: 'Trading conditions',
    calcel: 'Cancel',
    preservation: 'preservation',
    today: 'today',
    yesterday: 'yesterday',
    lastSevenDay: 'Last 7 days',
    lastFifteenDay: 'Last 15 days',
    lastThirtyDay: 'Last 30 days',
    lastFortyFiveDay: 'Last 45 days',
    lastSixtyDay: 'Last 60 days',
    lastNinetyDay: 'Last 90 days',
    lastOneHundredEightyDay: 'Last 180 days',
    thisMonth: 'This month',
    lastMonth: 'Last month',
    customRange: 'Custom range',
    unlimited: 'Unlimited',
    updateSucceeded: 'Update succeeded',
    deletionSucceeded: 'Deletion succeeded',
    labelNameNullTips: 'The label name is null',
    becomeCustomerTimeNullTips: 'User registration time is null',
    followTimeNullTips: 'Time of concern is null',
    becomeMemberTimeNullTips: 'Become a paid member time is null',
    memberTime: 'Member time',
    consuNumNullTips: 'Number of consumption is null',
    consuAmountNullTips: 'Amount of consumption is null',
    averPriNullTips: 'Average order price is null',
    rechargeAmountNullTips: 'Recharge amount is null',
    rechargeNumberNullTips: 'Number of top-ups is null',
    requireOne: 'It requires one condition at least',
    enterRechargeAmount: 'Please enter the recharge amount',
    rewardAmount: 'Additional amount',
    enterRewardAmount: 'Please enter the reward amount',
    reward: 'Reward',
    rewardScore: 'Reward score',
    enterRewardScore: 'Please enter the reward score',
    rewardGrowthValue: 'Reward growth value',
    enterRewardGrowthValue: 'Please enter the reward growth value',
    rewardCoupon: 'Reward coupon',
    updateGrowth: 'Modify growth value',
    tagging: 'Tagging',
    updateScore: 'Modify integral',
    customerLabel: 'customer\'s tag',
    memberTags: 'member\'s tag',
    export: 'export',
    registrationTime: 'Registration time',
    openingTime: 'Opening time',
    lastConsumptionTime: 'Last consumption time',
    modifyBalance: 'Modify balance',
    rechargeTimes: 'Recharge times',
    cumulativeScore: 'Cumulative score',
    currentBalance: 'Current balance',
    cumulativeBalances: 'cumulative balances',
    tagTip1: 'Please enter tag name',
    growthTip1: 'Positive number means increase, negative number means decrease, only integer can be entered, and membership level is recalculated according to the modified growth value',
    growthTip2: 'Growth value quantity',
    growthTip3: 'Can only be positive or negative integers',
    growthTip4: 'Growth value quantity cannot be empty',
    updateUserScore: 'Modify user score',
    scoresChange: 'scores (increase or decrease)',
    scoreTip1: 'Positive number means increase, negative number means decrease, only integers can be entered',
    scoreTip2: 'Change value cannot be empty',
    couponDscription: 'Coupon description',
    stockNum: 'Inventory quantity',
    balanceUpdate: 'Modify user balance',
    balanceRecharge: 'Balance recharge',
    changeQuantity: 'Change quantity',
    balanceTip1: 'Positive number means increase, negative number means decrease, only numbers can be entered, up to two decimal places',
    balanceTip2: 'Modified quantity cannot be empty',
    balanceTip3: 'Please enter a value that is not equal to 0 or keep two decimal places',
    balanceTip4: 'Batch to users to recharge the balance, only batch to users to increase the balance, please enter the number greater than zero',
    balanceTip5: 'Please enter a value greater than 0 and retain up to 2 decimal places',
    balanceTip6: 'The user currently has a balance of ',
    balanceTip7: 'The maximum user balance cannot exceed ********.99 and the minimum cannot be less than 0',
    balanceTip8: 'If the user balance plus the modified balance is greater than the maximum value, the maximum value is taken as the user balance',
    accountAssets: 'Account assets',
    accountWallet: 'Account Wallet',
    currentScore: 'Current score',
    notUsed: 'not used',
    used: 'used',
    invalid: 'Invalid',
    otherInfo: 'Other information',
    distributionInfo: 'Distribution information',
    distributionTime: 'Time to become a distributor',
    scoreDetails: 'Score details',
    balanceDetails: 'Balance details',
    couponDetails: 'Coupon details',
    scorePay: 'Points payment',
    scoreOrder: 'Score order',
    changeTime: 'Change time',
    changeReason: 'Reason for change',
    registerScore: 'Register to get points',
    lockScore: '(Points locked in)',
    shopping: 'shopping',
    shoppingDeducteScore: 'Shopping deduction score',
    scoreExpired: 'Score expired',
    rechargeBalance: 'Recharge your balance',
    sysChangeScore: 'System change score',
    changeScoreNum: 'Number of change score',
    changeType: 'Type of change',
    recharge: 'recharge',
    payment: 'payment',
    refund: 'refund',
    platformChange: 'Platform manual modification',
    rechargeMember: 'recharge member',
    changeBalance: 'Change balance',
    getCouponTime: 'Time to get coupons',
    effectiveTime: 'Effective time',
    couponStatus: 'Coupon status',
    userRules: 'Rules of Use',
    discountMsg: 'There is a FOLD discount for ￥PRICE',
    tipError2: 'Select up to NUM Tags',
    userImport: 'Import user',
    refundRetrunScore: 'Refund order\'s used score',
    integerGreaterThanZero: 'Please enter an integer greater than 0',
    add: 'Add',
    reset: 'Reset',
    editInfo: 'Modify user information',
    invitees: 'Inviter',
    status: 'Status',
    ban: 'Temporary ban',
    normal: 'Normal',
    cleared: 'Cleared',
    sendCouponTips: "Tip: When a coupon is issued, if the stock is insufficient or reaches the user's coupon limit, the user cannot receive the coupon.",
    succeeded: 'Succeeded operation',
    selectTag: 'Select label',
    selectTagError: 'Please Select a tag',
    disableRemark: 'Reason for disabling',
    disableRemarkNoNull: 'Disable reason cannot be empty',
    grade: 'Grade',
    save: 'Save',
    imgsTip: 'Suggest size',
    px: 'px',
    rightsInterests: 'User rights interests',
    couponNumber: 'Coupon number',
    recruitmentSituation: 'Recruitment situation',
    stopRecruitment: 'Stop recruitment',
    beginRecruitment: 'Begin recruitment',
    freeShipping: 'Free shipping',
    isDeleOper: 'Are you sure to delete',
    buyLevel: 'Buy membership',
    buyTimes: 'buy times',
    payAmount: 'Pay amount',
    revise: 'revise',
    delete: 'Delete',
    isBeginRecruitment: 'Are you sure to continue recruiting "',
    isStopRecruitment: 'Continue to confirm the cessation of recruitment of "',
    isBeginRecruitment2: '" members',
    isDelete: 'Are you sure to delete?',
    discountVoucher: 'Discount Voucher',
    coinCertificate: 'Coin Certificate',
    custom: 'custom',
    suspendRecruitment: 'Suspend recruitment',
    recruiting: 'Recruiting',
    importMemberMode: 'Import the membership template.xlsx',
    isMember: 'Is member',
    userTag: 'Member Tags',
    userTagDefaultTxt: 'Please select the membership tab',
    levelNameCn: 'Chinese Level Name',
    levelNameEn: 'English Level Name',
    enterGradeNameCn: 'Please enter the Chinese grade name',
    enterGradeNameEn: 'Please enter the English level name',
    gradeNmaeCannotEmptyCn: 'Chinese grade name cannot be empty',
    noUpdate: 'No update',
    successfullyFileImport: 'This file was imported successfully',
    failedFileImport: 'This file failed to be imported'
  },
  userRights: {
    customBenefits: 'Custom benefits',
    rightsNameCn: 'Chinese Rights name',
    rightsNameEn: 'English Rights name',
    enterContentCn: 'Please enter the content, no more than five characters in length',
    enterContentEn: 'Please enter the content, not more than twenty characters in length',
    rightsIntroduceCn: 'Chinese rights introduce',
    rightsIntroduceEn: 'English rights introduce',
    nameCanNotEmptyCn: 'Chinese Rights name cannot be blank',
    intrCanNotEmptyCn: 'Chinese Rights introduction cannot be blank',
    systemRights: 'System rights',
    rightsName: 'Rights name',
    enterContent: 'Please enter the content, no more than five characters in length',
    rightsIcon: 'Rights icon',
    rightsIntroduce: 'Rights introduce',
    nameCanNotEmpty: 'Rights name cannot be blank',
    iconCanNotEmpty: 'The rights icon cannot be empty',
    intrCanNotEmpty: 'Rights introduction cannot be blank',
    serialNumber: 'Serial number',
    sequence: 'sequence',
    errorIntegerTip1: 'Please enter the integer'
  },
  formData: {
    export: 'Export',
    exportIng: 'In the export',
    shop: 'Shop',
    stockLimit: 'Please enter an integer lower than 2147483600',
    natureDay: 'Natural day',
    natureWeek: 'Natural week',
    naturalMoon: 'Natural month',
    customTime: 'Custom time',
    specifyTimeRange: 'Specified time range',
    near: 'Near',
    reportName: 'Report name',
    pleaseEnterTheReportName: 'Please enter report name',
    typeOfData: 'Data type',
    timePeriod: 'Time period',
    accordingToTheSelectedType: 'According to the selected type, generate a report corresponding to the time period',
    timeFormat: 'Time format',
    timeLimit: 'Time range',
    pleaseEnterTheTime: 'Please enter the time',
    day: 'Day',
    date: 'Day',
    daterange: 'Date range',
    monthrange: 'Month range',
    week: 'Week',
    month: 'Month',
    serialNumber: 'Serial number',
    selectIndicator: 'Select indicator',
    lastWeek: 'Last week',
    lastMonth: 'Last month',
    lastThreeMonths: 'Last three months',
    theReportNameCannotBeEmpty: 'Report name cannot be empty',
    startTimeCannotBeEmpty: 'Start time cannot be empty ',
    endTimeCannotBeEmpty: 'End time cannot be empty',
    timeRangeCannotBeEmpty: 'Time range cannot be empty',
    timeCannotBeEmpty: 'Time cannot be empty',
    pleaseSelectAnIndicator: 'Please select an indicator',
    timeTip: 'If you select the last 1 day, the report will automatically update to the current day’s data, and in the last 2 days, the report will automatically update to the current day + yesterday’s data',
    pleaseThan0: 'Please enter an integer greater than 0',
    numberOfIndicators: 'Number of Indicators',
    reportIndicators: 'Report Indicators',
    addMyReport: 'Add My Report',
    natural: 'Natural',
    noTimeZero: 'Time range cannot be 0'
  },
  groups: {
    tip10: 'The goods after group purchase are released cannot be replaced. Please select the correct goods',
    selectProd: 'Select products',
    newJoinGroupActivity: 'New group event',
    GroupActivityInfo: 'Grouping Activity Details',
    startUsing: 'Enable',
    editEvent: 'Edit',
    sku: 'spec',
    manageEventProducts: 'Manage products',
    invalidActivity: 'Invalid',
    applyForListing: 'Apply on shelve',
    ifNotDealtWith: 'If not processed, the products of the group event will not be reopened',
    groupOfPeople: 'Group',
    notEnabled: 'Not enabled',
    notOpen: 'Not open',
    hasNotStarted: 'Not started',
    processing: 'In progress',
    over: 'Ended',
    expired: 'Invalidated',
    offlineViolation: 'Offline violation',
    moderated: 'Awaiting review',
    confirmDelete: 'Are you sure to delete 【',
    active: '】Activity? ',
    determinedToFail: 'Are you sure to invalidate【',
    actTip: '】Activity? After the expiration, if the event is activated to simulate a group, the group order in the group joining still needs to be processed. New group joining activity',
    editGroupActivities: 'Edit group activities',
    eventName: ' Activity name',
    enterEventName: 'Enter event name',
    relatedProducts: 'Related products',
    edit: 'Edit',
    activityStartTime: 'Activity start time',
    selectEventStartTime: 'Select event start time',
    eventEndTime: 'Activity end time',
    deliveryTime: 'The delivery time cannot be earlier the current time',
    selectEventEndTime: 'Select event end time',
    numberOfParticipants: 'Number of participants in the group',
    people: 'People',
    groupValidityPeriod: 'Group validity period',
    minute: 'Minute',
    limitPurchase: 'Limit purchase',

    turnOn: 'Turn on',
    limitedPurchaseQuantity: 'Limited purchase quantity',
    simulation: 'Simulation',
    groupLeaderOffer: 'Group Leader Offer',
    groupMode: 'Group mode',
    activityWarmUp: 'Activity preheating',

    cannotrOpen: 'After opening, the product detail page display has not started Group joining activities, but users cannot join the group purchase before the activity starts',
    submit: 'Submit',
    submitAndProducts: 'Submit and manage event products',
    activityTimeTime: 'Activity time cannot be less than current time',
    startTime: 'The activity start time cannot be less than the current time',
    alterActivityTime: 'The start time cannot be less than current time as you have altered the activity',
    endTime: 'The end time cannot be earlier than current time',
    endTimeIsTooSmall: 'The start time cannot be greater than or equal to the end time',
    extendedEndTime: ' The end time can only be extended and cannot be less than the original set value',
    pleaseEnterEventName: 'Please enter the activity name',
    pleaseEnterEventNum: 'Please input the limited quantity',
    pleaseEnteNumGrp: 'Please enter the number of people in the group',
    pleaseEnterTimeGroup: 'Please enter the effective time of the group',
    startTimeCannotBeEmpty: 'Start time cannot be empty',
    endTimeCannotBeEmpty: 'End time cannot be empty',
    failureTip: '"Invalid active goods" will end immediately and can no longer be edited. Non grouped orders will be automatically closed and refunded, and grouped orders still need to be processed in time. The expired product activity can be deleted.',

    invalidate: '"Invalidate" means that it ends immediately and cannot be edited again. Orders that are not grouped will be automatically closed and refunded. Orders that have been grouped still need to be processed in time. Commodity activities after the expiration can be deleted.',
    tip1: 'The number of people in the group cannot be less than 2',
    groupNumberLimit: 'The number of people in the group cannot be more than 1000000000',
    tip2: 'The effective time of group formation cannot be less than 15 minutes',
    tip3: 'The effective time of the group cannot be more than one day (1440 minutes)',
    tip4: 'If you set 30 minutes, the user needs to form a group within 30 minutes after opening a group, and the group will fail if overtime',
    memberPeople: 'Member/people',
    tip5: 'After enabling the simulation to form a group, if the group is not full within the validity period, the system will use "virtual users" to make up the full group to make the group successful. You only need to ship to real buyers who have paid to join the group. It is recommended to open it reasonably to increase the grouping rate.',
    tip6: 'After the group leader (group organizer) discount is enabled, the group leader will enjoy a more favorable price, which will help increase the group opening rate and the group formation rate.',
    tip7: 'Note: When the "Simulated group" is turned on, the group leader can also enjoy the group leader discount, please set it carefully to avoid capital loss.',
    tip8: 'After opening the group, for buyers who have not joined the group, the event product details page will display a list of groups that have not been grouped. Buyers can directly choose one to join the group to increase the group rate.',
    tip9: 'After opening, the product details page and preferential group purchase list and so on will show the upcoming group activity, but before the activity starts, users cannot make group purchase.',
    groupStatus: 'Group status (0: pending, 1: successful, 2: faled)',

    returnToJoinGroupActivities: 'Return to join group activity',
    newEventProduct: 'New activity product',
    activitySpecifications: 'Activity specification',
    expiredGoods: 'Invalidated product',
    determine: 'OK',
    cancel: 'Cancel',
    successfulOperation: ' Successful operation',
    isSuer: 'Are you suer【',
    isDele: '】active products deleted?',

    activityId: 'Activity id',
    proId: 'Product id',
    commSpenMan: 'Commodity specification management',
    costOfProduction: 'Original price',
    headPrice: 'Group leader price',
    stock: 'Inventory',
    groupPrice: 'Group price',
    skuName: 'Sku name',
    groupPriceYuan: 'Group price(￥)',
    headPriceYuan: 'Group leader price(￥)',
    enterPrice: 'Enter price',
    batchSettings: 'Bulk setting',

    activityPriceMus: 'Activity price must be greater than 0 yuan',
    theLeaderPriceMu: 'Group leader price must be greater than 0 yuan',
    canTPlateauPrice: 'Activity price cannot be higher than the original price of the product',
    notTPY: 'Group leader price cannot be high In the original price of the product',
    notTAY: 'The leader price cannot be higher than the activity price',
    pleaseSetTGP: 'Please set the group price in batches first, or cancel the batch setting operation',
    pleaseSetFirst: 'Please set the team leader price in batches first, or cancel the batch setting operation',
    groupPriceMusThan0: 'Fight The group price must be greater than 0',
    viewGroupOrders: 'View orders in the same group',
    viewGroupActivityInfo: 'View the details of the activity',
    groupOrderList: 'List of orders in the same group',
    actProductAmount: 'Active product amount',
    membership: 'Member status',
    head: 'Group leader',
    member: 'Member',
    robot: 'Robot',
    invalidatePrductPre: 'Are you sure to invalidate the event product',
    invalidatePrductSuf: '？'
  },
  marketing: {
    castTime: 'Effective time',
    modifyCoupon: 'Modify coupon',
    newCoupon: 'New coupon',
    couponName: 'Coupon name',
    couponSubtitle: 'Coupon subtitle',
    conditionsOfUse: 'Conditions of use',
    discountTip1: 'The discount benefits here can be customized',
    discountTip2: 'Other rights and benefits checked will be displayed in the list of user level rights and benefits, only for icon display without corresponding functions',
    fullConsumption: 'Consumption amount full',
    reductionAmount: 'Reduction amount',
    discountAmount: 'Discount limit',
    discountNoZero: 'The discount amount cannot be 0',
    pleaseEnterDiscount: 'Please enter the discount amount. Example: 9.6 is 9.6 fold',
    fold: 'Fold',
    effectiveTime: 'Effective time',
    fixedTime: 'Fixed time',
    effectiveAfterReceipt: 'Effective after receiving',
    chooseStartTime: 'Select starting time',
    chooseEndTime: 'Select ending time',
    afterReceivingTheCoupon: 'After receiving coupons ',
    effectiveDays: 'Valid in days',
    maxTimeTip: 'The maximum shall not exceed 10 years, i.e. 3652 days',
    validDate: 'Valid days',
    perPerson: 'Limited per person',
    piece: 'Piece',
    inventory: 'Inventory',
    activeInventory: 'Active Inventory',
    activeStationInventory: 'Activity store inventory',
    applicableGoods: 'Applicable products',
    allProdsPar: 'All products participate',
    participateInD: 'Specified products participate',
    specifiedProduct: 'Specified products do not participate',
    pleaseaTo0: 'Please enter an integer greater than or equal to 0',
    pleaseaTo1: 'Please enter an integer greater than or equal to 1',
    pleaseTo10: 'Please enter an integer within 10 or a positive number with two decimal places',
    timeCanThanOrEqualTo: 'Start time cannot be less than or equal to end time',
    couponNameCannotBeEmpty: 'Offer Coupon name cannot be empty',
    theDedEmpty: 'Reduction amount cannot be empty',
    theLimitetBeEmpty: 'Limited amount cannot be empty',
    collectioeEmpty: 'Days after receiving cannot be empty',
    effectiveEmpty: 'Valid days cannot be empty',
    theDiscouBeEmpty: 'Discount amount cannot be empty Example: 9.6 9.6 fold',
    conditionBeEmpty: 'Use conditions cannot be empty',
    effectiveotBeEmpty: 'Effective time cannot be empty',
    invenEmpty: 'Stock cannot be empty',
    applicabltBeEmpty: 'Applicable product type cannot be empty',
    quantitssThan0: 'Quantity cannot be less than 0',
    amounnCannotBe: 'Reduction amount cannot be greater than or Equal to the amount used',
    mobiltyDiagram: 'Mobile activity diagram',
    suggest1: 'Recommendation: Use dark pictures as much as possible for the mobile activity diagram',
    pcActivityListChart: 'PC activity list image',
    pcActiviroundMap: 'PC activity background image',
    activTime: 'Activity time',
    typeOfActivity: 'Activity type',
    fullMoneoney: 'Full money minus money',
    fullMoneyDiscount: 'Full money discount',
    typeOfExemption: 'Reduction type',
    decreaseOestLevel: 'One deduction according to the highest level of satisfaction',
    decreaeryTime: 'One deduction every time when full',
    offerContent: 'Offer content',
    activityLevel: 'Activity level',
    everyFull: 'Every full ',
    conditionsOfUseIn: 'The use conditions need to increase with the increase of the activity level',
    discountedPrice: 'Offer amount',
    reducea: 'Reduce',
    dozen: 'Dozen',
    pleaseThan0: 'Please enter a number greater than 0',
    pleaseEnterTheNumber010: 'Please enter a number greater than 0 and less than 10',
    discountTip: 'Spike items in the event are not eligible for the full discount',
    maximumDiscountAmount: 'Limit of discount amount',
    applicableProductType: 'Applicable product type',
    pleaseUploadAPicture: 'Please upload pictures',
    item: 'Pieces',
    theConditierThan: 'The use condition must be greater than',
    theOfferAmountThan: 'The Offer amount must be greater than',
    theOfferDiscountThan: 'The Offer discount must be less than',
    activimount: 'The activity condition amount must be greater than the preferential amount',
    activitytBeEmpty: 'The activity condition cannot be empty',
    promotioBeEmpty: 'The promotion amount of the activity cannot be empty',
    maximumDiscountAmountBig: 'The upper limit of the promotion amount must be greater than or equal to the minimum amount of the promotion',
    pleaseSelectAProduct: 'Please select a product',
    eventEndanStartTime: 'The end time of the event should be greater than the start time',
    subHeadings: 'Upper category',
    newDisProducts: 'New distribution products ',
    modifyDisnProducts: 'Modify Distributed Products',
    defaultReward: 'System Setup',
    proporteward: 'Proportional Reward',
    rewardByFixedValue: 'Reward based on a fixed value',
    notBasedOnGrade: 'Reward not based on level',
    rewardBasedOnLevel: 'Reward based on level',
    inviterReward: 'Interpush Bonus',
    amountSetting: 'Reward Settings',
    inviterRewardAmount: 'Interpush Bonus',
    name: 'Grade Name',
    rewardAmount: 'Direct Push Bonus',
    theProduList: 'The product is already in the distribution product list',
    valueSetBeEmpty: 'The value setting cannot be empty',
    valueSetting: 'The value setting can only enter an integer greater than 0 or two decimal places',
    couponTypeCannotBeEmpty: 'Coupon type cannot be empty',
    fullDiscount: 'Full discount',
    full: 'Full ',
    noUse: ' No use',
    use: 'Use',
    rewardRatio: 'Reward ratio',
    rewardSet: 'Reward Method',
    tips: 'The actual amount of payment for the product (excluding shipping costs) must be greater than the amount of the reward, otherwise the distribution is judged to be invalid',
    newDiscount: 'New full discount campaign',
    viewDiscount: 'Editorial Full Discount Event',
    totalAwardOverTips: ' should be less than 100%',
    maxActivityLevelsTips: 'Activity level up to 5 levels',
    stockReplenishment: 'Stock replenishment',
    stockReplenishmentTips: 'Inventory that is not finished in seconds after opening will be replenished to the corresponding inventory point'
  },
  seckill: {
    unlimited: 'Unlimited',
    makeSurateTheAct: 'Are you sure to invalidate the activity?',
    purcPerPerson: 'Limited purchase per person',
    cancelTheOrder: 'Order canceled',
    unpaidnutes: 'Orders not paid in minutes, order cancelled',
    warning: 'Warning: Please make reasonable arrangements for seckill products using independent inventory',
    price: 'Product sku original price (￥):',
    exisocks: 'Product sku inventory:',
    pleaheyTime: 'Please select event time',
    timeCanEmpty: 'Time cannot be empty',
    evenBeEmpty: 'Activity name cannot be empty',
    theOrionEmpty: 'Order cancellation time cannot be empty',
    commoBeEmpty: 'Product price cannot be empty',
    commodiBeEmpty: ' Commodity inventory cannot be empty',
    enableMustOne: 'Please enable at least one sku',
    pleaSenProc: 'Please select an active product',
    openPurchaseLimit: 'Open purchase limit',
    canBePurcPer: 'Can be purchased per person',
    newSeckill: 'New seconds added',
    viewSeckill: 'Check out the seconds',
    productHasBeenDeleted: 'Deleted Already',
    skuStockNotZero: 'The inventory of goods cannot be 0'
  },
  retailProd: {
    number: 'Number',
    pic: 'Pic',
    barCode: 'Single product barcode',
    name: 'Name',
    spec: 'Spec',
    categoryName: 'Category',
    costPrice: 'Cost Price',
    stocks: 'Stock',
    unit: 'Unit',
    supplier: 'supplier',
    chooseSupplier: 'Choose Supplier',
    export: 'Export',
    import: 'Import',
    chooseFile: 'Choose File',
    DownloadTemplate: 'Download Template',
    confirmImport: 'Confirm Import',
    batchDelete: 'Batch Delete',
    barCodePlaceholder: 'If there is no barcode, the system will automatically generate',
    barCodeAppend: 'Used to quickly identify the item',
    unitPlaceholder: 'Choose unit',
    tipError: 'Please enter an integer no less than 0',
    tip2Error: 'Please select at least one item',
    upLoadRetailProd: 'UpLoad prod',
    uploadRetailProdTip: 'If a supplier is selected when importing a single item, it will be stored in the supplier\'s product with a minimum purchase price of $0.01 and a minimum order quantity of 1.'
  },
  menuList: {
    prodManage: 'Product Manage',
    releaseGoods: 'Publish Products',
    itemManagement: 'Single Product Manage',
    classificationManage: 'Classify Manage',
    groupManage: 'Group management',
    commentManage: 'Comment Manage',
    specManage: 'Norm Manage',
    brandManage: 'Brand Manage',
    storeManage: 'Store Manage',
    announcementManage: 'Notify Manage',
    storeAddressManage: 'Store Address Manage',
    freightTemplate: 'Freight Template',
    carouselManage: 'Carousel Image Manage',
    newsPush: 'Message Push',
    hotSearchManage: 'Hot Search Manage',
    pickupPointManage: 'Stores Manage',
    cityDistributionManage: 'Same City Delivery',
    storeManagement: 'Store Setting',
    storeBalance: 'Store Balance',
    supplierManage: 'Supplier Manage',
    groupManagement: 'Group Manage',
    groupActivities: 'Group Activity',
    groupOrder: 'Group Orders',
    marketingManage: 'Marketing Manage',
    spikeManage: 'Seckill Manage',
    fullDiscount: 'Full Discount',
    couponManage: 'Coupon Manage',
    distrProManage: 'Distribu Product Manage',
    distrReco: 'Distribu Product Record',
    orderManage: 'Order Manage',
    refundManage: 'Refund Manage',
    dataReport: 'Data report',
    myReport: 'My report',
    recommendedReport: 'Recomm Report',
    dataAnalysis: 'Data Analysis',
    commodityOverview: ' Commodity Overview',
    commodityInsight: 'Commodity Insights',
    distributionManage: 'Distribu Manage',
    freTempl: 'Freight Template',
    liveBroadManage: 'Live Manage',
    liveRoomMerchand: 'Live Room Products',
    liveUser: 'member management',
    liveRoomManage: 'Live Room Manage',
    shopFeature: 'Shop feature'
  },
  components: {
    pictureManager: 'Picture Manager',
    selectImage: 'Select Picture',
    pictureName: 'Picture Name',
    uploadImage: 'Upload Picture',
    pleSeleLocImaUpload: 'Please select a local picture to upload',
    confirmUpload: 'OK to upload',
    originalName: 'Original name',
    editName: 'Modify name',
    pleEntNewPicName: 'Please enter a new picture Name',
    sizeCannotExceed: 'Size cannot exceed',
    imageExc300Px: 'Image width or height cannot exceed 300px',
    numHasReaLimit: 'The number of selectable photos has reached the upper limit',
    onlyUploadPictures: 'Only upload images, other files have been cleared',
    onlySupportsPictures: 'Only support jpg/png/gif images ',
    theSizeCannotExceed2M: 'Size cannot exceed 2M',
    already: 'Already',
    pictures: 'Pictures',
    willUploadSoon: 'Upload soon',
    youCanAlsoChoose: 'You can also choose',
    imageUpload: ' Picture uploads',
    theServerTookANap: 'Server took a nap^_^',
    youCanOnlySelectAtMost: 'Currently the most You can only choose',
    avaExc2MB: 'Upload avatar image size cannot exceed 2MB!',
    maxiNumPicture: 'Maximum number of pictures is',
    violationManagement: 'Management violation',
    handler: 'Processor',
    processingStatus: 'Processing status',
    platformOffline: 'Platform offline',
    failed: 'Fail',
    auditNotPassed: 'Approval failed',
    reasonForOffline: 'Offline reason',
    offlineTime: 'Offline time',
    reasonForApply: 'Appliy reason',
    applicationLog: 'Appliy log',
    applicationTime: 'Appliy time',
    reviewTime: 'Approval time',
    denialReason: 'Rejection reason',
    submitApplication: 'Submit application',
    retryAfterRefresh: 'Product is not under review, please refresh Try again later',
    stationRetryAfterRefresh: 'Station is not under review, please refresh Try again later',
    applicaEmpty: 'The reason for application cannot be empty',
    areYouSureAtion: 'Are you sure to submit the application? ',
    modifyReceivingInfo: 'Modify receiving information',
    consultWithUsers: 'Please negotiate with the user before modifying the information',
    userComments: 'User remarks',
    pleaseEnterRemarks: 'Please enter remarks information',
    commodityPrice: 'Commodity price',
    groupWhetherToContinue: 'This operation will be part of the limited time special offer Some products are submitted to the group activity, do you want to continue?',
    seckillWhetherToContinue: 'This operation will be part of the limited time special offer Some products are submitted to the seckill activity, do you want to continue?',
    fileSizeIsTooLarge: 'The file size is too large',
    somethhTheSer: 'The server has a small gap',
    editPictureName: 'Edit picture name',
    widthNoExc300Px: 'The image width cannot exceed 300 px',
    heightNoExc300Px: 'The image height cannot exceed 300 px',
    upImaSizeNotExc: 'Upload image size cannot exceed 2MB!',
    loading: 'Loading...',
    noDistribution: 'Distribution not opened',
    selecTips: 'Selection of the current type is not supported, please switch to a conforming type',
    maxChoice: 'Maximum choice',
    templateRestrictionTips: 'Template name cannot be "Shipping" or "Fixed Shipping"',
    chosenTip: 'a region'
  },
  resource: {
    newGroup: 'New Group',
    Delete: 'Delete',
    Move: 'Move',
    selectAll: 'select All',
    selectGroup: 'select a group',
    group: 'group',
    modifyName: 'modify Name',
    update: 'Update',
    groupName: 'group Name',
    cancel: 'cancel',
    confirm: 'confirm',
    mobileGroup: 'mobile Group',
    CannotBeEmpty: 'The group name cannot be empty',
    successTips: 'New group created successfully',
    successTips1: 'Update group created successfully',
    tips: 'tips',
    uploadSuccess: ' upload successf',
    updateSuccess: 'Modified successfully'
  },
  pictureManager: {
    picManager: 'Picture Manager',
    choosePic: 'Select Picture',
    picName: 'Picture name',
    query: 'query',
    tips1: 'Please select a picture',
    tips2: 'Sure to perform [Delete] operation?',
    tips3: 'The resource may have been used for an event or commodity,sure to perform [Delete] operation?',
    uploadPic: 'Upload pictures',
    selectLocalPic: 'Please select a local image to upload:',
    confirmUpload: 'Confirm upload',
    revisePicName: 'Modify picture name',
    revisePicNameAndGroup: 'Modify image name and grouping',
    oldName: 'Old name',
    revName: 'Edit name',
    inputNewName: 'Please enter a new picture name',
    superiorLimit: 'The number of selectable photos has reached the upper limit',
    onlyPictures: 'Only support image format, size can not exceed 2m, other files have been cleared',
    onlySupported: 'Only supports',
    pic: 'picture',
    imageSize: 'Image size is',
    allPictures: 'All images',
    notExceed: 'Size cannot exceed',
    alreadyExist: 'Already existing',
    soonUpload: 'About to upload',
    unit: 'picture',
    upload: 'Upload',
    remainder: 'There are also options',
    maxSelect: 'At present, you can only select at most',
    requestError: 'The server is dozing off^_^',
    uploadPDF: 'Only one PDF file can be uploaded!',
    pdfTypeErrorTips: 'Please upload a PDF file!',
    pdfUploadErrorTips: 'Please upload the correct PDF file!'
  },
  videoManager: {
    picManager: 'Video Manager',
    chooseVideo: 'Video Management',
    videoName: 'Video name',
    AllVideos: 'All Videos',
    query: 'query',
    tips1: 'Please select video',
    tips2: 'Please upload the correct video format',
    tips3: 'The size of the uploaded video cannot exceed 20MB!',
    uploadVideo: 'Upload video',
    selectLocalVideo: 'Please select local video upload:',
    confirmUpload: 'Confirm upload',
    revisePicName: 'Modify video name',
    revisePicNameAndGroup: 'Modify video name and grouping',
    oldName: 'Old name',
    revName: 'Edit name',
    inputNewName: 'Please enter a new video name',
    superiorLimit: 'The number of selectable video has reached the upper limit',
    onlyPictures: 'Only support video format, size can not exceed 2m, other files have been cleared',
    onlySupported: 'Only supports',
    video: 'video',
    notExceed: 'Size cannot exceed',
    alreadyExist: 'Already existing',
    soonUpload: 'About to upload',
    unit: 'video',
    upload: 'Upload',
    remainder: 'There are also options',
    maxSelect: 'At present, you can only select at most',
    requestError: 'The server is dozing off^_^'
  },
  shopWallet: {
    operationAmount: 'operationAmount',
    platformAmount: 'platformAmount',
    shopAmount: 'Merchant Allowance Amount',
    platformCommission: 'Platform commission',
    amountChangeReason: 'Reason for fund change',
    orderSettlement: 'Order Settlement',
    applyRefund: 'User request for refund',
    rejectApplyRefund: 'Refusal of user refund requests',
    exportTips: 'Export filtered transaction records',
    excelName: 'Store statement.xlsx'
  },
  utils: {
    requestErr: 'The http request method is wrong',
    serverErr: 'Something went wrong with the server, please try again later',
    serverNoSupp: 'The server does not support a function required by the current request'
  },
  notice: {
    plaNotice: 'Platform Notice',
    moreNotice: 'More Notice',
    noticeList: 'Platform Notice List',
    publishTime: 'publish time',
    msgContent: 'Message content',
    msgType: 'Message Type',
    msgStatus: 'Message Status',
    selectedItem: 'Selected information',
    allRead: 'All read',
    batchRead: 'Batch read',
    productRemovalReminder: 'Product removal reminder',
    commodityAuditResultReminder: 'Commodity audit result reminder',
    userConfirmsReceiptNotification: 'User confirms receipt notification',
    reminderOfPendingShipment: 'Reminder of pending shipment',
    reminderOfPendingRefund: 'Reminder of Pending refund',
    reminderToNeReceived: 'Reminder to be received',
    menu: 'Menu',
    pushNode: 'Push Node',
    enableReminder: 'Enable Reminder',
    turnOffReminder: 'Turn off reminder',
    msgList: 'Message List',
    msgSettings: 'Message Settings',
    bulletinBoard: 'Bulletin Board',
    messageNotification: 'Message notification',
    more: 'more',
    ReminderChangeConsignment: 'Reminder of change of consignment goods',
    ReminderPurchased: 'Reminder to be purchased',
    ReminderMarketingActivityOffShelf: 'Reminder of marketing activity off the shelf',
    ActivityAuditResultReminder: 'Activity audit result reminder',
    ReminderSuccessfulPayment: 'Reminder of successful payment',
    buyerReturnedGoodsReminder: 'Buyer returned goods reminder',
    contentLimitTxt: 'The announcement content has too many words, please re-enter!'
  },
  noGetUserInfor: 'Unable to obtain user information, jump to the login page',
  idIsLoginInElse: 'The account is already logged in elsewhere',
  reLogin: 'Login again',
  otherParIsNoOL: 'The other party is not online',
  activityDetails: 'Activity details',
  addAct: 'Add activity',
  shopFeature: {
    editMiniPage: 'Edit mini-page',
    chooseFeature: {
      choosePageTips: 'Please select a micro page',
      pageTitle: 'Page title',
      createTime: 'Creation time'
    },
    template: {
      blank: 'Blank',
      newTemplate: 'New template',
      refresh: 'Refresh',
      edit: 'Edit',
      delete: 'Delete',
      copy: 'Copy',
      copySuccess: 'Copy success',
      selectTemplate: 'Select a template'
    },
    allCanUse: {
      basicComponents: 'Basic components',
      config: 'Configuration',
      businessSigns: 'Brand',
      titleText: 'Title',
      notice: 'Notice',
      goodsList: 'Product list',
      imgAd: 'Image ads',
      navigationBar: 'Navigation bar',
      activities: 'Activities',
      productMarketing: 'Product marketing',
      extendComponent: 'Extend the component',
      goodsModule1: 'Commodity Module 1',
      goodsModule2: 'Commodity Module 2',
      goodsModule3: 'Commodity Module 3',
      goodsModule4: 'Commodity Module 4',
      goodsModule5: 'Commodity Module 5',
      goodsWaterfall: 'Waterfall flow'
    },
    goods: {
      prod: 'Product',
      prodName: 'Product name',
      prodDesc: 'Product description',
      prodPrice: 'Product price',
      pleaseAddProd: 'Please add product',
      listStyle: 'List style',
      showContent: 'Show contents',
      oneLineItem1: 'One item in a line',
      oneLineItem2: 'Two items in a line',
      oneLineItem3: 'Three items in a line'
    },
    goodsWaterfall: {
      sortType: 'Sorting mode',
      timeType: 'Time range',
      timeStatus: 'Sort ascending and descending',
      shelfTime: 'Shelf time',
      salesVolume: 'Sales volume',
      commentCount: 'Comment count',
      lastYear: 'Last year',
      threeMonths: 'Last three months',
      lastMonth: 'Last month',
      lastWeek: 'Last week',
      ascendingOrder: 'Ascending order',
      descendingOrder: 'Descending order',
      tip1: 'Waterfall flow components can only be displayed at the bottom!',
      tip2: 'Only one waterfall stream can be added!'
    },
    header: {
      microTitle: 'Micro Title',
      pageName: 'Page name',
      pageNameCanntEmpty: 'The page name can\'t be empty',
      pageNamePlaceholder: 'Please input the page name'
    },
    headerAd: {
      pageTitle: '[Page title]',
      pageHeader: 'Page header',
      pageFooter: 'Page footer',
      microPage: 'Micro page',
      prodDetail: 'Product detail',
      showPosition: 'Show position:',
      showPages: 'Show pages:'
    },
    imageAd: {
      carouselPoster: 'Carousel Poster',
      lateralSliding: 'Lateral Sliding',
      hotArea: 'Hot Area',
      prodDetPage: 'Product details',
      actPage: 'Activity page',
      widthSuggest: 'Recommended image size is 750*300',
      imageAd: 'Image Ads',
      selModel: 'Select template',
      changeModelTips: 'Note：the hot area will be cleared when switching templates',
      isShowDots: 'Indicator：',
      dotsColor: 'Indicator color',
      reset: 'Reset',
      actDotColor: 'Activate color',
      actDotLoc: 'Point position',
      addPic: 'Add picture',
      max10Ads: 'Tips: Add up to 10 ads',
      imgSizeTip: 'As a top carousel, it is recommended that the image size be 750*300',
      imgHeight: 'Picture height',
      picTit: 'Title',
      addHotArea: 'Add Hot Area',
      setRoute: 'Set link',
      addBgImg: 'Add background image',
      save: 'Save',
      hotAreaTit: 'Universal hot zone',
      setHotAreaRoute: 'Please set hot area link'
    },
    notice: {
      announcement: 'Carousel announcement',
      announcementTips1: 'Show the top announcement in the mall in the form of carousel',
      announcementTips2: 'Please check the effect on the client'
    },
    promotionalActivities: {
      addProd: 'Please add product',
      groupPurchase: 'Group purchase',
      spike: 'Spike',
      more: 'More ',
      promotionalActivity: 'Promotional activity',
      addActivityProds: 'Add activity products',
      addActivityProdsTip: 'Add up to 3 items',
      activityType: 'Activity type',
      promotionPrice: 'PromotionPrice',
      tipsContent: 'Invalid active goods are detected in the component. Do you want to refresh the data immediately?',
      tips: 'Tips',
      max5Prods: 'Display up to 5'
    },
    tabNav: {
      picNav: 'Picture',
      picTextNav: 'Picture & Text',
      tetxNav: 'Text',
      line4item: 'Four item a row',
      line5item: 'Five item a row',
      cart: 'Shopping Cart',
      personalCenter: 'Personal Center',
      customPath: 'Custom path',
      fullFillTips: 'Please complete the information',
      pleaseAddNav: 'Please add navigation',
      pleaseAddPic: 'Please add a picture',
      pleaseFillNavTitle: 'Please fill in the navigation title',
      pleaseChooseRouteLink: 'Please choose route link',
      pleaseFillThePath: 'Please fill the link path',
      navBar: 'Navigation bar',
      countInLine: 'Quantity per row',
      changePic: 'Change picture',
      addPic: 'Add',
      tit: 'Title',
      routeLink: 'Route link',
      choose: 'Please choose',
      addNav: 'Add navigation',
      microPage: 'Micro page',
      confirm: 'Confirm',
      cancel: 'Cancel',
      link: 'Link'
    },
    goodsModule: {
      mainTitCon: 'Selling is recommended',
      snappedUpImmediately: 'Snapped up immediately',
      subTitTwo: 'Select the corresponding column number to set details',
      labelSubTit: 'Customize the subtitle',
      conTwo1: 'Group purchase is more favorable',
      conTwo2: '72 new pieces are available today',
      conTwo3: 'Find a good shop',
      conOne1: 'Today\'s hot style',
      conOne2: 'A better new life',
      conOne3: 'New start',
      conOne4: 'Everyone is buying it',
      toPay: 'To snap up',
      maxAdd: 'Add the most',
      addBigImg: 'Add larger version',
      piece: ' goods',
      suggestChoose: 'Suggest to choose',
      sameScalePic: ' or a picture of the same scale',
      goodsModuleTip1: ' the custom title cannot be empty',
      goodsModuleTip2: ' custom subheadings cannot be empty',
      goodsModuleTip3: ' snap Up Now Jump link cannot be empty',
      goodsModuleTip4: ' the picture cannot be empty',
      goodsModuleTip5: ' the jump link cannot be empty',
      goodsModuleTip6: ' merchandise cannot be empty',
      goodsModuleTip7: ' the main header text cannot be empty',
      goodsModuleTip8: ' the subheading text cannot be empty',
      goodsModuleTip9: ' large images cannot be empty',
      goodsModuleTip10: ' large image jump links cannot be empty'
    },
    titText: {
      linkText: 'More',
      prodDet: 'Product details',
      pleaseFillTit: 'Please fill in the title',
      pleaseChoosePord: 'Please choose product',
      titText: 'Title text',
      titContent: 'Title content',
      decsContent: 'Description content',
      decsContentPlaceholder: 'Please enter the description, up to 100 words',
      showPos: 'Position',
      left: 'Left',
      center: 'Center',
      titSize: 'Title size',
      titWeight: 'Title weight',
      descWeight: 'Description weight',
      normal: 'Normal',
      bold: 'Bold',
      more: 'More',
      style: 'Style',
      textContent: 'Text content',
      pleaseFillTextContent: 'Please fill in the text'
    },
    businessSigns: {
      prodNum: 'Goods quantity',
      saleGoods: 'Sale goods ',
      selfEmployed: 'Self',
      preferred: 'preferred',
      fans: ' Fans',
      follow: 'Follow',
      fen: ' Points',
      wan: ' Ten Thousand',
      tip1: 'Merchant sign components can only be displayed on the head!',
      tip2: 'Business signs can only add one!'
    },
    edit: {
      save: 'Save',
      saveAndCon: 'Save and continue',
      moreOper: 'More operations',
      noneUseableCom: 'There are no components available',
      saveSuccess: 'Saved successfully',
      deleteConfirm: 'Are you sure you want to delete this component?',
      cancel: 'Cancel',
      confirm: 'Confirm',
      componentManagement: 'Component management',
      emptyTemplate: 'The page component is empty',
      removeAll: 'Remove all components',
      removeComponent: 'Remove component',
      back: 'Back',
      componentSorting: 'Component Sorting'
    },
    list: {
      newMicroPage: 'New page',
      pageName: 'Page name',
      shopHomePage: 'Homepage',
      createTime: 'Creation time',
      updateTime: 'Update time',
      oper: 'Operation',
      edit: 'Edit',
      view: 'View',
      modify: 'Modify',
      delete: 'Delete',
      setHomePage: 'Set Homepage',
      cancelHomePage: 'Cancel Homepage',
      deleteTips: 'Are you sure to detele？',
      tips: 'Tips',
      deleteSuccess: 'Delete successfully!',
      operSuccess: 'Operation successful!'
    },
    searchBar: {
      searchBar: 'Search Bar',
      search: 'Search',
      normal: 'Normal',
      topThenLock: 'Roll to the top to fix',
      square: 'Square',
      fillet: 'Fillet',
      left: 'Left',
      center: 'Center',
      showPos: 'Display location',
      textPos: 'Text location',
      boxStyle: 'Box style',
      boxHeight: 'Box height',
      bgColor: 'Background color',
      boxColor: 'Box color',
      textColor: 'Text color'
    }
  },
  settlementDetail: {
    tip1: 'Settlement details',
    tip2: 'All orders are credited on the day after the order is completed and the refund time limit is exceeded. If there are unprocessed returns, they are delayed until the day after the return order is processed (corresponding to the status of the return order: refused receipt, refused refund, voided, completed);',
    tip3: 'Terminology:',
    tip4: 'Product unit price: the original price of the product, including the amount after setting the price;',
    tip5: 'quantity (excluding refunds): the number of goods in the order at the time of settlement needs to exclude the number of refunds completed, the amount of full discount offers, store coupon offers, the amount of platform coupon offers, the use of points, the difference in the price change of the order, the amount paid for goods, the amount of supply are calculated without the amount of refunds;',
    tip6: 'Full discount discount amount: the amount of goods to participate in the full reduction, full discount activities to reduce the cost of the merchant;',
    tip7: 'store coupon discount amount: the amount of goods using the store coupon reduction, the cost is borne by the merchant;',
    tip8: 'Platform coupon discount amount: the amount of goods using the general coupon reduction, the cost is borne by the platform, compensated to the merchant at the settlement;',
    tip9: 'order change difference: the difference in price resulting from the merchant modifying the order amount, the difference can be positive or negative;',
    tip10: 'Use of points: the user uses the part of the use of points when paying, the cost is borne by the platform, compensated to the merchant at the settlement;',
    tip11: 'Product amount paid: the amount paid for the product after various promotional activities, virtual asset credits, and order price changes;',
    tip12: 'The actual amount paid for an unchanged order = original price of the product - full discount offer - full discount offer - store coupon offer amount - platform coupon offer amount - use of points',
    tip13: 'The actual amount paid for an order with price change = the amount after price change',
    tip14: 'Supply amount: the amount that needs to be settled to the supplier on behalf of the supplier is goods;',
    tip15: 'Share ratio: the commission to be paid to the platform when sales are generated by the category to which the goods belong, the settlement ratio shall prevail when the user places an order;',
    tip16: 'Platform commission: the commission receivable by the platform, the platform commission per product = (product paid amount + platform discount) * category deduction rate, the product paid amount does not include the product shipping cost;',
    tip17: 'refund change difference: order refund, if the merchant modifies the refund amount resulting in the actual refund amount of goods less than the amount paid, the difference will be compensated to the merchant at settlement;',
    tip18: 'Shipping: The shipping cost paid by the user, collected by the platform on behalf of the merchant, will be returned to the merchant at settlement;',
    tip19: 'Store paid amount: each order store paid amount = the actual amount of goods paid + the difference between the return order price change + shipping + the amount of the platform coupon discount + the use of points - platform commission - distribution commission.',
    tip20: 'Total store receipts: Total store receipts = the sum of store receipts per order',
    tip21: 'Total amount paid for goods: the sum of the amount paid for goods for each order;',
    tip22: 'Total amount of supply: the sum of the supply amount to be settled to the supplier for each order;',
    tip23: 'Total Platform Commission: the sum of the platform commission for each order;',
    tip24: 'Distribution Commission Ratio: The commission ratio set by the merchant for the distributor to receive on behalf of the goods, the settlement ratio shall prevail when the user places an order;',
    tip25: 'Distribution commission: the commission paid by the merchant to the distributor, each product platform commission = the amount paid for the product * commission ratio;',
    tip26: 'Total Distribution Commission: the sum of the distribution commissions for each order;',
    tip27: 'Settlement instructions',

    tip2_2: 'If an order is refunded in its entirety or if each order item has been refunded, then the merchant will be billed immediately. In all other cases, settlement is made 15 days after confirmation of receipt.',
    tip2_3: 'Explanation of terms.',
    tip2_4: '- Purchase quantity: the number of goods purchased by the user, excluding after-sales quantities.',
    tip2_5: '- Total amount of goods: the original price of goods * the number of purchases, excluding the amount of after-sales.',
    tip2_6: '- amount paid by the user: the actual amount paid for the goods, after deducting the platform discount (platform coupon discount, member discount discount, points credit amount), store discount (coupon, full discount, second discount, group discount, discount package, order change price).',
    tip2_7: '- merchant discount amount: the total amount of goods using store discount, including the amount of merchant price change.',
    tip2_8: '- the amount of platform offers: the total sum of the goods using platform offers, the cost of which is borne by the platform and compensated to the merchant at the time of settlement.',
    tip2_9: 'merchants did not change the price: the amount of platform offers = platform coupon offers + member discount offers + points credit amount',
    tip2_10: 'merchants change the price: the amount of platform benefits = original platform benefits - (the amount of goods reduced / original price of goods * (platform coupon offers + member discount offers + points credit amount))',
    tip2_11: "- the amount of reduction in platform allowance: the amount of platform discount reduced by the merchant's price change.",
    tip2_12: 'merchant to change the price: the amount of the reduction of the platform allowance = the amount of the reduction of goods / original price of goods * (platform coupon offer + member discount offer + points credit amount)',
    tip2_13: '- distribution amount: the commission paid by the merchant to the distributor.',
    tip2_14: 'Reward by proportion: distribution amount = goods minus the amount paid for the platform discount * distribution reward ratio',
    tip2_15: 'Reward by fixed value: distribution amount = fixed value reward, goods deducted from the platform discount payment amount if less than the reward value, the distribution amount is 0',
    tip2_16: '- the use of points: the points used by the user in the payment.',
    tip2_17: "- category deduction rate: the corresponding deduction rate of the commodity's contracted classification.",
    tip2_18: '- Platform commission: the commission to be paid to the platform when the category to which the commodity belongs generates sales.',
    tip2_19: 'Platform commission for each product = (actual amount paid for the product + platform coupon discount + member discount discount + credit amount) * category deduction rate.',
    tip2_20: '- Points credit amount: the amount of points credit used by the user, calculated according to the use ratio and credit ratio at the platform end.',
    tip2_21: '- Member discount amount: the amount of discount generated by the discount in the membership level privilege, the cost of which is borne by the platform and compensated to the merchant at the time of settlement.',
    tip2_22: '- (b) Platform coupon discount amount: the amount of goods reduced by the use of platform coupons, the cost of which is borne by the platform and compensated to the merchant at settlement.',
    tip2_23: '- merchant coupon discount amount: the amount of the merchandise used the store coupon reduction, the cost of which is borne by the merchant.',
    tip2_24: '- full discount discount amount: the amount of the goods to participate in the full reduction, full discount activities reduced, the cost borne by the merchant.',
    tip2_25: '- the amount of group discount: the amount reduced by the goods participating in group activities, the cost of which is borne by the merchant',
    tip2_26: '- second discount amount: the amount of goods to participate in the second sale activities to reduce the cost borne by the merchant.',
    tip2_27: '- Package discount amount: the amount of goods to participate in discount package activities, the cost borne by the merchant.',
    tip2_28: '- store price change amount: the amount of merchandise changed by the merchant after the user submits the order.',
    tip2_29: '- Refund amount: the amount of refund applied by the user after the order occurs after sale.',
    tip2_30: '- refund quantity: the amount of refund applied by the user after the order occurs after the sale.',
    tip2_31: '- Shipping cost: the shipping cost is not included in the calculation of the offer.',
    tip2_32: 'merchant to change the shipping cost: the user submits an order after the merchant to change the order shipping amount',
    tip2_33: 'Direct store package: the amount of discount generated by the user level privileges in the package, the cost is borne by the platform and compensated to the merchant at the time of settlement',

    prodTotalAmount: 'Total amount of the product',
    multishopReduce: 'Merchant Offer Amount',
    platformShareReduce: 'Platform offer amount',
    useScore: 'use points',
    scoreAmount: 'Points credit amount',
    distributionAmount: 'Distribution amount',
    platMemberAmount: 'Platform Member discount',
    shopMemberAmount: 'Shop Member discount',
    platformCouponAmount: 'platformCouponAmount',
    shopCouponAmount: 'Merchant coupon amount',
    discountAmount: 'Full discount amount',
    comboAmount: 'Package discount amount',
    seckillAmount: 'Amount of the spike discount',
    groupAmount: 'Grouping discount amount',
    shopChangeFreeAmount: 'storeChangeAmount',
    platformShopChangeAmount: 'Platform offer reduction amount',
    refundAmount: 'refund amount',
    refundCount: 'refund amount',
    freeFreightAmount: 'Shipping change amount:',
    freeFreightAmountTips: 'Order shipping amount modified in order management',
    platformFreeFreightAmount: 'Platform shipping reduction amount',
    platformFreeFreightAmountTips: 'the amount of discount generated by the package shipping in user level privileges',
    rate: 'split rate'
  },
  chat: {
    clickToLoadMore: 'Click to load more',
    mallCustomerService: 'Mall Customer Service',
    sendLink: 'Send link',
    send: 'Send',
    inquiring: 'Inquiring',
    myOrders: 'My Orders',
    recentlyViewed: 'Recently Viewed',
    orderNumber: 'Order number',
    loading: 'Loading',
    noMore: 'No more',
    read: 'Read',
    unRead: 'Un read',
    noRecord: 'No record',
    chatRecordTips1: 'The above is',
    chatRecordTips2: 'customer service chat record',
    customerOffline: 'Current customer service is not online',
    loginOtherSide: 'The user is logged in elsewhere, please refresh the page',
    sendOrderNumber: 'Send orderNumber',
    pleaseLoginAgain: 'Account is offline, please login again',
    paymentAmount: 'Payment amount',
    onlineCustomerService: 'Online Customer Service',
    selectOnlineCustomerService: 'Select other customer service online',
    productLinks: 'Product links',
    orderLink: 'Order Link',
    none: 'None',
    pleaseSelectAcustomerService: 'Please select a customer service transfer',
    storeGoneOffline: 'The shop',
    offline: ' has gone offline',
    resumeGoingLive: 'Please go live again',
    Closed: ' has closed',
    notEnabled: ' not enabled',
    messageBox: 'Message box',
    transferred: 'Transferred',
    pendingPayment: 'Pending Payment',
    pendingDelivery: 'Pending Delivery',
    pendingReceipt: 'Pending Receipt',
    pendinEvaluation: 'Pending Evaluation',
    grouping: 'Grouping',
    evaluated: 'Evaluated',
    shipped: 'Shipped',
    completed: 'Completed',
    canceled: 'Canceled',
    sorryYouHaveBeenDisconnected: 'Sorry you have been disconnected, reconnecting now',
    notYourResponsibility: 'Customers who are not your responsibility',
    noContactReceivedMessage: 'No contact received the message',
    transferToCustomerService: 'Transfer to Customer Service',
    transferTips: 'Multiple customer service can be set up to add accounts with customer service role privileges in the background [system a staff list], and other customer service accounts can be transferred when they are online.',
    connetBroken: 'The connection has been broken, please refresh the page and try again',
    tips: 'Tips',
    confirm: 'Confirm',
    cancel: 'Cancel',
    noContacts: 'No contact',
    cannotSendBlankMessage: 'Cannot send blank messages',
    justNow: 'JN',
    yesterday: 'YD',
    twoDaysAgo: 'TDAGO',
    Monday: 'Mon',
    Tuesday: 'Tue',
    Wednesday: 'Wed',
    Thursday: 'Thu',
    Friday: 'Fri',
    Saturday: 'Sat',
    PleaseSelectAContactPerson: 'Please select a contact person',
    AccountCancelled: 'Account cancelled'
  },
  shopProcess: {
    auditTip1: 'Submit business information audit success',
    auditTip2: 'Withdrawal of amendment request is successful',
    viewModify: 'View modification informations',
    notAudit: 'NotAudit',
    passed: 'Passed',
    notPass: 'Failed to pass',
    denialReason: 'Reason for rejection',
    businessTerm: 'Business Term',
    merchantDetail: 'merchant details',
    basicInfo: 'Basic Information',
    businessInfo: 'Business Information',
    signUpInfo: 'Signup Information',
    financeInfo: 'Financial Information',
    theContractIsNotValid: 'the contract is not valid',
    validityPeriodOfTheContractHasExpired: 'the validity period of the contract has expired',
    disable: 'Disable',
    enable: 'Enable',
    delete: 'Deleted',
    deleted: 'Deleted',
    closed: 'out of business',
    inOperation: 'In Operation',
    onlinePendingReview: 'Online pending review',
    shopApplication: 'Opening a shop application',
    storeOpenPendingReview: 'Opening a shop pending review',
    onlineAuditFailure: 'Online audit failure',
    storeAccount: 'Store Account',
    merchantSelfIncreasing: 'merchantSelfIncreasing',
    validityPeriodOfContract: 'Contract validity period',
    validPeriod: 'Merchant shop validity period',
    shopType: 'Shop Type',
    ordinaryShop: 'Ordinary shop',
    preferredGoodShop: 'Preferred GoodShop',
    masterAccount: 'master account',
    yes: 'yes',
    no: 'No',
    group: 'group',
    audit: 'audit',
    reject: 'reject',
    rejectRemarks: 'Please fill in the reason for rejection in 200 words or less',
    rejectInputTips: 'Please fill in the reason for rejection',
    merchantReview: 'merchant review',
    addSigning: 'Please add sign up information',
    submit: 'Submit',
    customRate: 'Custom category deduction rate',
    saveSubmit: 'Save and submit for review',
    changeBusinessInfor: 'Application for change of business information',
    reAmendmentRequest: 'Revocation of amendment request',
    close: 'Close',
    noYet: 'Not yet',
    detailed: 'Detailed address',

    openAShop: 'Apply to open a shop',
    auditStatus: 'Audit Status',
    accountStatus: 'Account status',
    shopStatus: 'Shop status',
    notSubmitApplyTips: 'You don\'t have a shop yet, please submit your application to open a shop!',
    applyShop: 'Apply to open a shop',
    applyAuditingTips: 'Your application is being reviewed! Please wait patiently~',
    applyAuditing: 'Your application is being audited',
    applyFailTips: 'Your application for opening a shop has not been approved! The reason is:',
    applyFail: 'Your application has not been approved',
    previousStep: 'Previous Step',
    seePreviousStep: 'View previous step',
    nextStep: 'Next Step',
    submitAndNextStep: 'Submit, Next Step',
    seeNextStep: 'View next step',
    merchantName: 'merchant name',
    shopName: 'shop name',
    detailAddr: 'detail address',
    email: 'contact email',
    tel: 'contact details',
    receiveMobile: 'Receive notification cell phone number',
    receiveMobileTips: 'Receive SMS notifications on cell phone number',
    addr: 'Location',
    intro: 'shop profile',
    logo: 'Shop logo',
    logoTips: 'jpg, gif, png only, only 1 upload',
    shopLogoPicTips: 'Suggested size is 80 * 80 pixels, ',
    backgroundPic: 'Store background image',
    mobileBackgroundPic: 'Mobile background picture',
    mobilePicTips: '(recommended size 750*380)',
    pcBackgroundPic: 'pc background image',
    pcPicTips: '(recommended size 590*220)',
    businessLicense: 'Business license electronic version',
    example: 'example',
    corporateIdentityCard: 'Corporate identity card',
    identityCardTips: 'Please upload a photo of the front and back of your ID card, jpg, gif, png only, size no more than 2M, only 2 photos',
    identityCardFront: 'Portrait face example',
    identityCardLater: 'Example of national',
    merchantNameNotEmpty: 'The merchant name cannot be empty',
    merchantNameErrorTips: 'Merchant name length is between 2 and 10 characters',
    merchantNameInputTips: 'Please enter a merchant name of 2-10 characters or less',
    shopNameNotEmpty: 'The shop name cannot be empty',
    shopNameInputTips: 'Please enter a shop name of 2-20 characters or less',
    emailNotEmpty: 'Contact email must not be empty',
    emailInputTips: 'Please enter your usual contact email',
    emailErrorTips: 'Please enter the correct email address',
    inputAllSpace: 'The content must not be all spaces',
    reasonAllSpace: 'The reasons for application cannot be blank spaces',
    telNotEmpty: 'Please fill in your contact details',
    receiveMobileNotEmpty: 'Please fill in the receiving notification cell phone number',
    telErrorTips: 'Please enter the correct contact information',
    telInputTips: 'Please enter the 11-digit mobile phone number of your usual contact',
    addrNotEmpty: 'Please select the province and city first',
    addrInputTips: 'Please select your region',
    detailAddrNotEmpty: 'The address details cannot be empty',
    detailAddrInputTips: 'Please enter a detailed address of 50 characters or less',
    introInput: 'Please enter a shop description in 200 characters or less',
    logoNotEmpty: 'Please upload your shop logo',
    mobilePicNotEmpty: 'Please upload a mobile background image',
    pcPicNotEmpty: 'Please upload a background image for pc',
    businessLicenseNotEmpty: 'Business license needs to be uploaded',
    identityCardFrontNotEmpty: 'Please upload the portrait side of your ID card',
    identityCardLaterNotEmpty: 'Please upload the national logo side of your ID card',
    baseSaveSuccess: 'Basic information saved successfully',
    businessSaveSuccess: 'Business information saved successfully',
    categorySaveSuccess: 'Category saved successfully',
    categoryAddSuccess: 'New category succeeded',
    categoryApplySuccess: 'Submitted category application',
    brandSaveSuccess: 'Brand saved successfully',
    brandAddSuccess: 'New brand succeeded',
    brandApplySuccess: 'Submitted contracted brand application',
    creditCode: 'Uniform social credit code',
    creditCodeErrorTips: 'Please enter the correct Uniform Social Credit Code',
    creditCodeInputTips: 'Please enter the unified credit code',
    creditCodeNotEmpty: 'The social credit code cannot be empty',
    firmName: 'Firm name',
    firmNameInputTips: 'Please enter a firm name of 50 characters or less',
    firmNameNotEmpty: 'The firm name cannot be empty',
    residence: 'Residence',
    residenceInputTips: 'Please enter a residence of 50 characters or less',
    representative: 'Legal representative',
    representativeInputTips: 'Please enter a legal representative in 20 characters or less',
    representativeNotEmpty: 'The legal representative cannot be empty',
    capital: 'Registered capital',
    tenThousandYuan: 'ten thousand yuan',
    fountTime: 'Date of establishment',
    startTime: 'start date',
    endTime: 'end date',
    businessTime: 'date of business',
    businessTimeNotEmpty: 'The business date cannot be empty',
    businessScope: 'Scope of business',
    noFixedTerm: 'No fixed term',
    businessScopeInputTips: 'Please enter a business scope of 500 characters or less',
    businessScopeNotEmpty: 'The business scope cannot be empty',
    signingCategory: 'Signing category',
    category: 'Category',
    categoryAddMaxLimitTips: 'Up to 200 categories',
    categoryName: 'Category name',
    parentCategoryName: 'parent category',
    categoryRate: 'Category deduction rate',
    categoryQualifications: 'Business Qualifications',
    brandQualifications: 'Authorization document',
    brandQualificationsNotEmpty: 'Please upload a brand license file',
    brandMaxLimitTips: 'You can only sign up for a maximum of 50 brands',
    categoryStatus: 'Category Status',
    signingBrand: 'Signing brands',
    brandStatus: 'Brand Status',
    customBrand: 'Custom Brand',
    brand: 'brand',
    brandName: 'brand name',
    brandNameRepeatTip: 'Duplicate brand name',
    firstLetter: 'Retrieve initials',
    brandLogo: 'Brand logo',
    editSigningCategory: 'New Signing Category',
    editSigningBrand: 'New signing brand',
    signingCategoryTips: 'Please select the last category to sign up',
    signing: 'Signed',
    preSigned: 'Signed',
    application: 'Applying',
    chosen: 'Selected',
    piece: 'piece',
    mostSigning: 'The maximum number of signings available',
    categorySigningNotEmpty: 'Please select at least one category',
    add: 'add',
    added: 'added',
    mostAdd: 'can add up to',
    settlementAccountEditTips: 'If you need to modify the settlement account, please operate in the store information module',
    settlementAccount: 'Settlement Account',
    addSettlementAccount: 'Add Account',
    bankName: 'bank Name',
    brandNameInputTips: 'Please enter the bank name',
    brandNameNotEmpty: 'Bank name cannot be empty',
    brandNameErrorTips: 'The length of the bank card is 2-20 characters',
    accountErrorTips: 'Please enter the correct account',
    cardNo: 'account Number',
    account: 'account Number',
    cardNoInputTips: 'Please enter the bank card number',
    cardNoNotEmpty: 'Account cannot be empty',
    accountNotEmpty: 'Account cannot be empty',
    recipientName: 'account Name',
    recipientNameInputTips: 'The length of the account name is between 2 and 20 characters',
    bankCardInputTips: 'Cards can be between 8 and 30 characters long',
    recipientNameNotEmpty: 'Account name cannot be empty',
    cardNoErrorTips: 'Please enter the correct bank card number',
    openingBank: 'bank Account',
    openingBankNotEmpty: 'Account bank cannot be empty',
    openingBankErrorTips: 'theOpeningBankNameIs2To20CharactersLong',
    branchInputTips: 'For example: Yuhua Sub-branch, Yuhua District, Nanjing City',
    branchNameNotEmpty: 'branchNameCannotBeEmpty',
    cardMaxLimitTips: 'upTo5SettlementAccountsCanBeAdded',
    submitApply: 'Submit Application',
    submitApplySuccessTips: 'storeApplicationSubmittedSuccessfully',

    addSigningCategory: 'New Signing Category',
    applySigningCategory: 'Apply for Signing Category',
    applySigningBrand: 'New Signing Brand',
    seeApplySigningCategory: 'View Classification Application',
    seeApplySigningBrand: 'View Brand Application',
    setMainAccount: 'Set as master account',
    setNotMainAccount: 'cancelTheMainAccount',
    readShopProtocolTips: 'Please read and agree to the "Business Entry Agreement" below',
    incompleteInformation: 'incompleteInformation',
    applyOnline: 'applyToGoOnline',
    saveBasicInfo: 'Save Information',
    saveCompanyInfo: 'Save Information',
    saveCompanyInfoSuccess: 'businessInformationSavedSuccessfully',
    shopBankCardDeletePre: 'Are you sure to delete the tail number',
    shopBankCardDeleteAfter: 'settlement account?',
    setSuccess: 'setSuccessfully',
    cancelMainAccountSuccess: 'cancelTheMainAccountSuccessfully',
    platformBrand: 'platformBrand',
    merchantCustomBrand: 'merchantCustomBrand',
    cancel: 'cancel',
    confirm: 'determine',
    topNavbarTitle: 'MALL4J SHOP',
    saveSuccessfully: 'Save Successfully!',
    deleteSuccessfully: 'Deleted Successfully!',
    noBrandSelected: 'No brand selected!',
    modify: 'modify',
    businessStartEndTime: 'The business start date cannot be later than the business end time',
    saveSuccess: 'Saved successfully',
    deleteSuccess: 'Delete successfully',
    yourStoreIsOfflineBecauseOf: 'Your store is offline because of:',
    onlineShopTips: 'If you have any questions, you can reapply to go online on the store information page',
    shopStopReason: 'Your shop is closed because',
    shopStopTips: 'If you have any questions, please contact the platform',
    applyingOnline: 'Your shop is applying to go live',
    shopIsUnderReview: 'The shop is under review',
    withdrawalApplicationOfContract: 'Are you sure you want to withdraw your application for this contract?',
    contractApplicationWithdrawn: 'Contract application withdrawn',
    addShopErrorTip7: 'Passwords can only be numbers or letters'
  },
  purchase: {
    order: {
      purchaseNumber: 'Purchase Number',
      dvyFlowId: 'dvy Flow Id',
      supplier: 'supplier',
      supplierProd: 'Prod',
      deliverTime: 'Deliver Time',
      deliverTimeNotEmpty: 'Deliver Time cannot be empty',
      remark: 'Remark',
      deliveryMethod: 'Delivery Method',
      newPurchaseOrder: 'Add Purchase Order',
      viewPurchaseOrder: 'View Purchase Order',
      newPurchaseInbound: 'Add Purchase Inbound',
      viewPurchaseInbound: 'View Purchase Inbound',
      purchaseOrderInbound: 'Purchase Order Inbound',
      purchaseNum: 'Purchase Num',
      purchasePrice: 'Purchase price (RMB)',
      purchaseNumThanMinimum: 'The quantity purchased must be greater than the minimum order quantity of the supplier\'s goods',
      purchasePriceMustThen0: 'The purchase price needs to be greater than the supplier\'s purchase unit price',
      purchaseAmount: 'Purchase Amount',
      purchaseProdUploadTip: 'The purchase price of goods imported into the self-pick supplier is the original price of the goods by default',
      selectSupplierProduct: 'Please select supplier product',
      selectSupplier: 'Please select supplier',
      inbound: 'Inbound',
      nullify: 'Nullify',
      finish: 'finish',
      estimatedIncomingQuantity: 'Estimated incoming quantity',
      numberInStock: 'Number in stock',
      remainingIncomingQuantity: 'Remaining incoming quantity',
      actualIncomingQuantity: 'Actual incoming quantity',
      voided: 'Nullify',
      warehoused: 'Inbound',
      partiallyComplete: 'Partially complete',
      complete: 'Complete',
      stockNumber: 'Stock Number',
      purchaseProdTemplate: 'Purchase Product.xlsx',
      purchaseOrderTemplate: 'Purchase Order.xlsx',
      determineCancellationPurchaseOrder: 'Determine the cancellation of the purchase order',
      finalizePurchaseOrder: 'Finalize the purchase order',
      exportInboundProd: 'Export Inbound Prod',
      importInboundProd: 'Import Inbound Prod',
      deleted: 'Deleted'
    }
  },
  stock: {
    exportRadioEmptyTips: 'Please select how you want to export',
    exportStockLogSearchEmptyTips: 'The current list of filtered data is empty',
    exportStockLogSelectEmptyTips: 'Please select the data to be exported',
    skuStockInfo: 'Product inventory information',
    skuStockXls: 'Product Stock Info.xlsx',
    stockFlowXls: 'Stock Flow Info.xlsx',
    stockBillLogXls: 'Inbound details.xlsx',
    stockBillLogOutXls: 'outbound details.xlsx',
    newOutboundDetails: 'New outbound details',
    newWarehousingDetails: 'New inbound details',
    confirmDelivery: 'Confirm Outbound Delivery',
    confirmWarehousing: 'Confirm Inbound',
    deliveryCount: 'Outbound quantity',
    warehousingCount: 'Inbound Quantity',
    saveDraft: 'Save draft',
    exportSuccess: 'Export successful',
    deliveryDate: 'Date of export',
    deliveryDateNotEmpty: 'Delivery time cannot be empty',
    warehousingDate: 'Inbound date',
    warehousingDateNotEmpty: 'Inbound time cannot be empty',
    deliveryReason: 'Reason for shipping',
    deliveryReasonNotEmpty: 'Reason for shipping cannot be empty',
    warehousingReason: 'Inbound reason',
    warehousingReasonNotEmpty: 'Inbound reason cannot be empty',
    appendix: 'Attachment',
    deliveryCertificate: 'Outbound Certificate',
    warehousingCertificate: 'Inbound certificate',
    businessInQualTips: 'Please upload qualification files such as incoming vouchers, support png, jpg, jpeg files, up to 4',
    businessOutQualTips: 'Please upload png, jpg, jpeg files, up to 4, for outgoing vouchers etc.',
    batchImport: 'Batch import',
    batchExport: 'Batch export',
    prodCountTips: 'Kind of products, total incoming quantity',
    prodCountOutTips: 'Total outgoing quantity for each item',
    prodNotEmpty: 'Product details cannot be empty, please select product',
    unit: 'Unit',
    pieces: 'Pieces',
    outInStock: 'In/Out Stock',
    afterStock: 'Remaining quantity',
    availableStock: 'available stock',
    newlyBuildOtherStorage: 'New Other Storage',
    exportStorageDetails: 'Export inbound details',
    export: 'Export',
    exportOfSearchStorage: 'Export filtered inbound details',
    exportOfSelectStorage: 'Export selected inbound details',
    exportOfSearchFlow: 'Export the filtered inventory flow',
    exportOfSelectFlow: 'Export selected inventory flow',
    exportOfSearchInquire: 'Export filtered inventory information',
    exportOfSelectInquire: 'Export selected inventory information',
    newlyBuildOtherOutbound: 'New Other Outbound',
    exportOutboundDetails: 'Export outbound details',
    retailInfo: 'Item information',
    exportOfSearchOutbound: 'Export filtered outbound details',
    exportOfSelectOutbound: 'Export selected outbound details',
    all: 'All',
    waitSubmit: 'Pending submission',
    inStorage: 'Inbound',
    inOutbound: 'Outbound',
    voided: 'voided',
    spec: 'spec',
    outProdFileName: 'Other outbound excel templates.xlsx',
    inProdFileName: 'Other inbound excel templates.xlsx',
    downloadSuccessful: 'Downloaded successfully',
    stockBillReason: 'Reason for incoming and outgoing stock',
    stockBillCnReason: 'Chinese reasons',
    stockBillReasonNotEmpty: 'The reason for entering and leaving the warehouse cannot be empty',
    stockBillEnReason: 'English reasons',
    stockBillReasonInputTips: 'Please enter the reason for incoming and outgoing stock',
    stockType: 'In/out category',
    sysSet: 'System built-in',
    sendStock: 'Outbound',
    receiveStock: 'Inbound',
    viewRemark: 'View Remarks',
    stockTypeNotEmpty: 'Inbound and outbound category cannot be empty',
    remark: 'Remarks',
    remarkCn: 'Chinese remarks',
    remarkCnNotEmpty: 'Chinese remarks cannot be empty',
    remarkEn: 'English remarks',
    price: 'Sales price ($)',
    queryCondition: 'View same item',
    stockBillNo: 'Document number',
    sourceOrderNo: 'Related order number',
    pleaseStockBillNo: 'Enter document number',
    pleaseSourceOrderNo: 'Enter related order number',
    stockBillType: 'Bill type',
    billStatus: 'Bill status',
    createOrderTime: 'Order creation time',
    purchaseInStock: 'PurchaseInStock',
    returnToStorage: 'refund to storage',
    editStorage: 'editInStorage',
    otherEntries: 'Other incoming',
    sellOut: 'Sales Outbound',
    otherOutbound: 'Other Outbound',
    editOutBound: 'edit outbound',
    profitStorage: 'ProfitStorage',
    lossOutBound: 'LossOutBound',
    inventoryInitialization: 'Inventory initialization',
    orderCancelled: 'Order cancelled',
    outStockTime: 'Outbound time',
    inStockTime: 'In Stock Time',
    outStockOrderNo: 'OutStockOrderNo',
    inStockOrderNo: 'Inbound order number',
    actualOutCount: 'actual outbound quantity',
    actualInCount: 'actual inbound quantity',
    totalOutAmount: 'Total outgoing amount ($)',
    totalOutAmount2: 'Total outgoing amount',
    totalInAmount: 'Total Inbound Amount ($)',
    totalInAmountTips: 'Total warehousing amount = warehousing amount * actual warehousing quantity; Warehousing amount: The cost price is used for purchasing warehousing, and the sales price set at the time of release is used for other document types',
    totalInAmount2: 'totalInAmount',
    costAmount: 'cost unit price ($)',
    costTotalAmount: 'Cost amount ($)',
    voidInventoryTips: 'You will not be able to continue editing after voiding, sure you want to void?',
    uploadNotEmptyTips: 'Added items are not empty and do not support bulk import',
    countNotEmptyOr0: 'The quantity cannot be empty or equal to 0',
    createOrderRemark: 'Order Remark',
    deleteFailureProd: 'Delete Failure Product',
    include: 'Include',
    failureCountTips: 'a commodity that is no longer valid',
    viewTransferOrder: 'View the transfer order',
    transferNumber: 'Transfer number',
    outStockPoints: 'Call out stock points',
    inStockPoints: 'Transfer to stock point',
    warehouse: 'warehouse',
    station: 'station',
    transferType: 'Transfer type',
    transferQuantity: 'Transfer quantity',
    transferDate: 'Transfer date',
    partiallyComplete: 'Partially complete',
    complete: 'Complete',
    voidInventory: 'Voided',
    details: 'details',
    warehoused: 'warehoused',
    transferVoidInventoryTip: 'Are you sure to cancel the transfer order?',
    tip: 'tip',
    confirm: 'confirm',
    cancel: 'cancel',
    success: 'operate successfully',
    createTransferOrder: 'New transfer order',
    warehouseTransferOrder: 'Transfer orders into the warehouse',
    pleaseSelect: 'Please select',
    pleaseSelectOutPoint: 'Please select the call out stock point',
    pleaseSelectInPoint: 'Please select the call in inventory point',
    prodInfo: 'Prod info',
    transferOutStorage: 'Transfer out of storage',
    transferInStorage: 'Transfer in of storage',
    product: 'Product',
    prodName: 'Product name',
    estimatedNumberIncomingStorage: 'Estimated number of incoming storage',
    actualNumberStorage: 'Actual number of storage',
    defaultWarehouseAddress: 'Please fill in the address information of the default warehouse',
    outStockPointEmptyTip: 'The call out stock point cannot be empty',
    inPointEmptyTip: 'The call-in inventory point cannot be empty',
    pleaseSelectPointTip: 'Please select the call-out point and call-in point first',
    transferNumberTip: 'The number of transfers cannot be 0',
    transferOrderProductTemplate: 'Transfer order product template',
    uploadTransferOrderProd: 'Import transfer order goods',
    uploadSuccess: 'Import successfully',
    commodityPrice: 'Commodity price',
    warehouseName: 'Warehouse name',
    inputWareHouseName: 'Enter warehouse name',
    warehouseNameEmpty: 'The warehouse name cannot be empty',
    warehouseType: 'Warehouse type',
    regionalWarehouse: 'Regional warehouse',
    defaultWarehouse: 'Default warehouse',
    custodian: 'custodian',
    inputCustodian: 'Import manager',
    search: 'search',
    reset: 'reset',
    newlyIncreased: 'Newly increased',
    warehouseAddress: 'Warehouse address',
    custodianPhone: 'Custodian telephone',
    edit: 'edit',
    merchandiseInventory: 'merchandise inventory',
    delete: 'delete',
    isDeleOper: 'Confirm the deletion operation',
    supplyArea: 'Supply area',
    setArea: 'Set area',
    InputCorrectPhone: 'Please enter the correct phone number',
    custodianEmptyTip: 'The manager cannot be empty',
    warehouseNameTip: 'Please fill in the warehouse name',
    custodianPhoneTip: 'Please fill in the phone number of the manager',
    custodianTip: 'Please fill in Manager',
    supplyAreaTip: 'Please select the delivery area',
    warehouseAddressTip: 'Please fill in the warehouse address',
    newWarehouse: 'New warehouse',
    editWarehouse: 'Edit warehouse',
    inventory: 'Inventory',
    currentWarehouseInventory: 'Current warehouse inventory',
    stockPointName: 'Stock point name',
    pleaseSelectStockPoint: 'Please select an inventory point',
    pleaseSelectStockPointType: 'Please select the inventory point type',
    viewStock: 'View inventory',
    warehouseInventory: 'warehouse inventory',
    storeInventory: 'Store inventory',
    stationNames: 'Store name',
    stockPointType: 'Inventory point type',
    pleaseSelectStockPointFirst: 'Please select the inventory point first',
    stockMold: 'Stock mold',
    sharedHeadquartersInventory: 'Shared headquarters inventory',
    stockMoldTip1: 'When the store generates sales, the inventory of goods in the default warehouse is deducted',
    independentSellingInventory: 'Independent selling inventory',
    stockMoldTip2: 'When a store generates sales, the inventory of goods in that store is deducted',
    switchTo: 'Switch to',
    sharedHeadquartersInventoryTip1: '1. After the switch, the saleable inventory of goods in the store will be synchronized to the inventory of the default warehouse (headquarters), and the inventory of the default warehouse (headquarters) will be deducted after the sale of goods;',
    sharedHeadquartersInventoryTip2: '2. After the switch, the inventory of the store cannot be modified separately;',
    sharedHeadquartersInventoryTip3: '3. Switching inventory mode does not take effect on goods that are in the process of instant killing;',
    sharedHeadquartersInventoryTip4: '4. Please choose a reasonable way to deal with the original inventory of the store and return it to the headquarters or clear the original inventory of the store.',
    independentSellingInventoryTip1: '1. After the switch, the available inventory of goods in the store will be determined by the actual inventory in the store, and the inventory in the store will be deducted after the sale of goods;',
    independentSellingInventoryTip2: '2. After switching, the store\'s inventory can be modified separately; ',
    independentSellingInventoryTip3: '3. Switching inventory mode does not take effect on goods that are in the process of instant killing;',
    originalStock: 'Original stock',
    originalStockSelect1: 'Store inventory synchronization back to default warehouse (headquarters)',
    originalStockSelect2: 'Store inventory cleared',
    transferWarehouse: 'Transfer to the warehouse',
    transferOutWarehouse: 'Transfer out of the warehouse',
    inventoryModeSwitchesOutInventory: 'Inventory mode switches out of inventory',
    inventoryModeSwitchesInInventory: 'Inventory mode switches into of inventory',
    secondKillReplenishmentStock: 'Second kill replenishment stock',
    voucherExpire: 'Voucher expire',
    code: 'code',
    inquire: 'inquire',
    prodStatus: 'Product Status',
    LowerShelf: 'Lower shelf',
    UpperShelf: 'Upper shelf',
    platformFrame: 'Platform frame',
    moderated: 'Moderated',
    noData: 'No data',
    switchInventoryMode: 'Switch inventory mode',
    numericOrAlphabetic: 'The account number can only be numeric or alphabetic',
    landlineNumberNoNUll: 'Please enter the correct landline number',
    QQNumberNoNull: 'Please enter the correct QQ number',
    microSignalNoNull: 'Please enter the correct micro-signal',
    productAvailable: 'Current product is no longer available',
    hellow: 'Hello',
    defaultWarehouseTip: 'Default warehouse default national supply, that is, all regions outside the regional warehouse can be supplied, without the need to set up the supply area',
    regionalWarehouseTip: 'Regional warehouse supply area does not overlap, and express orders within the region will be supplied by the warehouse, please rationalize the allocation of warehouse inventory'
  },
  combo: {
    name: 'Package name',
    comboStatus: 'Active status',
    type: 'Package Type',
    totalAmount: 'Package sales price',
    addCombo: 'New package',
    nameNotEmpty: 'Package name cannot be empty',
    editCombo: 'Edit package',
    fixedCombo: 'Fixed package',
    close: 'Close',
    open: 'Open',
    fixedComboTips: 'All items in the package are sold as a package, consumers need to buy the whole package as a set',
    matchingCombo: 'Matching package',
    matchingComboTips: 'The main product in the package must be selected, matching products can be set optional or unselectable',
    comboAmount: 'Package amount',
    comboProd: 'Package item',
    comboPic: 'Package main image',
    comboPicNotEmpty: 'Package main picture cannot be empty',
    mainProdErrorTips: 'Main product not empty',
    matchingProdErrorTips: 'The number of matching products is between 1 and 4, and there must be a matching product that is mandatory',
    comboMainProd: 'Package main product',
    comboMatchingProd: 'Package matching product',
    isRequired: 'If or not it is required',
    soldNum: 'Package sales',
    yes: 'Yes',
    no: 'No',
    matchingPrice: 'matching price',
    mainProdTips: 'The main product is a must-buy product, this product details page shows matching packages',
    matchingProdTips: 'Matching products users can choose to buy, up to 4 pieces',
    participateSpec: 'Participating specifications',
    leastNum: 'Starting quantity',
    maxChose: 'The maximum number of items that can be selected',
    editSpec: 'Select specifications',
    countProd: 'Pieces of goods',
    delete: 'Delete',
    mainProd: 'Main prod',
    matchingProd: 'Matching product',
    mainAndMatchProdNotDvyTips: 'This item does not have the same delivery method as the main item or mandatory item and cannot be purchased'
  },
  giveaway: {
    name: 'giveaway name',
    mainProdName: 'Main product name',
    mainProdInfo: 'mainProdInfo',
    giveawayStatus: 'Activity status',
    type: 'Giveaway type',
    totalAmount: 'Sales amount',
    addGiveaway: 'Add Giveaway',
    nameNotEmpty: 'Giveaway name cannot be empty',
    editGiveaway: 'Edit Giveaway',
    close: 'Close',
    open: 'Open',
    matchGiveaway: 'Matching Giveaways',
    giveawayAmount: 'Giveaway amount',
    giveawayProd: 'Giveaway item',
    giveawayPicNotEmpty: 'Giveaway main image cannot be empty',
    mainProdErrorTips: 'Main product cannot be empty',
    giveawayProdErrorTips: 'Giveaway product cannot be empty',
    giveawayMainProd: 'Main product',
    giveawayMatchingProd: 'GiveawayProd',
    isRequired: 'Whether it is mandatory',
    soldNum: 'Giveaway sales',
    yes: 'Yes',
    no: 'No',
    refundPrice: 'Aftermarket price',
    mainProdTips: 'The main product is a must buy product, this product details page display matching gifts',
    matchingProdTips: 'Matching products users can choose to buy, choose at least one with the main product can enjoy the discount price when buying, up to 4 pieces',
    participateSpec: 'Participating specifications',
    giveawayNum: 'Number of giveaways',
    maxChose: 'The maximum number of items you can choose',
    countProd: 'Pieces of product',
    countSku: 'Pieces of sku',
    delete: 'delete',
    addInKindProd: 'Add a physical product',
    giveawayLimitTips: "Set up to 15 sku's for giveaway items",
    refundPriceTips: "When a gift order is returned, the 'aftermarket price' will be deducted from the user's refund amount as the refund amount of the gift.",
    buyNum: 'Purchase quantity',
    buyNumTips: 'Gifts will only be given when the purchase quantity reaches the corresponding value',
    mainAndGiveProdNotDvyTips: 'This item does not have the same delivery method as the main item and will not be displayed on the event page'
  },
  pcdecorate: {
    decorateNavbar: {
      setTemplate: 'Set as the template',
      preview: 'Preview',
      templateInfo: 'Template information',
      addComponent: 'Add component',
      templateTip: 'Please enter your template description, copy no more than 100 words.',
      templateImg: 'Template thumbnail',
      templateRemark: 'Template remark',
      templateNameNotNull: 'The template name cannot be empty',
      moveTip: 'Tip: Long press to drag components'
    },
    componentTitle: {
      businessSigns: 'Brand',
      pictureBy: 'Picture',
      AuxiliaryInterval: 'Interval',
      floorTitle: 'Title',
      goodsList: 'GoodsList',
      UniversalHotspot: 'HotArea',
      storeList: 'GoodShops',
      subTitleCon: 'Good store, unlimited buyback',
      marketing: 'Marketing Activities',
      limitedKill: 'SecondKill',
      discountCoupon: 'DisCoupon',
      goodsModule1: 'GoodsOne',
      goodsModule2: 'GoodsTwo',
      goodsModule3: 'GoodsThree',
      goodsModule4: 'GoodsFour',
      goodsModule5: 'GoodsFive',
      component: 'Component',
      pageName: 'Page Name',
      pageBackground: 'Page Background',
      componentSort: 'Component Sort',
      clearComponent: 'Clear Component',
      pageTitle: 'pageName is cannot Empty',
      businessSignsTip1: 'Mall signs can only add one',
      businessSignsTip2: 'Mall signboard components can only be displayed on the head',
      componentTip: 'The current configuration component is empty and cannot be saved'
    },
    baseComponent: {
      base: 'Base Component',
      businessSigns: 'Brand',
      pictureBy: 'Picture',
      AuxiliaryInterval: 'Interval',
      floorTitle: 'Title',
      goodsList: 'GoodsList',
      UniversalHotspot: 'HotArea',
      storeList: 'GoodShops'
    },
    marketingActive: {
      marketing: 'Marketing Activity',
      limitedKill: 'SecondKill',
      discountCoupon: 'DisCoupon'
    },
    extendComponent: {
      extend: 'Extend Component',
      goodsModule1: 'GoodsOne',
      goodsModule2: 'GoodsTwo',
      goodsModule3: 'GoodsThree',
      goodsModule4: 'GoodsFour',
      goodsModule5: 'GoodsFive'
    },
    commonModal: {
      smallTitle: 'Micro Page Title',
      pageName: 'PageName',
      search: 'search',
      cancel: 'cancel',
      sure: 'submit',
      delText: 'Are you delete the component',
      component: 'component',
      storeName: 'Store Name',
      categoryOne: 'Select commodity categories',
      categoryTwo: 'Secondary commodity classification',
      categoryThree: 'Tertiary classification of commodities',
      goodsType: {
        normalgoods: 'normal goods',
        spellgroup: 'spell group',
        secondskill: 'second skill',
        integral: 'integral',
        package: 'package',
        activitiy: 'activitiy'
      },
      pageComponent: {
        index: 'Index Page',
        cart: 'Shopping Cart',
        order: 'Order Center',
        profile: 'Profile Center',
        collection: 'Collect Goods',
        recommand: 'News Recommand',
        limitTime: 'Limited Time',
        discount: 'Discount Page',
        skill: 'Skill Page',
        coupon: 'Coupon Center',
        IntegralMall: 'Integral Mall',
        memberCenter: 'Member Center',
        modalDistribution: 'Distribution Centre',
        modalLive: 'Broadcast center',
        memberIndex: 'Points Check-in'
      },
      pageTitle: 'Page Title',
      createTime: 'CreateTime',
      goods: 'Goods',
      category: 'Category',
      store: 'Store',
      page: 'Page',
      smallPage: 'Micro Page',
      customLink: 'Custom Link',
      customLinkTips: 'Please enter custom link',
      customLinkTips1: 'For external links, fill in the complete address. For internal links, write \'/\' + page path',
      customLinkOpen: 'Whether to open the browser in a new TAB',
      selectTypeTips: 'Please select the appropriate option'
    },
    shopMessage: {
      storeMessage: 'Shop Message',
      shopName: 'Shop Name',
      shopType: 'Shop Type',
      shopStatus: 'Shop Status',
      ordinaryShops: 'Ordinary Shop',
      preferShops: 'Prefer Shop',
      goods: 'Goods',
      goodsPrice: 'Goods Price',
      inventory: 'Inventory',
      goodsType: 'Goods Type',
      goodsCategory: 'GoodsCategory',
      prodNameTips: 'Please enter Goods',
      shopNameTips: 'Please enter Shop Name'
    },
    placeholder: {
      selectGoods: 'goods type',
      selectCategory: 'goods category',
      link: 'Select the jump path',
      pageName: 'please select the pageName'
    },
    storeSignate: {
      businessSigns: 'Business signs',
      customerService: 'Customer service',
      collectionShops: 'Collection shops',
      qualifications: 'qualifications',
      searchPlaceholder: 'do a search',
      search: 'Search',
      cart: 'Shopping Cart',
      style1: 'Style 1',
      style2: 'Style 2',
      goodsCategory: 'All Category',
      logo: 'logo',
      show: 'show',
      hide: 'hide',
      category: 'All Category',
      searchBar: 'Search Bar',
      navsBar: 'Nav Bar',
      navsBarTip: 'A maximum of seven items can be added to the navigation bar',
      navsBarTip1: 'Enter navigation name',
      addNavs: 'Add Navs',
      navBarsArea: 'Navs Bar Area',
      signboardBack: 'Sign Background',
      signTips: 'It is recommended to upload images with a size of 1920*170 pixels',
      addImg: 'Add Images',
      navs: 'navs',
      navsBack: 'Navigation background',
      path: 'path',
      warning1: 'A maximum of seven items can be added to the navigation bar',
      warning2: 'The navigation bar of business signage cannot be empty',
      warning3: 'Business sign configuration information cannot be empty'
    },
    pictureBy: {
      picsize: 'Image Size',
      picTips: 'It is recommended to upload a 1920*500 scale image as a top rotation image',
      highly: 'By highly',
      pagenation: 'pager',
      show: 'show',
      hide: 'hide',
      updatePic: 'Update Image',
      link: 'Link',
      addImg: 'Add Images',
      suggest: 'Recommended image size 1920*500',
      warning1: 'The configuration information of image rotation cannot be empty',
      warning2: 'The rotation height of picture rotation cannot be 0',
      warning3: 'Image rotation images cannot be empty',
      warning4: 'Image rotation image link cannot be empty!'
    },
    axinterval: {
      highly: 'Height',
      background: 'BgColor',
      reset: 'Reset',
      warning1: 'The height of the auxiliary space cannot be less than 5'
    },
    floorTitle: {
      mainTitCon: 'Selling is recommended',
      subTitCon: 'Selected goods are sold well in the mall',
      mainTitle: 'Main Title',
      mainTitleLabel: 'Main Title',
      subTitle: 'Sub Title',
      subTitleLabel: 'Sub Title',
      more: 'View More',
      show: 'show',
      hide: 'hide',
      mainTitleColor: 'Main Title Color',
      subTitleColor: 'Sub Title Color',
      moreTitleColor: 'View More Color',
      titleBgColor: 'Title Background Color',
      titleSize: 'Main Size',
      subTitleSize: 'Sub Size',
      marginTop: 'MTop',
      marginBottom: 'MBottom',
      warning1: 'The configuration information of the floor title cannot be empty',
      warning2: 'The main title of the floor title cannot be empty',
      warning3: 'The subtitle of the floor title cannot be empty',
      warning4: 'Jump paths cannot be empty'
    },
    goodsList: {
      listStyle: 'List Style',
      three: 'A line of three',
      four: 'A line of four',
      five: 'A line of five',
      showContent: 'Show Content',
      goodsName: 'Goods Name',
      goodsDescription: 'Goods Des',
      goodsPrice: 'Goods Price',
      goods: 'Goods',
      addgoods: 'Add Goods',
      price: 'price',
      warning1: 'The configuration information of the commodity list cannot be empty',
      warning2: 'Item list item cannot be empty'
    },
    univerHot: {
      picSize: 'Image Size',
      one: '1920',
      two: '1200',
      addImage: 'Add Image',
      jumpLinks: 'Jump links',
      imageTips: 'Add up to 10 hot zone ads',
      addHot: 'Add Hot',
      updateImage: 'Update Image',
      warning1: 'Universal hot zone configuration information cannot be empty',
      warning2: 'Universal hot zone background image cannot be empty',
      warning3: 'Hotzone jump link cannot be empty'
    },
    storeList: {
      storeTitle: 'Preferred good shops',
      storeSubTitle: 'sub Title',
      showSubTitle: 'Show SubTitle',
      show: 'show',
      hide: 'hide',
      mainTileColor: 'Main Title Color',
      subTitleColor: 'Sub Title Color',
      titleBgColor: 'Title Background Color',
      titleSize: 'Title Size',
      subTitleSize: 'Sub Size',
      marginTop: 'MTop',
      marginBottom: 'MBottom',
      addStore: 'Add Store',
      storeIn: 'come in',
      storeName: 'store name',
      storeAttention: 'atteniton',
      warning1: 'The store list configuration information cannot be empty',
      warning2: 'Store list subtitle cannot be empty',
      warning3: 'Store list The store cannot be empty'
    },
    limitedSkill: {
      mainTitle: 'Seconds kill zone',
      subTitle: 'Selected products, limited time to kill',
      warning1: 'Limit the second kill configuration information cannot be empty',
      warning2: 'A timed kill subtitle cannot be empty',
      warning3: 'The aerial shot with time limit cannot be blank'
    },
    disCountForm: {
      mainTitle: 'Discount coupon',
      subTitle: 'Good things recommend, together together',
      warning1: 'Discount group purchase configuration information cannot be empty',
      warning2: 'The title of the coupon cannot be empty',
      warning3: 'Discount group purchase goods can not be empty'
    },
    goodsModule1: {
      select: 'Select the corresponding column number to set details',
      mainTitle: 'Main Title',
      subTitle: 'Sub Title',
      mainTitleCon: 'Selling is recommended',
      subTitleCon: 'Sales lead the new trend of shopping',
      titleLink: 'Title Link',
      warning1: 'The configuration information of commodity module 1 cannot be empty',
      warning2: 'The main title of commodity module 1 cannot be empty',
      warning3: 'The subtitle of commodity module 1 cannot be empty',
      warning4: 'The title jump link of commodity module 1 cannot be empty',
      warning5: 'Items in commodity module 1 cannot be empty',
      addOtherGoods: 'A maximum of three can be added'
    },
    goodsModule2: {
      customTitle: 'Custom Title',
      placeholder: 'Please enter a custom title',
      titleLink: 'Title Link',
      addMainGoods: 'Add Goods(A maximum of one can be added)',
      addOtherGoods: 'Add Other Goods(A maximum of six can be added)',
      warning1: 'The configuration information of commodity module 2 cannot be empty',
      warning2: 'The title of commodity module 2 cannot be empty',
      warning3: 'The title link for goods module 2 cannot be empty',
      warning4: 'The main item in commodity module 2 cannot be empty!',
      warning5: 'Other items in commodity module 2 cannot be empty!'
    },
    goodsModule3: {
      addOtherGoods: 'Add Goods(A maximum of three can be added)',
      warning1: 'The configuration information of commodity module 3 cannot be empty',
      warning2: 'The title of commodity module 3 cannot be empty',
      warning3: 'The title link for goods module 3 cannot be empty',
      warning4: 'Items in commodity module 3 cannot be empty'
    },
    goodsModule4: {
      addBg: 'Add Bg',
      addTips: 'It is recommended to upload images with a size of 1200 x 300 pixels',
      picLink: 'Image Link',
      addOtherGoods: 'Add Goods(A maximum of four can be added)',
      warning1: 'The configuration information of commodity module 4 cannot be empty',
      warning2: 'Image links in commodity module 4 cannot be empty',
      warning3: 'Items in commodity module 4 cannot be empty',
      warning4: 'background in commodity module 4 cannot be empty'
    },
    goodsModule5: {
      addMain: 'Add Main Bg(232*320px images are recommended)',
      addImage: 'AddImage',
      link: 'Link',
      addGoods: 'Add Goods(A maximum of four can be added)',
      warning1: 'The configuration information of commodity module 5 cannot be empty',
      warning2: 'The main diagram of commodity module 5 cannot be empty',
      warning3: 'The main diagram link for commodity module 5 cannot be empty',
      warning4: 'Items in commodity module 5 cannot be empty'
    }
  },
  imMsgBizSkills: {
    noNull: ' cannot be empty',
    keyword: 'keyword',
    content: 'reply content',
    automaticReply: 'automatic reply',
    enablingStatus: 'enabling status:',
    enablingStatusTips: 'After activation, the system will automatically respond to the first message sent by the buyer user on the same day or when there is no customer service available online',
    enablingStatusTips2: 'After executing an auto-reply, a manual reply is made, and the next auto-reply will be made only when the user inquires again without customer service online',
    sendTextMessage: 'send text message',
    sendCommonQuestions: 'send common questions',
    addQuestion: 'add question',
    question: 'question',
    answer: 'answer:',
    addUpToTenItems: 'Add up to ten items',
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    maximumwordCount: 'The question should not exceed 20 words',
    maximumwordCount2: 'Here is a question with an answer of no more than 500 words',
    up: 'Up',
    down: 'Down',
    delete: 'Delete',
    canAlsoInput: 'You can also input',
    words: 'words',
    checkOneOptions: 'Please check one of the automatic reply options',
    questionEmpty: 'The automatic reply to the question is empty',
    messageEmpty: 'Automatic reply text message is empty',
    replyContentEmpty: 'The automatic reply content cannot be empty',
    confirm: 'confirm'
  },
  allinpay: {
    allinpayBusinessTip1: 'The platform opens the link payment, please resubmit the business information and financial information',
    allinpayBusinessTip2: 'Business information has been saved, please confirm and submit financial information',
    allinpayBusinessTip3: 'The current copy status is abnormal, you can refresh the copy status in the industry and commerce information',
    photocopyUpload: 'Photocopy upload',
    auditFailureTip1: 'Review failed, please upload again',
    auditFailureTip2: 'Corporate ID card audit failed, please upload again',
    auditFailureTip3: 'Business license review failed, please upload again',
    legalPhone: 'Legal phone',
    legalPhoneNotEmpty: 'The legal person\'s mobile phone number cannot be empty',
    updateTime: 'Update time',
    notSufficientFundsTip1: 'not sufficient funds',
    notSufficientFundsTip2: 'Yuan, can not withdraw this time',
    corporateAccount: 'Corporate account',
    companyAccount: 'Company account',
    captchaPrompt: 'The verification code will be sent to the mobile phone number reserved for the bank card. After sending the verification code, the withdrawal amount will be frozen and irrevocable. Please exercise caution',
    selectBankTip: 'Please select your bank card first!',
    missingInfoTip1: 'Whether the withdrawal agreement has been signed to the public account',
    missingInfoTip2: 'Whether a legal person withdrawal agreement has been signed',
    missingInfoTip3: 'Whether a mobile phone has been bound',
    missingInfoTip4: 'Whether a store member has been created',
    missingInfoTip5: 'Whether the enterprise information audit status is normal',
    missingInfoTip6: 'Whether the photocopy status is normal',
    confirmTip: 'Please confirm',
    mobile: 'mobile',
    untapePhone: 'Untape the phone number',
    beTiedPhone: 'be tied to a phone number',
    pleaseEnterPhone: 'Please enter your phone',
    getCode: 'Get code',
    code: 'verification code',
    pleaseEnterCode: 'Please enter the verification code',
    cancel: 'cancel',
    confirm: 'confirm',
    unbinding: 'remove binding',
    binding: 'Binding',
    InputCorrectPhone: 'Please enter the correct phone number',
    mobilePhoneNoNull: 'Phone number cannot be empty',
    capNoNull: 'Captcha cannot be empty',
    saveSuccess: 'Save success',
    accountType: 'account type',
    bankName: 'Bank name',
    pleaseSelect: 'Please select',
    branchName: 'Branch name',
    forExample: 'For example:',
    businessPublicAccounts: 'Business to public accounts',
    pleaseEntePublicAccount: 'Please enter a business-to-business account',
    paymentLineNumber: 'Payment line number',
    enterLineNumber: 'Please enter the payment line number',
    enterLineNumber2: 'Please fill in the correct payment line number',
    identityCard: 'identity card',
    pleaseEnterIdentityCard: 'Please enter your ID number',
    pleaseEnterCardPhone: 'Please enter the bank\'s mobile phone number',
    PleaseEnterCorporateIDCard: 'Please enter the correct corporate ID card',
    IDCardNumberEmpty: 'The ID number cannot be empty',
    pleaseEnterDigits: 'Please enter 12 digits',
    pleaseEnterBankName: 'Please fill in the bank name',
    companyNotEmpty: 'The enterprise to public account cannot be empty',
    corporateIdentityCard: 'Corporate identity card',
    corporateIdentityCardNotEmpty: 'The legal person ID card cannot be empty',
    pleaseEnterIDCard: 'Please enter the corporate ID number',
    pleaseEnterLegalPhone: 'Please enter the French phone number',
    added: 'Has been added ',
    added2: ' settlement account. A maximum of ',
    added3: ' settlement accounts can be added',
    added4: ' corporate accounts',
    addedPhone: ', binding mobile phone number:',
    signAgreement: 'Sign an agreement',
    clearingBankAccount: 'Clearing bank account',
    pleaseAddPublicAccount: 'Please add public account',
    submit: 'submit',
    branchTip: 'Please fill in the name of the branch',
    branchTip2: 'The branch name must be 2 to 20 characters in length',
    accNoNull: 'Account cannot be empty',
    signFail: 'Failure to sign',
    signSuccess: 'Sign success',
    submitSuccess: 'Submit success',
    applicationSuccess: 'Application submitted successfully!',
    signType: 'Type of agreement:',
    pleaseSelectCategory: 'Please select a contract category',
    privateContract: 'Private contract',
    contractWithPublic: 'Contract with the public',
    pleaseBindPhone: 'Please bind the phone number first',
    pleaseBindLegalAccNo: 'Please bind the corporate account first',
    processBindAccNoTip: 'At the time of opening, you can add up to 1 public account',
    pleaseEnterDigitLineNumber: 'Please enter a 12-digit payment line number',
    allinpayWithdrawalUnpaid: 'unpaid',
    allinpayWithdrawalApproved: 'application approved',
    allinpayWithdrawalSuccessful: 'Successful withdrawal',
    allinpayWithdrawalFailure: 'Withdrawal failure',
    dateloading: 'Data processing in progress...'
  },
  outletConfig: {
    addrManage: 'Manage Shipping Address',
    addrEmptyTips: 'Shipping address cannot be empty',
    shipperEmptyTips: 'Shipper cannot be empty',
    deliveryCompanyTypeEmptyTips: 'Courier company cannot be empty',
    oneSheet: 'Single-sheet Shipping Label',
    twoSheet: 'Double-sheet Shipping Label',
    deliveryCompanyType: 'Courier Company Type',
    shipper: 'Shipper',
    mobile: "Shipper's Phone Number",
    partnerId: 'Electronic Shipping Label Customer Account or Monthly Settlement Account',
    partnerKey: 'Electronic Shipping Label Password',
    net: 'Recipient Outlet Name',
    paperSize: 'Paper Size',
    isDefault: 'Default'
  },
  printer: {
    printerId: 'printer id',
    shopId: 'shop id',
    printerName: 'printer name',
    siid: 'siid',
    printerRemark: 'printer remark',
    isDefault: 'is default',
    deviceInfo: 'Device Connection Instructions',
    siidEmptyTips: 'The device code cannot be empty',
    siidRuleTips: 'The device code can only be English and numeric',
    printerNameEmptyTips: 'Please enter a printer name',
    printerNameEmptyTips2: 'The printer name cannot be empty',
    defaultTips: 'The platform has not yet opened the electronic manifest function, if you need to use please go to the platform background configuration.',
    specialDevices1: '1、Complete the printer installation, connect the power supply, turn on the switch;<br/>',
    specialDevices2: "2、Make sure your phone's Bluetooth is on;<br/>",
    specialDevices3: '3、Open wechat to scan the QR code at the bottom of the cloud printer and enter the "cloud printer Manager" mini program for networking operations.'
  },
  popupAd: {
    popupAd: 'popup ad',
    addPopup: 'add popup',
    popupName: 'popup name',
    popupState: 'popup state',
    notStarted: 'not started',
    release: 'release',
    finished: 'finished',
    triggerPage: 'trigger page',
    platHome: 'platform home page',
    memberCenter: 'Member Center',
    paySuccess: 'payment success',
    shopHome: 'Shop homepage',
    prodDetail: 'product details',
    selectProd: 'choose products',
    redirect: 'redirect',
    redirectTips: 'If the skip path is a coupon, the coupon collection page configured by the system is displayed',
    selectRedirect: 'Please select a jump path',
    popupImg: 'Popup picture',
    pushUser: 'Push user',
    allUsers: 'all users',
    FreeMember: 'Free membership',
    paidMember: 'premium paid member',
    shopCustomer: 'Store customer',
    shopMember: 'Shop member',
    memberLevel: 'grade of membership',
    pushTime: 'Push time',
    pushFrequency: 'Push frequency',
    pushFrequencyTips: 'When the jump path is a coupon, if the user has received the coupon, it will not be pushed anymore; if the user has not received the coupon, it will be pushed according to the set push frequency',
    onceForAll: 'Once for all',
    perEntry: 'Per entry',
    customFrequency: 'Custom frequency',
    every: 'every',
    pushOnce: 'push once',
    frequencyDayTips: 'Please fill in the correct frequency days',
    frequencyWeekTips: 'Please select frequency weeks',
    invalidConfirmTips: 'Confirm to perform the failure operation?',
    noNull: 'cannot be empty'
  },
  data: {
    integralPrice: 'integral price (math.)'
  },
  fitment: {
    maxCountTips: 'The current number of added products has reached the maximum number.',
    failImgTip: 'Image failed to load'
  },
  feature: {
    CategoryList: 'Product Category List',
    category: 'category',
    headerImage: 'classification header image',
    addImg: 'Add a background image',
    changeImg: 'Change image',
    jump: 'jump',
    chooseJump: 'Choose the page to jump to',
    subcategory: 'subcategory',
    title: 'title',
    addSubcategory: 'Add a subcategory',
    addMainCategory: 'Add a main category',
    widthPxTip1: 'Recommended width 765*282 pixels',
    widthPxTip2: 'Recommended width 150*150 pixels',
    addshopTips: 'Add products (up to 2)'
  },
  prod: {
    stockpilesTips: 'Tip: There is no common supply warehouse for the current associated product',
    OriginalSalesPrice: 'Original sales price',
    stockpilesTotal: 'Total common warehouse inventory',
    stocksEditContent: 'Combination item inventory is jointly determined by the inventory of the associated item and the number of pairings, and cannot be edited individually',
    voucherStocksTip: 'After the commodity is associated with the coupon, the inventory is determined by the number of coupons issued and cannot be edited separately',
    setStockQuantity: 'Stores sharing HQ inventory do not need to set additional item stock quantities',
    prodNameTip: 'Please complete the product name',
    correlationTip: 'Combined commodity specifications need to be associated with at least one commodity!',
    bindVoucherTip: 'There are specifications without bound coupons',
    bankName: 'Industrial and Commercial Bank of China Limited Beijing Cherry Orchard Sub-branch'
  },
  // 主动退款组件
  proactiveRefund: {
    proactiveRefund: 'Proactive refund',
    currentlyRefundable: 'Currently refundable',
    refundAmount: 'Refund amount',
    enterRefundAmount: 'Please enter the refund amount',
    fullRefund: 'Full refund',
    enterCorrectAmount: 'Please enter the correct amount',
    tipOne: 'The current after-sales application time for the order has expired, but the product is still within the validity period for verification',
    tipTwo: 'If the user needs after-sales service, they can negotiate with the buyer to initiate a refund before the order is settled',
    tipThree: 'After the order has been settled, please negotiate a refund method with the buyer on your own',
    endWriteOff: 'End write off',
    WriteOffHasEnded: 'Write-off has ended'
  },
  // 电子卡券相关
  voucher: {
    add: 'Add voucher',
    update: 'Update voucher',
    inputTips: 'Please fill in the name of the voucher',
    exportDate: 'Import Date',
    cardNum: 'Card number',
    cardPWD: 'Card Password',
    cardCode: 'Discount Code',
    uploadText: 'Upload coupon instructions',
    uploadTipsOne: '1、 Please download the coupon import template first and fill in the information according to the template. Failure to fill in the required information will result in coupon import failure',
    uploadTipsTwo: '2、 Please choose between. xlsx or. xls files. Only one file can be imported at a time, and it is recommended to import no more than 5000 pieces of data at a time',
    uploadTipsThree: '3、 It is not recommended to keep blank rows, as it may lead to data errors and prevent successful import',
    excelModel: 'Voucher Details Import Template',
    voucherDetail: 'Voucher details',
    chooseVoucher: 'Choose voucher',
    voucherList: 'Voucher List',
    preBindVoucher: 'The voucher originally bound to the product'
  },
  virtualLog: {
    code: 'Write off the voucher code',
    time: 'Write-off time',
    station: 'Write-off store',
    virtualLogXls: 'Write-off record.xlsx'
  }
}

export default en
