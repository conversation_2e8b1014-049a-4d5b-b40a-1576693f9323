// 计算sku库存
const setSkuStock = function (skuList, deliveryMode, shopDeliveryMode) {
  for (const sku of skuList) {
    if (sku.mold !== 0) {
      // 虚拟商品直接返回
      return skuList
    }
    // 根据是否自提配送以及sku区域库存列表，计算sku的库存
    // 秒杀sku库存
    if (sku.seckillStockPointSkuList) {
      sku.seckillWarehouseStock = 0
      sku.seckillStationStock = 0
      sku.seckillStocks = 0
      for (const stockPoint of sku.seckillStockPointSkuList) {
        stockPoint.seckillStocks = stockPoint.seckillStocks ? stockPoint.seckillStocks : 0
        // if (!canDelivery(stockPoint, deliveryMode, shopDeliveryMode)) {
        //   // 区域库存不符合销售条件
        //   continue
        // }
        if (stockPoint.stockPointType === 1) {
          // 仓库库存
          sku.seckillWarehouseStock += stockPoint.stock
          sku.seckillStocks += stockPoint.stock
        } else {
          // 门店库存
          sku.seckillStationStock += stockPoint.stock
          sku.seckillStocks += stockPoint.stock
        }
      }
    }
    // 普通sku库存计算
    if (sku.stockPointList) {
      sku.warehouseStock = 0
      sku.stationStock = 0
      sku.stocks = 0
      for (const stockPoint of sku.stockPointList) {
        stockPoint.stock = stockPoint.stock ? stockPoint.stock : 0
        // if (!canDelivery(stockPoint, deliveryMode, shopDeliveryMode)) {
        //   // 区域库存不符合销售条件
        //   console.log('continue')
        //   continue
        // }
        if (stockPoint.stockPointType === 1) {
          // 仓库库存
          sku.warehouseStock += stockPoint.stock
          sku.stocks += stockPoint.stock
        } else {
          // 门店库存
          sku.stationStock += stockPoint.stock
          sku.stocks += stockPoint.stock
        }
      }
    }
  }
  return skuList
}

/**
 * 判断区域库存是否可售卖
 * @param {*} stockPoint 区域库存数据
 * @param {*} deliveryMode 商品物流类型数据
 * @param {*} shopDeliveryMode 店铺共享门店的配送类型数据 hasUserPickUp / hasCityDelivery === true: 店铺存在营业状态，且支持自提、同城的共享门店
 * @returns true: 当前区域库存可售卖 false: 当前区域库存不可售卖
 */
// const canDelivery = function (stockPoint, deliveryMode, shopDeliveryMode) {
//   // debugger
//   if (stockPoint.stockPointType === 1) {
//     // 仓库库存
//     // 商品支持快递配送
//     if (deliveryMode.hasShopDelivery) {
//       console.log('canDelivery-1')
//       return true
//     }
//     // 商品不支持快配送，但商品支持自提、同城，并拥有营业状态的门店支持自提、同城
//     // (商品支持自提 + 存在营业且支持自提的共享门店) 或者 (商品支持同城 + 存在营业且支持同城的共享门店)
//     if (stockPoint.type === 0 && ((deliveryMode.hasUserPickUp && shopDeliveryMode.hasUserPickUp) || (deliveryMode.hasCityDelivery && shopDeliveryMode.hasCityDelivery))) {
//       console.log('canDelivery-2')
//       return true
//     }
//   } else {
//     // 门店库存
//     // 商品不支持自提和同城配送, 或者门店状态不是营业
//     if ((!deliveryMode.hasUserPickUp && !deliveryMode.hasCityDelivery) || stockPoint.status !== 1) {
//       console.log('canDelivery-3')
//       return false
//     }
//     // (商品支持自提 + 门店支持自提) 或者 (商品支持同城 + 门店支持同城)
//     if ((deliveryMode.hasUserPickUp && stockPoint.selfPickup === 1) || (deliveryMode.hasCityDelivery && stockPoint.sameCityDelivery === 1)) {
//       console.log('canDelivery-4')
//       return true
//     }
//   }
//   console.log('canDelivery-5')
//   return false
// }

export { setSkuStock }
