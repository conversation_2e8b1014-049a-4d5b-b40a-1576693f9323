import { defineStore } from 'pinia'
export const useAllinpayStore = defineStore('allinpay', {
  state: () => {
    return {
      paySettlementType: 0, // 启用通联支付 1：启用 0：未启用
      idCardCollectProcessStatus: -1, // 影印件采集审核状态 0.未上传 1.待审核 2.只有工商认证通过 3.只有法人信息通过 4.均审核通过
      shopStatus: null, // 店铺状态 -1:已删除 0: 停业中 1:营业中 2:平台下线 3:待审核 4:店铺申请中 5:申请失败 6:上线申请中
      companyInfoProcessStatus: -1 // 企业信息审核状态 0.未提交 1.待审核 2.审核成功 3.审核失败
    }
  },
  actions: {
    updatePaySettlementType (paySettlementType) {
      this.paySettlementType = paySettlementType
    },
    updateIdCardCollectProcessStatus (idCardCollectProcessStatus) {
      this.idCardCollectProcessStatus = idCardCollectProcessStatus
    },
    updateShopStatus (shopStatus) {
      this.shopStatus = shopStatus
    },
    updateCompanyInfoProcessStatus (companyInfoProcessStatus) {
      this.companyInfoProcessStatus = companyInfoProcessStatus
    },
    getPaySettlementType () {
      return new Promise((resolve) => {
      // 获取并更新通联支付配置信息
        http({
          url: http.adornUrl('/sys/config/paySettlementType'),
          method: 'GET',
          params: http.adornParams()
        }).then((res) => {
          if (res && res.data) {
            const data = res.data ? JSON.parse(res.data) : {}
            this.paySettlementType = data.paySettlementType ? data.paySettlementType : 0
            resolve(data.paySettlementType)
          }
        })
      })
    }
  }
})
