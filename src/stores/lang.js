import { defineStore } from 'pinia'

export const useLanguageStore = defineStore('lang', {
  state: () => {
    return {
      langList: [], // 语言列表
      langName: '', // 当前所选语言名称
      language: ''
    }
  },
  actions: {
    updateLang (langInfo) {
      const { langItemList, name, language } = langInfo
      const lang = localStorage.getItem('bbcLang')
      const langList = langItemList.filter(f => !f.hide) // 过滤掉隐藏的语言
      // 当前语言存在语言列表中
      let isExist = false
      // 是否已缓存语言
      if (lang) {
        for (const it of langList) {
          if (it.language === lang) {
            this.language = it.language
            this.langName = it.name
            isExist = true
            break
          }
        }
      }
      // 当前无缓存语言或当前缓存语言不在语言列表时
      if (!lang || !isExist) {
        this.langName = name
        this.language = language
        localStorage.setItem('bbcLang', language)
        // window.location.reload()
      }
      this.langList = langList
    },
    switchLang (langInfo) {
      const { name, language } = langInfo
      this.language = language
      this.langName = name
      localStorage.setItem('bbcLang', language)
      // 改变bbcRefreshFlag的值，使得监听器生效，刷新所有标签页
      localStorage.setItem('bbcRefreshFlag', 'true')
      // 刷新当前标签页
      location.reload()
    }
  }
})
