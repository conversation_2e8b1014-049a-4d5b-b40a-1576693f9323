import { defineStore } from 'pinia'
export const useUserStore = defineStore('user', {
  namespaced: true,
  // 数据持久化
  persist: true,
  state: () => {
    return {
      id: 0,
      name: '',
      mobile: '',
      userName: '',
      shopId: '',
      type: '',
      employeeId: '',
      shopStatus: -2,
      isPassShop: '',
      channelId: '',
      shopLogo: ''
    }
  },
  actions: {
    login () {
      return new Promise((resolve, reject) => {
        http({
          url: http.adornUrl('/shop/shopDetail/info'),
          method: 'get',
          params: http.adornParams()
        }).then(({ data }) => {
          this.name = data.shopName
          this.mobile = data.mobile
          this.type = data.type
          this.userName = data.userName
          this.shopStatus = data.shopStatus
          this.shopId = data.shopId
          this.employeeId = data.employeeId
          this.isPassShop = data.isPassShop
          this.shopLogo = data.shopLogo
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },
    getUserInfo () {
      return new Promise((resolve, reject) => {
        http({
          url: http.adornUrl('/shop/shopDetail/info'),
          method: 'get',
          params: http.adornParams()
        }).then(({ data }) => {
          this.name = data.shopName
          this.mobile = data.mobile
          this.type = data.type
          this.userName = data.userName
          this.shopStatus = data.shopStatus
          this.shopId = data.shopId
          this.employeeId = data.employeeId
          this.isPassShop = data.isPassShop
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },
    saveChannelId (ids) {
      this.channelId = ids
    },
    removeChannelId () {
      this.channelId = []
    }
  }
})
