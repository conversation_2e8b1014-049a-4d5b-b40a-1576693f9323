/**
 * 检测输入框为el-input的或是input
 * @param {*} node 节点信息
 * @returns 返回input的节点
 */
const onHandleNode = (node) => {
  if (/input/i.test(node.nodeName)) {
    return node
  }
  return node.querySelector('input')
}

/**
 * 处理指令修饰符所标识的规则
 * @param {*} rules 指令修饰符
 * @param {*} value 当前输入框的值
 * @returns 根据指定规则替换后的值
 */
let oldValue = '' // 输入框旧值
const onHandleValue = (rules, value) => {
  // 只允许输入数字
  if (rules.num) {
    return Number(value.replace(/[^\d]/g, ''))
  }
  // 允许输入小数，默认保留两位小数
  if (rules.float) {
    const priceArr = value.replace(/[^\d.]/g, '').split(/\.+/)
    if (priceArr?.length > 2) {
      return oldValue
    }
    const integer = priceArr[0].length > 1 ? priceArr[0].replace(/^0/, '') : priceArr[0]
    const digits = priceArr[1] ? `.${priceArr[1].substring(0, 2)}` : (priceArr[1] === '' ? '.' : '')
    return integer + digits
  }
  // 消除字符串两端空格，配合lazy使用达到输入完成后再消除空格
  if (rules.tirm) {
    return value.replace(/(^\s*)|(\s*$)/g, '')
  }
  return value.replace(/\s/g, '')
}

// 处理输入的内容
export const onHandleInput = {
  updated (el, { modifiers }) {
    try {
      const inpNode = onHandleNode(el)
      const newval = onHandleValue(modifiers, inpNode.value)
      oldValue = newval
      if (inpNode.value !== newval) {
        inpNode.value = newval
        inpNode.dispatchEvent(new Event(modifiers.lazy ? 'change' : 'input'))
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log(e)
    }
  },
  // 当输入框抬起回车时，使当前元素失焦
  mounted: function (el, binding) {
    // 监听键盘抬起事件
    el.addEventListener('keyup', function (e) {
      // 判断是否是回车键
      if (e.key === 'Enter') {
        // 调用方法
        if (typeof binding.value === 'function') {
          binding.value.call(this, e)
        }
        // 使当前元素失焦
        e.target.blur()
      }
    })
  },
  beforeDestroy: function (el, binding, vnode) {
    // 可选：如果元素可能被销毁，确保移除事件监听器
    vnode.context.$once('hook:beforeDestroy', function () {
      el.removeEventListener('keyup', el._keydownHandler)
    })
  }
}
