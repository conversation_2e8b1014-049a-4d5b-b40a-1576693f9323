<template>
  <el-config-provider
    :locale="language"
    namespace="el"
  >
    <router-view />
  </el-config-provider>
</template>

<script setup>
import zh from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'

const language = localStorage.getItem('bbcLang')?.indexOf('zh') !== -1 ? zh : en
onMounted(() => {
  useAllinpayStore().getPaySettlementType()
})

// (监听刷新参数变化，发生变化时刷新所有标签页)
window.addEventListener('storage', function (event) {
  if (event.key === 'bbcRefreshFlag' && event.newValue === 'true') {
    // 刷新其它标签页
    location.reload()
    // 清除刷新标记（可选，防止重复刷新)
    localStorage.removeItem('bbcRefreshFlag')
  }
})
</script>

<!-- eslint-disable-next-line vue-scoped-css/enforce-style-type -->
<style lang="scss">
@use '@/assets/app.scss';
</style>
