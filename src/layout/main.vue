<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="site-wrapper"
    :class="{ 'site-sidebar--fold': sidebarFold }"
    :element-loading-text="$t('homes.loading')"
  >
    <template v-if="!loading">
      <MainNavbar v-if="isDecorate" />
      <MainSidebar v-if="isDecorate" />
      <div
        :class="{'site-content__wrapper' : isDecorate}"
        :style="{ 'min-height': documentClientHeight + 'px' }"
      >
        <main-content />
      </div>
    </template>
  </div>
</template>

<script setup>
import MainNavbar from './main-navbar.vue'
import MainSidebar from './main-sidebar.vue'
import MainContent from './main-content.vue'

const loading = ref(true)
const userStore = useUserStore()
// 获取用户信息
onBeforeMount(async () => {
  await userStore.getUserInfo()
  loading.value = false
})

const route = useRoute()
const isDecorate = computed(() => {
  return !(route.path === '/fitment/feature/create/edit/index' || route.path === '/fitment/decorate/create/edit/index')
})

const commonStore = useCommonStore()
const documentClientHeight = computed(() => commonStore.documentClientHeight)
const sidebarFold = computed(() => commonStore.sidebarFold)
onMounted(() => {
  resetDocumentClientHeight()
})
const resetDocumentClientHeight = () => {
  commonStore.documentClientHeight = document.documentElement.clientHeight - 50
  window.onresize = () => {
    commonStore.documentClientHeight = document.documentElement.clientHeight - 50
  }
}
</script>
