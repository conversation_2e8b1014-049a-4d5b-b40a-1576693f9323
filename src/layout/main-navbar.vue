<template>
  <div>
    <nav class="site-navbar">
      <div
        class="site-navbar-header"
        :style="{ 'margin-right': sidebarFold ? 0 : '20px' }"
      >
        <img
          v-if="configuration.bsTopBarIcon"
          class="menu-image-logo"
          :src="checkFileUrl(configuration.bsTopBarIcon)"
          alt="logo"
        >
        <span
          v-if="!sidebarFold"
          class="site-navbar-lg"
        >
          {{ configuration.bsMenuTitleOpen }}
        </span>
        <span
          v-else
          class="site-navbar-mini"
          :style="fontCloseSize"
        >
          {{ configuration.bsMenuTitleClose }}
        </span>
      </div>
      <div class="site-navbar-content">
        <div class="navbar-content-left">
          <svg-icon
            class="left-item"
            icon-class="icon-zhedie"
            @click="setSidebarFold"
          />
        </div>
        <div class="navbar-content-right">
          <div
            v-if="isAuth('message:message:view')"
            class="content-right-item service-box"
            @click="openUrl"
          >
            <span
              :class="['service-icon',showHidden?'show':'hidden']"
              style="padding-right:5px"
            >
              <img
                style="width: 18px;height: 18px;"
                src="../assets/img/msg.png"
                alt=""
              >
              <i
                v-if="message"
                class="message-radio"
              />
            </span>

            <span
              class="el-dropdown-link"
            >{{
              $t('home.customerService')
            }}</span>
          </div>
          <el-dropdown
            class="content-right-item"
            @command="switchLang"
          >
            <span class="el-dropdown-link lang-name">
              {{ langName }}
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(langItem,inx) in langList"
                  :key="inx"
                  :command="langItem.language"
                >
                  {{ langItem.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown
            class="content-right-item"
            :show-timeout="0"
            placement="bottom"
          >
            <span class="el-dropdown-link">{{ userName }}</span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="updatePasswordHandle">
                  {{
                    $t('homes.updatePwd')
                  }}
                </el-dropdown-item>
                <el-dropdown-item @click="logoutHandle">
                  {{ $t('homes.exit') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <!-- 弹窗, 修改密码 -->
      <UpdatePassword
        v-if="updatePassowrdVisible"
        ref="updatePassowrdRef"
      />
    </nav>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import cookie from 'vue-cookies'
import UpdatePassword from './main-navbar-update-password.vue'
import { useLanguageStore } from '@/stores/lang.js'
import { WKSDK, ConnectStatus } from 'wukongimjssdk'
import { Convert } from '@/views/common/message-box/components/Conversation/convert.js'
import { isAuth } from '@/utils/index.js'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const userName = computed(() => {
  return userStore.name
})

const configuration = ref({
  bsLoginLogoImg: null,
  bsLoginBgImg: null,
  bsCopyright: null,
  bsTitleContent: null,
  bsTitleImg: null,
  bsMenuTitleOpen: null,
  bsMenuTitleClose: null
})

const webConfigStore = useWebConfigStore()
const fontOpenSize = reactive({
  fontSize: '16px'
})
const fontCloseSize = reactive({
  fontSize: '16px'
})
const updateWebConfigData = () => {
  http({
    url: http.adornUrl('/sys/webConfig/getActivity'),
    method: 'get'
  }).then(({ data }) => {
    data = formatConfigInfo(data)
    webConfigStore.addData(data)
    configuration.value = data
    if (data.bsMenuTitleOpen.length >= 17) {
      fontOpenSize.fontSize = '16px'
    }
    if (data.bsMenuTitleClose.length >= 6) {
      fontCloseSize.fontSize = '18px'
    }
  })
}

const langStore = useLanguageStore()
const langList = computed(() => langStore.langList)
const langName = computed(() => langStore.langName)
const switchLang = lang => {
  const langInfo = langList.value.find(f => f.language === lang)
  if (langInfo) {
    langStore.switchLang(langInfo)
  }
}

// 通知权限判断
const notification = ref(false)

onMounted(() => {
  updateWebConfigData()
  let notifFlag = true
  // 获取国际化语言列表
  getLangList()
  if (window.Notification) {
    // 浏览器通知--window.Notification
    if (Notification.permission === 'granted') {
      notification.value = false
      notifFlag = false
    } else if (Notification.permission !== 'denied') {
      notification.value = false
      Notification.requestPermission()
    }
  } else {
    notification.value = true
  }
  if (notifFlag) {
    notification.value = true
  }
  // 判断是否有查看消息权限
  if (isAuth('message:message:view')) {
    getWuKong()
    WKSDK.shared().connectManager.addConnectStatusListener(connectStatusListener) // 监听连接状态
    WKSDK.shared().chatManager.addCMDListener(cmdListener) // 监听cmd消息
  }
})
let messageListener
const getWuKong = () => {
  http.post('/shop/wuKongIm/registerOrLogin').then(({ data }) => {
    // 认证信息
    const config = WKSDK.shared().config
    config.addr = import.meta.env.VITE_APP_WS_IM_API
    config.uid = data.uid // 用户uid（需要在悟空通讯端注册过）
    config.token = data.token// 用户token （需要在悟空通讯端注册过）
    WKSDK.shared().config = config
    WKSDK.shared().connect()
    const isJSON = (str) => {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
    }
    // 监听消息
    messageListener = (msg) => {
      if (isJSON(msg.content.text)) {
        msg.content.text = JSON.parse(msg.content.text)
      }
      const ids = +cookie.get('bbcVsChannelIds')
      if ((!msg.send || msg.content.text.sender === 1) && (ids === 0 || ids === null)) {
        message.value = 1
        popNotice($t('home.notification'), $t('home.haveANewUnreadMessage')) // 启用通知
        newMessage.value = true
        clearInterval(messageReminding.value)
        if (notification.value) {
          showMessage()
        }
        messageRemindingFn()
      }
    }
    WKSDK.shared().chatManager.addMessageListener(messageListener)
  })
}
let messageCount = 0
const connectStatusListener = async (status) => {
  if (status === ConnectStatus.Connected) {
    const remoteConversations = await WKSDK.shared().conversationManager.sync() // 同步最近会话列表
    if (remoteConversations && remoteConversations.length > 0) {
      messageCount = WKSDK.shared().conversationManager.getAllUnreadCount()
      if (messageCount > 0) {
        message.value = 1
        messageRemindingFn()
        if (notification.value) {
          showMessage()
        }
      }
    }
  }
}
// 同步自己业务端的最近会话列表
WKSDK.shared().config.provider.syncConversationsCallback = async () => {
  const data = await sendDelayedRequest()
  return data
}
const sendDelayedRequest = () => {
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      try {
        const response = await http({
          url: http.adornUrl('/shop/wuKongIm/conversation/sync/page'),
          method: 'get',
          params: {
            version: 0,
            current: 1,
            size: 20
          }
        })
        const data = response.data.records
        const conversationList = data
        const resultConversations = []

        if (conversationList) {
          conversationList.forEach((v) => {
            const conversation = Convert.toConversation(v)
            conversation.lastMessage.content.text = JSON.parse(conversation.lastMessage?.content.text)
            resultConversations.push(conversation)
          })
        }
        resolve(resultConversations)
      } catch (error) {
        reject(error)
      }
    }, 100)
  })
}
onUnmounted(() => {
  WKSDK.shared().chatManager.removeMessageListener(messageListener)
  WKSDK.shared().connectManager.removeConnectStatusListener(connectStatusListener)
  WKSDK.shared().chatManager.removeCMDListener(cmdListener)
  WKSDK.shared().disconnect()
})

const newMessage = ref(false)
const notificationDebounce = ref(1)
const messageReminding = ref(null)
const message = ref(0)

// 获取国际化语言列表
const getLangList = () => {
  http({
    url: http.adornUrl('/sys/lang'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    langStore.updateLang(data)
  })
}

const commonStore = useCommonStore()
const sidebarFold = computed(() => commonStore.sidebarFold)
const setSidebarFold = () => {
  const len = commonStore.selectMenu.length
  const flag = sessionStorage.getItem('bbcIsExpand')
  if ((route.path === '/home' || len === 1) && flag === '0') {
    commonStore.updateSidebarFold(true)
  } else {
    const foldFlag = sidebarFold.value
    commonStore.updateSidebarFold(!foldFlag)
  }
}
const logoutHandle = () => {
  ElMessageBox.confirm($t('homes.isExit'), $t('text.tips'), {
    confirmButtonText: $t('homes.yes'),
    cancelButtonText: $t('homes.no'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/logOut'),
      method: 'post',
      data: http.adornData()
    }).then(() => {
      clearLoginInfo()
      WKSDK.shared().disconnect()
      userStore.removeChannelId()
      router.push({ name: 'login' })
    })
  })
}
const cmdListener = (msg) => {
  if (msg.content.contentObj.cmd === 'logOut' && msg.content.contentObj.param === cookie.get('bbcAuthorization_vs')) {
    WKSDK.shared().disconnect()
  }
}
const updatePassowrdVisible = ref(false)
const updatePassowrdRef = ref(null)
// 修改密码
const updatePasswordHandle = () => {
  updatePassowrdVisible.value = true
  nextTick(() => {
    updatePassowrdRef.value?.init()
  })
}

const showMessage = Debounce(function () {
  ElMessage({
    message: $t('publics.newUnreadMessages'),
    offset: 10,
    duration: 1500
  })
}, 1800)
const showHidden = ref(true)
const messageRemindingFn = () => {
  clearInterval(messageReminding.value)
  messageReminding.value = setInterval(() => {
    // 如果没有获取焦点就判断名称是否包含未读消息
    if (newMessage.value) {
      // 如果包含就显示为空
      newMessage.value = false
      showHidden.value = false
    } else {
      // 否则显示未读消息，间隔0.5秒实现闪烁
      newMessage.value = true
      showHidden.value = true
    }
  }, 500)
}
const popNotice = (user, content) => {
  if (Notification.permission === 'granted') {
    const notification = new Notification(user, {
      body: content
    })
    notification.onclick = function () {
      nextTick(() => {
        setTimeout(() => {
          notificationDebounce.value = 1
          message.value = null
          // 获取客服页路径
          const url = router.resolve({
            name: 'message-box'
          })
          // 打开新窗口
          window.open(url.href)
          // 具体操作
        }, 500)
      })
      // 可直接打开通知notification相关联的tab窗
      window.focus()
      notification.close()
    }
    notification.onshow = function () {
      setTimeout(() => {
        notificationDebounce.value = 1
        notification.close.bind(notification)
      }, 3000)
    }
  }
}

const openUrl = () => {
  // 获取客服页路径
  const url = router.resolve({
    name: 'message-box'
  })
  // 打开新窗口
  window.open(url.href)
  message.value = null
  showHidden.value = true
  clearInterval(messageReminding.value)
}
</script>

<style lang="scss" scoped>
.site-navbar {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  color: #333333;
  border-bottom: 1px solid #ebedf0;
  .site-navbar-header {
    display: flex;
    align-items: center;
    margin-left: 20px;
    color: #333;
    font-weight: 700;
    height: 50px;
    line-height: 50px;
    .site-navbar-lg {
      font-size: 16px;
      word-break: break-all;
      word-wrap: break-word;
    }
    .site-navbar-lg,
    .site-navbar-mini {
      margin: 0 5px;
    }
  }
  .site-navbar-content {
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    font-size: 18px;
    align-items: center;
    .navbar-content-left {
      flex: 1;
      .left-item {
        cursor: pointer;
      }
    }

    .navbar-content-right {
      display: flex;
      .content-right-item {
        line-height: 20px;
        font-size: 14px;
        margin-right: 25px;
        cursor: pointer;
        &:last-child {
          margin-right: 0;
        }
        &.service-box{
          display: flex;
          align-items: flex-end;
        }
        .el-dropdown-link {
          color: #333;
          &.lang-name{
            display: flex;
            align-items: center;
          }
        }
        .el-dropdown-link:focus {
          outline: none;
        }
        .service-icon{
          position: relative;
          transition: .1s;
          &.show{
            opacity: 1;
          }
          &.hidden{
            opacity: 0;
          }
          .message-radio {
            border-radius: 50%;
            width: 5px;
            height: 5px;
            background-color: red;
            display: inline-block;
            position: absolute;
            top: 2px;
            left: 12px;
          }
        }
      }
    }
  }
}
.menu-image-logo {
  object-fit: contain;
  height: 18px;
  width: 59px;
  margin-right: 10px;
}
</style>
