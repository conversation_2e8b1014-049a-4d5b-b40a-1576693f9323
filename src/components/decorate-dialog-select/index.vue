<template>
  <div class="dialog-message-content">
    <dialog-component
      :dialog-visible="dialogVisible"
      :dialog-width="'914px'"
      @handle-close="handleClose"
      @closed="activeName=''"
    >
      <template #title>
        <div>
          <el-tabs
            v-model="activeName"
            class="dialog-tab-pc"
          >
            <el-tab-pane
              v-for="item in switchTitleData"
              :key="item.id"
              :label="item.title"
              :name="item.id"
            >
              <component
                :is="com[item.components]"
                ref="tabbarRef"
                :active-name="activeName"
                :device-type="deviceType"
                :goods-type="goodsType"
                :is-mulilt="isMulilt"
                :is-select-all-show="isSelectAllShow"
                :goods-number="goodsNumber"
                :prod-type="prodType"
                :echo-data-list="echoDataList"
                :data-url="dataUrl"
                :custom-link-arr="customLinkArr"
                @handle-goods-select="handleGoodsSelect"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>
      <template #footers>
        <el-button
          @click="handleClose"
        >
          {{ $t('pcdecorate.commonModal.cancel') }}
        </el-button>
        <el-button

          type="primary"
          @click="handleSure"
        >
          {{ $t('pcdecorate.commonModal.sure') }}
        </el-button>
      </template>
    </dialog-component>
  </div>
</template>

<script setup>
import goodsComponent from './components/goods-component/index.vue' // 商品组件
import categoryComponent from './components/category-component/index.vue' // 分类组件
import storeComponent from './components/store-component/index.vue' // 店铺组件
import pageComponent from './components/page-component/index.vue' // 页面组件
import smallPageComponent from './components/small-page-component/index.vue' // 微页面组件
import customComponent from './components/custom-component/index.vue' // 自定义链接
import couponSelect from './components/coupon-select/index.vue' // 优惠券选择组件
import { ElMessage } from 'element-plus'

const props = defineProps({
  dialogVisible: { // 弹窗是否显示
    type: Boolean,
    default: () => false
  },
  currentSelectType: { // 当前可选择的类型
    type: Array,
    default: () => []
  },
  goodsType: { // 查询的商品类型
    type: [String, Number],
    default: () => ''
  },
  isMulilt: { // 是否支持多选
    type: Boolean,
    default: () => false
  },
  goodsNumber: { // 限制添加的个数
    type: [String, Number],
    default: () => 0
  },
  deviceType: { // 弹窗类型 pc端： pc , 移动:mobile
    type: String,
    default: () => 'pc'
  },
  prodType: { // 类型
    type: Number,
    default: null
  },
  dataUrl: { // 活动的url
    type: String,
    default: ''
  },
  echoDataList: { // 回显数据
    type: Array,
    default: () => []
  },
  customLinkArr: { // 自定义链接回显数据
    type: Object,
    default: () => {}
  },
  isSelectAllShow: { // 是否显示全选
    type: Boolean,
    default: () => false
  }
})
const emit = defineEmits(['handleClose', 'handleDialogSubmit'])

const com = shallowReactive({
  goodsComponent,
  categoryComponent,
  storeComponent,
  pageComponent,
  smallPageComponent,
  customComponent,
  couponSelect
})

const activeName = ref('') // 当前激活哪个tabbar
const switchTitleData = ref([]) // 弹窗的列表
const titleArr = [ // 对应的标题组件
  { title: $t('pcdecorate.commonModal.goods'), id: '1', components: 'goodsComponent' },
  { title: $t('pcdecorate.commonModal.category'), id: '2', components: 'categoryComponent' },
  { title: $t('pcdecorate.commonModal.store'), id: '3', components: 'storeComponent' },
  { title: $t('pcdecorate.commonModal.page'), id: '4', components: 'pageComponent' },
  { title: $t('pcdecorate.commonModal.smallPage'), id: '5', components: 'smallPageComponent' },
  { title: $t('pcdecorate.commonModal.customLink'), id: '6', components: 'customComponent' },
  { title: $t('user.coupons'), id: '7', components: 'couponSelect' }
]
let currentSelectedObj = { // 选择信息
  goodsItem: {}, // 选中的商品
  categoryItem: {}, // 选中的分类
  storeItem: {}, // 选中的店铺
  pageItem: {}, // 选中的页面
  smallPageItem: {}, // 选中的微页面
  customLink: '' // 自定义链接
}

const tabbarRef = ref(null)
watch(() => props.dialogVisible, (val) => {
  if (val) {
    const newArr = []
    if (props.currentSelectType && Array.isArray(props.currentSelectType)) {
      props.currentSelectType.forEach(item => {
        titleArr.forEach(ele => {
          if (Number(item) === Number(ele.id)) {
            newArr.push(ele)
            if (ele.components === 'goodsComponent') {
              tabbarRef.value?.[0].onClearSelect()
            }
          }
        })
      })
      switchTitleData.value = newArr
    }
    nextTick(() => {
      if (props.customLinkArr && props.customLinkArr.type != '') { // 判断如果在操作热区
        activeName.value = props.customLinkArr.type
      } else {
        activeName.value = switchTitleData.value[0].id || '1' // 进来默认激活第一个
      }
    })
    currentSelectedObj = {
      goodsItem: {}, // 选中的商品
      categoryItem: {}, // 选中的分类
      storeItem: {}, // 选中的店铺
      pageItem: {}, // 选中的页面
      smallPageItem: {}, // 选中的微页面
      customLink: '', // 自定义链接
      couponsItem: {}// 优惠券选择
    }
  }
})

// 弹窗关闭
const handleClose = () => {
  emit('handleClose')
}
// 选中的数据
const handleGoodsSelect = ({ type, value }) => {
  currentSelectedObj[type] = value
}
// 弹窗确定
const handleSure = () => {
  if (props.currentSelectType.indexOf(Number(activeName.value)) == -1) {
    return ElMessage.warning($t('components.selecTips'))
  }
  if (handleIsValidate(activeName.value)) {
    if (activeName.value === '6') {
      return ElMessage.warning($t('pcdecorate.commonModal.customLinkTips'))
    } else {
      return ElMessage.warning($t('pcdecorate.commonModal.selectTypeTips'))
    }
  }
  emit('handleDialogSubmit', { type: activeName.value, value: currentSelectedObj })
  setTimeout(() => {
    activeName.value = ''
  }, 500)
}
// 弹窗验证
const handleIsValidate = (active) => {
  switch (active) {
    case '1': // 商品
      return isDataValidate(currentSelectedObj.goodsItem)
    case '2': // 分类
      return isDataValidate(currentSelectedObj.categoryItem)
    case '3': // 店铺
      return isDataValidate(currentSelectedObj.storeItem)
    case '4': // 页面
      return isDataValidate(currentSelectedObj.pageItem)
    case '5': // 微页面
      return isDataValidate(currentSelectedObj.smallPageItem)
    case '6': // 自定义链接
      return isDataValidate(currentSelectedObj.customLink)
    case '7': // 优惠券
      return isDataValidate(currentSelectedObj.couponsItem)
    default:
      break
  }
}
// 判断是否符合
const isDataValidate = (val) => {
  return JSON.stringify(val) === '{}' || val == undefined || val == null || val == ''
}

</script>

<style lang="scss" scoped>
:deep(.dialog-tab-pc) {
  width: 100%;
  .el-tabs__header {
    margin: 0 0 20px;
    .el-tabs__nav-scroll {
      .el-tabs__nav {
        margin-left: 40px;
      }
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
    }
  }
  .el-tabs__item {
    padding: 0 31px;
  }
}
</style>
