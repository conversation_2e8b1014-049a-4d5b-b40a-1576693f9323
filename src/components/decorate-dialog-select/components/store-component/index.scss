.component-store {
  .category-form {
    .searchbtn {
      width: 68px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #155BD4;
      border-radius: 2px;
      margin: 4px 0 0 10px;
    }
  }

  .tables {
    width: 93%;
    margin: 0 auto 20px;

    .shopName-content {
      display: flex;
      align-items: center;

      .el-image {
        width: 48px;
        height: 48px;
        margin-right: 15px;
      }
    }
  }

  .noraml-store {
    display: flex;
    justify-content: center;

    span {
      min-width: 66px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333333;
      font-size: 12px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 3px;
    }
  }

  .pre-store {
    display: flex;
    justify-content: center;

    span {
      min-width: 66px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #155BD4;
      color: #155BD4;
      font-size: 12px;
      border-radius: 3px;
    }
  }

  .tag-text {
    height: 24px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    display: inline-block;
    font-size: 12px;
    padding: 0 8px;
    line-height: 24px;

    &.active {
      border: 1px solid #155BD4;
      color: #155BD4;
    }
  }
}

.component-store {
  min-height: 450px;
  max-height: 450px;
  height: 450px;
  overflow-y: auto;

  .el-input__inner {
    height: 32px;
  }

  .table-header {
    &:nth-child(1) {
      .cell {
        display: none;
      }
    }
  }
}

.tables-checkedbox {
  .el-checkbox {
    .el-checkbox__inner {
      position: relative;

      &:after {
        border: 0;
        position: absolute;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #fff;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .el-checkbox__inner {
      border-radius: 50%;
    }

    &.is-checked {
      .el-checkbox__inner {
        position: relative;

        &:after {
          border: 0;
          position: absolute;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #fff;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
}
