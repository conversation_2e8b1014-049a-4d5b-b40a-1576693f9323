<template>
  <div class="category-modal-content component-store">
    <div class="category-form">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="searchFormRef"
        inline
        :model="searchForm"
        label-width="108px"
        @submit.prevent
      >
        <el-form-item :label="$t('pcdecorate.commonModal.storeName')+'：'">
          <el-input
            v-model.trim="searchForm.shopName"
            style="width: 200px"
            clearable
            :placeholder="$t('pcdecorate.shopMessage.shopNameTips')"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="searchbtn"
            @click="handleSearch"
          >
            {{ $t('pcdecorate.commonModal.search') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tables">
      <el-table
        ref="multipTableRef"
        v-loading="tableLoading"
        style="width: 100%"
        header-cell-class-name="table-header"
        row-class-name="table-row-low"
        row-key="shopId"
        :data="tableList"
        height="320"
        :class="{'tables-checkedbox': !isMulilt}"
        @selection-change="selectChange"
      >
        <el-table-column
          type="selection"
          align="center"
          width="55"
        />
        <el-table-column
          prop="shopName"
          :label="$t('pcdecorate.shopMessage.storeMessage')"
          align="left"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div class="shopName-content">
              <el-image
                :src="checkFileUrl(scope.row.shopLogo)"
                fit="fill"
              />
              <span style="width: calc(100% - 65px);overflow:hidden;text-overflow: ellipsis;white-space: nowrap">{{ scope.row.shopName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="type"
          width="200px"
          :label="$t('pcdecorate.shopMessage.shopType')"
          align="center"
        >
          <template #default="scope">
            <div
              v-if="scope.row.type === 0"
              class="noraml-store"
            >
              <span>{{ storeType(scope.row.type) }}</span>
            </div>
            <div
              v-else-if="scope.row.type === 1"
              class="pre-store"
            >
              <span>{{ storeType(scope.row.type) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="shopStatus"
          width="150"
          :label="$t('pcdecorate.shopMessage.shopStatus')"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              v-if="scope.row.shopStatus === -1"
              class="tag-text"
            >{{ $t('shop.nonactivated') }}</span>
            <span
              v-else-if="scope.row.shopStatus === 0"
              class="tag-text"
            >{{ $t('shop.closed') }}</span>
            <span
              v-else-if="scope.row.shopStatus === 1"
              class="tag-text active"
            >{{ $t('shop.inOperation') }}</span>
            <span
              v-else-if="scope.row.shopStatus === 2"
              class="tag-text"
            >
              {{ $t('groups.offlineViolation') }}
            </span>
            <span
              v-else-if="scope.row.shopStatus === 4"
              class="tag-text"
            >
              {{ $t('distributionMsg.applying') }}
            </span>
            <span
              v-else-if="scope.row.shopStatus === 3 || scope.row.shopStatus === 5"
              class="tag-text"
            >
              {{ $t('product.pendingReview') }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        style="margin-top: 12px; text-align: right;"
        :current-page="perProps.page"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="perProps.perPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="perProps.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  activeName: {
    type: String,
    default: () => ''
  },
  isMulilt: { // 是否支持多选
    type: Boolean,
    default: () => false
  }
})
const emit = defineEmits(['handleGoodsSelect', 'handleGoodsSelect'])

const searchForm = reactive({})
const tableList = ref([]) // 店铺列表
const perProps = { // 分页
  page: 1,
  perPage: 10,
  total: 0
}

const storeType = computed(() => {
  return (type) => {
    if (type === 0) {
      return $t('pcdecorate.shopMessage.ordinaryShops')
    } else if (type === 1) {
      return $t('pcdecorate.shopMessage.preferShops')
    } else {
      return ''
    }
  }
})

watch(() => props.activeName, (val) => {
  if (val === '3') {
    searchForm.shopName = ''
    // 获取店铺列表
    getStoreList()
  }
})

let multipleSelection = [] // 表格选中的数据
const multipTableRef = ref(null)
// 选中
const selectChange = (rows) => {
  multipleSelection = rows
  if (props.isMulilt) { // 支持多选
    emit('handleGoodsSelect', { type: 'storeItem', value: multipleSelection })
  } else { // 仅支持单选
    if (multipleSelection.length === 1 || multipleSelection.length === 0) {
      emit('handleGoodsSelect', { type: 'storeItem', value: multipleSelection[0] })
    } else if (multipleSelection.length > 1) {
      multipTableRef.value?.toggleRowSelection(multipleSelection[0])
    }
  }
}

// 搜索
const handleSearch = () => {
  getStoreList('search')
}

const tableLoading = ref(false) // 表格loading
// 获取店铺列表
const getStoreList = (val) => {
  if (val === 'search') {
    perProps.page = 1
  }
  const { page, perPage } = perProps
  tableLoading.value = true
  http({
    url: http.adornUrl('/platform/shopDetail/searchShops'),
    method: 'get',
    params: http.adornParams({
      current: page,
      size: perPage,
      shopName: searchForm.shopName
    })
  }).then(({ data }) => {
    tableList.value = data.records
    perProps.total = data.total
    tableLoading.value = false
  }).catch(() => {
    tableLoading.value = false
  })
}

// 分页每页显示多少条
const onPageSizeChange = (val) => {
  perProps.perPage = val
  getStoreList()
}

// 分页显示第几页
const onPageChange = (val) => {
  perProps.page = val
  getStoreList()
}

</script>
<style lang="scss" scoped>
@use "index";
</style>
