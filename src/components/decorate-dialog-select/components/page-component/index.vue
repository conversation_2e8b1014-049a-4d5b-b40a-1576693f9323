<template>
  <div class="page-modal-content component-page-component">
    <div class="page-contents">
      <div
        v-for="(item, index) in currentPageList"
        :key="index"
        :class="{'page-items': true, 'active': currentIndex === index}"
        @click="handleSelectPage(item, index)"
      >
        <img
          :src="item.img"
          alt
        >
        <span>{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import pcModalIndexPng from '@/assets/img/pc-micro-page/pc_modal_index.png'
import pcModalCartPng from '@/assets/img/pc-micro-page/pc_modal_cart.png'
import pcModalOrderPng from '@/assets/img/pc-micro-page/pc_modal_order.png'
import pcModalProfilePng from '@/assets/img/pc-micro-page/pc_modal_profile.png'
import pcModalCollectionPng from '@/assets/img/pc-micro-page/pc_modal_collection.png'
import pcModalNewPng from '@/assets/img/pc-micro-page/pc_modal_new.png'
import pcModalPreferentialPng from '@/assets/img/pc-micro-page/pc_modal_preferential.png'
import pcModalBulkPng from '@/assets/img/pc-micro-page/pc_modal_bulk.png'
import pcModalSkillPng from '@/assets/img/pc-micro-page/pc_modal_skill.png'
import pcModalSecuritiesPng from '@/assets/img/pc-micro-page/pc_modal_securities.png'
import pcModalIntegralPng from '@/assets/img/pc-micro-page/pc_modal_integral.png'
import pcModalMembersPng from '@/assets/img/pc-micro-page/pc_modal_members.png'
import pcModalDistributionPng from '@/assets/img/pc-micro-page/pc_modal_distribution.png'
import pcModalLivePng from '@/assets/img/pc-micro-page/pc_modal_live.png'
import pcPointsCheckInPng from '@/assets/img/pc-micro-page/pc_points_checkIn.png'
import { ElMessage } from 'element-plus'

const props = defineProps({
  activeName: { // 动态切换tab
    type: String,
    default: () => ''
  },
  deviceType: { // 弹窗类型 pc端： pc , 移动:mobile
    type: String,
    default: () => 'pc'
  },
  customLinkArr: { // 自定义链接回显数据
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['handleGoodsSelect', 'handleGoodsSelect'])

const pageList = [
  {
    img: pcModalIndexPng,
    title: $t('pcdecorate.commonModal.pageComponent.index'),
    link: '/shop-index'
  },
  {
    img: pcModalCartPng,
    title: $t('pcdecorate.commonModal.pageComponent.cart'),
    link: '/cart'
  },
  {
    img: pcModalOrderPng,
    title: $t('pcdecorate.commonModal.pageComponent.order'),
    link: '/user-center/uc-order'
  },
  {
    img: pcModalProfilePng,
    title: $t('pcdecorate.commonModal.pageComponent.profile'),
    link: '/user-center'
  },
  {
    img: pcModalCollectionPng,
    title: $t('pcdecorate.commonModal.pageComponent.collection'),
    link: '/user-center/uc-collection'
  },
  {
    img: pcModalNewPng,
    title: $t('pcdecorate.commonModal.pageComponent.recommand'),
    link: '/list?st=1'
  },
  {
    img: pcModalPreferentialPng,
    title: $t('pcdecorate.commonModal.pageComponent.limitTime'),
    link: '/special-discount'
  },
  {
    img: pcModalBulkPng,
    title: $t('pcdecorate.commonModal.pageComponent.discount'),
    link: '/group-buy'
  },
  {
    img: pcModalSkillPng,
    title: $t('pcdecorate.commonModal.pageComponent.skill'),
    link: '/flash-sale'
  },
  {
    img: pcModalSecuritiesPng,
    title: $t('pcdecorate.commonModal.pageComponent.coupon'),
    link: '/coupons'
  },
  {
    img: pcModalIntegralPng,
    title: $t('pcdecorate.commonModal.pageComponent.IntegralMall'),
    link: '/member-center/integral-mall'
  },
  {
    img: pcModalMembersPng,
    title: $t('pcdecorate.commonModal.pageComponent.memberCenter'),
    link: '/member-center/member-center'
  }
]
const currentIndex = ref('') // 当前点击选中的哪个
const mobileList = [ // 移动端页面内容
  {
    img: pcModalIndexPng,
    title: $t('pcdecorate.commonModal.pageComponent.index'),
    link: '/package-shop/pages/feature-index/feature-index0'
  },
  {
    img: pcModalCartPng,
    title: $t('pcdecorate.commonModal.pageComponent.cart'),
    link: '/pages/basket/basket'
  },
  {
    img: pcModalOrderPng,
    title: $t('pcdecorate.commonModal.pageComponent.order'),
    link: '/package-user/pages/order-list/order-list'
  },
  {
    img: pcModalProfilePng,
    title: $t('pcdecorate.commonModal.pageComponent.profile'),
    link: '/pages/user/user'
  },
  {
    img: pcModalCollectionPng,
    title: $t('pcdecorate.commonModal.pageComponent.collection'),
    link: '/package-user/pages/prod-classify/prod-classify?sts=5'
  },
  {
    img: pcModalNewPng,
    title: $t('pcdecorate.commonModal.pageComponent.recommand'),
    link: '/package-user/pages/prod-classify/prod-classify?sts=1'
  },
  {
    img: pcModalPreferentialPng,
    title: $t('pcdecorate.commonModal.pageComponent.limitTime'),
    link: '/package-activities/pages/special-discount/special-discount'
  },
  {
    img: pcModalBulkPng,
    title: $t('pcdecorate.commonModal.pageComponent.discount'),
    link: '/package-activities/pages/a-bulk-list/a-bulk-list'
  },
  {
    img: pcModalSkillPng,
    title: $t('pcdecorate.commonModal.pageComponent.skill'),
    link: '/package-activities/pages/snap-up-list/snap-up-list'
  },
  {
    img: pcModalSecuritiesPng,
    title: $t('pcdecorate.commonModal.pageComponent.coupon'),
    link: '/package-activities/pages/coupon-center/coupon-center'
  },
  {
    img: pcModalIntegralPng,
    title: $t('pcdecorate.commonModal.pageComponent.IntegralMall'),
    link: '/package-member-integral/pages/integral-index/integral-index'
  },
  {
    img: pcModalMembersPng,
    title: $t('pcdecorate.commonModal.pageComponent.memberCenter'),
    link: '/package-member-integral/pages/member-new-index/member-new-index'
  },
  {
    img: pcModalDistributionPng,
    title: $t('pcdecorate.commonModal.pageComponent.modalDistribution'),
    link: '/package-distribution/pages/dis-center/dis-center'
  },
  {
    img: pcModalLivePng,
    title: $t('pcdecorate.commonModal.pageComponent.modalLive'),
    link: '/pages/live-broadcast/live-broadcast'
  },
  {
    img: pcPointsCheckInPng,
    title: $t('pcdecorate.commonModal.pageComponent.memberIndex'),
    link: '/package-member-integral/pages/member-index/member-index'
  }
]
const currentPageList = ref([]) // 当前的页面

// 激活tabbar
watch(() => props.activeName, (newVal) => {
  if (newVal === '4') {
    resetComponent()
    setPageContent()
    onGetDistributionSet()
  }
})

// 分销设置开关
const distributionSwitch = ref(false)
const onGetDistributionSet = () => {
  http({
    url: http.adornUrl('/sys/config/getDistribution'),
    method: 'get'
  }).then(({ data }) => {
    distributionSwitch.value = data
  })
}

// 选中哪个页面
const handleSelectPage = (item, index) => {
  if (item.link === '/package-distribution/pages/dis-center/dis-center' && !distributionSwitch.value) {
    ElMessage({
      message: $t('components.noDistribution'),
      type: 'error',
      duration: 1500
    })
    return
  }
  currentIndex.value = index
  emit('handleGoodsSelect', { type: 'pageItem', value: item })
}

// 重置组件的信息
const resetComponent = () => {
  currentIndex.value = ''
}

// 获取店铺的店铺id
const getShopIDs = () => {
  return new Promise((resolve) => {
    http({
      url: http.adornUrl('/shop/shopDetail/getShopInfo'),
      method: 'get'
    }).then(res => {
      resolve(res.data.shopId)
    })
  })
}

// 设置页面内容
const setPageContent = async () => {
  const shopId = await getShopIDs()
  if (props.deviceType === 'pc') { // 如果是pc端选择进来
    currentPageList.value = pageList
    currentPageList.value[0].link = '/shop-index?sid=' + shopId
  } else if (props.deviceType === 'mobile') { // 否则就是移动端选择进来
    currentPageList.value = mobileList
  }
  // 回显数据
  if (props.customLinkArr && props.customLinkArr.type != '' && props.customLinkArr.type === '4') { // 如果在操作热区
    currentPageList.value.forEach((item, index) => {
      if (item.link === props.customLinkArr.link) {
        currentIndex.value = index
        emit('handleGoodsSelect', { type: 'pageItem', value: item })
      }
    })
  }
}

</script>
<style lang="scss" scoped>
.component-page-component {
  min-height: 450px;
  max-height: 450px;
  height: 450px;
  overflow-y: auto;

  .page-contents {
    width: calc(100% - 40px);
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;
    margin: 20px;

    .page-items {
      width: 20%;
      height: 88px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
      cursor: pointer;

      &:hover {
        border: 1px solid #155BD4;
        border-radius: 6px;
      }

      &.active {
        border: 1px solid #155BD4;
        border-radius: 6px;
      }

      img {
        width: 40px;
        height: 40px;
      }

      span {
        margin-top: 6px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        color: #333;
      }
    }
  }
}
</style>
