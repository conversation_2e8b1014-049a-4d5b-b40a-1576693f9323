.component-goods {
  .goods-form {
    .searchbtn {
      width: 68px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #155BD4;
      border-radius: 2px;
      margin: 4px 0 0 10px;
    }
  }

  .tables {
    width: 93%;
    margin: 0 auto 20px;
  }
}

.component-goods {
  min-height: 450px;
  max-height: 450px;
  height: 450px;
  overflow-y: auto;

  &:deep(.goods-form) {
    .el-input__inner {
      height: 32px;
    }
  }

  &:deep(.table-header) {
    &:nth-child(1) {
      .cell {
        // 全选框隐藏
        display: none;
      }
    }
  }
  &:deep(.table-select-all-header) {
    &:nth-child(1) {
      .cell {
        // 全选框显现
        display: block;
      }
    }
  }
}

:deep(.tables-checkedbox) {
  .el-checkbox {
    .el-checkbox__inner {
      position: relative;

      &:after {
        border: 0;
        position: absolute;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #fff;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .el-checkbox__inner {
      border-radius: 50%;
    }

    &.is-checked {
      .el-checkbox__inner {
        position: relative;

        &:after {
          border: 0;
          position: absolute;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #fff;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
}
