.component-category {
  min-height: 450px;
  max-height: 450px;
  height: 450px;
  padding: 0 27px;
  overflow-y: auto;

  .category-select {
    width: 100%;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid #E1E6F0;

    .top-header {
      width: 100%;
      height: 70px;
      display: flex;
      align-items: center;

      .one-input {
        width: 100%;
        height: 100%;
        border-right: 1px solid #E1E6F0;
        display: flex;
        align-items: center;
        padding: 0 20px;

        &:last-child {
          border-right: none;
        }

        span {
          width: 100%;
          display: block;
          background: rgba(247, 249, 251, 0.39);
          border: 1px solid #E1E6F0;
          border-radius: 2px;
          font-size: 13px;
          color: #aaa;
          height: 30px;
          line-height: 30px;
          padding-left: 10px;
        }
      }
    }

    .select-options {
      width: 100%;
      display: flex;
      position: relative;
      height: 360px;
      flex-direction: column;
      overflow-y: auto;
      padding: 0 20px;

      .category-items {
        width: 100%;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
        padding-left: 10px;

        &:hover {
          color: #155BD4;
          background: #f8fafb;
        }

        &.active {
          color: #155BD4;
          background: #f8fafb;
        }
      }
    }
  }
}
