<template>
  <div class="categort-page-conntent component-category">
    <div class="category-select">
      <div class="top-header">
        <div class="one-input">
          <span>{{ $t('pcdecorate.commonModal.categoryOne') }}</span>
        </div>
      </div>
      <div class="select-options">
        <div
          v-for="(item, index) in options"
          :key="index"
          :class="{'category-items': true, 'active': active === index}"
          @click="handleGoodsSelect(item, index)"
        >
          {{ item.categoryName }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>

const props = defineProps({
  activeName: { // 激活tab名
    type: String,
    default: () => ''
  },
  customLinkArr: { // 自定义链接回显数据
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['handleGoodsSelect', 'handleGoodsSelect'])

const active = ref('') // 当前激活
const options = ref([])

const casCaderRef = ref(null)
watch(() => props.activeName, (val) => {
  if (val === '2') {
    // 获取分类列表
    active.value = ''
    casCaderRef.value && casCaderRef.value?.clearCheckedNodes()
    options.value = []
    setCategoryList()
  }
})

// 获取分类列表
const getCategoryList = () => {
  return new Promise((resolve) => {
    http({
      url: http.adornUrl('/prod/category/shopCategory'),
      method: 'get'
    }).then(({ data }) => {
      resolve(data)
    })
  })
}

// 渲染分类列表
const setCategoryList = async () => {
  // 获取一级分类
  options.value = await getCategoryList()
  // 数据回显
  if (props.customLinkArr && props.customLinkArr.type != '' && props.customLinkArr.type === '2') {
    options.value.forEach((item, index) => {
      if (item.categoryId == props.customLinkArr.link.categoryId) {
        active.value = index
        emit('handleGoodsSelect', {
          type: 'categoryItem',
          value: {
            data: item,
            label: item.categoryName
          }
        })
      }
    })
  }
}

// 选择分类
const handleGoodsSelect = (item, index) => {
  active.value = index
  emit('handleGoodsSelect', {
    type: 'categoryItem',
    value: {
      data: item,
      label: item.categoryName
    }
  })
}

</script>
<style lang="scss" scoped>
@use "index";
</style>
