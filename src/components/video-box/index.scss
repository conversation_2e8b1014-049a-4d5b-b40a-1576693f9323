.elx-video-dialog {
  $bg: #f6f6f6;
  height: 100% !important;
  // eslint-disable-next-line vue-scoped-css/no-unused-selector
  .el-dialog {
    width: 820px;
    // eslint-disable-next-line vue-scoped-css/no-unused-selector
    .el-dialog__header {
      border-bottom: 1px solid #e8e8e8;
    }

    // eslint-disable-next-line vue-scoped-css/no-unused-selector
    .el-dialog__body {
      padding: 0;
      background: $bg;
    }
  }
  .pick-block {
    position: relative;
    $bg: #f6f6f6;
    .elx-img-list-loading {
      position: absolute;
      top: 60px;
      z-index: 9;
      left: 0;
      right: 0;
      width: 100%;
      height: 520px;
      // 加载层高度
      background: #fff;
      text-align: center;

      .el-icon-loading {
        font-size: 50px;
        color: #409eff;
        line-height: 460px;
      }
    }

    .elx-img-list {
      padding: 10px;
      min-height: 530px;
      // 视频列表高度

      .img-item {
        $imgSize: 175px;
        $size: 179px;
        float: left;
        margin: 10px 20px 20px 10px;
        width: $imgSize;
        cursor: pointer;
        position: relative;
        font-size: 12px;

        .title {
          line-height: 24px;
          height: 24px;
          display: block;
          overflow: hidden;
          background: $bg;
          padding: 0 5px;
        }

        .title {
          line-height: 24px;
          height: 24px;
          display: block;
          overflow: hidden;
          background: $bg;
          padding: 0 5px;
        }

        .selected {
          position: absolute;
          right: -3px;
          top: -3px;
          width: $size;
          height: 143px;
          border: 3px solid #155bd4;
          border-radius: 3px;
          text-align: right;
          .icon {
            background: #155bd4;
            text-align: center;
            height: 24px;
            width: 24px;
            line-height: 24px;
            display: inline-block;
            font-size: 16px;
            color: #fff;
            border-radius: 0 0 0 3px;
            position: absolute;
            right: 0;
            top: 0;
          }
        }
      }

      &::after {
        content: " ";
        display: table;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }

    // eslint-disable-next-line vue-scoped-css/no-unused-selector
    .el-pagination {
      position: relative;
      padding: 5px;
      text-align: right;
      float: none;
      margin-right: 25px;
    }
  }

  .elx-foot {
    padding: 15px 0 0 10px;
    text-align: right;
    margin-right: 30px;

  }
  .upload-block {
    .upload-img-preview {
      padding: 20px;
    }

    .upload-tip {
      padding: 0 20px;
      font-size: 13px;
      color: #999;
    }
    .elx-upload-main {
      padding-left: 70px;
      .upload-title {
        font-size: 16px;
        color: #666;
        padding: 20px 0 0 20px;
      }
      .select-group-box {
        padding: 20px 0 0 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .select-group-box-item {
          margin-right: 10px;
        }
        :deep(.el-select) {
          width: 200px;
        }
      }
      .elx-upload-foot {
        padding: 15px 0 15px 10px;
        text-align: left;
      }
    }

    .upload-video-box {
      .video-preview {
        width: 175px;
        height: 100px;
        display: inline-block;
        text-align: center;
        box-sizing: border-box;
        margin-right: 8px;
        margin-bottom: 5px;
        margin-top: 20px;
      }
      .avatar-uploader-popup{
        width: 100%;
        background: #f6f6f6;
        height: 18px;
      }
    }
  }
  .form {
    padding: 15px 0 0 20px;
  }

  .box {
    display: flex;
    justify-content: flex-start;
    .group-box {
      height: 600px;
      min-width: 200px;
      overflow-x:hidden;
      overflow-y: auto;
    }
    .group {
      min-width: 200px;
      height: 600px;
      padding: 10px;
      .group-item {
        height: 38px;
        line-height: 38px;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        cursor:pointer;

        .group-name {
          width: 180px;
        }
      }
      .group-item:hover{
        background: #F7F7F7;
      }
      .active {
        background: #F7F7F7;
      }
    }
    .img-list {
      width: 1100px;
      position: relative;
      .data-tips {
        position: absolute;
        top: 30%;
        left: 38%;
        color: #999;
      }
    }
  }
}
