<template>
  <div class="video-box">
    <el-dialog
      v-model="visible"
      :title="$t('videoManager.picManager')"
      class="elx-video-dialog"
      top="40px"
      width="900px"
      :append-to-body="visible"
      @close="handleDialogClose"
    >
      <div
        class="demo-tabs"
      >
        <div
          v-if="options.activeTab==='pick'"
          class="pick-block"
        >
          <div
            v-if="isLoading"
            class="elx-img-list-loading"
          >
            <div class="el-icon-loading" />
          </div>
          <el-form
            :inline="true"
            class="demo-form-inline form"
            @submit.prevent
          >
            <el-form-item>
              <el-input
                v-model="fileName"
                :placeholder="$t('videoManager.videoName')"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <div
                class="primary-btn default-btn"
                @click="searchImg"
              >
                {{ $t("videoManager.query") }}
              </div>
              <div
                v-if="options.enableUpload"
                class="default-btn primary-btn"
                style="margin-left:10px"
                @click="uploadFileBtn"
              >
                {{ $t("videoManager.uploadVideo") }}
              </div>
            </el-form-item>
          </el-form>
          <div class="box">
            <div class="group group-box">
              <div
                :class="['group-item',groupId === 0 ?'active': '']"
                @click="clickGroup(0)"
              >
                <span class="group-name">{{ $t("videoManager.AllVideos") }}</span>
              </div>
              <div
                v-for="item in groupList"
                :key="item.attachFileGroupId"
                :class="['group-item',groupId === item.attachFileGroupId ?'active': '']"
              >
                <span
                  class="group-name"
                  @click="clickGroup(item.attachFileGroupId)"
                >{{ item.name }}</span>
              </div>
            </div>
            <div class="img-list">
              <div class="elx-main elx-img-list">
                <div
                  v-for="(img, itemIndex) in imgRes.records"
                  :key="itemIndex"
                  class="img-item"
                >
                  <div
                    class="thumb-wp"
                    @click="onClickListImage(img)"
                  >
                    <video
                      :src="checkFileUrl(img.filePath)"
                      controls="controls"
                      class="showVideo"
                      style="width:175px;height:88px;"
                    />
                  </div>
                  <div
                    class="title"
                    @click="onClickListImage(img)"
                  >
                    {{ img.fileName }}
                  </div>
                  <span
                    v-if="img.selected"
                    class="selected"
                    @click="onClickListImage(img)"
                  >
                    <el-icon
                      class="icon el-icon-check"
                    >
                      <Check />
                    </el-icon>
                  </span>
                </div>
              </div>
              <div
                v-if="!(imgRes.records&&imgRes.records.length)"
                class="data-tips"
              >
                {{ $t("order.noData") }}
              </div>
              <el-pagination
                layout="total, prev, pager, next"
                :current-page="imgRes.current"
                :page-size="imgRes.size"
                :total="imgRes.total"
                @current-change="onPageNumChange"
              />
            </div>
          </div>
          <div class="elx-foot">
            <el-badge
              v-if="!type"
              :value="images.length"
              class="item"
            >
              <div
                class="default-btn"
                @click="visible = false"
              >
                {{ $t("resource.cancel") }}
              </div>
              <div
                :class="[images.length == 0?'disabled-btn':'','default-btn primary-btn']"
                style="margin-right:3px;"
                @click="onConfirm"
              >
                {{ $t("resource.confirm") }}
              </div>
            </el-badge>
            <div
              v-else
              :class="[images.length == 0?'disabled-btn':'','default-btn primary-btn']"
              @click="onConfirm"
            >
              {{ $t("resource.confirm") }}
            </div>
          </div>
        </div>

        <div
          v-if="options.enableUpload&&options.activeTab==='upload'"
          class="upload-block"
        >
          <div class="elx-upload-main">
            <div class="upload-title">
              {{ $t("resource.selectGroup") }}:
            </div>
            <div class="select-group-box">
              <div class="select-group-box-item">
                {{ $t("resource.group") }}:
              </div>
              <el-select
                v-model="selectGroup"
                clearable
                :placeholder="$t('resource.selectGroup')"
                class="select-group-box-item"
              >
                <el-option
                  v-for="item in groupList"
                  :key="item.attachFileGroupId"
                  :label="item.name"
                  :value="item.attachFileGroupId"
                />
              </el-select>
              <el-button
                style="margin-left: 10px"
                type="primary"
                link
                @click="createGroup()"
              >
                {{ $t('resource.newGroup') }}
              </el-button>
            </div>
            <div class="upload-title">
              {{ $t("videoManager.selectLocalVideo") }}
            </div>
            <!-- 视频预览 -->
            <div class="upload-video-box">
              <div
                v-for="(item, index) in videoList"
                :key="index"
                class="video-preview"
              >
                <video
                  v-if="item.url"
                  class="upShowVideo"
                  :src="item.url"
                  controls="controls"
                  style="width:175px;height:88px;"
                />
                <!-- 显示查看和删除的按钮弹窗 -->
                <div
                  class="avatar-uploader-popup"
                >
                  <el-icon
                    @click="preDeleteVideo(item, index)"
                  >
                    <Delete />
                  </el-icon>
                </div>
              </div>
            </div>
            <el-upload
              v-show="!uploadHide"
              ref="uploadRef"
              class="upload-img-preview"
              list-type="picture-card"
              action=""
              :multiple="true"
              accept="video/*"
              :auto-upload="false"
              :show-file-list="false"
              :limit="options.limit"
              :http-request="httpRequest"
              :on-change="onUploadChange"
              :on-exceed="onUploadExceedTip"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>

            <div
              v-show="uploadHide"
              style="height:20px;"
            />

            <div class="upload-tip">
              {{ uploadTips() }}
            </div>

            <div class="elx-upload-foot">
              <div
                class="default-btn"
                @click="options.activeTab = 'pick'"
              >
                {{ $t("shopFeature.edit.back") }}
              </div>
              <div
                class="primary-btn default-btn"
                @click="onUploadConfirm"
              >
                {{ $t("videoManager.confirmUpload") }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <group-add-or-update
      v-if="groupVisible"
      ref="groupAddOrUpdateRef"
      @get-group-data="getGroupList"
    />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import groupAddOrUpdate from '@/components/group-add-or-update/index.vue'
import http, { uploadFile } from '@/utils/http.js'
const emit = defineEmits(['refreshPic'])
const options = reactive({
  multiple: true, // 是否支持选取多个视频
  limit: 20, // 最多可选择视频数量
  maxSize: 2, // 最大尺寸（M）
  activeTab: 'pick',
  enableUpload: true, // 是否启用视频上传
  callback: null
})

onMounted(() => {
  getGroupList()
  loadListImage()
})

/**
 * 初始化
 * typePar 1：单  2：多
 */
const uploadRef = ref(null)
const type = ref(2)
const images = ref([]) // 已选视频
let fileIds = [] // 已选视频的fileId
const isLoading = ref(true)
const visible = ref(false)
const disabled = ref(false)
const init = (typePar, limit) => {
  visible.value = true
  isLoading.value = false
  options.activeTab = 'pick'
  images.value = []
  fileIds = []
  if (typePar === 1) {
    type.value = true
    disabled.value = true
  } else {
    type.value = false
    options.limit = limit || 20
  }
  if (uploadRef.value) {
    uploadRef.value.uploadFiles = []
  }
  // 获取产品数据 - 第一页的数据
  loadListImage(1)
  getGroupList()
}

const onConfirm = () => {
  if (type.value) {
    emit('refreshPic', images.value[0].filePath)
  } else {
    const imgPaths = images.value.map(file => {
      return file.filePath
    }).join(',')
    emit('refreshPic', imgPaths)
  }
  visible.value = false
}

const selectGroup = ref('')

/**
 * 点击视频时选中或取消选中视频
 * @param img object
 */
const onClickListImage = (img) => {
  // 单选图片
  if (type.value) {
    clearListSelected()
    images.value = []
    fileIds = []
    disabled.value = false
  } else {
    // 多选图片-如果已选中则取消选中
    const imgIndex = selectedImageIndex(img)
    if (imgIndex >= 0) {
      // 取消图片已选状态
      img.selected = false
      images.value.splice(imgIndex, 1)
      fileIds.splice(imgIndex, 1)
      return
    }
  }
  if (type.value && !uploadNumberLimit()) {
    message($t('videoManager.superiorLimit'))
    return false
  }
  images.value.push(JSON.parse(JSON.stringify(img)))
  fileIds.push(img.fileId)
  img.selected = true
}
/**
 * 清除所有已点击视频样式
 */
const clearListSelected = () => {
  if (type.value) {
    const list = imgRes.value.records
    list.forEach(element => {
      element.selected = false
    })
  }
}
/**
     * 按视频名称搜索视频
     */
const searchImg = () => {
  loadListImage(1)
}
/**
 * 加载视频列表数据
 * @param page
 */
const fileName = ref('')
const imgRes = ref({
  current: 1,
  size: 9,
  total: 0
})
const groupId = ref(0) // 点击分组
const loadListImage = (current) => {
  isLoading.value = true
  http({
    url: http.adornUrl('/admin/file/attachFilePage'),
    method: 'get',
    params: http.adornParams({
      current,
      size: 9,
      fileName: fileName.value || null,
      attachFileGroupId: groupId.value,
      type: 2
    })
  }).then(({ data }) => {
    imgRes.value = data
    imgRes.value.records.forEach(img => {
      img.selected = fileIds.some(item => img.fileId === item)
    })
    isLoading.value = false
  })
}

/**
 * 视频已选则返回下标，未选则返回-1
 */
const selectedImageIndex = (img) => {
  for (let i = 0; i < images.value.length; i++) {
    const selectedImg = images.value[i]
    if (selectedImg.fileId === img.fileId) {
      return i
    }
  }
  return -1
}

/**
 * 分页页面变化时刷新数据
 * @param page
 */
const onPageNumChange = (page) => {
  loadListImage(page)
}
/**
 * 获取分组列表
 */
const groupVisible = ref(false)
const groupList = ref([]) // 分组列表
const getGroupList = () => {
  groupVisible.value = false
  http({
    url: http.adornUrl('/admin/fileGroup/list'),
    method: 'get',
    params: {
      type: 2 // 1、视频 2、视频 3、文件
    }
  }).then(res => {
    groupList.value = res.data
  })
}
/**
 * 点击分组
 */
const clickGroup = (id) => {
  groupId.value = id
  loadListImage(1)
}
/**
 * 点击上传视频按钮
 */
const videoList = ref([])
const uploadFileBtn = () => {
  options.activeTab = 'upload'
  videoList.value = []
  images.value = []
  fileIds = []
  if (uploadRef.value) {
    uploadRef.value.uploadFiles = []
  }
}
/**
 * 提交上传
 */
let isSubmit = false
let isConfirm = false
const onUploadConfirm = () => {
  if (!fileNum) {
    message($t('publics.videoNoNull'), false)
    return
  }
  if (isConfirm) {
    return
  }
  isConfirm = true
  isSubmit = true
  fileIds = []
  nextTick(() => {
    uploadRef.value?.submit()
  })
}
/**
 * 上传视频
 */
const httpRequest = (event) => {
  videoList.value.forEach(item => {
    if (item.uid === event.file.uid) {
      uploadFileVideo(event)
    }
  })
}
let resData = []
const uploadFileVideo = (event) => {
  const file = event.file
  http({
    url: http.adornUrl('/admin/file/getPreSignUrl'),
    method: 'get',
    params: http.adornParams({
      fileName: file.name,
      isImFile: false
    })
  }).then(async ({ data }) => {
    await uploadFile(data.preSignUrl, event.file).then(() => {
      resData.push({ fileId: data.fileId, attachFileGroupId: selectGroup.value, fileSize: file.size, type: 2 })
    })
    if (resData.length === uploadFileNum) {
      http({
        url: http.adornUrl('/admin/file/uploadSuccess'),
        method: 'put',
        data: resData
      }).then(() => {
        resData = []
        images.value = []
        fileIds = []
        event.onSuccess(data.fileId)
        disabled.value = true
        uploadRef.value.uploadFiles = []
        options.activeTab = 'pick'
        setTimeout(() => {
          isSubmit = false
          videoList.value = []
          uploadFileNum = 0
        })
        isConfirm = false
        loadListImage(1)
      }).catch((err) => {
        message($t('videoManager.requestError'), true)
        throw err
      })
    }
  })
}
// 视频预上传
let fileNum = 0
const onUploadChange = (file, fileList) => {
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error($t('videoManager.tips3'))
    uploadRef.value?.uploadFiles?.splice(-1, 1)
    return
  }
  fileNum = fileList.length
  if (['video/mp4', 'video/quicktime', 'video/webm'].indexOf(file.raw.type) === -1) {
    ElMessage.error($t('videoManager.tips2'))
    return false
  }
  if (isSubmit) {
    return
  }
  const showVideoList = document.querySelectorAll('.upShowVideo') || []
  const videoSrc = URL.createObjectURL(file.raw)
  videoList.value.push({ url: videoSrc, uid: file.uid })
  showVideoList.forEach((item) => {
    if (!item.paused) item.pause()
  })
}
// 删除预览视频
const preDeleteVideo = (file, index) => {
  // handleRemove为官方自带外部方法
  uploadRef.value?.handleRemove(file)
  videoList.value.splice(index, 1)
}

let uploadFileNum = 0
// 新建分组
const groupAddOrUpdateRef = ref(null)
const createGroup = () => {
  groupVisible.value = true
  nextTick(() => {
    groupAddOrUpdateRef.value?.show(2)
  })
}

const uploadNumberLimit = () => {
  if (!options.multiple) {
    return 1
  }
  return options.limit - images.value.length - uploadFileNum
}

const uploadTypeTip = () => {
  return $t('videoManager.onlySupported') + ' mp4/mov/webm ' + $t('videoManager.video')
}

const uploadSizeTip = () => {
  return $t('videoManager.notExceed') + '20M'
}

const uploadHide = ref(false)
const uploadTips = () => {
  const tips = [uploadTypeTip(), uploadSizeTip()]
  if (!options.multiple) {
    return tips.join('，')
  }
  if (images.value.length > 0) {
    tips.push($t('videoManager.alreadyExist') + images.value.length + $t('videoManager.unit'))
  }
  uploadFileNum = videoList.value.length
  if (uploadFileNum > 0) {
    tips.push($t('videoManager.soonUpload') + uploadFileNum + $t('videoManager.unit'))
  }
  uploadHide.value = uploadFileNum >= options.limit
  tips.push($t('videoManager.remainder') + (options.limit - images.value.length - uploadFileNum) + $t('videoManager.unit') + $t('videoManager.upload'))

  return tips.join(',')
}

/**
 * 选择上传文件超过限制文件个数提示
 */
const onUploadExceedTip = () => {
  message($t('videoManager.maxSelect') + uploadNumberLimit() + $t('videoManager.unit') + $t('videoManager.upload'))
}
const message = (msg, isInfo) => {
  let type = 'error'
  if (isInfo) {
    type = 'info'
  }
  ElMessage({
    message: msg,
    type,
    duration: 1500
  })
}

/**
 * 关闭回调
 */
const handleDialogClose = () => {
  fileName.value = ''
}

defineExpose({
  init
})

</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
