.component-dialog-container {
  &:deep(.el-dialog) {
    top: 50% !important;
    margin-top: 0 !important;
    transform: translate(0, -50%) !important;

    .el-dialog__header {
      padding: 0;
    }

    .el-dialog__body {
      padding: 0 !important;
    }

    .el-dialog__headerbtn {
      top: 18px;
      right: 21px;
    }
  }
}

.dialog-footers {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;

  .el-button {
    padding: 9px 20px;
  }
}
