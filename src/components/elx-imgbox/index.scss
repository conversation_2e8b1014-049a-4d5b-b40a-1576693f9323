.elx-images {
  &:deep(.el-upload-list__item),
  &:deep(.el-upload--picture-card) {
    width: 78px;
    height: 78px;
    line-height: 82px;
    border-radius: 5px;
  }
}

.elx-imgbox-dialog {
  $bg: #f6f6f6;
  .el-badge {
    vertical-align: bottom;
  }

  .el-dialog {
    width: 820px;
    &:deep(.el-dialog__header) {
      border-bottom: 1px solid #e8e8e8;
    }

    &:deep(.el-dialog__body) {
      padding: 0;
      background: $bg;
    }
  }

  &:deep(.el-tabs) {
    .el-tabs__header {
      margin-right: 0;
      margin-top: 5px;
      .el-tabs__nav-wrap::after {
        background: $bg;
      }
    }
    &:deep(.el-tabs__item.is-active) {
      background: #fff;
    }
    &:deep(.el-tabs--left .el-tabs__nav-wrap) {
      padding-top: 15px;
    }

    &:deep(.el-tabs__content) {
      height: 740px;
      // 弹窗高度
      background: #fff;
    }
  }

  .elx-foot {
    padding: 15px 0 0 10px;
    text-align: right;
    margin-right: 30px;
    .el-button {
      margin: 0 0 0 10px;
    }
  }
  .elx-upload-foot {
    padding: 15px 0 0 10px;
    text-align: left;
    .el-button {
      margin: 0 0 0 10px;
    }
  }

  .pick-block {
    position: relative;

    .elx-img-list-loading {
      position: absolute;
      top: 60px;
      z-index: 9;
      left: 0;
      right: 0;
      width: 100%;
      height: 520px;
      // 加载层高度
      background: #fff;
      text-align: center;

      .el-icon-loading {
        font-size: 50px;
        color: #409eff;
        line-height: 460px;
      }
    }
    :deep(.el-form-item .el-form-item__content .el-input) {
      width: 200px;
      white-space: nowrap;
      display: -webkit-inline-flex;
    }

    .elx-img-list {
      padding: 10px;
      height: 530px;
      // 图片列表高度
      width: 650px;
      .img-item {
        $imgSize: 100px;
        $size: 104px;
        float: left;
        margin: 10px;
        width: $imgSize;
        cursor: pointer;
        position: relative;
        font-size: 12px;

        img {
          width: $imgSize;
          height: $imgSize;
          display: block;
          object-fit: contain;
        }

        .title {
          line-height: 24px;
          height: 24px;
          display: block;
          overflow: hidden;
          background: $bg;
          padding: 0 5px;
        }

        .title {
          line-height: 24px;
          height: 24px;
          display: block;
          overflow: hidden;
          background: $bg;
          padding: 0 5px;
        }
        .operate {
          line-height: 24px;
          height: 24px;
          display: block;
          overflow: hidden;
          margin-top: 2px;
          padding: 0 5px;
          .edit {
            float: left;
            padding-left: 5px;
          }
          .del {
            float: right;
            padding-right: 5px;
          }
        }

        .label {
          position: absolute;
          z-index: 9;
          left: 0;
          bottom: 24px;
          width: 100%;
          height: 21px;
          line-height: 21px;
          text-align: center;
          color: #fff;

          &:after {
            content: " ";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 21px;
            background: #000;
            opacity: 0.3;
            z-index: -1;
          }
        }

        .selected {
          position: absolute;
          right: -3px;
          top: -3px;
          width: $size;
          height: 130px;
          border: 3px solid #155bd4;
          border-radius: 3px;
          text-align: right;
          .icon {
            background: #155bd4;
            text-align: center;
            height: 24px;
            width: 24px;
            line-height: 24px;
            display: inline-block;
            font-size: 16px;
            color: #fff;
            border-radius: 0 0 0 3px;
            position: absolute;
            right: 0;
            top: 0;
          }
        }
      }

      &::after {
        content: " ";
        display: table;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }

    .el-pagination {
      padding: 5px;
      text-align: right;
      float: none;
      margin-right: 25px;

      * {
        background: none;
      }
    }
  }

  .upload-block {
    .upload-img-preview {
      padding: 20px;

      /* 上传图片预览改为使用背景图片按比例缩放方式 */
      &:deep(.el-upload--picture-card),
      &:deep(.el-upload-list--picture-card .el-upload-list__item),
      &:deep(.el-upload-list--picture-card .el-upload-list__item .wp) {
        width: 90px;
        height: 90px;
        line-height: 98px;
        background-size: cover;
        background-position: 50% 50%;
      }

      &:deep(.el-upload-list--picture-card .el-upload-list__item-status-label i) {
        margin-top: 12px;
        vertical-align: top;
      }
    }

    .upload-tip {
      padding: 0 20px;
      font-size: 13px;
      color: #999;
    }

    .upload-title {
      font-size: 16px;
      color: #666;
      padding: 20px 0 0 20px;
    }
  }
}

.form {
  padding: 15px 0 0 20px;
}

.el-upload-list__item-actions {
  a {
    i {
      color: #fff;
    }
  }
}

.main {
  display: flex;
  justify-content: flex-start;
  .group {
    min-width: 200px;
    padding: 10px;
    .group-item {
      height: 38px;
      line-height: 38px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      cursor:pointer;

      .group-name {
        width: 180px;
      }
      .sidebar-operate {
        .el-icon-delete,
        .el-icon-edit {
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
    .group-item:hover{
      background: #F7F7F7;
    }
    .active {
      background: #F7F7F7;
    }
  }
  .img-list {
    position: relative;
    .data-tips {
      position: absolute;
      top: 30%;
      left: 38%;
      color:#999
    }
  }
  .group-box {
    height: 600px;
    min-width: 200px;
    overflow-x:hidden;
    overflow-y: auto;
  }

}
.upload-box {
  margin-left: 50px;
  .select-group-box {
    padding: 20px 0 0 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .select-group-box-item {
      margin-right: 10px;
    }
    :deep(.el-select) {
      width: 200px;
    }
  }
}

:deep(.el-input__suffix>.el-icon-circle-close) {
  display: none
}

div :deep(.el-tabs__header){
  display: none !important;
}
.upload-img-preview :deep(.is-ready) {
  line-height:86px !important;
}
.hide :deep(.el-upload--picture-card){
  display: none;
}
.upload-box :deep(.el-upload-list__item img.el-upload-list__item-thumbnail){
  vertical-align: initial;
  object-fit: contain;
}
