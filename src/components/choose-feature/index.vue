<template>
  <div class="choose-goods">
    <!-- <evue-table  :data="list" :option="option" @currentRowChange="currentRowChange"></evue-table> -->
    <el-table
      :data="list"
      style="width: 100%"
      highlight-current-row
      @current-change="onPageChange"
    >
      <el-table-column
        prop="name"
        :label="$t('shopFeature.chooseFeature.pageTitle')"
      />
      <el-table-column
        prop="createTime"
        :label="$t('shopFeature.chooseFeature.createTime')"
      />
    </el-table>
    <el-pagination
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalPage"
      @size-change="onPageSizeChange"
      @current-change="handleCurrentPageChange"
    />
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  isGetChooseData: { // 是否开始返回数据
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['chooseChooseFeaturesFun'])

let currentchooseFeatures = {} // 当前选择的
watch(() => props.isGetChooseData, () => {
  if (!currentchooseFeatures.renovationId) {
    ElMessage.error($t('shopFeature.chooseFeature.choosePageTips'))
    return
  }
  emit('chooseChooseFeaturesFun', {
    data: currentchooseFeatures
  })
})

onMounted(() => {
  getMiniPagesList()
})

const list = ref([])
const totalPage = ref(0)
const pageIndex = ref(1)
const pageSize = ref(10)
/**
 * 获取微页面列表
 */
const getMiniPagesList = () => {
  http({
    url: http.adornUrl('/shop/shopRenovation/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageIndex.value,
          size: pageSize.value
        }
      )
    )
  }).then(({ data }) => {
    list.value = data.records
    totalPage.value = data.total
  }).catch(() => {})
}

// 每页数
const onPageSizeChange = (val) => {
  pageSize.value = val
  pageIndex.value = 1
  getMiniPagesList()
}

// 当前页
const handleCurrentPageChange = (val) => {
  pageIndex.value = val
  getMiniPagesList()
}

/**
 * 选择微页面
 */
const onPageChange = (ev) => {
  currentchooseFeatures = ev
}

</script>

<style lang="scss" scoped>
.choose-goods {
  &:deep(.goods) {
    display: flex;

    img {
      width: 60px;
      height: 60px;
      object-fit: cover;
      object-position: center;
    }

    .goodsName {
      flex: 1;
      padding-top: 5px;
      padding-left: 5px;

      p {
        color: #38f;
        cursor: pointer;
      }

      span {
        color: #f60;
      }
    }
  }
}
</style>
