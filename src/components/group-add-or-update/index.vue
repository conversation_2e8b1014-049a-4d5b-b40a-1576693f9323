<template>
  <el-dialog
    v-if="groupVisible"
    v-model="groupVisible"
    :title="$t('resource.newGroup')"
    top="200px"
    :append-to-body="groupVisible"
    width="500px"
  >
    <!-- native modifier has been removed, please confirm whether the function has been affected  -->
    <el-form
      v-if="groupVisible"
      ref="groupFormRef"
      label-width="80px"
      @submit.prevent
    >
      <el-form-item :label="$t('resource.groupName')">
        <el-input
          v-model="createGroupName"
          clearable

          maxlength="20"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div
        class="dialog-footer"
      >
        <div
          class="default-btn"
          @click="groupVisible = false"
        >
          {{ $t("resource.cancel") }}
        </div>
        <div
          class="primary-btn default-btn"
          @click="submitGroup()"
        >
          {{ $t("resource.confirm") }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script type="text/babel" setup>
import { Debounce } from '@/utils/debounce'
import { ElMessage } from 'element-plus'

const emit = defineEmits(['pageUpdateGroup', 'getGroupData'])

const createGroupName = ref('') // 新建分组名
const groupVisible = ref(false)
const type = ref(1)

const show = (typePar) => {
  groupVisible.value = true
  createGroupName.value = ''
  type.value = typePar
}

const hide = () => {
  groupVisible.value = false
}

/**
 * 新建分组
 */
const submitGroup = Debounce(() => {
  if (!createGroupName.value.trim()) {
    return ElMessage.error($t('resource.CannotBeEmpty'))
  }
  const method = 'post'
  const param = {
    name: createGroupName.value,
    type: type.value // 1、图片 2、视频 3、文件
  }
  http({
    url: http.adornUrl('/admin/fileGroup'),
    method,
    data: param
  }).then(() => {
    ElMessage({
      message: $t('resource.successTips'),
      type: 'success',
      duration: 1000,
      onClose: () => {
        groupVisible.value = false
        emit('pageUpdateGroup')
        emit('getGroupData')
      }
    })
  })
}, 1000)

defineExpose({
  show,
  hide,
  submitGroup
})

</script>

<style lang="scss" scoped>
@use "index";
</style>
