<template>
  <div class="component-upload-video">
    <div class="plugin-video">
      <div
        tabindex="0"
        class="el-upload el-upload--text"
        @click.prevent.stop="videoboxHandle"
      >
        <video
          v-if="modelValue"
          ref="myVideoRef"
          class="video"
          :src="checkFileUrl(modelValue)"
          controls="controls"
          @error="error"
        />
        <el-icon
          v-else
          size="24"
          color="#999"
        >
          <Plus />
        </el-icon>
      </div>
    </div>
    <div
      v-if="modelValue"
      class="default-btn"
      @click="deleteImg"
    >
      {{ $t("resource.Delete") }}
    </div>
    <!-- 弹窗, 新增视频 -->
    <video-box
      v-if="videoboxVisible"
      ref="videoboxRef"
      @refresh-pic="refreshPic"
    />
  </div>
</template>

<script setup>
import VideoBox from '@/components/video-box/index.vue'

const emit = defineEmits(['update:modelValue'])
defineProps({
  modelValue: {
    default: '',
    type: String
  }
})

// 打开视频选择窗
const videoboxVisible = ref(false)
const videoboxRef = ref(null)
const videoboxHandle = () => {
  videoboxVisible.value = true
  nextTick(() => {
    videoboxRef.value?.init(1)
  })
}

const refreshPic = (imagePath) => {
  emit('update:modelValue', imagePath)
}
const deleteImg = () => {
  emit('update:modelValue', '')
}
const error = () => {
  emit('update:modelValue', '')
}
</script>

<style lang="scss" scoped>
.plugin-video {
  display: inline-block;
  width: auto;
  vertical-align: bottom;
  .el-upload {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    min-width: 120px;
    max-width: 300px;
    height: auto;
    min-height: 120px;
    max-height: 250px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    .video {
      width: auto;
      max-width: 300px;
      height: auto;
      max-height: 250px;
      display: block;
    }
    .el-upload:hover {
      border-color: #409eff;
    }
  }
}
</style>
