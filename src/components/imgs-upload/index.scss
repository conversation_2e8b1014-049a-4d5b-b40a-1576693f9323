.mul-pic-upload {
  .upload-component {
    display: inline;
  }

  .el-upload--picture-card {
    border: 0;
  }
  .hideShadow {
    .el-upload-list__item-actions {
      display: none;
    }
  }
  .el-upload.el-upload--picture-card,
  .el-upload-list .el-upload-list--picture-card,
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    border-radius: 2px;
  }
  .el-upload--picture-card {
    position: relative;
    background-color: #fff;
    .el-icon-plus {
      color: #dcdfe6;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%,-50%);
    }
  }
  .rightTop {
    position: absolute;
    right: 13px;
    top: -2px;
  }
  .el-icon-check {
    color: #fff;
    margin-top: 10px;
  }
}
:deep(.el-dialog) {
  img {
    display: inline-block;
    width: auto;
    height: auto;
    max-width: 100%;
  }
}
