<template>
  <div class="up-img-box">
    <div class="plugin-images">
      <ul
        v-if="modelValue"
        class="el-upload-list el-upload-list--picture-card"
      >
        <li
          tabindex="0"
          class="el-upload-list__item is-success"
          :style="customStyle"
        >
          <img
            :src="checkFileUrl(modelValue)"
            class="el-upload-list__item-thumbnail"
            alt
            @error="imageError"
          >

          <a class="el-upload-list__item-name">
            <el-icon><Document /></el-icon>
          </a>
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview">
              <el-icon
                @click="openImg"
              >
                <ZoomIn />
              </el-icon>
            </span>
            <span
              v-if="!disabled"
              class="el-upload-list__item-delete"
              @click="deleteImgae"
            >
              <el-icon><Delete /></el-icon>
            </span>
          </span>
        </li>
      </ul>

      <div
        v-else
        tabindex="0"
        class="el-upload el-upload--picture-card"
        @click="elxImgboxHandle"
      >
        <el-icon><Plus /></el-icon>
      </div>
    </div>
    <!-- 弹窗, 新增图片 -->
    <elx-imgbox
      v-if="elxImgboxVisible"
      ref="elxImgboxRef"
      :max-size="maxSize"
      :img-size-limit="imgSizeLimit"
      @refresh-pic="refreshPic"
    />

    <el-dialog
      v-model="visible"
      :append-to-body="visible"
    >
      <ImgShow :src="modelValue" />
    </el-dialog>
  </div>
</template>

<script setup>
import defImg from '@/assets/img/def.png'
const emit = defineEmits(['input', 'update:modelValue'])

const props = defineProps({
  modelValue: {
    default: '',
    type: String
  },
  disabled: {
    default: false,
    type: Boolean
  },
  maxSize: {
    default: 2,
    type: Number
  },
  imgSizeLimit: {
    default: false,
    type: Boolean
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => {}
  }
})

const elxImgboxVisible = ref(false)

const elxImgboxRef = ref(null)
// 打开图片选择窗
const elxImgboxHandle = () => {
  if (props.disabled) {
    if (props.modelValue) openImg()
    return false
  }
  elxImgboxVisible.value = true
  nextTick(() => {
    elxImgboxRef.value?.init(1)
  })
}

const visible = ref(false)
const openImg = () => {
  visible.value = true
}

const refreshPic = (imagePath) => {
  emit('update:modelValue', imagePath)
  emit('input', imagePath)
}
const deleteImgae = () => {
  emit('update:modelValue', '')
  emit('input', '')
}

const imageError = (e) => {
  e.target.src = defImg
}
</script>

<style lang="scss" scoped>
@use "index";
</style>
