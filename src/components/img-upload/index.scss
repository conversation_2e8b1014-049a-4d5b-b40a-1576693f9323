.up-img-box {
  .plugin-images {
    .el-upload--picture-card {
      position: relative;
      border: 1px solid #dcdfe6;
      border-radius: 2px;
      background-color: #fff;
      .el-icon-plus {
        color: #dcdfe6;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
      }
    }
  }
}
.pic-uploader-component .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  .pic-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .pic {
    width: 100px;
    height: 100px;
    display: block;
  }
}
.pic-uploader-component .el-upload:hover {
  border-color: #409eff;
}
