<template>
  <div>
    <el-select
      v-model="selDvyId"
      v-loadmore="loadmore"
      :style="selStyle"
      :teleported="false"
      filterable
      remote
      :remote-method="remoteMethod"
      :placeholder="placeTips"
      :disabled="disabled"
      @change="change"
      @focus="focus"
      @blur="onBlur"
    >
      <el-option
        v-for="item in devList"
        :key="item.dvyId"
        :label="item.dvyName"
        :value="item.dvyId"
      />
    </el-select>
  </div>
</template>

<script setup>
const emit = defineEmits(['update:modelValue'])

const vLoadmore = {
  mounted (el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
    SELECTWRAP_DOM?.addEventListener('scroll', debounce(() => {
      if (!isFocus) {
        return
      }
      const condition = SELECTWRAP_DOM.scrollHeight - SELECTWRAP_DOM.scrollTop <= SELECTWRAP_DOM.clientHeight
      if (condition) {
        binding.value()
      }
    }))
  }
}
let isFocus = false
const onBlur = () => {
  if (devList.value.length) {
    devList.value = []
  }
  isFocus = false
}

// 防抖函数
function debounce (fn, delay = 50) {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(fn, delay)
  }
}

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  selStyle: {
    type: Object,
    default: () => {}
  },
  placeTips: {
    type: String,
    default: $t('stock.pleaseSelect')
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const selDvyId = ref('') // 当前选择的值
const size = 20
let current = 1
const dvyName = ref('')
let pages = 0
const devList = ref([])
let isSearch = false
watch(() => props.modelValue, (val) => {
  selDvyId.value = val
}, {
  immediate: true
})

onMounted(() => {
  getDeliveryList()
})

const change = () => {
  isFocus = false
  emit('update:modelValue', selDvyId.value)
}
const focus = (e) => {
  isFocus = true
  if (devList.value.length === 0) {
    remoteMethod()
  }
}
const remoteMethod = (name) => {
  isSearch = true
  current = 1
  dvyName.value = name
  getDeliveryList()
}
// 获取物流列表
const getDeliveryList = () => {
  http({
    url: http.adornUrl('/admin/delivery/page'),
    method: 'get',
    params: http.adornParams({
      size,
      current,
      dvyName: dvyName.value
    })
  }).then(({ data }) => {
    current = data.current
    pages = data.pages
    if (data.current === 1) {
      devList.value = data.records
      if (selDvyId.value && !isSearch) {
        checkCurSel(devList.value, selDvyId.value)
      }
    } else {
      const fList = data.records.filter(r => r.dvyId !== selDvyId.value)
      devList.value.push(...fList)
    }
  })
}
// 获取当前id的物流
const getDeliveryById = (dvyId) => {
  http({
    url: http.adornUrl('/admin/delivery/page'),
    method: 'get',
    params: http.adornParams({
      dvyId
    })
  }).then(({ data }) => {
    devList.value.unshift(...data.records)
  })
}
// 分页加载
const loadmore = () => {
  if (current < pages) {
    current++
    getDeliveryList()
  }
}
// 筛查出当前所选的对象
const checkCurSel = (curList, curId) => {
  for (const item of curList) {
    if (item.dvyId === curId) {
      return
    }
  }
  getDeliveryById(curId)
}

</script>
