<template>
  <div>
    <el-dialog
      v-model="visible"
      :title="$t('user.selectTag')"
      width="40%"
      class="el-upload-list el-upload-list--picture-card"
    >
      <el-form
        :inline="true"
        :model="searchForm"
        class="demo-form-inline form"
      >
        <el-form-item>
          <el-input
            v-model="tagName"
            size="default"
            :placeholder="$t('user.tagTip1')"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <div
            class="default-btn primary-btn"
            @click="onSearch"
          >
            {{ $t("pictureManager.query") }}
          </div>
        </el-form-item>
      </el-form>
      <el-main>
        <div class="main">
          <div
            v-for="(item, index) in tagList"
            :key="index"
          >
            <span
              :class="active.includes(item)?'active':'Classification'"
              @click="oncheck(item)"
            >{{ item.tagName }}</span>
          </div>
        </div>
        <div v-if="tagList.length < 1">
          {{ $t('user.noData') }}
        </div>
      </el-main>

      <!-- 分页 -->
      <el-pagination
        v-if="tagList.length"
        :current-page="page.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
      <!-- /分页 -->
      <div
        class="default-btn"
        @click="visible = false"
      >
        {{ $t('user.cancel') }}
      </div>
      <div
        class="default-btn primary-btn"
        @click="confirm"
      >
        {{ $t('user.confirm') }}
      </div>
    </el-dialog>
  </div>
</template>

<script setup>

const props = defineProps({
  value: {
    default: '',
    type: String
  },
  // 最大上传数量
  limit: {
    default: 9,
    type: Number
  },
  tagCategory: {
    default: 0,
    type: Number
  }
})
const emit = defineEmits(['refreshTagsArr'])

const dataForm = reactive({
  tagList: [], // 标签
  userIds: []
})

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  tagName: null
})

onActivated(() => {
  getTagList()
})

const tagName = ref(null)
const active = ref([]) // 选中的标签
let allList = []
const visible = ref(false)
const init = (data) => {
  active.value = []
  allList = []
  searchForm.tagName = ''
  tagName.value = ''
  visible.value = true
  searchForm.tagName = ''
  if (data.length > 0) {
    allList = JSON.parse(JSON.stringify(data))
  }
  getTagList()
}

// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getTagList(page)
}

// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getTagList(page)
}

const tagList = ref([]) // 标签
// 分页获取手动标签
const getTagList = (pageParam) => {
  active.value = []
  const allListPar = allList.map(x => x.userTagId)
  http({
    url: http.adornUrl('/user/userTag/byTagType'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize,
          tagCategory: props.tagCategory
        },
        searchForm
      )
    )
  }).then(({ data }) => {
    tagList.value = data.records
    if (allListPar.length > 0) {
      tagList.value.forEach(x => {
        if (allListPar.indexOf(x.userTagId) > -1) {
          active.value.push(x)
        }
      })
    }
    page.total = data.total
  })
}

// 搜索
const onSearch = () => {
  searchForm.tagName = tagName.value
  getTagList(page)
}

// 多选
const oncheck = (item) => {
  if (active.value.includes(item)) {
    const index = active.value.indexOf(item)
    active.value.splice(index, 1)
    const index1 = allList.indexOf(item)
    allList.splice(index1, 1)
  } else {
    active.value.push(item)
    allList.push(item)
  }
}

const confirm = () => {
  if (!allList.length) {
    visible.value = false
    return
  }
  dataForm.tagList = []
  let arr = []
  allList.forEach(item => {
    arr.push({ userTagId: item.userTagId, tagName: item.tagName })
    dataForm.tagList.push(item.userTagId)
  })
  emit('refreshTagsArr', arr, allList)
  arr = []
  active.value = []
  allList = []
  dataForm.tagList = []
  visible.value = false
}

defineExpose({
  init
})

</script>
<style lang="scss" scoped>
.active {
  float: left;
  margin-left: 10px;
  padding: 10px;
  background: #155bd4;
  color: #ffffff;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}
.Classification {
  float: left;
  margin-left: 10px;
  padding: 10px;
  background: #f7f7f7;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}
</style>
