<template>
  <div class="component-prod-inventory">
    <el-dialog
      v-model="visible"
      :title="$t('stock.merchandiseInventory')"
      :close-on-click-modal="false"
      width="1000px"
    >
      <div class="search-bar">
        <el-form
          ref="searchFormRef"
          :inline="true"
          :model="searchForm"
          label-width="auto"
          @submit.prevent
        >
          <div class="input-row">
            <el-form-item
              :label="$t('stock.prodName') + ':'"
              prop="spuName"
            >
              <el-input
                v-model="searchForm.spuName"
                :placeholder="$t('stock.prodName')"
              />
            </el-form-item>
            <el-form-item
              label-width="100"
              :label="$t('product.shopCategories') + ':'"
              prop="categoryId"
            >
              <el-cascader
                v-model="selectedCategory"
                expand-trigger="hover"
                :options="categoryList"
                :props="categoryTreeProps"
                :clearable="true"
                @change="onChangeSelectCategory"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch(true)"
              >
                {{ $t('stock.search') }}
              </el-button>
              <el-button
                @click="onResetSearch(searchFormRef)"
              >
                {{ $t('stock.reset') }}
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="main-content">
        <!-- 表格 -->
        <div class="table-con">
          <el-table
            ref="dataListRef"
            :data="dataList"
            header-cell-class-name="table-header"
            row-class-name="table-row"
            style="width: 100%;"
            :row-key="row => { return row.partyCode }"
          >
            <el-table-column
              width="380"
              :label="$t('stock.prodName')"
            >
              <template #default="scope">
                <div class="table-cell-con">
                  <div class="table-cell-image">
                    <ImgShow
                      :src="scope.row.pic"
                      :img-style="{ width: '60px', height: '60px' }"
                      @img-error="scope.row.pic = ''"
                    />
                  </div>
                  <span class="table-cell-text">
                    {{ scope.row.prodName }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="skuName"
              :label="$t('product.productSpecifi')"
            >
              <template #default="scope">
                <div
                  v-for="item in scope.row.skuList"
                  :key="item.skuId"
                  class="name"
                >
                  <template v-if="item.skuName || (item.skuLangVOList && item.skuLangVOList.length)">
                    {{ item.skuName || item.skuLangVOList[0].skuName || '-' }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="price"
              :label="$t('stock.commodityPrice')"
            >
              <template #default="scope">
                <div
                  v-for="sku in scope.row.skuList"
                  :key="sku.skuId"
                  class="name"
                >
                  {{ sku.price }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('stock.inventory')"
              prop="stock"
            >
              <template #default="scope">
                <div
                  v-for="sku in scope.row.skuList"
                  :key="sku.skuId"
                  class="name"
                >
                  {{ sku.stocks || 0 }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-show="page.total>0"
        style="text-align: right;padding-top:30px"
        :total="page.total"
        :current-page="searchForm.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.size"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onSizeChangeHandle"
        @current-change="onCurrentChangeHandle"
      />
    </el-dialog>
  </div>
</template>

<script setup>
// 1：查询仓库，2：查询门店
const props = defineProps({
  stockPointType: {
    default: 1,
    type: Number
  }
})

onMounted(() => {
  // 获取分类列表
  onGetCategoryList()
})

const stockPointId = ref(null)
const visible = ref(false)
// type:仓库类型(0默认仓库，1区域仓库)(门店查询需要传1) stockMode:门店库存类型 （1共享总部库存 2独立销售库存）
const init = (id, type, stockMode) => {
  onReset()
  stockPointId.value = id
  searchForm.type = type
  visible.value = true
  searchForm.stockMode = stockMode || null
  searchForm.current = 1
  onGetPageData(true)
}

let tempSearchForm = null // 保存上次点击查询的请求条件
// 头部搜索表单
const searchForm = reactive({
  size: 10,
  current: 1,
  spuName: '',
  stockPointType: props.stockPointType,
  categoryId: '',
  type: null,
  stockMode: null
})
const page = reactive({
  total: 0 // 一共多少条数据
})
const dataList = ref([])
// 获取数据列表
const onGetPageData = (newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  } else {
    tempSearchForm.current = searchForm.current
    tempSearchForm.size = searchForm.size
  }
  http({
    url: http.adornUrl('/m/stockPointSku/page'),
    method: 'get',
    params: http.adornParams({
      ...tempSearchForm,
      stockPointId: stockPointId.value
    })
  }).then(({ data }) => {
    dataList.value = data?.records || []
    page.total = data.total
  })
}

// 条件查询
const onSearch = (newData = false) => {
  searchForm.current = 1
  searchForm.size = 10
  onGetPageData(newData)
}
// 搜索表单重置
const searchFormRef = ref(null)
const onResetSearch = (formEl) => {
  selectedCategory.value = []
  formEl.resetFields()
}

/* 获取店铺分类列表 */
const onGetCategoryList = () => {
  http({
    url: http.adornUrl('/prod/category/listCategory'),
    method: 'get',
    params: http.adornParams({
      status: 1
    })
  }).then(({ data }) => {
    categoryList.value = treeDataTranslate(data, 'categoryId', 'parentId')
  })
}

const categoryList = ref([])
const selectedCategory = ref([])
const categoryTreeProps = reactive({
  value: 'categoryId',
  label: 'categoryName'
})
const onChangeSelectCategory = () => {
  searchForm.categoryId = (selectedCategory.value && selectedCategory.value[0]) || ''
}

// 每页数
const onSizeChangeHandle = (val) => {
  searchForm.size = val
  searchForm.current = 1
  onGetPageData()
}

// 当前页
const onCurrentChangeHandle = (val) => {
  searchForm.current = val
  onGetPageData()
}

const onReset = () => {
  selectedCategory.value = []
  searchForm.size = 10
  searchForm.pageNum = 1
  searchForm.spuName = ''
  searchForm.categoryId = ''
}
defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.component-prod-inventory {
  .name {
    width: 100px;
    font-size: 14px;
    color: #333333;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .table-cell-con {
    display: flex;
    align-items: center;
    .table-cell-image {
      height: 60px;
      width: 60px;
    }
    .table-cell-text {
      flex: 1;
      margin-left: 10px;
      text-overflow: ellipsis;
      word-break: break-word;
      line-break: anywhere;
      overflow: hidden;
      line-height: 20px;
    }
  }
}
</style>
