<template>
  <el-dialog
    v-model="visible"
    class="component-offline-event-handle"
    :title="dataForm.status === 1 ? $t('groups.applyForListing') : $t('groups.moderated')"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      :label-width="$t('language') === 'English' ? '125px' : '80px'"
      @submit.prevent
      @keyup.enter="dataFormSubmit()"
    >
      <el-form-item
        :label="$t('components.handler')"
        prop="handler"
      >
        <el-input
          v-model="dataForm.handler"
          :disabled="true"
        />
      </el-form-item>
      <!--      <el-form-item-->
      <!--        :label="$t('components.processingStatus')"-->
      <!--        prop="status"-->
      <!--      >-->
      <!--        &lt;!&ndash; 1平台进行下线 2 重新申请，等待审核 3.审核通过 4 审核未通过 &ndash;&gt;-->
      <!--        <span v-if="dataForm.status === 1">{{ $t("components.platformOffline") }}</span>-->
      <!--        <span v-else-if="dataForm.status === 2">{{ $t("groups.moderated") }}</span>-->
      <!--        <span v-else-if="dataForm.status === 3">{{ $t("productComm.pass") }}</span>-->
      <!--        <span v-else-if="dataForm.status === 4">{{ $t("components.auditNotPassed") }}</span>-->
      <!--        <span v-else>{{ $t("product.other") }}</span>-->
      <!--      </el-form-item>-->
      <el-form-item
        :label="$t('components.reasonForOffline')"
        prop="offlineReason"
      >
        <el-input
          v-model="dataForm.offlineReason"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 10 }"
          maxlength="200"
          :disabled="true"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('components.offlineTime')"
        prop="createTime"
      >
        <el-input
          v-model="dataForm.createTime"
          :disabled="true"
        />
      </el-form-item>
      <el-form-item
        v-if="dataForm.status === 1 || dataForm.status === 4"
        class="textarea-right"
        :label="$t('components.reasonForApply')"
        prop="reapplyReason"
      >
        <el-input
          v-model="dataForm.reapplyReason"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 10 }"
          maxlength="200"
          show-word-limit
          :disabled="!(dataForm.status === 1 || dataForm.status === 4)"
        />
      </el-form-item>
    </el-form>
    <el-divider v-if="dataForm.offlineHandleEventItemList && dataForm.offlineHandleEventItemList[0].createTime" />
    <div
      v-if="dataForm.offlineHandleEventItemList && dataForm.offlineHandleEventItemList[0].createTime"
      style="marginLeft: 12px"
    >
      <div style="margin-bottom:10px">
        <strong>{{ $t("components.applicationLog") }}</strong>
      </div>
      <div
        v-for="item in dataForm.offlineHandleEventItemList"
        :key="item.eventItemId"
      >
        <el-row
          type="flex"
          justify="space-around"
        >
          <el-col
            :span="4"
            :class="[$t('language') === 'English' ? 'text-width' : '']"
          >
            <strong>{{ $t("components.applicationTime") }}：</strong>
          </el-col>
          <el-col :span="20">
            <span class="content-color">{{ item.createTime }}</span>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          justify="space-around"
        >
          <el-col
            :span="4"
            :class="[$t('language') === 'English' ? 'text-width' : '']"
          >
            <strong>{{ $t("components.reasonForApply") }}：</strong>
          </el-col>
          <el-col :span="20">
            <div>
              <span class="content-color">{{ item.reapplyReason }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row
          v-if="item.auditTime"
          type="flex"
          justify="space-around"
        >
          <el-col
            :span="4"
            :class="[$t('language') === 'English' ? 'text-width' : '']"
          >
            <strong>{{ $t("components.reviewTime") }}：</strong>
          </el-col>
          <el-col :span="20">
            <div>
              <span class="content-color">{{ item.auditTime }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row
          v-if="item.refuseReason"
          type="flex"
          justify="space-around"
        >
          <el-col
            :span="4"
            :class="[$t('language') === 'English' ? 'text-width' : '']"
          >
            <strong>{{ $t("components.denialReason") }}：</strong>
          </el-col>
          <el-col :span="20">
            <div>
              <span class="content-color">{{ item.refuseReason }}</span>
            </div>
          </el-col>
        </el-row>
        <div style="width:100%">
          <el-divider />
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <div
          size="mini"
          class="default-btn"
          @click="visible = false"
        >{{ $t("crud.filter.cancelBtn") }}</div>
        <div
          :class="[dataForm.status !== 1 && dataForm.status !== 4 ? 'disabled-btn' : '', 'default-btn primary-btn']"
          @click="dataFormSubmit(dataForm.status !== 1 && dataForm.status !== 4)"
        >{{ $t("components.submitApplication") }}
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const emit = defineEmits(['refreshDataList'])
const props = defineProps({
  selectUrl: {
    default: '',
    type: String
  },
  applyUrl: {
    default: '',
    type: String
  },
  // 类型：prod（商品） station（门店）
  type: {
    default: 'prod',
    type: String
  }
})

const validateReason = (rule, value, callback) => {
  const reg = /^\s+$/g
  if (!value) {
    callback(new Error($t('components.applicaEmpty')))
  } else {
    if (reg.test(value)) {
      callback(new Error($t('shopProcess.reasonAllSpace')))
    }
    callback()
  }
}

const Data = reactive({
  visible: false,
  dataForm: {
    eventId: null,
    handleType: null,
    handleId: null,
    shopId: null,
    handlerId: null,
    status: null,
    reapplyReason: '',
    offlineReason: null
  },
  id: 0,
  dataRule: {
    reapplyReason: [
      { validator: validateReason, trigger: 'blur' }
    ]
  }
})

const { visible, dataForm, dataRule } = toRefs(Data)

const init = (id) => {
  Data.id = id || 0
  Data.visible = true
  nextTick(() => {
    dataFormRef.value.resetFields()
    if (Data.id) {
      http({
        url: http.adornUrl(props.selectUrl + '/' + id),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        if (!data) {
          ElMessage({
            message: props.type === 'prod' ? $t('components.retryAfterRefresh') : $t('components.stationRetryAfterRefresh'),
            type: 'error',
            duration: 1500,
            onClose: () => {
              Data.dataForm.reapplyReason = ''
              Data.visible = false
              emit('refreshDataList')
            }
          })
        } else {
          Data.dataForm = data
        }
      })
    }
  })
}

const dataFormRef = ref()
// 表单提交
const dataFormSubmit = (status) => {
  if (status) {
    return
  }
  dataFormRef.value.validate((valid) => {
    if (valid) {
      sendData()
    } else {
      return false
    }
  })
}

const sendData = () => {
  ElMessageBox.confirm($t('components.areYouSureAtion'), $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn')
  }).then(() => {
    http({
      url: http.adornUrl(props.applyUrl),
      method: 'post',
      data: http.adornData({
        eventId: Data.dataForm.eventId,
        handleId: Data.id,
        reapplyReason: Data.dataForm.reapplyReason
      })
    }).then(() => {
      Data.visible = false
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          Data.dataForm.reapplyReason = ''
          emit('refreshDataList')
        }
      })
    }).catch(() => {
    })
  })
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.content-color {
  word-wrap: break-word;
  color: #155bd4
}

</style>
