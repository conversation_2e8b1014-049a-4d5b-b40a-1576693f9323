.page-message-box {
	width: 100%;
	height: 100vh;
	background-color: #2e2f3d;
	position: fixed;
	.chat-box {
		width: 100%;
		height: 100%;
		background: #ffffff;
		opacity: 1;
		margin: 0;
		.chat-box-right {
			width: 100%;
			height: 100%;
		}
		.chat-box-left {
			overflow: auto;
			width: 300px;
			height: 100%;
			background: #ffffff;
			opacity: 1;
			border-right: 1px solid #eceef0;
			flex-shrink: 0;
      .shop-list {
        padding: 0;
        margin: 0;
        .shop-item {
          display: flex;
          align-items: center;
          cursor: pointer;
          &:hover {
            background-color: #d0e9ff;
          }
        }
      }
      .shop-item {
        img {
          width: 36px;
          height: 36px;
          margin: 12px 12px 12px 15px;
        }
        .shop-info {
          position: relative;
          width: 100%;
          display: flex;
          flex-direction: column;
          .shop-info-name {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            line-height: 20px;
            color: #333333;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
          }
        }
      }
		}
	}

.wrapper span {
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  position: relative;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
}

.chat-box {
  .chat-box-top {
    width: 100%;
    height: 53px;
    padding-left: 15px;
    box-sizing: border-box;
    background: #f7f8fa;
    border-bottom: 1px solid #eceef0;
    border-radius: 5px 5px 0 0;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    line-height: 53px;
    color: #333333;
    opacity: 1;
    .user-name {
      position: absolute;
      left: 310px;
      white-space: nowrap;
    }
  }
  .chat-box-main {
    display: flex;
    height: calc(100% - 53px);
    overflow: hidden;
  }
}




.shop-info-name {
	.name {
		width: 80%;
		color: #333333;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: break-word;
	}
	.tips {
		height: 16px;
		line-height: 16px;
		border-radius: 16px;
		padding: 0 5px;
		margin-right: 5px;
		box-sizing: border-box;
		color: #fff;
		font-size: 10px;
		content: " ";
		background: #fc1b35;
	}
}
.chat-box-right {
	.im-box {
		display: flex;
		position: relative;
		height: 100%;
	}
	.im-main {
		width: 100%;
		height: 100%;
		border-right: 1px solid #ebedf0;
    .chat-main-form {
      position: relative;
      z-index: 800;
      display: flex;
      flex-direction: column;
      height: 100%;
      .display-infor {
        height: 70%;
        overflow-y: auto;
        box-sizing: border-box;
      }
      .reply {
        width: 100%;
        height: 30%;
        border-top: 1px solid #ebedf0;
        position: relative;
        top: 0;
        .small-box {
          display: flex;
          .customer-service {
            margin: 17px 0 8px 22px;
            cursor: pointer;
          }
        }
        .upload {
          width: 22px;
          height: 18px;
          background-image: url('@/assets/img/upload.png');
          margin: 17px 0 8px 22px;
          cursor: pointer;
        }
        .reply-area {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          height: 60%;
          textarea {
            margin-left: 12px;
            border: none;
            resize: none;
            outline: none;
            box-shadow: none;
            width: 98%;
            height: 100%;
          }
        }
        .send {
          margin-right: 15px;
          width: 62px;
          height: 26px;
          line-height: 26px;
          text-align: center;
          background: #155bd4;
          color: #fff;
          cursor: pointer;
          border-radius: 4px;
          opacity: 1;
        }
      }
    }
	}
}


/* 中间时间样式 */
.topTime {
  text-align: center;
  margin-bottom: 15px;
  height: 14px;
  font-size: 10px;
  font-family: PingFang SC;
  font-weight: 400;
  line-height: 14px;
  color: #aaaaaa;
  opacity: 1;
}

/* 订单商品详情 */

.prod-item {
	img {
		width: 60px;
		height: 60px;
		margin-right: 12px;
	}
	.prod-name {
		font-size: 12px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		line-height: 18px;
		color: #333333;
		opacity: 1;
		word-break: break-word;
	}
}


.prod-b .prod-price {
  margin-top: 5px;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  line-height: 18px;
  color: #e1251b;
  opacity: 1;
}

// 图片上传
.search-btnn {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}
.imgUp {
  position: absolute;
  left: 22px;
  top: 18px;
}
.imgUp:hover {
  background-image: url('@/assets/img/upload-on.png');
  cursor: pointer;
}

/* 商品链接样式 */
.prod-number {
  margin-bottom: 5px;
}
.link-prod {
  display: flex;
  padding-bottom: 9px;
  border-bottom: 1px solid #eeeeee;
  cursor: pointer;

  img {
    width: 88px;
    height: 88px;
  }

  .link-detail {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-top: 1px;
    margin-left: 13px;
  }

  .prod-price {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    font-family: PingFang SC;
    padding-bottom: 5px;
    font-weight: 400;
    color: #999999;
    opacity: 1;
    word-break: break-all;
  }

  .payment-amount {
    flex: 1;
  }

  .prod-status {
    width: 35%;
    text-align: end;
  }
}

.prod-box {
  display: flex;
}
.link-prod {
	img {
		width: 88px;
		height: 88px;
	}
	.link-detail {
		display: flex;
		width: 70%;
		flex-direction: column;
		justify-content: space-between;
		margin: 15px;
		box-sizing: border-box;
	}
	.prod-price {
		margin-top: 10px;
		font-size: 12px;
		font-family: PingFang SC;
		font-weight: 400;
		color: #999999;
		opacity: 1;
		display: flex;
		justify-content: space-between;
		word-break: break-all;
	}
}
.link-detail {
	.prod-name {
		font-size: 12px;
		font-family: PingFang SC;
		padding-top: 5px;
		font-weight: 400;
		color: #333333;
		opacity: 1;
		word-break: break-word;
	}
}

/*不限制图片大小，实现居中*/
.img-layer img {
  width: 100%;
  height: 100%;
  cursor: pointer;
  object-fit: contain
}
/* 点击加载更多 */
.display-infor .more {
  font-size: 12px;
  color: #5a606b;
  text-align: center;
  margin-bottom: 20px;
  cursor: pointer;
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;
}

.unread{
  font-size: 12px;
  color: #AAAAAA;
  margin-right: 9px;
}
.left .message-box{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.prods-select-body {
  height: 430px;
  overflow: auto;
  .username {
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #f6f7fa;
  }
  .service-list {
    position: relative;
    height: 50px;
    border-bottom: 1px solid #ededed;
  }
  .nick-name {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
   }
  .el-radio {
    position: relative;
    height: 50px;
    margin-left: 30px;
  }
  .no-data {
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
  }
}
.message-content{
  display: flex;
  position: relative;
  justify-content: flex-end;
  width: 100%;
}
.message-left {
	display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 10px;
  .message-content {
    position: relative;
    justify-content: flex-start;
    width: 100%;
    .username{
      position: absolute;
      top: 0;
      margin-left: 50px;
      margin-bottom: 2px;
    }
  }
  .bubble-left{
    margin-left: 10px;
    margin-top: 20px;
    max-width: 70%;
    .bubble-content {
      max-width: 100%;
      font-size: 14px;
      min-height: 42px;
      padding: 12px 14px 11px 16px;
      word-break: break-word;
      box-sizing: border-box;
      background-color: #f7f8fa;
      color: #000;
      opacity: 1;
      border-radius: 0 10px 10px;
    }
  }
	.bubble {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 0 10px 10px 10px;
		padding: 10px;
		color: black;

		.text {
			display: flex;
			text-align: left;
			font-size: 14px;
			max-width: 250px;
			word-break: break-all;
      flex-direction: column;
		}
	}
	.status {
		font-size: 12px;
		color: #d0e9ff;
		margin-left: 10px;
		margin-bottom: 5px;
		display: flex;
		align-items: center;
	}
}
.message.right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
	justify-content: flex-end;
  margin:0 20px;
  .username{
    position: absolute;
    top: 0;
    right: 4px;
    margin-right: 50px;
    margin-bottom: 2px;
  }
  .divider-box{
    width: 93%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    .divider{
      flex: 1;
      height: 1px;
      background-color: #ccc;
    }
    .divider-txt{
      max-width: 80%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      font-size: 12px;
      color: #999;
      padding: 0 12px;
    }
  }
  .read-status{
    font-size: 12px;
    color: #aaa;
    margin-right: 9px;
    display: flex;
    align-items: center;
  }
  .bubble-right {
    margin: 20px 15px;
    max-width: 70%;
    .bubble-content {
      max-width: 100%;
      font-size: 14px;
      min-height: 42px;
      padding: 12px 14px 11px 16px;
      word-break: break-word;
      box-sizing: border-box;
      background-color: #d0e9ff;
      color: #000;
      opacity: 1;
      border-radius: 10px 0 10px 10px;
      :deep(.el-button) {
        white-space: normal;
        word-break: break-all;
        word-wrap: break-word;
        text-align: start;
      }
    }
  }
}
.longimage {
  width: 100px;
  height: 100px;
}
@media screen and ( max-width: 1680px ) {
  .page-message-box .chat-box {
    opacity: 1;
    margin: 0;
  }
  .page-message-box{
    position: absolute;
  }
}

// 新消息过来用户栏用户背景颜色变化
.newMessage{
  background-color: #FFF9cd;
}
.changeUser{
  background-color: #D0E9FF;
}

/* 放大图片的右侧关闭图标 */
.bubble {
  :deep(.el-image-viewer__close){
    color:red !important
  }
}

.message-list {
  width: 100%;
  height: 100%;
  overflow: auto;
  .avatar {
		img {
      width: 40px;
		  height: 40px;
      border-radius: 2px;
    }
	}
}
/* 商品链接样式 */
.prod-link {
  width: 345px;
  background: #ffffff;
  border: 1px solid #f2f2f2;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 15px 13px 0 12px;
  cursor: pointer;

  .prod-number {
    margin-top: -7px;
    margin-bottom: 5px;
    border-bottom: 1px solid #eceef0;
    padding-bottom: 5px;

    &:hover {
      color: #9c1712;
    }
  }
}
}
