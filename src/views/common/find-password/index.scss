.page-find-password {
  width: 100%;
  height: 100%;
  background: no-repeat;
  background-size: cover;
  position: fixed;
  top: 0;

  .login-box {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    height: 100%;
    padding-top: 10%;

    .info{
      height: 40px;
    }

    .top {
      margin-bottom: 30px;
      text-align: center;

      .logo {
        font-size: 0;
      }

      .company {
        font-size: 16px;
        margin-top: 10px;
      }
    }

    .mid {
      font-size: 14px;

      .mobile-box {
        .mobile-int {
          width: 100%;
        }

        .get-code-btn {
          cursor: pointer;
        }

        .get-code-btn.num {
          cursor: not-allowed;
        }
      }

      .btn-box {
        width: 100%;
        .item-btn {
          margin-top: 20px;
          width: 100%;
          padding: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
        }

        .to-login {
          display: block;
          text-align: right;
          color: #999;
        }

        .bottom-row {
          margin-top: 10px;
          line-height: 1.5em;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .protocol {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .ag-txt {
              margin-left: 5px;
            }
          }
        }
      }
      :deep(.password .el-form-item__content .el-form-item__error){
        white-space: nowrap;
      }

      :deep(.el-input) {
        width: 287px;
      }
    }

    .bottom {
      position: absolute;
      bottom: 10%;
      width: 100%;
      color: #999;
      font-size: 12px;
      text-align: center;
    }
  }
}

.login-title {
  color: #000;
  vertical-align: middle;
  margin: 0.5em 0 0.5em 20px;
  font-size: 28px;
}

.login {
  .login-box {
    .mid {
      .mobile-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }
    }
  }
}

.login-captcha {
  height: 40px;
}

// 注册成功弹窗
.success-dialog {
  &:deep(.el-dialog__header) {
    border: none;
  }

  &:deep(.el-dialog__body) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px 30px 30px;

    .txt {
      font-size: 15px;
      line-height: 1em;
    }

    .success-icon {
      display: block;
      width: 25px;
      height: 25px;
      margin-right: 10px;
    }
  }
}

@media screen and (max-height: 580px) {
  .page-find-password .login-box {
    padding-top: 5%;
  }
  .page-find-password .login-box .bottom {
    bottom: 8%;
  }
}

@media screen and (max-height: 480px) {
  .page-find-password .login-box {
    padding-top: 2%;
  }
  .page-find-password .login-box .bottom {
    bottom: 0;
  }
}
