<template>
  <div class="page-home">
    <div
      v-if="shopStatusInfo.shopStatus !== null && shopStatusInfo.shopStatus !== 1"
      class="shop-inf-imperfect-tips"
    >
      <div v-if="shopStatusInfo.shopStatus === 2">
        <div>
          <el-icon><Warning /></el-icon>
          {{ $t("shopProcess.yourStoreIsOfflineBecauseOf") }}
          <span>{{ shopStatusInfo.offlineReason }}</span>
          ，{{ $t("shopProcess.onlineShopTips") }}
        </div>
      </div>
      <div v-if="shopStatusInfo.shopStatus === 0">
        <div>
          <el-icon><CircleCloseFilled /></el-icon>
          {{ $t("shopProcess.shopStopReason") }}
          <span>{{ shopStatusInfo.closedReason }}</span>
          ，{{ $t("shopProcess.shopStopTips") }}
        </div>
      </div>
      <div v-if="shopStatusInfo.shopStatus === 5 || shopStatusInfo.shopStatus === 3">
        <div>
          <el-icon><Warning /></el-icon>
          {{ $t("shopProcess.applyingOnline") }}，
          <span>{{ $t("shopProcess.shopIsUnderReview") }}</span>
        </div>
      </div>
    </div>

    <!-- 数据 -->

    <div style="display:flex">
      <div style="width: 80%;margin-right:20px">
        <!-- 订单商品栏 -->
        <div class="order-all-num">
          <el-row
            type="flex"
            class="row-bg"
          >
            <el-col
              :span="15"
              class="col-box"
            >
              <div class="num-item-box">
                <div
                  class="wait-payment-num order-num-item"
                  @click="toOrderPage(1)"
                >
                  <div class="item-box">
                    <div class="words">
                      {{ $t("home.pendingOrders") }}
                    </div>
                    <div class="number">
                      {{ isTrueData(orderStateCount.unPay) }}
                    </div>
                  </div>
                  <div class="item-img">
                    <img src="~@/assets/img/home/<USER>">
                  </div>
                </div>
              </div>
              <div class="num-item-box">
                <div
                  class="wait-delivery-num order-num-item"
                  @click="toOrderPage(2)"
                >
                  <div class="item-box">
                    <div class="words">
                      {{ $t("chat.pendingDelivery") }}
                    </div>
                    <div class="number">
                      {{ isTrueData(orderStateCount.payed) }}
                    </div>
                  </div>
                  <div class="item-img">
                    <img src="~@/assets/img/home/<USER>">
                  </div>
                </div>
              </div>
              <div class="num-item-box">
                <div
                  class="wait-receiving-goods-num order-num-item"
                  @click="toOrderPage(3)"
                >
                  <div class="item-box">
                    <div class="words">
                      {{ $t("chat.pendingReceipt") }}
                    </div>
                    <div class="number">
                      {{ isTrueData(orderStateCount.consignment) }}
                    </div>
                  </div>
                  <div class="item-img">
                    <img src="~@/assets/img/home/<USER>">
                  </div>
                </div>
              </div>
            </el-col>
            <el-col
              :span="15"
              class="col-box"
            >
              <div class="num-item-box">
                <div
                  class="wait-evaluate-num order-num-item"
                  @click="toOrderPage(5)"
                >
                  <div class="item-box">
                    <div class="words">
                      {{ $t("chat.completed") }}
                    </div>
                    <div class="number">
                      {{ isTrueData(orderStateCount.success) }}
                    </div>
                  </div>
                  <div class="item-img">
                    <img src="~@/assets/img/home/<USER>">
                  </div>
                </div>
              </div>
              <div class="num-item-box">
                <div
                  class="wait-after-sales-num order-num-item"
                  @click="toRefundOrderPage(1)"
                >
                  <div class="item-box">
                    <div class="words">
                      {{ $t("order.reimburse") }}/{{ $t("order.afterSale") }}
                      <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('order.afterSaleTips')"
                        placement="right"
                      >
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </div>
                    <div class="number">
                      {{ isTrueData(orderStateCount.refund) }}
                    </div>
                  </div>
                  <div class="item-img">
                    <img src="~@/assets/img/home/<USER>">
                  </div>
                </div>
              </div>
              <div class="num-item-box">
                <div
                  class="wait-evaluate-num order-num-item"
                  @click="toStockPage(1)"
                >
                  <div class="item-box">
                    <div class="words">
                      {{ $t("product.stockWarning") }}
                    </div>
                    <div class="number">
                      {{ isTrueData(stockWarningNum) }}
                    </div>
                  </div>
                  <div class="item-img">
                    <img src="~@/assets/img/home/<USER>">
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 实时概况板块 -->
        <div class="realtime-situation">
          <!-- 实时概况 -->
          <div class="realtime">
            <el-row
              type="flex"
              class="row-bg"
            >
              <el-col
                :span="15"
                class="col-item"
              >
                <div class="realtime-left">
                  <div class="title">
                    <div class="t-title">
                      {{ $t("home.realTimeData") }}
                    </div>
                    <span class="title-time">{{ $t("home.updateTime") }}：{{ nowDate }} <span style="margin-left:5px">{{
                      nowTime }}</span></span>
                    <div class="t-explain-item t-explain-first-item t-small-text t-today-data">
                      <span>{{ $t("home.dataTodayActualTotal") }}</span>
                    </div>
                    <div class="t-explain-item t-explain-second-item t-small-text t-yesterday-data">
                      <span>{{ $t("home.dataYesterdayActualTotal") }}</span>
                    </div>
                  </div>
                  <!-- 图表 -->
                  <div class="realtime-chart-box">
                    <div
                      id="real-time-data-chart"
                      style="width: 100%; height: 250px"
                    />
                  </div>
                  <!-- /图表 -->
                </div>
              </el-col>
              <el-col :span="9">
                <!-- 数据统计 -->
                <div class="realtime-right">
                  <div class="realtime-right-title">
                    {{ $t("home.statistics") }}
                  </div>

                  <!-- 内容 -->
                  <div class="realtime-right-box">
                    <div class="realtime-right-box-left">
                      <div class="payCustomers order-num-item">
                        <div class="item-img">
                          <img src="~@/assets/img/home/<USER>">
                        </div>
                        <div class="item-box">
                          <div class="words">
                            {{ $t("home.payCustomers") }}
                          </div>
                          <div class="number">
                            {{ isTrueData(dataInfo.payUserCount) }}
                          </div>
                          <div class="seq">
                            <span class="value">{{ dataInfo.yesterdayPayUserRate >= 0 ? '+' : '' }}{{
                              dataFormat(isTrueData(dataInfo.yesterdayPayUserRate)) }}%</span>
                            <span class="compare grap">{{ $t("home.sameAsYesterday") }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="payCount order-num-item">
                        <div class="item-img">
                          <img src="~@/assets/img/home/<USER>">
                        </div>
                        <div class="item-box">
                          <div class="words">
                            {{ $t("home.payCount") }}
                          </div>
                          <div class="number">
                            {{ isTrueData(dataInfo.payOrderCount) }}
                          </div>
                          <div class="seq">
                            <span class="value">{{ dataInfo.yesterdayPayOrderRate >= 0 ? '+' : '' }}{{
                              dataFormat(isTrueData(dataInfo.yesterdayPayOrderRate)) }}%</span>
                            <span class="compare grap">{{ $t("home.sameAsYesterday") }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="realtime-right-box-right">
                      <div class="refundAmount order-num-item">
                        <div class="item-img">
                          <img src="~@/assets/img/home/<USER>">
                        </div>
                        <div class="item-box">
                          <div class="words">
                            {{ $t("order.refundAmount") }}({{ $t("user.yuan") }})
                          </div>
                          <div class="number">
                            {{ isTrueData(dataInfo.refund) }}
                          </div>
                          <div class="seq">
                            <span class="value">{{ dataInfo.yesterdayRefundRate >= 0 ? '+' : '' }}{{
                              dataFormat(isTrueData(dataInfo.yesterdayRefundRate)) }}%</span>
                            <span class="compare grap">{{ $t("home.sameAsYesterday") }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="pendingReview order-num-item">
                        <div class="item-img">
                          <img src="~@/assets/img/home/<USER>">
                        </div>
                        <div class="item-box">
                          <div class="words">
                            {{ $t("home.customerPrice") }}({{ $t("user.yuan") }})
                          </div>
                          <div class="number">
                            {{ isTrueData(dataInfo.onePrice) }}
                          </div>
                          <div class="seq">
                            <span class="value">{{ dataInfo.yesterdayOnePriceRate >= 0 ? '+' : '' }}{{
                              dataFormat(isTrueData(dataInfo.yesterdayOnePriceRate)) }}%</span>
                            <span class="compare grap">{{ $t("home.sameAsYesterday") }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- /数据统计 -->
              </el-col>
            </el-row>
          </div>
        </div>
        <!-- /实时概况板块 End -->
      </div>
      <div class="order-data-right">
        <div class="data-item-box">
          <div class="data-title">
            <div class="left-title">
              {{ $t('notice.bulletinBoard') }}
            </div>
            <div
              class="right-title"
              @click="toNotify"
            >
              {{ $t('notice.more') }}
            </div>
          </div>
          <div
            v-for="(item, index) in platformNotify"
            :key="index"
            class="content"
            @click="handleShowNoticeDetails(item.id)"
          >
            {{ index + 1 + "、" + item.title }}
          </div>
          <div
            v-if="platformNotify.length <= 0"
            class="no-data"
          >
            {{ $t('shop.noData') }}
          </div>
        </div>
        <notice-details
          v-if="showNoticeDetails"
          ref="noticeDetailsRef"
          @on-close="handleCloseNoticeDetails"
        />
        <!-- 消息通知 -->
        <div class="data-item-box">
          <div class="data-title">
            <div class="left-title">
              {{ $t('notice.messageNotification') }}
            </div>
            <div
              class="right-title"
              @click="toManager"
            >
              {{ $t('notice.more') }}
            </div>
          </div>
          <div
            v-for="(item, index) in notifyList"
            :key="index"
            class="content notify-view"
            @click="notifyView(item)"
          >
            <span
              style="position: relative;margin-right: 6px;"
              :class="item.status ? 'reads-dot' : 'unread-point'"
            >●</span>
            {{ item.message }}
          </div>
          <div
            v-if="notifyList.length <= 0"
            class="no-data"
          >
            {{ $t('shop.noData') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 整体看板 -->
    <div class="whole-plate">
      <div class="related-income">
        <div class="title">
          <div class="t-title">
            {{ $t("home.displays") }}
          </div>
          <span class="title-time">
            {{ $t("home.updateTime") }}：{{ nowDate }}
            <span style="margin-left:5px">{{ nowTime }}</span>
          </span>
          <div class="t-explain-item t-explain-first-item t-small-text t-today-data">
            {{ $t("home.businessPay") }}
          </div>
        </div>
        <div class="money">
          <!-- 当天支付金额 -->
          <div class="related-income-item">
            <p class="item-tit">
              {{ $t("home.dayAmount") }}
            </p>
            <p class="item-num">
              <span class="item-num-price">{{
                dataInfo.payActualTotal
              }}</span>
            </p>
          </div>
          <!-- 本月完成金额 -->
          <div class="related-income-item">
            <p class="item-tit">
              {{ $t("home.monthAmount") }}
            </p>
            <p class="item-num">
              <span class="item-num-price">{{ sumPayActualTotal }}</span>
            </p>
          </div>
        </div>
      </div>
      <!-- 图表 -->
      <div
        id="entirety-data-chart"
        ref="entirety"
        class="integral-plate"
        style="width: 100%; height: 300px;"
      />

      <!-- /图表 -->
    </div>
    <!-- /整体看板 End -->

    <!-- 退款看板 -->
    <div class="refund-plate">
      <div class="ranking-box">
        <div class="ranking-left">
          <div class="ranking-title">
            {{ $t("home.refundReasonRank") }}
          </div>
          <div v-if="refundRankForCause.length">
            <table
              class="table"
              cellpadding="0"
              cellspacing="0"
            >
              <tr class="table-tit gray">
                <td class="table-tit-item ranks padLeft">
                  {{ $t("home.rank") }}
                </td>
                <td class="table-tit-item prods">
                  {{ $t("home.reason") }}
                </td>
                <td class="table-tit-item refs">
                  {{ $t("home.refundAmount") }}
                </td>
                <td class="table-tit-item prop">
                  {{ $t("home.moneyRatio") }}
                </td>
              </tr>
              <tr
                v-for="(item, index) in refundRankForCause"
                :key="index"
                class="table-tit"
              >
                <td class="padLeft">
                  {{ item[0] }}
                </td>
                <td>{{ item[1] }}</td>
                <td>{{ item[3] }}</td>
                <td>{{ item[4] }}%</td>
              </tr>
            </table>
          </div>
          <div
            v-else
            style="height:90%"
          >
            <table
              class="table"
              cellpadding="0"
              cellspacing="0"
            >
              <tr class="table-tit gray">
                <td class="table-tit-item ranks padLeft">
                  {{ $t("home.rank") }}
                </td>
                <td class="table-tit-item prods">
                  {{ $t("home.reason") }}
                </td>
                <td class="table-tit-item refs">
                  {{ $t("home.refundAmount") }}
                </td>
                <td class="table-tit-item prop">
                  {{ $t("home.moneyRatio") }}
                </td>
              </tr>
            </table>
            <div class="sign-noData">
              {{ $t("order.noData") }}
            </div>
          </div>
        </div>
        <!-- 退款率 -->
        <div class="refund-rate-box">
          <div class="refund-chart-msg">
            <div class="msg-tit-txt">
              <p class="msg-tit">
                {{ $t("home.refundRateToday") }}
              </p>
            </div>
          </div>
          <!-- 图表 -->
          <div
            id="refPer-chart"
            class="refund-chart"
            style="width: 100%; height: 450px"
          />
          <!--/ 图表 -->
        </div>
      </div>

      <!-- 下 -->
      <div class="refund-chart-box">
        <div class="ranking-right">
          <div class="ranking-title">
            {{ $t("home.refundProRank") }}
          </div>
          <div v-if="refundRankForActualTotal.length">
            <table class="table table-con">
              <tr class="table-tit gray">
                <td class="table-tit-item rank padLeft">
                  {{ $t("home.rank") }}
                </td>
                <td class="table-tit-item prod">
                  {{ $t("home.product") }}
                </td>
                <td
                  class="table-tit-item ref"
                  style="text-align:right"
                >
                  {{ $t("home.successfulRefundNum") }}
                </td>
              </tr>
              <tr
                v-for="(item, index) in refundRankForActualTotal"
                :key="index"
              >
                <td class="padLeft">
                  {{ item[0] }}
                </td>
                <td
                  class="line-clamp-one"
                  style="word-break: break-word;"
                >
                  {{ item[1] }}
                </td>
                <td style="text-align:right">
                  {{ item[2] }}
                </td>
              </tr>
            </table>
          </div>
          <div
            v-else
            style="height:90%"
          >
            <table class="table table-con">
              <tr class="table-tit gray">
                <td class="table-tit-item rank padLeft">
                  {{ $t("home.rank") }}
                </td>
                <td class="table-tit-item prod">
                  {{ $t("home.product") }}
                </td>
                <td
                  class="table-tit-item ref"
                  style="text-align:right"
                >
                  {{ $t("home.successfulRefundNum") }}
                </td>
              </tr>
            </table>
            <div class="sign-noData">
              {{ $t("order.noData") }}
            </div>
          </div>
        </div>
        <!-- 成功退款金额（元） -->
        <div class="successed-refund">
          <div class="refund-chart-msg">
            <div class="msg-tit-txt">
              <p class="msg-tit">
                {{ $t("home.successfulRefundAmountToday") }}
              </p>
            </div>
          </div>
          <!-- 图表 -->
          <div
            id="ref-chart"
            class="refund-chart"
            style="width: 100%; height: 450px"
          />
          <!--/ 图表 -->
        </div>
      </div>
    </div>
    <!-- /退款看板 -->
  </div>
</template>

<script setup>
import * as $echarts from 'echarts'
import elementResizeDetectorMaker from 'element-resize-detector'
import { onMounted, reactive, markRaw } from 'vue'
import { ElMessage } from 'element-plus'
import NoticeDetails from '@/views/common/home/<USER>/notice-details.vue'

const Data = reactive({
  value: [0, 1],
  sumPayActualTotal: 0.00,
  dataForm: {},
  realTimeDataXAxis: ['0:00', '1:00', '2:00', '3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'], // 实时数据的x轴数据
  payYesterdayActualTotal: [],
  dataArray: [],
  timeArray: [],
  dataInfo: {},
  rate: [],
  nowDate: null,
  nowTime: null,
  refundRateInfo: [],
  refundRankForActualTotal: [],
  refundRankForCause: [],
  dataList: [],
  noticeData: {},
  orderStateCount: {},
  stockWarningNum: '',
  viewDialog: false,
  observer: null,
  firedNum: 0,
  recordOldValue: { // 记录下旧的宽高数据，避免重复触发回调函数
    width: '0',
    height: '0'
  },
  entiretyChat: null, // 整体看版
  refChart: null, // 成功退款金额
  refPerChart: null, // 退款率

  // 店铺状态信息
  shopStatusInfo: {
    accountStatus: 0, // 账号状态， 1:启用 0:禁用 -1:删除
    shopStatus: null, // 店铺状态 -1:已删除 0: 停业中 1:营业中 2:平台下线 3:待审核 4:店铺申请中 5:申请失败 6:上线申请中
    offlineStatus: 0, // 下线状态 1平台进行下线 2 重新申请，等待审核 3.审核通过 4 审核未通过
    offlineReason: '', // 下线原因
    contractStartTime: '', // 签约起始时间
    contractEndTime: '', // 签约终止时间
    closedReason: '' // 停业原因
  },
  platformNotify: [],
  notifyList: []
})

const { sumPayActualTotal, dataInfo, nowDate, nowTime, refundRankForActualTotal, refundRankForCause, orderStateCount, stockWarningNum, shopStatusInfo, platformNotify, notifyList } = toRefs(Data)

onMounted(() => {
  getpayActualTotalByDay()
  getpayActualTotalByHour()
  getOrderRefundDayByTime()
  getOrderCount()
  getSkuStockWarning()
  getRefundInfo()
  getRefundRankingByProd()
  getRefundRankingByReason()
  getpayInfo()
  getRefSumEchartData()
  getRefPerEchartData()
  getAuditingInfo()
  getEntiretyDataChart()
  getPlatformNotify()
  getNotifyList()
  const erd = elementResizeDetectorMaker()
  erd.listenTo(document.getElementById('entirety-data-chart'), resizeFunc)
})

const dataFormat = (value) => {
  return (value * 100).toFixed(2).valueOf()
}

const resizeFunc = () => {
  nextTick(() => {
    // 使echarts尺寸重置
    Data.entiretyChat.resize()
    Data.refChart.resize()
    Data.refPerChart.resize()
  })
}

/**
 * 获取店铺状态信息
 */
const getAuditingInfo = () => {
  http({
    url: http.adornUrl('/shop/shopDetail/getStatusInfo'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    // 店铺状态shopStatus：-1:已删除 0: 停业中 1:营业中 2:平台下线 3:上线待审核 4:店铺申请中 5:开店待审核
    Data.shopStatusInfo = data
    if (data.shopStatus === 0) {
      const now = new Date()
      const contractStartTime = new Date(Date.parse(data.contractStartTime))
      Data.shopStatusInfo.closedReason = now < contractStartTime ? $t('shopProcess.theContractIsNotValid') : $t('shopProcess.validityPeriodOfTheContractHasExpired')
    }
  })
}

//  同比昨日
const isTrueData = (data) => {
  if (data) {
    return data
  }
  return 0
}

// 查询店铺商品库存预警数量
const getSkuStockWarning = () => {
  http({
    url: http.adornUrl('/sku/stockWarningCount'),
    method: 'get'
  }).then(({ data }) => {
    Data.stockWarningNum = data
  })
}

// 查询店铺订单各状态数量
const getOrderCount = () => {
  http({
    url: http.adornUrl('/shop/statisticsOrder/orderCount'),
    method: 'get'
  }).then(({ data }) => {
    Data.orderStateCount = data
  })
}

// 获取实时数据报表图
const getRealTimeDataChart = () => {
  const date = new Date()
  const seperator1 = '-'
  const year = date.getFullYear()
  let month = date.getMonth() + 1
  let strDate = date.getDate()
  const hours = date.getHours() // 获取当前小时数(0-23)
  const minutes = date.getMinutes() // 获取当前分钟数(0-59)
  const seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds() // 获取当前秒数(0-59)
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate
  }
  Data.nowDate = year + seperator1 + month + seperator1 + strDate
  Data.nowTime = hours + ':' + minutes + ':' + seconds

  const myChart = markRaw($echarts.init(document.getElementById('real-time-data-chart')))
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      padding: 8,
      textStyle: { // 提示框浮层的文本样式。
        fontSize: '12px',
        fontWeight: 400,
        color: '#33333',
        opacity: 1
      },
      extraCssText: 'box-shadow:0px 1px 11px rgba(213, 220, 226, 0.73);',
      formatter: function (params) {
        // return arg.name +
        // // return '反弹率:' + arg[0].data + '%'

        // params数组可以了解一下，一条线和多条线都是数组
        let html = ''
        for (const i in params) {
          const param = params[i]
          // echarts日期格式化api
          const date = param.name
          html += `<span style="color: #999999">${param.seriesIndex === 0 ? date : ''}</span><br /><div style="margin-top:${param.seriesIndex === 0 ? '12px' : '0'};display: flex;align-items: center;"><i style="display: inline-block;width: 6px;height: 6px;background-color: ${param.seriesIndex === 0 ? '#0058FF' : '#21D59B'};margin-right: 9px;border-radius: 50%;"></i><span style=" display:inline-block;">${param.seriesName}：${param.value}</span></div>`
        }
        return html
      }
    },
    grid: {
      left: 20,
      right: 20,
      top: 55,
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          show: false
        }
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      symbol: 'none',
      data: Data.realTimeDataXAxis,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E0E6F1'],
          width: 1,
          type: 'solid'
        }
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: $t('home.dataTodayActualTotal'),
        symbol: 'none',
        type: 'line',
        smooth: true,
        itemStyle: {
          lineStyle: {
            color: '#1890FF'
          }
        },
        areaStyle: {
          color: new $echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: 'rgba(0, 88, 255, 0.2)' },
              { offset: 1, color: 'rgba(255, 255, 255, 0)' }
            ]
          )
        },
        data: Data.dataForm.nowActualTotal
      },
      {
        name: $t('home.dataYesterdayActualTotal'),
        type: 'line',
        smooth: true,
        itemStyle: {
          lineStyle: {
            color: '#21D59B'
          }
        },
        areaStyle: {
          color: new $echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(41, 203, 151, 0.2)'
          }, {
            offset: 1,
            color: 'rgba(255, 255, 255, 0)'
          }])
        },
        data: Data.dataForm.payYesterdayActualTotal
      }
    ]
  }
  option && myChart.setOption(option)
  window.addEventListener('resize', () => {
    myChart.resize()
  })
}

// 获取整体看板柱状图
const getEntiretyDataChart = () => {
  Data.entiretyChat = markRaw($echarts.init(document.getElementById('entirety-data-chart')))
  const option = {
    color: ['red'],
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      padding: 8,
      textStyle: { // 提示框浮层的文本样式。
        fontSize: '12px',
        fontWeight: 400,
        color: '#33333',
        opacity: 1
      },
      extraCssText: 'box-shadow:0px 1px 11px rgba(213, 220, 226, 0.73);width:180px;height:62px',
      formatter: function (params) {
        // return arg.name +
        // // return '反弹率:' + arg[0].data + '%'

        // params数组可以了解一下，一条线和多条线都是数组
        let html = ''
        for (const i in params) {
          const param = params[i]
          // echarts日期格式化api
          const date = param.name
          html += `<span style="color: #999999">${date}</span><br /><div style="margin-top: 12px;display: flex;align-items: center;"><i style="display: inline-block;width: 6px;height: 6px;background-color: #0058FF;margin-right: 9px;border-radius: 50%;"></i><span style=" display:inline-block;">${param.seriesName}：${param.data}</span></div>`
        }
        return html
      },
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: 20,
      right: 20,
      width: '95%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        showMaxLabel: true
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      data: Data.timeArray
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E0E6F1'],
          width: 1,
          type: 'solid'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: $t('home.transactionAmount'),
        type: 'bar',
        itemStyle: {
          color: '#e8f0ff',
          borderRadius: [15, 15, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: '#1890FF'
          }
        },
        barWidth: 20,
        data: Data.dataArray
      }
    ]
  }

  option && Data.entiretyChat.setOption(option)
  window.addEventListener('resize', () => {
    Data.entiretyChat.resize()
  })
}

const getpayActualTotalByHour = () => {
  Data.dataListLoading = true
  http({
    url: http.adornUrl('/shop/statisticsOrder/getActualTotalByHour'),
    method: 'get'
  }).then(({ data }) => {
    if (data) {
      Data.dataForm = data
      getRealTimeDataChart()
      Data.dataListLoading = false
    }
  })
}

const getpayActualTotalByDay = () => {
  Data.dataListLoading = true
  http({
    url: http.adornUrl('/shop/statisticsOrder/getActualTotalByDay'),
    method: 'get'
  }).then(({ data }) => {
    if (data) {
      Data.dataArray = data
      Data.dataListLoading = false
      Data.dataForm = data
      Data.timeArray = []
      Data.dataArray = []
      for (let j = 0, len = Data.dataForm.length; j < len; j++) {
        Data.timeArray.push(Data.dataForm[j].dates)
        Data.dataArray.push(Data.dataForm[j].payActualTotal)
        if (j === len - 1) {
          Data.sumPayActualTotal = Data.dataForm[j].onePrice
        }
      }
      getEntiretyDataChart()
      Data.dataListLoading = false
    }
  })
}

const getOrderRefundDayByTime = () => {
  Data.dataListLoading = true
  http({
    url: http.adornUrl('/shop/statisticsOrder/getOrderRefundDayByTime'),
    method: 'get'
  }).then(({ data }) => {
    if (data) {
      Data.dataForm = data
      Data.timeArray = []
      Data.dataArray = []
      for (let j = 0, len = Data.dataForm.length; j < len; j++) {
        Data.timeArray.push(Data.dataForm[j].refundDateToString)
        Data.dataArray.push(Data.dataForm[j].payActualTotal)
        Data.rate.push(Data.dataForm[j].refundRate)
      }
      getRefSumEchartData()
      getRefPerEchartData()
      Data.dataListLoading = false
    }
  })
}

const getpayInfo = () => {
  http({
    url: http.adornUrl('/shop/statisticsOrder/orderPayByShopId'),
    method: 'get'
  }).then(({ data }) => {
    if (data) {
      Data.dataInfo = data
      Data.dataListLoading = false
    }
  })
}

const getRefundInfo = () => {
  http({
    url: http.adornUrl('/shop/statisticsOrder/getOrderRefundByTime'),
    method: 'get'
  }).then(({ data }) => {
    if (data) {
      Data.refundRateInfo = data
      Data.dataListLoading = false
    }
  })
}

const getRefundRankingByProd = () => {
  http({
    url: http.adornUrl('/shop/statisticsOrder/getRefundRankingByProd'),
    method: 'get',
    params: http.adornParams(
      Object.assign({
        size: 5
      })
    )
  }).then(({ data }) => {
    if (data) {
      Data.dataForm = data
      for (let j = 0, len = Data.dataForm.length; j < len; j++) {
        const refund = []
        refund.push(j + 1)
        refund.push(Data.dataForm[j].refundPordName)
        refund.push(Data.dataForm[j].refundCount)
        Data.refundRankForActualTotal.push(refund)
      }

      Data.dataListLoading = false
    }
  })
}

const getRefundRankingByReason = () => {
  http({
    url: http.adornUrl('/shop/statisticsOrder/getRefundRankingByReason'),
    method: 'get'
  }).then(({ data }) => {
    if (data) {
      Data.dataForm = data
      for (let j = 0, len = Data.dataForm.length; j < len; j++) {
        const refund = []
        refund.push(j + 1)
        refund.push(Data.dataForm[j].buyerReason)
        refund.push(Data.dataForm[j].refundCount)
        refund.push(Data.dataForm[j].payActualTotal)
        refund.push(Data.dataForm[j].refundRate)
        Data.refundRankForCause.push(refund)
      }

      Data.dataListLoading = false
    }
  })
}

const getRefSumEchartData = () => {
  Data.refChart = markRaw($echarts.init(document.getElementById('ref-chart')))
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      padding: 8,
      textStyle: { // 提示框浮层的文本样式。
        fontSize: '12px',
        fontWeight: 400,
        color: '#33333',
        opacity: 1
      },
      extraCssText: 'box-shadow:0px 1px 11px rgba(213, 220, 226, 0.73);width:180px;height:62px',
      formatter: function (params) {
        // return arg.name +
        // // return '反弹率:' + arg[0].data + '%'

        // params数组可以了解一下，一条线和多条线都是数组
        let html = ''
        for (const i in params) {
          const param = params[i]
          // echarts日期格式化api
          const date = param.name
          html += `<span style="color: #999999">${date}</span><br /><div style="margin-top: 12px;display: flex;align-items: center;"><i style="display: inline-block;width: 6px;height: 6px;background-color: #0058FF;margin-right: 9px;border-radius: 50%;"></i><span style=" display:inline-block;">${param.seriesName}：${param.data}</span></div>`
        }
        return html
      }
    },
    grid: {
      left: '2%',
      right: '2%',
      top: 55,
      width: '91%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          show: false
        }
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: Data.timeArray,
      nameTextStyle: {
        color: 'red'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E0E6F1'],
          width: 1,
          type: 'solid'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: $t('order.refundAmount'),
        type: 'line',
        smooth: true,
        itemStyle: {
          lineStyle: {
            color: '#29CB97'
          }
        },
        areaStyle: {
          color: new $echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(41, 203, 151, 0.2)'
          }, {
            offset: 1,
            color: 'rgba(255, 255, 255, 0.11)'
          }])
        },
        data: Data.dataArray
      }
    ]
  }
  option && Data.refChart.setOption(option)
  window.addEventListener('resize', () => {
    Data.refChart.resize()
  })
}

const getRefPerEchartData = () => {
  Data.refPerChart = markRaw($echarts.init(document.getElementById('refPer-chart')))
  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      padding: 8,
      textStyle: { // 提示框浮层的文本样式。
        fontSize: '12px',
        fontWeight: 400,
        color: '#33333',
        opacity: 1
      },
      extraCssText: 'box-shadow:0px 1px 11px rgba(213, 220, 226, 0.73);width:180px;height:62px',
      formatter: function (params) {
        // return arg.name +
        // // return '反弹率:' + arg[0].data + '%'

        // params数组可以了解一下，一条线和多条线都是数组
        let html = ''
        for (const i in params) {
          const param = params[i]
          // echarts日期格式化api
          const date = param.name
          html += `<span style="color: #999999">${date}</span><br /><div style="margin-top: 12px;display: flex;align-items: center;"><i style="display: inline-block;width: 6px;height: 6px;background-color: #0058FF;margin-right: 9px;border-radius: 50%;"></i><span style=" display:inline-block;">${param.seriesName}：${param.data}%</span></div>`
        }
        return html
      }
    },
    grid: {
      left: '2%',
      right: '2%',
      top: 55,
      width: '91%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          show: false
        }
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: Data.timeArray,
      lineStyle: {
        color: 'red'
      },
      nameTextStyle: {
        color: 'red'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#E0E6F1'],
          width: 1,
          type: 'solid'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: $t('home.refundRate'),
        type: 'line',
        smooth: true,
        itemStyle: {
          lineStyle: {
            color: '#1890FF'
          }
        },
        areaStyle: {
          color: new $echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(0, 88, 255, 0.1)'
          }, {
            offset: 1,
            color: '#ffffff'
          }])
        },
        data: Data.rate
      }
    ]
  }
  option && Data.refPerChart.setOption(option)
  window.addEventListener('resize', () => {
    Data.refPerChart.resize()
  })
}

const router = useRouter()
/**
 * 跳转订单页
 */
const toOrderPage = (sts) => {
  if (!isAuth('order:get:info')) {
    ElMessage.info($t('homes.noPermissionAccessPage'))
    return
  }
  router.push({
    path: '/order/order/index',
    query: { status: sts }
  })
}

const toStockPage = (sts) => {
  if (!isAuth('multishop:inquireStock:export')) {
    ElMessage.info($t('homes.noPermissionAccessPage'))
    return
  }
  router.push({
    path: '/stock/manage/inquire?isStockWarning',
    query: { isStockWarning: sts }
  })
}

/**
 * 跳转退款订单页
 */
const toRefundOrderPage = (sts) => {
  if (!isAuth('admin:orderRefund:update')) {
    ElMessage.info($t('homes.noPermissionAccessPage'))
    return
  }
  router.push({
    path: '/order/order-refund/index',
    query: { returnMoneySts: sts }
  })
}

const commonStore = useCommonStore()
const toNotify = () => {
  const routeList = commonStore.routeList
  const flag = routeList.some(item => '/' + item.url === '/sys/message-manager/platform-notify/index')
  if (!flag) {
    return ElMessage.info($t('homes.noPermissionAccessPage'))
  }
  router.push({
    path: '/sys/message-manager/platform-notify/index'
  })
}

const toManager = () => {
  const routeList = commonStore.routeList
  const flag = routeList.some(item => '/' + item.url === '/sys/message-manager/notify-manager/index')
  if (!flag) {
    return ElMessage.info($t('homes.noPermissionAccessPage'))
  }
  router.push({
    path: '/sys/message-manager/notify-manager/index'
  })
}

const getPlatformNotify = () => {
  http({
    url: http.adornUrl('/shop/notice/listPage'),
    method: 'get',
    params: http.adornParams({ size: 6, current: 1 })
  }).then(({ data }) => {
    Data.platformNotify = data.records
  })
}

const getNotifyList = () => {
  http({
    url: http.adornUrl('/multishop/notifyLog/pageByNotifyLogParam'),
    method: 'get',
    params: http.adornParams({ size: 6, current: 1 })
  }).then(({ data }) => {
    Data.notifyList = data.records
  })
}

const notifyView = (row) => {
  const routeList = commonStore.routeList
  const pathObj = {
    path: '',
    query: {}
  }
  if (row.sendType === 102 || row.sendType === 103 || row.sendType === 104) {
    pathObj.path = '/order/order-refund/index'
    pathObj.query.returnMoneySts = row.sendType === 102 ? 4 : row.sendType === 103 ? 1 : 5
  }
  if (row.sendType === 105 || row.sendType === 109) {
    pathObj.path = '/order/order/index'
    pathObj.query.status = row.sendType === 105 ? 2 : 3
  }
  if (row.sendType === 107 || row.sendType === 106) {
    pathObj.path = '/prod/manage/index'
  }
  if (row.sendType === 108) {
    pathObj.path = '/stock-purchasesOrder'
  }
  if (row.sendType === 110 || row.sendType === 111) {
    pathObj.path = '/marketing/discount/index'
  }
  const flag = routeList.some(item => '/' + item.url === pathObj.path)
  if (!flag) {
    return ElMessage.info($t('homes.noPermissionAccessPage'))
  }
  if (row.status === 0) {
    http({
      url: http.adornUrl('/multishop/notifyLog/is_read?type=0'),
      method: 'put',
      data: [row.logId]
    }).then(() => {
      router.push(pathObj)
    })
  } else {
    router.push(pathObj)
  }
}
const showNoticeDetails = ref(false)
const noticeDetailsRef = ref(null)
/**
 * 查看公告详情
 * @param { Number } noticeId 点击的公告
 */
const handleShowNoticeDetails = (noticeId) => {
  showNoticeDetails.value = true
  nextTick(() => {
    noticeDetailsRef.value?.init(noticeId)
  })
}

const handleCloseNoticeDetails = () => {
  showNoticeDetails.value = false
}
</script>

<!-- eslint-disable-next-line vue-scoped-css/enforce-style-type -->
<style lang="scss">
@use 'index';
</style>
