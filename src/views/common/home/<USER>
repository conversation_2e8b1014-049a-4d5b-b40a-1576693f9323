// e-chart
#real-time-data-chart canvas {
  width: 100%;
  height: 100%;
  padding: 0 20px !important
}

.page-home {

  // 店铺状态异常提示
  .shop-inf-imperfect-tips {
    width: 100%;
    line-height: 32px;
    font-size: 14px;
    color: #333;
    background: #FFF7DD;
    border: 1px solid #FFD888;
    border-radius: 2px;
    padding: 5px 10px;
    margin-bottom: 10px;

    .el-icon-warning {
      font-size: 16px;
      color: #FFA900;
      margin-left: 5px;
      margin-right: 5px;
    }
  }

  .grap {
    color: #999999;
    min-width: 56px;
    display: inline-block
  }

  // 订单栏项
  .order-all-num {
    .col-box {
      max-width: 50%;
    }

    .row-bg {
      .col-box {
        display: flex;
        justify-content: space-between;

        .num-item-box {
          position: relative;
          flex: 1;
          box-sizing: border-box;
        }
      }

      .col-box:first-child {
        .num-item-box {
          padding-right: 20px;
          box-sizing: border-box;
        }
      }

      .col-box:last-child {
        .num-item-box:not(:last-child) {
          padding-right: 20px;
          box-sizing: border-box;
        }
      }
    }

    .order-num-item {
      // width: calc((100% - 73px) * 0.2);
      display: flex;
      padding: 25px;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      box-sizing: border-box;
      border-radius: 4px;
      cursor: pointer;

      .item-box {
        width: 75%;
        .words {
          font-size: 16px;
          font-weight: 400;
          color: #666;
          height: 30px;
        }

        .number {
          font-size: 26px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #333;
          margin-top: 10px;
        }

        .compare {
          font-size: 14px;
        }
      }
    }

    // 图标过渡效果
    .order-num-item {
      position: relative;
      transition: 0.35s ease;

      &:hover {
        .item-img {
          bottom: 30%;
        }
      }

      .item-img {
        position: absolute;
        right: 5%;
        display: block;
        bottom: 15%;
        width: 30%;
        max-height: 60px;
        max-width: 60px;
        transition: 0.35s ease;

        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  // 添加公告
  .introduce {
    margin-bottom: 10px;
  }

  .router-link-active {
    //点击时去掉下划线
    text-decoration: none;
  }

  a {
    text-decoration: none;
  }

  .title {
    font-size: 22px;
    color: #111111;
    font-weight: bold;
    padding: 20px;
  }

  .btn-more {
    float: right;
    margin: 0 10px 10px 10px;
  }

  .introduce-container {
    padding: 10px;
    display: flex;
    background-color: #b4dff8;
    margin: 20px 0 10px 0;
  }

  .introduce-container .text {
    width: 50%;
    line-height: 30px;
    font-size: 22px;
  }

  // 栏目标题行
  .title-line {
    background: #f8f8f8;
    padding: 12px;
    font-size: 14px;
    line-height: 1em;
  }

  .blue-vertical {
    display: inline-block;
    width: 3px;
    height: 1em;
    background: #155bd4;
    margin-right: 0.5em;
    vertical-align: middle;
  }

  .title-txt {
    color: #000;
    font-weight: bold;
    margin-right: 1em;
    vertical-align: middle;
  }

  .title-time {
    color: #999999;
    font-size: 12px;
    margin-left: 12px;
    vertical-align: middle;
    white-space: nowrap;
  }

  .realtime {
    // display: flex;
    margin-top: 20px;
    padding: 0;
  }

  /**
实时概况
*/
  .realtime-situation {

    //左边
    .realtime-left {
      height: 100%;
      background: #fff;
      border-radius: 4px;
    }

    .col-item {
      padding-right: 20px;
      box-sizing: border-box;
    }

    // 标题
    .title {
      position: relative;
      display: flex;
      align-items: center;
      box-sizing: border-box;

      .t-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        white-space: nowrap;
      }

      .update-time {
        font-size: 12px;
        color: #999999;
        margin-left: 10px;
      }

      .t-small-text {
        font-size: 12px;
        color: #999;
      }

      .t-update-time {
        margin-left: 12px;
      }

      .t-explain-item::before {
        display: inline-block;
        content: '';
        width: 18px;
        height: 8px;
        background: #FF4141;
        border-radius: 8px;
        margin-right: 10px;
      }

      .t-explain-first-item {
        margin-left: 50px;
      }

      .t-explain-second-item {
        margin-left: 40px;
      }

      .t-red::before {
        background: #FF4141;
      }

      .t-dark-green::before {
        background: #42B983;
      }

      .t-today-data {
        min-width: 75px;
      }

      .t-today-data>span {
        display: inline-block;
      }

      .t-today-data::before {
        background: #1890FF;
      }

      .t-yesterday-data {
        min-width: 75px;
      }

      .t-yesterday-data>span {
        display: inline-block;
      }

      .t-yesterday-data::before {
        background: #21D59B;
      }

      .t-pay-amount {
        position: absolute;
        right: 0;
        top: 0;
        display: flex;

        .t-pay-item {
          color: #333;
          text-align: left;
          margin-right: 30px;

          .tt-title {
            font-size: 14px;
          }

          .tt-num {
            font-size: 20px;
            font-weight: bold;
            margin-top: 13px;
          }
        }

        .t-today {
          margin-right: 150px;
        }
      }
    }

    .pay-amount-text {
      font-weight: bold;
    }

    .pay-amount-num {
      font-size: 24px;
      min-height: 24px;
      font-weight: 700;
      margin: 10px 0;
      line-height: 24px;
    }

    .pay-amount-tips {
      color: #9b9b9b;
    }

    // 图表
    .realtime-chart-box {
      left: -10px !important;
      // margin-top: 10px;
    }

    .wrapper__summary:nth-child(2) {
      margin-top: 120px;
    }

    // 右边
    .realtime-right {
      min-width: calc((100% - 20px) * 0.2);
      min-height: 426px;
      height: 100%;
      padding: 20px 0 0 20px;
      background-color: #fff;
      box-sizing: border-box;
      border-radius: 4px;

      .realtime-right-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 45px;
      }

      .realtime-right-box {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 100%;

        .realtime-right-box-left,
        .realtime-right-box-right {
          width: 100%;
          display: flex;
          justify-content: space-between;
        }

        .realtime-right-box-right {
          margin-top: -30px;
        }

        .order-num-item {
          width: 100%;
          display: flex;

          // 兼容 火狐67 高度不自动适应问题
          min-height: 200px;

          .item-img {
            margin-right: 10%;
            min-width: 30px;
            min-height: 30px;

            .words {
              font-size: 16px;
              font-weight: 400;
              color: #666;
            }

            .number {
              font-size: 24px;
              font-family: Microsoft YaHei;
              font-weight: bold;
              color: #333;
              margin: 14px 0;
            }

            .compare {
              font-size: 14px;
            }
          }

          .item-box {
            .words {
              font-size: 16px;
              font-weight: 400;
              color: #666;
            }

            .number {
              font-size: 26px;
              font-family: Microsoft YaHei;
              font-weight: bold;
              color: #333;
              margin: 14px 0;
            }

            .seq {

              // display: flex;
              .value {
                color: #333333;
                font-weight: bold;
                margin-right: 8px;
              }
            }
          }
        }

        // .order-num-item:nth-child(even){
        //     margin-left: 86px;
        // }
      }
    }

    .realtime .wrapper__summary {
      display: flex;
    }

    .realtime .wrapper__summary .summary__box {
      display: inline-flex;
      flex: 1;
      margin-left: 20px;
    }

    .realtime .wrapper__summary .summary__item {
      position: relative;
      width: 100%;
    }

    //客户数信息
    .customer-number-info {
      margin-left: 60px;
    }

    .text-tit {
      font-weight: 600;
    }

    .text-num {
      font-size: 24px;
      min-height: 24px;
      font-weight: 700;
      margin: 15px 0;
      line-height: 24px;
    }

    .text-tips {
      color: #9b9b9b;
    }

    // 客户数icon
    svg {
      fill: currentColor;
      vertical-align: middle;
    }

    .realtime .wrapper__summary .summary__svg {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  /**
整体看板
*/
  .whole-plate {
    margin: 20px 0 20px;
    min-height: 460px;
    background-color: #fff;
    border-radius: 4px;

    // 标题
    .title {
      width: 80%;
      position: relative;
      display: flex;
      align-items: center;

      .t-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }

      .update-time {
        font-size: 12px;
        color: #999999;
        margin-left: 10px;
      }

      .t-small-text {
        font-size: 12px;
        color: #999;
      }

      .t-update-time {
        margin-left: 12px;
      }

      .t-explain-item::before {
        display: inline-block;
        content: '';
        width: 18px;
        height: 8px;
        background: #FF4141;
        border-radius: 8px;
        margin-right: 10px;
      }

      .t-explain-first-item {
        margin-left: 50px;
      }

      .t-explain-second-item {
        margin-left: 40px;
      }

      .t-red::before {
        background: #FF4141;
      }

      .t-dark-green::before {
        background: #42B983;
      }

      .t-today-data::before {
        background: #1890FF;
      }

      .t-yesterday-data::before {
        background: #21D59B;
      }

      .t-pay-amount {
        position: absolute;
        right: 0;
        top: 0;
        display: flex;

        .t-pay-item {
          color: #333;
          text-align: left;
          margin-right: 30px;

          .tt-title {
            font-size: 14px;
          }

          .tt-num {
            font-size: 20px;
            font-weight: bold;
            margin-top: 13px;
          }
        }

        .t-today {
          margin-right: 150px;
        }
      }
    }

    .money {
      width: 20%;
      display: flex;
      justify-content: space-between;

      .related-income-item {
        text-align: center;
      }
    }

    .related-income {
      margin: 20px 0 0 0;
      padding-right: 50px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .item-tit {
      margin-top: 23px;
      margin-bottom: 15px;
    }

    .item-num {
      height: 24px;
      line-height: 24px;
    }

    .item-num-price {
      font-size: 18px;
      font-weight: 700;
    }

    .item-num a {
      color: #155bd4;
      text-decoration: none;
    }

    .integral-plate {
      margin-top: 15px;
    }
  }

  /**
退款看板
*/
  .refund-plate {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .sign-noData {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999999;
      width: 100%;
      height: 100%;
    }

    // 上
    .ranking-box {
      display: flex;
      flex-wrap: wrap;
      height: auto;
      align-content: flex-start;
      width: 50%;
      padding-right: 20px;

      .ranking-left,
      .refund-rate-box {
        display: flex;
        flex-direction: column;
        width: 100%;
        background: #fbfbfb;
        padding: 20px;
        box-sizing: border-box;
        height: calc((100% - 20px) * 0.5);
        border-radius: 4px;
      }

      .ranking-left {
        height: calc((100% - 20px) * 0.43);
      }

      .ranking-title {
        font-weight: bold;
        color: #333;
        font-size: 18px;
        margin-bottom: 10px;
      }

      .table {
        width: 100%;
        border: none;
        background: #fbfbfb;
        // border-spacing: 10px 20px;
        text-align: left;

        tr {
          border: none;
          height: 3em;
          line-height: 1.5em;
        }

        td {
          border: none;
          word-break: break-wrod;
        }

        .gray {
          background: #F7F8FA;
        }

        .table-tit-item {
          color: #999;
          text-align: left;
        }

        .rank {
          width: 20%;
        }

        .prod {
          width: 50%;
        }

        .ref {
          width: 20%;
        }

        .ranks {
          width: 20%;
        }

        .prods {
          width: 40%;
        }

        .refs {
          width: 30%;
        }

        .prop {
          width: 10%;
          min-width: 60px;
          // text-align: center;
        }
      }

      .refund-rate-box {
        margin-top: 20px;
      }

      .no-data {
        height: 60px;
        line-height: 60px;
        text-align: center;
        font-size: 14px;
        color: #999;
      }

      .line-clamp-one {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        /* autoprefixepxr: off */
        -webkit-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
      }

      .padLeft {
        padding-left: 20px;
      }

      .msg-tit {
        font-weight: bold;
        color: #333;
        font-size: 18px;
        margin-bottom: 15px;
      }
    }

    //  下
    .refund-chart-box {
      display: flex;
      flex-wrap: wrap;
      height: auto;
      align-content: flex-start;
      width: 50%;

      .successed-refund,
      .ranking-right {
        display: flex;
        width: 100%;
        flex-direction: column;
        background: #fbfbfb;
        padding: 20px;
        box-sizing: border-box;
        height: calc((100% - 20px) * 0.43);
        border-radius: 4px;
      }

      .successed-refund {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        width: 100%;
        background: #fbfbfb;
        padding: 20px;
        box-sizing: border-box;
        height: calc((100% - 20px) * 0.5);
      }

      .ranking-title {
        font-weight: bold;
        color: #333;
        font-size: 18px;
        margin-bottom: 10px;
      }

      .table {
        width: 100%;
        border: none;
        background: #fbfbfb;
        // border-spacing: 10px 20px;
        text-align: left;

        tr {
          border: none;
          height: 3em;
          line-height: 1.5em;
        }

        td {
          border: none;
          word-break: break-wrod;
        }

        .gray {
          background: #F7F8FA;
        }

        .table-tit-item {
          color: #999;
          text-align: left;
        }

        .rank {
          width: 20%;
        }

        .prod {
          width: 50%;
        }

        .ref {
          width: 20%;
        }

        .ranks {
          width: 20%;
        }

        .prods {
          width: 40%;
        }

        .refs {
          width: 30%;
        }

        .prop {
          width: 10%;
          // text-align: center;
        }
      }

      .msg-tit {
        font-weight: bold;
        color: #333;
        font-size: 18px;
        margin-bottom: 15px;
      }

      .padLeft {
        padding-left: 20px;
      }

      .msg-price {
        margin: 6px 0;
        padding: 0;
        font-size: 24px;
        font-weight: bold;
        line-height: 1em;
      }

      .msg-txt-p {
        width: 146px;
      }

      .compare {
        color: #9b9b9b;
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        padding: 5px;
      }

      .compare-left {
        margin-right: 10px;
        white-space: nowrap;
      }
    }

    .integral-plate canvas {
      left: -5px !important;
    }
  }

  .order-data-right {
    width: 20%;
    display: flex;
    flex-direction: column;

    .data-item-box {
      flex: 1;
      background: #FFFFFF;
      margin-bottom: 20px;
      padding: 20px;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .data-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        align-items: center;

        .left-title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
        }

        .right-title {
          color: #B3B3B3;
          font-size: 12px;
          cursor: pointer;
        }
      }

      .content {
        font-size: 14px;
        margin: 20px 0;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .notify-view {
        cursor: pointer;

        &:hover {
          color: #155bd4;
        }
      }

      .no-data {
        font-size: 16px;
        position: absolute;
        color: #999999;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .reads-dot {
    color: #999;
  }

  .unread-point {
    color: #155bd4;
  }
}

@media screen and (max-width: 1680px) {
  .page-home {

    // 订单栏项
    .order-all-num {
      .order-num-item {
        // width: calc((100% - 73px) * 0.2);
        display: flex;
        padding: 25px 10px;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        box-sizing: border-box;
        border-radius: 4px;
        cursor: pointer;

        .item-box {
          .words {
            font-size: 16px;
            font-weight: 400;
            color: #666;
          }

          .number {
            font-size: 26px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #333;
            margin-top: 10px;
          }

          .seq {
            display: flex
          }

          .compare {
            font-size: 14px;
          }
        }
      }

      // 图标过渡效果
      .order-num-item {
        position: relative;
        transition: 0.35s ease;

        &:hover {
          .item-img {
            bottom: 30%;
          }
        }

        .item-img {
          position: absolute;
          right: 8px;
          display: block;
          bottom: 15%;
          width: 30%;
          max-height: 60px;
          max-width: 60px;
          transition: 0.35s ease;

          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
