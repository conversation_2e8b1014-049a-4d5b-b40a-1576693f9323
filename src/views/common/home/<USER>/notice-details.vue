<template>
  <el-dialog
    v-model="visible"
    :title="$t('notice.plaNotice')"
    width="690px"
    :before-close="beforeClose"
  >
    <p class="notice-title">
      {{ noticeInfo.title }}
    </p>
    <div class="notice-html-con">
      <span v-rich="noticeInfo.content" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn primary-btn"
          @click="beforeClose"
        >
          {{ $t('common.confirm') }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const noticeInfo = ref({})
const visible = ref(false)

const init = (noticeId) => {
  http({
    url: http.adornUrl('/shop/notice/info/' + noticeId),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    visible.value = true
    noticeInfo.value = data
  })
}

const emit = defineEmits(['on-close'])
const beforeClose = (done) => {
  visible.value = false
  emit('on-close')
  if (done) done()
}
defineExpose({
  init
})

</script>

<style lang="scss" scoped>
.notice-title {
  text-align: center;
}
.notice-html-con {
  max-height: 370px;
  overflow-x: hidden;
  word-break: break-word;
}
.notice-html-con::-webkit-scrollbar {
  width: 6px;
  height: 1px;
  border-radius: 4px;
  background: #f7f8fa;
}
.notice-html-con::-webkit-scrollbar-thumb {
  background: #e9ecf3;
  border-radius: 4px;
}
</style>
