<template>
  <div class="mod-shop-notice page-platform-notify">
    <div class="search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="title"
            :label="$t('shop.announcementTitle')+':'"
          >
            <el-input
              v-model="searchForm.title"
              type="text"
              clearable
              :placeholder="$t('shop.announcementTitle')"
            />
          </el-form-item>
          <el-form-item
            prop="publishTime"
            :label="$t('publics.releaseTime')+':'"
          >
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              clearable
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="resetForm()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="table-con">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('number')"
            type="index"
            align="left"
            width="100"
          />
          <el-table-column
            prop="title"
            :label="$t('shop.announcementTitle')"
          />
          <el-table-column
            prop="publishTime"
            :label="$t('publics.releaseTime')"
          />
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="250"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  class="default-btn text-btn"
                  @click="viewNotice(scope.row.id)"
                >
                  {{ $t('live.view') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <el-dialog
      v-model="viewDialog"
      :title="$t('notice.plaNotice')"
      width="690px"
      :before-close="handleClose"
    >
      <p style="text-align: center">
        {{ dataForm.title }}
      </p>
      <div class="notice-html-con">
        <span v-rich="dataForm.content" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="default-btn primary-btn"
            @click="viewDialog = false"
          >
            {{ $t('groups.determine') }}
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>

let tempSearchForm = null // 保存上次点击查询的请求条件
let theParams = null // 保存上次点击查询的请求条件
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  title: ''
})
const dateRange = ref([])

const commonStore = useCommonStore()
onMounted(() => {
  getDataList(page)
  const title = $t('notice.plaNotice')
  commonStore.replaceSelectMenu(title)
})

const dataForm = ref({})
// 查看单个平台公告
const init = (id) => {
  dataForm.value.id = id || 0
  nextTick(() => {
    if (dataForm.value.id) {
      http({
        url: http.adornUrl('/shop/notice/info/' + dataForm.value.id),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        dataForm.value = data
      })
    }
  })
}

const viewDialog = ref(false)
// 关闭弹窗
const handleClose = (done) => {
  done()
  viewDialog.value = false
}

const dataList = ref([])
const getDataList = (pageParam, newData = false) => {
  if (newData || !tempSearchForm) {
    theParams = JSON.parse(JSON.stringify(searchForm))
    tempSearchForm = {
      current: pageParam == null ? page.currentPage : pageParam.currentPage,
      size: pageParam == null ? page.pageSize : pageParam.pageSize,
      beginTime: dateRange.value ? dateRange.value[0] : null, // 开始时间
      endTime: dateRange.value ? dateRange.value[1] : null // 结束时间
    }
  } else {
    tempSearchForm.current = pageParam == null ? page.currentPage : pageParam.currentPage
    tempSearchForm.size = pageParam == null ? page.pageSize : pageParam.pageSize
  }
  http({
    url: http.adornUrl('/shop/notice/listPage'),
    method: 'get',
    params: http.adornParams(Object.assign(tempSearchForm, theParams))
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 查看
const viewNotice = (id) => {
  viewDialog.value = true
  nextTick(() => {
    init(id)
  })
}

const onSearch = (newData = false) => {
  page.currentPage = 1
  page.pageSize = 10
  getDataList(page, newData)
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList()
}

const onPageChange = (val) => {
  page.currentPage = val
  getDataList()
}

const searchFormRef = ref(null)
const resetForm = () => {
  searchFormRef.value.resetFields()
  dateRange.value = []
}

</script>

<style lang="scss" scoped>
.page-platform-notify {
  .notice-html-con {
    max-height: 370px;
    overflow-x: hidden;
    word-break: break-word;
  }
  .notice-html-con::-webkit-scrollbar {
    width: 6px;
    height: 1px;
    border-radius: 4px;
    background: #f7f8fa;
  }
  .notice-html-con::-webkit-scrollbar-thumb {
    background: #e9ecf3;
    border-radius: 4px;
  }
}
</style>
