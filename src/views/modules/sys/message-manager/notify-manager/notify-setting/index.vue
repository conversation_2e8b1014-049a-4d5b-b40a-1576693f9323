<template>
  <div class="Mall4j component-notify-setting">
    <!-- 列表相关区域 -->
    <div class="table-con">
      <el-table
        v-loading="pageLoading"
        :data="pageVO.records"
        header-cell-class-name="table-header"
        row-class-name="table-row-low"
        style="width: 100%;"
      >
        <el-table-column
          prop="menu"
          align="left"
          :label="$t('notice.menu')"
          width="100"
        />
        <el-table-column
          :label="$t('notice.msgType')"
          prop="label"
          align="left"
        />
        <el-table-column
          align="left"
          prop="nodeName"
          :label="$t('notice.pushNode')"
        />
        <!-- 操作 -->
        <el-table-column
          :label="$t('crud.menu')"
          align="center"
          width="230"
          class-name="small-padding fixed-width"
        >
          <template #default="{row}">
            <div class="table-btn-con">
              <el-button
                type="primary"
                link
                @click="change(row)"
              >
                {{ row.isRemind === 0 ? $t('notice.enableReminder') : $t('notice.turnOffReminder') }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页条 -->
    <el-pagination
      v-if="pageVO.total"
      :current-page="pageQuery.current"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageQuery.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageVO.total"
      @size-change="onPageSizeChange"
      @current-change="onPageChange"
    />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const pageQuery = reactive({
  size: 10,
  current: 1
})

// 返回参数
const pageVO = ref({
  records: [], // 返回的列表
  total: 0, // 一共多少条数据
  pages: 0 // 一共多少页
})

const msgStatusList = computed(() => {
  return [{
    value: 102,
    label: $t('notice.userConfirmsReceiptNotification')
  }, {
    value: 103,
    label: $t('notice.reminderOfPendingRefund')
  }, {
    value: 104,
    label: $t('notice.buyerReturnedGoodsReminder')
  }, {
    value: 105,
    label: $t('notice.ReminderSuccessfulPayment')
  }, {
    value: 106,
    label: $t('notice.productRemovalReminder')
  }, {
    value: 107,
    label: $t('notice.commodityAuditResultReminder')
  }, {
    value: 108,
    label: $t('notice.ReminderChangeConsignment')
  }, {
    value: 109,
    label: $t('notice.reminderOfPendingShipment')
  }, {
    value: 110,
    label: $t('notice.ReminderMarketingActivityOffShelf')
  }, {
    value: 111,
    label: $t('notice.ActivityAuditResultReminder')
  }]
})

onMounted(() => {
  getPage()
})

const emit = defineEmits(['changeSendTypeList'])
const pageLoading = ref(false)
const getPage = () => {
  pageLoading.value = true
  http({
    url: http.adornUrl('/multishop/notifyTemplateRemind/page'),
    method: 'get',
    params: http.adornParams(pageQuery)
  }).then(({ data }) => {
    pageVO.value = data
    const list = []
    pageVO.value.records.forEach(item => {
      msgStatusList.value.forEach(obj => {
        if (obj.value === item.sendType) {
          item.label = obj.label
          list.push(obj)
        }
      })
    })
    emit('changeSendTypeList', list)
    pageLoading.value = false
  })
}

const change = (row) => {
  const obj = JSON.parse(JSON.stringify(row))
  obj.isRemind = obj.isRemind === 1 ? 0 : 1
  http({
    url: http.adornUrl('/multishop/notifyTemplateRemind'),
    method: 'put',
    data: http.adornParams(obj)
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1500,
      onClose: () => {
        getPage()
      }
    })
  })
}

const onPageSizeChange = (val) => {
  pageQuery.size = val
}

const onPageChange = (val) => {
  pageQuery.current = val
}

</script>

<style lang="scss" scoped>

.component-notify-setting{
  .table-btn-con{
    :deep(.el-button.is-link) {
      &:focus{
        color: var(--el-button-text-color,#155BD4);
      }
      &:hover{
        color: var(--el-color-primary-light-5,#a0cfff);
      }
    }
  }
  .table-con {
    padding-bottom: 30px;
  }
}
</style>
