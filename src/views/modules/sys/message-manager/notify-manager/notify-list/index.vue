<template>
  <div class="Mall4j mod-prod-list component-notify-list">
    <!-- 1.条件筛选 -->
    <div class="search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        :inline="true"
        :model="searchParam"
        @submit.prevent
      >
        <div class="input-row">
          <!-- 商品名称 -->
          <el-form-item :label="$t('notice.msgContent') + '：'">
            <el-input
              v-model="searchParam.message"
              :placeholder="$t('notice.msgContent')"
              maxlength="100"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('notice.msgType') + '：'">
            <el-select
              v-model="searchParam.sendType"
              :placeholder="$t('notice.msgType')"
              clearable
            >
              <el-option
                v-for="item in sendTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('notice.msgStatus') + '：'">
            <el-select
              v-model="searchParam.status"
              :placeholder="$t('notice.msgStatus')"
              clearable
            >
              <el-option
                :label="$t('chat.read')"
                :value="1"
              />
              <el-option
                :label="$t('chat.unRead')"
                :value="0"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('notice.publishTime')+ '：'">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              clearable
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
            />
          </el-form-item>
          <!-- 查询按钮 -->
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="loadSearchParam(true)"
            >
              {{ $t('shopFeature.searchBar.search') }}
            </div>
            <div
              class="default-btn"
              @click="clearSearchInfo"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <div class="main-content">
      <!--  -->
      <!-- 2.发布/展示/处理商品操作 -->
      <div class="operation-bar">
        <el-checkbox
          v-if="pageVO.records.length"
          v-model="isSelectedAll"
          :indeterminate="isIndeterminate"
          class="all-check-btn"
          @change="onSelectAll"
        >
          {{ $t('publics.currentSelectAll') }}
        </el-checkbox>
        <span
          v-if="selectedProdList.length"
          class="had-selected-tips"
        >{{ $t('notice.selectedItem') }} {{ selectedProdList.length }}</span>
        <el-button
          v-show="pageVO.records.length"
          type="primary"
          @click="batchRead([])"
        >
          {{ $t('notice.allRead') }}
        </el-button>
        <el-button
          plain
          :disabled="!selectedProdList.length"
          @click="batchRead(selectedProdList)"
        >
          {{ $t('notice.batchRead') }}
        </el-button>
      </div>
      <!-- 商品展示表格 -->
      <div class="table-con">
        <el-table
          ref="prodListTableRef"
          :data="pageVO.records"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          style="width: 100%"
          @selection-change="onSelectSome"
        >
          <el-table-column
            type="selection"
            align="center"
            width="50"
          />
          <el-table-column
            :label="$t('notice.msgContent')"
            min-width="600"
            prop="message"
          />
          <el-table-column
            :label="$t('notice.msgType')"
            width="260"
            prop="label"
            min-width="120"
          />
          <el-table-column
            :label="$t('notice.msgStatus')"
            prop="status"
            min-width="120"
          >
            <template #default="{ row }">
              {{ row.status === 0 ? $t('chat.unRead') : $t('chat.read') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('notice.publishTime')"
            prop="createTime"
            width="260"
            min-width="120"
          />
          <el-table-column
            fixed="right"
            align="center"
            :label="$t('crud.menu')"
            width="120"
          >
            <template #default="{ row }">
              <el-button
                link
                type="primary"
                @click="toReleasePage(row)"
              >
                {{ $t('crud.viewBtn') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页条 -->
      <el-pagination
        v-if="pageVO.total"
        :current-page="pageQuery.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageQuery.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageVO.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  sendTypeList: {
    type: Array,
    default: () => []
  }
})

const searchParam = reactive({
  message: '',
  sendType: '',
  skuCode: '',
  spuCode: ''
})
const pageQuery = reactive({
  size: 10,
  current: 1
})

const msgStatusList = computed(() => {
  return [
    {
      value: 101,
      label: $t('admin.timeoutRem')
    }, {
      value: 102,
      label: $t('admin.confirmReceRem')
    }, {
      value: 103,
      label: $t('admin.buyerRefundRem')
    }, {
      value: 104,
      label: $t('admin.buyerReturnRem')
    }, {
      value: 105,
      label: $t('admin.buyerPaySuccessRem')
    }, {
      value: 106,
      label: $t('notice.productRemovalReminder')
    }, {
      value: 107,
      label: $t('notice.commodityAuditResultReminder')
    }, {
      value: 110,
      label: $t('notice.ReminderMarketingActivityOffShelf')
    }, {
      value: 111,
      label: $t('notice.ActivityAuditResultReminder')
    }]
})

onMounted(() => {
  getData()
})

const dateRange = ref([])
const loadSearchParam = () => {
  pageQuery.current = 1
  pageQuery.message = searchParam.message
  pageQuery.sendType = searchParam.sendType
  pageQuery.status = searchParam.status
  pageQuery.startTime = dateRange.value === null ? null : dateRange.value[0] // 开始时间
  pageQuery.endTime = dateRange.value === null ? null : dateRange.value[1] // 结束时间
  getData()
}

// 返回参数
const pageVO = ref({
  records: [], // 返回的列表
  total: 0, // 一共多少条数据
  pages: 0 // 一共多少页
})

// 多选
const isSelectedAll = ref(false)
const isIndeterminate = ref(false)
const selectedProdList = ref([]) // 被选中的列表
/**
 * 获取消息列表
 */
const getData = () => {
  http({
    url: http.adornUrl('/multishop/notifyLog/pageByNotifyLogParam'),
    method: 'get',
    params: http.adornParams(pageQuery)
  }).then(({ data }) => {
    pageVO.value = data
    pageVO.value.records.forEach(item => {
      msgStatusList.value.forEach(obj => {
        if (obj.value === item.sendType) {
          item.label = obj.label
        }
      })
    })
  })
  isSelectedAll.value = false
  isIndeterminate.value = false
  selectedProdList.value = []
}

const commonStore = useCommonStore()
const router = useRouter()
// 查看跳转页面
const toReleasePage = (row) => {
  const routeList = commonStore.routeList
  const pathObj = {
    path: '',
    query: {}
  }
  if (row.sendType === 102 || row.sendType === 103 || row.sendType === 104) {
    pathObj.path = '/order/order-refund/index'
    pathObj.query.returnMoneySts = row.sendType === 102 ? 4 : row.sendType === 103 ? 1 : 5
  }
  if (row.sendType === 105 || row.sendType === 109) {
    pathObj.path = '/order/order/index'
    pathObj.query.status = row.sendType === 105 ? 2 : 3
  }
  if (row.sendType === 107 || row.sendType === 106) {
    pathObj.path = '/prod/manage/index'
  }
  if (row.sendType === 108) {
    pathObj.path = '/stock-purchasesOrder'
  }
  if (row.sendType === 110 || row.sendType === 111) {
    pathObj.path = '/marketing/discount/index'
  }
  const flag = routeList.some(item => '/' + item.url === pathObj.path)
  if (!flag) {
    return ElMessage.info($t('homes.noPermissionAccessPage'))
  }
  if (row.status === 0) {
    http({
      url: http.adornUrl('/multishop/notifyLog/is_read?type=0'),
      method: 'put',
      data: [row.logId]
    }).then(() => {
      router.push(pathObj)
    })
  } else {
    router.push(pathObj)
  }
}

/**
 * 监听表格选中
 */
const onSelectSome = (val) => {
  selectedProdList.value = val.map(item => item.logId)
  isSelectedAll.value = val.length === pageVO.value.records.length
}

const prodListTableRef = ref(null)
/**
 * 全选按钮
 */
const onSelectAll = () => {
  isSelectedAll.value = prodListTableRef.value?.getSelectionRows().length < pageVO.value.records.length
  prodListTableRef.value?.toggleAllSelection()
}

// 批量已读
const batchRead = (ids) => {
  const type = ids.length > 0 ? 0 : 1
  http({
    url: http.adornUrl('/multishop/notifyLog/is_read?type=' + type),
    method: 'put',
    data: ids
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1500
    })
    setTimeout(getData, 1000)
  })
}

/**
 * 清空
 */
const clearSearchInfo = () => {
  searchParam.message = ''
  searchParam.status = ''
  searchParam.sendType = ''
  dateRange.value = []
}

const onPageSizeChange = (val) => {
  pageQuery.size = val
  getData()
}

const onPageChange = (val) => {
  pageQuery.current = val
  getData()
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
