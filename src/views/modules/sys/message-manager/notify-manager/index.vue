<template>
  <div class="Mall4j app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane
        :label="$t('notice.msgList')"
        name="first"
      >
        <notify-list
          :send-type-list="sendTypeList"
        />
      </el-tab-pane>
      <el-tab-pane
        :label="$t('notice.msgSettings')"
        name="second"
      >
        <notify-setting
          @change-send-type-list="getSendTypeList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import notifyList from './notify-list/index.vue'
import notifySetting from './notify-setting/index.vue'

const activeName = ref('first')

const sendTypeList = ref([])
const getSendTypeList = (val) => {
  sendTypeList.value = val
}

</script>
