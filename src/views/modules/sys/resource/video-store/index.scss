.page-video-store {
  .el-upload-list__item,
  .el-upload--picture-card {
    width: 78px;
    height: 78px;
    line-height: 82px;
    border-radius: 5px;
  }

  .elx-head {
    background: #F7F7F7;
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;

    .text {
      color: #606266;
      font-size: 14px;
      margin-left: 7px;
      cursor: pointer;
    }
  }


  div :deep(.el-input-group__append), .el-input-group__prepend {
    background: #155BD4 !important;
    color: #FFFFFF;
    border: none;
  }

  .search-bar {
    padding-bottom: 0;
  }

  .box {
    display: flex;
    justify-content: flex-start;

    .group {
      min-width: 200px;

      // 增加滚动条样式
      .group-list {
        height: 490px;
        overflow: auto;

        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 5px;
          -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background-color: #c1c1c1;
        }

        &::-webkit-scrollbar-track {
          -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          border-radius: 5px;
          background-color: #f1f1f1;
        }
      }

      .group-item {
        height: 44px;
        line-height: 44px;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        .group-name {
          width: 200px;
        }

        .sidebar-operate {
          .default-btn + .default-btn {
            margin-left: 12px;
          }
        }
      }

      .group-item:hover {
        background: #F2F7FF;
      }

      .active {
        background: #F2F7FF;
      }

      .active-title {
        height: 40px;
        line-height: 40px;
        background: #f7f8fa !important;
        cursor: auto;
      }
    }
  }

  .group-box {
    height: 530px;
    min-width: 380px;
    margin-right: 25px;
    border: 1px solid #E8E9EC;
  }

  .select-group-box {
    padding: 20px 0 0 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .select-group-box-item {
      margin-right: 10px;
    }
  }

  .pick-block {
    $bg: #f6f6f6;
    position: relative;

    .elx-img-list-loading {
      width: 100%;
      height: 100%;
      // 加载层高度
      background: #fff;
      text-align: center;

      .el-icon-loading {
        font-size: 50px;
        color: #409eff;
        line-height: 460px;
      }
    }

    .elx-img-list {
      min-height: 530px;
      max-width: 1200px;
      border: 1px solid #E8E9EC;
      // 图片列表高度
      .img-item-con {
        padding-top: 15px;
      }

      .img-item {
        $imgSize: 175px;
        $size: 175px;
        float: left;
        width: $imgSize;
        cursor: pointer;
        position: relative;
        font-size: 12px;
        margin: 0 10px 20px;

        img {
          width: $imgSize;
          height: $imgSize;
          display: block;
          object-fit: contain
        }

        .title {
          height: 24px;
          display: block;
          overflow: hidden;
          background: $bg;
          padding: 0 5px;
          cursor: text;
          word-break: break-word;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          line-height: 20px;
        }

        .operate {
          line-height: 24px;
          height: 24px;
          display: block;
          overflow: hidden;
          margin-top: 2px;
          padding: 0 5px;

          .edit {
            float: left;
            padding-left: 5px;
          }

          .del {
            float: right;
            padding-right: 5px;
          }
        }

        .label {
          position: absolute;
          z-index: 9;
          left: 0;
          bottom: 24px;
          width: 100%;
          height: 21px;
          line-height: 21px;
          text-align: center;
          color: #fff;

          &:after {
            content: " ";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 21px;
            background: #000;
            opacity: 0.3;
            z-index: -1;
          }
        }

        .selected {
          position: absolute;
          right: 0;
          top: 0;
          width: $size;
          height: 115px;
          border: 1px solid #155BD4;
          text-align: right;

          .icon {
            background: #155BD4;
            text-align: center;
            height: 18px;
            width: 18px;
            line-height: 18px;
            display: inline-block;
            font-size: 16px;
            color: #fff;
            border-radius: 0 0 0 3px;
            position: absolute;
            right: 0;
            top: 0;
          }
        }

        .selected-def {
          position: absolute;
          right: 0;
          top: 0;
          width: 175px;
          height: 50%;
          text-align: left;
        }
      }

      &::after {
        content: " ";
        display: table;
        height: 0;
        clear: both;
        visibility: hidden;
      }
    }
  }

  div :deep(.el-tabs__header) {
    display: none !important;
  }

  .operation-bar {
    margin: 20px 0;
  }

  .img-list {
    width: 100%;
    position: relative;

    .data-tips {
      position: absolute;
      top: 30%;
      left: 38%;
      color: #999;
    }
  }

  .pagination {
    margin-top: 30px;
    margin-right: 30px;
  }
}
