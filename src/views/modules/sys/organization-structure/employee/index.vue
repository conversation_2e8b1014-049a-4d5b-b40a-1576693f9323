<template>
  <div class="mod-user">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="userName"
            :label="$t('sys.userName')+':'"
          >
            <el-input
              v-model="searchForm.username"
              type="text"
              clearable
              :placeholder="$t('sys.userName')"
            />
          </el-form-item>
          <el-form-item
            prop="nickName"
            :label="$t('sys.nickName')+':'"
          >
            <el-input
              v-model="searchForm.nickname"
              type="text"
              clearable
              :placeholder="$t('sys.nickName')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="resetForm()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <div class="main-container">
      <div class="operation-bar">
        <el-checkbox
          v-model="selectAll"
          class="all-check-btn"
          :disabled="!dataList.length"
          @change="onSelectAll"
        >
          {{ $t('publics.selectAll') }}
        </el-checkbox>
        <span
          v-if="dataListSelections.length"
          class="had-selected"
        >{{ $t('publics.selected') }} {{ dataListSelections.length }}</span>
        <div
          v-if="isAuth('sys:shopEmployee:save')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate(0)"
        >
          {{ $t('crud.addBtn') }}
        </div>
        <div
          v-if="isAuth('sys:shopEmployee:delete')"
          :class="[!dataListSelections.length ? 'disabled-btn':'','default-btn']"
          @click="onDelete()"
        >
          {{ $t('sys.batchDelete') }}
        </div>
      </div>
      <div class="table-con">
        <el-table
          ref="tableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
          @selection-change="selectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            align="left"
            prop="username"
            :label="$t('sys.userName')"
            width="290"
          >
            <template #default="scope">
              <div class="table-cell-text line-clamp-one">
                <span>{{ scope.row.username }}</span>
                <span
                  v-if="scope.row.type === 0"
                  class="super-admin"
                >{{ $t('sys.superAdmin') }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="nickname"
            :label="$t('sys.nickName')"
            width="260"
          >
            <template #default="scope">
              <div class="table-cell-text line-clamp-one">
                {{ scope.row.nickname }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="email"
            :label="$t('sys.email')"
            width="260"
          >
            <template #default="scope">
              <span>{{ scope.row.email || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="mobile"
            :label="$t('shop.phoneNumber')"
            width="200"
          >
            <template #default="scope">
              <span>{{ scope.row.mobile || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="createTime"
            :label="$t('sys.creationTime')"
            width="200"
          />
          <el-table-column
            align="center"
            :label="$t('product.status')"
          >
            <template #default="scope">
              <span>{{ [$t('publics.disable'),$t('publics.normal')][scope.row.status] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            fixed="right"
            width="200"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('sys:shopEmployee:update')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.employeeId)"
                >
                  {{ $t('temp.modify') }}
                </div>
                <div
                  v-if="isAuth('sys:shopEmployee:delete') && scope.row.type !== 0"
                  class="default-btn text-btn"
                  @click="onDelete(scope.row.employeeId)"
                >
                  {{ $t('text.delBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import AddOrUpdate from './add-or-update.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  username: '',
  nickname: ''
})
onMounted(() => {
  getDataList(page)
})

let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
// 获取数据列表
const getDataList = (pageParam, newData = false) => {
  if (page) {
    const size = Math.ceil(page.total / page.pageSize)
    page.currentPage = (page.currentPage > size ? size : page.currentPage) || 1
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/sys/shopEmployee/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

const tableRef = ref(null)
const selectAll = ref(false)
/**
 * 全选按钮
 */
const onSelectAll = () => {
  selectAll.value = tableRef.value?.getSelectionRows().length < dataList.value.length
  tableRef.value?.toggleAllSelection()
}

// 条件查询
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}

// 刷新回调用
const refreshChange = () => {
  getDataList(page)
}

const dataListSelections = ref([])
// 多选变化
const selectionChange = (val) => {
  dataListSelections.value = val
  selectAll.value = val.length === dataList.value.length
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}

const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}

const resetForm = () => {
  searchForm.username = ''
  searchForm.nickname = ''
}

const addOrUpdateVisible = ref(false)
const addOrUpdateRef = ref(null)
// 新增 / 修改
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id)
  })
}

// 删除
const onDelete = (id) => {
  const ids = id ? [id] : dataListSelections.value.map(item => {
    return item.employeeId
  })
  if (!ids.length) {
    return
  }
  ElMessageBox.confirm(`${$t('sys.makeSure')}${id ? $t('text.delBtn') : $t('sys.batchDelete')}${$t('text.menu')}`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/sys/shopEmployee'),
      method: 'delete',
      data: http.adornData(ids, false)
    }).then(() => {
      page.total = page.total - ids.length
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          refreshChange()
        }
      })
    })
  }).catch(() => { })
}

</script>

<style lang="scss" scoped>
.mod-user {
  .super-admin {
    color: #e43130;
  }
}
</style>
