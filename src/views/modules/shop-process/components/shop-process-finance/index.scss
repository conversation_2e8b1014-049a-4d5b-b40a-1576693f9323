.component-shop-process-finance {
  display: block;
  width: 90%;
  margin: 0 auto;
  // 表格上方标题
  .table-data-title {
    display: flex;
    align-items: center;
    height: 50px;

    .text {
      font-size: 16px;

      .stress {
        color: #FF2120;
        padding-right: 5px;
      }
    }

    .tips {
      font-size: 12px;
      color: #999;
      margin-left: 10px;

      .txt-bold {
        color: #333;
      }
    }

    .edit-btn {
      margin-left: auto;
      cursor: pointer;
    }
  }

  // 表格
  .table-con.settlement-accounts-table {
    &:deep(.el-form-item) {
      margin-top: 16px;
      margin-bottom: 16px;
    }

    // 删除银行卡icon
    .el-icon-remove-outline {
      display: inline-block;
      width: 100%;
      text-align: center;
      vertical-align: middle;
    }

    &:deep(.el-icon-remove-outline::before) {
      font-size: 20px;
      cursor: pointer;
    }

    .footer {
      margin-top: 50px;
    }
  }
}
