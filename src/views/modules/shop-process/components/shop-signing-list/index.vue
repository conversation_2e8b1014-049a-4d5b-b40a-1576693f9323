<template>
  <!-- 签约信息 -->
  <div class="shop-signing-list component-shop-signing-list">
    <div class="signing-content">
      <!-- 表格上方标题 -->
      <div class="table-data-title">
        <div class="text">
          <span
            v-if="signingType === 1"
            class="stress"
          >*</span>{{ $t('shopProcess.signing') }}{{ signingType === 2 ? $t('shopProcess.brand') : $t('shopProcess.category') }}
        </div>
        <div class="tips">
          {{ $t('shopProcess.chosen') }}
          <span class="txt-bold">{{ dataList.length }}</span>
          {{ $t('shopProcess.piece') }}{{ signingType === 2 ? $t('shopProcess.brand') : $t('shopProcess.category') }}&nbsp;{{ $t('shopProcess.mostAdd') }}
          <span class="txt-bold">{{ signingType === 2 ? maxNumOfBrandsSigneds : maxNumOfCategoriesSigneds }}</span>
          {{ $t('shopProcess.piece') }}{{ signingType === 2 ? $t('shopProcess.brand') : $t('shopProcess.category') }}
        </div>
        <div
          v-if="!isNotEdit"
          class="edit-btn default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ signingType === 2 ? $t('shopProcess.editSigningBrand') : $t('shopProcess.editSigningCategory') }}
        </div>
      </div>

      <!-- 类目表格 -->
      <div
        v-if="signingType === 1"
        class="table-con signing-table"
      >
        <el-table
          ref="categoriesSigningTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            :label="$t('shopProcess.categoryName')"
          />
          <el-table-column
            prop="parentName"
            :label="$t('shopProcess.parentCategoryName')"
          />
          <el-table-column
            prop="platformRate"
            :label="$t('shopProcess.categoryRate')"
          >
            <template #default="{ row }">
              <span v-if="row.platformRate || row.platformRate === 0">{{ row.platformRate }} %</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="imgs"
            :label="$t('shopProcess.categoryQualifications')"
          >
            <template #default="{row}">
              <div
                v-if="row.imgs.length"
                class="img-box"
              >
                <el-image
                  v-for="(img,index) in row.imgs"
                  :key="index"
                  class="info-img"
                  :src="img"
                  fit="cover"
                  preview-teleported
                  :preview-src-list="row.imgs"
                  :initial-index="initialIndex"
                  @click="initialIndex=index"
                />
              </div>
              <span v-if="!row.imgs.length">--</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="categoryStatus"
            :label="$t('shopProcess.categoryStatus')"
            align="center"
          >
            <template #default="scope">
              <div v-if="scope.row.categoryStatus === 1">
                {{ $t('publics.normal') }}
              </div>
              <div v-if="scope.row.categoryStatus === 0">
                {{ $t('publics.LowerShelf') }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 品牌表格 -->
      <div
        v-if="signingType === 2"
        class="table-con signing-table"
      >
        <el-table
          ref="brandsSigningTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            :label="$t('shopProcess.brandName')"
          />
          <el-table-column
            prop="firstLetter"
            :label="$t('shopProcess.firstLetter')"
            align="center"
          />
          <el-table-column
            prop="brandLogo"
            :label="$t('shopProcess.brandLogo')"
          >
            <template #default="scope">
              <div class="brand-logo img-box">
                <el-image
                  v-if="scope.row.imgUrl"
                  class="info-img"
                  preview-teleported
                  :src="checkFileUrl(scope.row.imgUrl)"
                  fit="cover"
                  :preview-src-list="[checkFileUrl(scope.row.imgUrl)]"
                />
                <span v-if="!scope.row.imgUrl">--</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="imgs"
            :label="$t('shopProcess.brandQualifications')"
          >
            <template #default="{row}">
              <div
                v-if="row.imgs.length"
                class="brand-logo img-box"
              >
                <el-image
                  v-for="(item,index) in row.imgs"
                  :key="index"
                  class="info-img"
                  fit="cover"
                  :src="checkFileUrl(item)"
                  preview-teleported
                  :preview-src-list="checkFileUrl(row.imgs)"
                  :initial-index="initialIndex"
                  @click="initialIndex=index"
                />
              </div>
              <div v-if="!row.imgs.length">
                --
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="brandStatus"
            :label="$t('shopProcess.brandStatus')"
            align="center"
          >
            <template #default="scope">
              <div
                v-if="scope.row.brandStatus === -1"
                class="tag-txt"
              >
                {{ $t('shop.notAudit') }}
              </div>
              <div
                v-if="scope.row.brandStatus === 1"
                class="tag-txt"
              >
                {{ $t('publics.normal') }}
              </div>
              <div
                v-if="scope.row.brandStatus === 0"
                class="tag-txt red-tag-txt"
              >
                {{ $t('publics.LowerShelf') }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 类目签约弹窗 -->
    <category-add-or-update
      v-if="cateAddOrUpdateVisible"
      ref="cateAddOrUpdateRef"
      :is-create="true"
      :is-first="false"
      :sign-category-list="signCategories"
      @close-popup="closePopup"
    />
    <!-- 品牌签约弹窗 -->
    <brand-add-or-update
      v-if="brandAddOrUpdateVisible"
      ref="brandAddOrUpdateRef"
      :is-first="false"
      :brand-name-list="brandNameList"
      @close-popup="closePopup"
    />
  </div>
</template>

<script setup>
import categoryAddOrUpdate from '../category-add-or-update/index.vue'
import brandAddOrUpdate from '../brand-add-or-update/index.vue'

const props = defineProps({
  // 申请步骤 3.签约信息
  applyStep: {
    default: 3,
    type: [String, Number]
  },
  // 是否不可以编辑信息, 当申请状态为待审核时不能编辑
  isNotEdit: {
    default: false,
    type: Boolean
  },
  // 店铺申请状态 0：未审核 1：已通过 -1：未通过 -2：未提交过申请
  applyStatus: {
    default: 0,
    type: [String, Number]
  },
  // 签约类型：1类目 2品牌
  signingType: {
    default: 1,
    type: Number
  }
})

const emit = defineEmits(['getCategories'])

const initialIndex = ref(0) // 图片预览索引

// 最大类目签约数量
const maxNumOfCategoriesSigneds = ref(200)
// 最大品牌签约数量
const maxNumOfBrandsSigneds = ref(50)
// 签约（类目/品牌）列表
const dataList = ref([])

// 签约类目列表
const signCategories = ref([])

// 类目签约弹窗显隐
const cateAddOrUpdateVisible = ref(false)
// 品牌签约弹窗显隐
const brandAddOrUpdateVisible = ref(false)
// 已有品牌类目
const brandNameList = ref([])

onMounted(() => {
  if (props.applyStep === 3 && props.signingType === 1) {
    // 类目
    getSignedCategories()
  }
  if (props.applyStep === 3 && props.signingType === 2) {
    getSignedBrands()
  }
})

/**
 * 签约类目列表
 */
const getSignedCategories = () => {
  http({
    url: http.adornUrl('/prod/category/listSigningCategory'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (data) {
      signCategories.value = data
      signCategories.value.forEach(item => {
        item.imgs = item.qualifications ? item.qualifications.split(',') : []
        if (item.imgs.length) {
          item.imgs = item.imgs.map(img => {
            return getImgSrc(img)
          })
        }
      })
      dataList.value = signCategories.value
      emit('getCategories', signCategories.value)
    }
  })
}

/**
 * 获取图片路径
 */
const getImgSrc = (img) => {
  return checkFileUrl(img)
}

/**
 * 获取签约品牌列表
 */
const getSignedBrands = () => {
  http({
    url: http.adornUrl('/admin/brand/listSigningBrand'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (data) {
      let signedBrands = []
      const brandNameListPar = []
      data.platformBrandList.forEach(item => {
        brandNameListPar.push(item.name)
      })
      data.platformBrandList.length ? signedBrands.push(...data.platformBrandList) : signedBrands = []
      data.customizeBrandList.forEach(el => {
        el.brandStatus = -1
      })
      if (data.customizeBrandList.length > 0) {
        signedBrands.push(...data.customizeBrandList)
      }
      signedBrands.forEach(item => {
        item.imgs = item.qualifications ? item.qualifications.split(',') : []
        if (item.imgs.length) {
          item.imgs = item.imgs.map(img => {
            return getImgSrc(img)
          })
        }
      })
      dataList.value = signedBrands
      brandNameList.value = brandNameListPar
    }
  })
}

const cateAddOrUpdateRef = ref(null)
const brandAddOrUpdateRef = ref(null)
/**
 * 编辑签约信息弹窗
 */
const onAddOrUpdate = () => {
  if (props.applyStatus === 0) {
    return
  }
  if (props.signingType === 1) {
    cateAddOrUpdateVisible.value = true
    nextTick(() => {
      cateAddOrUpdateRef.value?.init()
    })
    return
  }
  if (props.signingType === 2) {
    brandAddOrUpdateVisible.value = true
    nextTick(() => {
      brandAddOrUpdateRef.value?.init()
    })
  }
}

/**
 * 关闭弹窗
 */
const closePopup = (val) => {
  if (val === 'category') {
    cateAddOrUpdateVisible.value = false
    getSignedCategories()
  } else if (val === 'brand') {
    brandAddOrUpdateVisible.value = false
    getSignedBrands()
  }
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
