.component-shop-signing-list {
  .signing-content {
    display: block;
    width: 90%;
    margin: 0 auto;
  }

  // 表格上方标题
  .table-data-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .text {
      font-size: 16px;

      .stress {
        color: #FF2120;
        padding-right: 5px;
      }
    }

    .tips {
      font-size: 12px;
      color: #999;
      margin-left: 10px;

      .txt-bold {
        color: #333;
      }
    }

    .edit-btn {
      width: auto;
      min-width: 117px;
      padding: 0 8px;
      height: 30px;
      line-height: 28px;
      font-size: 14px;
      text-align: center;
      border-radius: 2px;
      box-sizing: border-box;
      margin-left: auto;
      cursor: pointer;
    }
  }

  // 表格
  .table-con.signing-table {
    margin-bottom: 30px;
    // 表格滚动条设置
    &:deep(.el-table__body-wrapper) {
      max-height: 312px;
      overflow-y: scroll;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
    }

    &:deep(.el-table__body-wrapper::-webkit-scrollbar) {
      display: none; /* Chrome Safari */
    }

    &:deep(.el-table__body) {
      width: 100%;
    }

    .img-box {
      display: block;
      width: 100%;
      height: 60px;

      & .info-img {
        width: 60px;
        height: 60px;
        margin-right: 5px;
      }
    }
  }

}
