.component-shop-process-business {
  display: block;
  width: 90%;
  margin: 0 auto;

  .ci-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    // 上传图片提示
    .upload-tips {
      font-size: 12px;
      color: #999;
      height: 16px;
      line-height: 20px;
      margin-top: 13px;
    }

    .left-info {
      max-width: 50%;
      min-width: 40%;
      margin-left: -55px;
    }

    .right-info {
      max-width: 45%;
      min-width: 30%;
      margin-left: 55px;
    }

    // 注册资本
    .capital-int {
      &:deep(.el-input) {
        width: 60%;
        min-width: 250px;

        .el-input__inner {
          padding-right: 0 !important;
          border-radius: 2px 0 0 2px !important;
        }
      }
    }

    // 成立日期
    .found-time {
      &:deep(.el-date-editor--date) {
        width: 60%;
        min-width: 250px;
      }
    }

    // 营业期限
    &:deep(.business-term.el-range-editor.el-input__inner) {
      width: 100%;

      .el-range-separator {
        width: 8%;
      }
    }
  }
}

.right-info {
  .license-content-box {
    width: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    &:deep(.el-form-item__content) {
      display: block;
    }
    .upload-tips {
      width: 290px;
    }
  }

  .license-content {
    display: flex;
  }

  .example-left {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .upload-tips {
      width: 290px;
    }
  }

  // 图片示例
  .upload-example {
    display: flex;

    .example-box {
      margin-left: 0;

      &:nth-child(2) {
        margin-left: 30px;
      }
    }
  }

  // 示例框
  .example-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 130px;
    min-width: 130px;
    height: 130px;
    background: #FFFFFF;
    border: 1px solid #EAEAEA;
    border-radius: 3px;
    box-sizing: border-box;
    margin-left: 30px;

    img {
      display: block;
      width: auto;
      max-width: 100%;
      height: auto;
      max-height: 100%;
    }

    .tips {
      position: absolute;
      left: -1px;
      bottom: 0;
      width: 130px;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      background: rgba(51, 51, 51, 0.5);
      text-align: center;
      border-radius: 0 0 3px 3px;
    }
  }

  .id-box {
    .upload-content {
      .upload-img {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        div {
          &:nth-child(2) {
            margin-left: 30px;
          }
        }

        .disabled-upload {
          &:deep(.el-upload) {
            background: #f5f7fa;
          }
        }

        &:deep(.el-form-item__content) {
          height: 130px;
        }

        &:deep(.el-form-item) {
          margin-bottom: 10px;
        }
      }

      .upload-img.en-upload-img {
        &:deep(.el-form-item.is-error) {
          margin-bottom: 45px;
        }
      }

      .upload-img.zh-upload-img {
        &:deep(.el-form-item.is-error) {
          margin-bottom: 22px;
        }
      }
    }
  }
}

.component-shop-process-business .rotating-img {
  width: 130px;
  height: 130px;
}

.component-shop-process-business .ci-wrapper {
  :deep(.up-img-box .plugin-images .el-upload.el-upload--picture-card) {
    width: 130px;
    height: 130px;
  }
}

:deep(.plugin-images .el-upload-list--picture-card .el-upload-list__item) {
  width: 130px;
  height: 130px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  margin-right: 0;
}

.end-time {
  position: relative;

  .text {
    position: absolute;
    left: 30px;
    color: #C0C4CC;
  }
}

.el-date-editor :deep(.el-input__suffix>.el-icon-circle-close) {
  display: none
}

.el-date-editor :deep(.el-input__suffix>.el-icon-circle-check) {
  display: none
}
