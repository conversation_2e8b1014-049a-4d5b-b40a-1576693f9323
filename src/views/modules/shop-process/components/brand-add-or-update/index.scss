.component-shop-process-brand {
  .popup {
    display: block;
    // 表格上的标题
    .title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 64px;
      border-bottom: 1px solid #EAEAEA;

      .text {
        height: 23px;
        line-height: 21px;
        font-size: 16px;

        .stress {
          color: #FF2120;
          padding-right: 5px;
        }
      }

      .tips {
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        color: #999;
        margin-left: 10px;

        .bold {
          color: #000;
        }
      }

      .close-btn {
        font-size: 23px;
        color: #999;
        margin-left: auto;
        cursor: pointer;
      }
    }

    // 内容
    .content {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
      margin-top: 20px;

      .left-box {
        width: 20%;

        .search-box {
          position: relative;
          width: 100%;
          height: 36px;
          background: #FFFFFF;
          border: 1px solid #E8E9EC;
          box-sizing: border-box;

          .search-input {
            width: 100%;
            height: 100%;
            padding: 0 0 0 7px;
            border: none;
            outline: none;
            box-sizing: border-box;
          }

          input::-webkit-input-placeholder {
            font-size: 14px;
            color: #999;
          }

          .search-btn {
            position: absolute;
            right: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 55px;
            height: 100%;
            font-size: 20px;
            color: #CBCED4;
            border-left: 1px solid #E8E9EC;
            cursor: pointer;
          }
        }

        .brand-box {
          width: 100%;
          height: 525px;
          border: 1px solid #E9EAEC;
          box-sizing: border-box;
          overflow-y: scroll;
          margin-top: 14px;

          .brand-list {
            .brand-item {
              width: 100%;
              height: 47px;
              line-height: 47px;
              font-size: 14px;
              padding-left: 14px;
              overflow: hidden;
              cursor: pointer;
              margin-bottom: 3px;

              .brand-item-txt {
                display: inline-block;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .active {
              background: rgba(24, 144, 255, 0.13);
            }
          }

          /* 谷歌隐藏滚动条 */
          &::-webkit-scrollbar {
            display: none;
          }

          /* 隐藏滚动条，当IE下溢出，仍然可以滚动 */
          /* IE隐藏滚动条 */
          -ms-overflow-style: none;
        }

        /* 谷歌隐藏滚动条 */
        &::-webkit-scrollbar {
          display: none;
        }

        /* 隐藏滚动条，当IE下溢出，仍然可以滚动 */
        /* IE隐藏滚动条 */
        -ms-overflow-style: none;
      }

      .right-box {
        width: 80%;
        // width: 946px;
        margin-left: 21px;

        .table-box {
          border-left: 1px solid #E9EAEC;
          border-right: 1px solid #E9EAEC;
          box-sizing: border-box;
          // 表格滚动条设置
          &:deep(.el-table__body-wrapper) {
            max-height: 432px;
            overflow-y: scroll;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE 10+ */
          }

          &:deep(.el-table__body-wrapper::-webkit-scrollbar) {
            width: 0; // 横向滚动条
            height: 6px; // 纵向滚动条 必写
          }

          &:deep(.el-table__body) {
            width: 100%;
          }

          &:deep(tr.el-table__row:last-child) {
            td {
              border-bottom: 0;
            }
          }

          &:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
            background-color: #ddd;
            border-radius: 3px;
          }

          // 上传图片样式
          .business-qual {
            &:deep(.up-img-box),
            &:deep(.plugin-images),
            &:deep(.mul-pic-upload) {
              height: 80px;
            }

            &:deep(.el-upload-list--picture-card .el-upload-list__item) {
              width: 80px;
              height: 80px;
              margin-bottom: 0;
              border-radius: 0;
            }

            &:deep(.pic-uploader-component .el-upload .pic-uploader-icon),
            &:deep(.el-upload--picture-card) {
              width: 80px;
              height: 80px;
              line-height: 80px;
            }

            &:deep(.plugin-images .el-upload) {
              width: 80px;
              height: 80px;

              .pic {
                display: block;
                width: 100%;
                height: 100%;
              }
            }

            &:deep(.pic-uploader-component .el-upload .pic) {
              width: 80px;
              height: 80px;
            }

            &:deep(.el-upload-list--picture-card .el-upload-list__item) {
              border-color: #dcdfe6;
            }
          }

          .add-bank-info-table {
            &:deep(.el-form-item) {
              margin-top: 16px;
              margin-bottom: 16px;
            }
          }
        }

        .table-box.big-tb {
          &:deep(.el-table__body-wrapper) {
            max-height: 525px;
          }
        }

        .custom-box {
          display: flex;
          align-items: center;
          width: 100%;
          height: 60px;
          font-size: 14px;
          background: #FAFAFA;
          border: 1px solid #EAEAEA;
          box-sizing: border-box;
          margin-top: 17px;
          padding: 0 10px;

          .text {
            padding-left: 10px;
          }

          .add-btn {
            margin-left: auto;
            cursor: pointer;
          }
        }

      }
    }

    .foot-btn.btn-row {
      margin-top: 30px;
      text-align: right;
    }
  }
}
