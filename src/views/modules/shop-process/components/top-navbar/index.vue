<template>
  <div class="shop-process-navbar component-top-navbar">
    <div class="navbar-content">
      <div class="left-menu">
        <div
          v-if="configuration.bsMenuTitleOpen"
          class="title"
        >
          {{ configuration.bsMenuTitleOpen }}
        </div>
        <div
          v-else
          class="title"
        >
          {{ $t('shopProcess.topNavbarTitle') }}
        </div>
      </div>
      <div class="right-menu">
        <div class="item">
          <el-dropdown @command="switchLang">
            <span class="el-dropdown-link">
              {{ langName }}
              <el-icon
                v-if="langList.length>1"
                class="el-icon-arrow-down el-icon--right"
              >
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu
                v-if="langList.length>1"
              >
                <template
                  v-for="(langItem,inx) in langList"
                  :key="inx"
                >
                  <el-dropdown-item

                    :command="langItem.language"
                  >
                    {{ langItem.name }}
                  </el-dropdown-item>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="item">
          <el-dropdown
            class="avatar-container"
            trigger="hover"
          >
            <span class="el-dropdown-link">{{ shopName ? shopName : shopUserName }}</span>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- native modifier has been removed, please confirm whether the function has been affected  -->
                <el-dropdown-item @click="logoutHandle()">
                  {{ $t("homes.exit") }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { clearLoginInfo } from '@/utils'
import { formatConfigInfo } from '@/utils/website-config.js'
import { useLanguageStore } from '@/stores/lang.js'
import { ElMessageBox } from 'element-plus'

const shopName = ref('')
const shopUserName = ref('')
// 网站配置
const configuration = ref('')
const webConfigStore = useWebConfigStore()
const router = useRouter()

const langStore = useLanguageStore()
const langName = computed(() => {
  return langStore.langName
})

const langList = computed(() => {
  return langStore.langList
})

onMounted(() => {
  getUserInfo()
  updateWebConfigData()
  getLangList()
})

/**
 * 获取国际化语言列表
 */
const getLangList = () => {
  http({
    url: http.adornUrl('/sys/lang'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    langStore.updateLang(data)
  })
}

// 更新网站配置信息
const updateWebConfigData = () => {
  http({
    url: http.adornUrl('/sys/webConfig/getActivity'),
    method: 'get'
  }).then(({ data }) => {
    data = formatConfigInfo(data)
    webConfigStore.addData(data)
    configuration.value = data
  })
}
// 获取当前管理员信息
const getUserInfo = () => {
  http({
    url: http.adornUrl('/shop/shopDetail/info'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    shopName.value = data.shopName
    shopUserName.value = data.userName
  })
}

const switchLang = (lang) => {
  const langInfo = langList.value.find(f => f.language === lang)
  if (langInfo) {
    langStore.switchLang(langInfo)
  }
}

// 退出
const logoutHandle = () => {
  ElMessageBox.confirm($t('homes.isExit'), $t('text.tips'), {
    confirmButtonText: $t('homes.yes'),
    cancelButtonText: $t('homes.no'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/logOut'),
      method: 'post',
      data: http.adornData()
    }).then(() => {
      clearLoginInfo()
      router.push({ name: 'login' })
    })
  }).catch(() => { })
}

defineExpose({
  getUserInfo
})

</script>

<style lang="scss" scoped>
@use "index";
</style>
