.component-shop-process-category {
  .signing-categiries-content {
    // 表格上的标题
    .title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 64px;
      border-bottom: 1px solid #EAEAEA;

      .text {
        height: 23px;
        line-height: 21px;
        font-size: 16px;

        .stress {
          color: #FF2120;
          padding-right: 5px;
        }
      }

      .tips {
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        color: #999;
        margin-left: 10px;

        .bold {
          color: #000;
        }
      }

      .close-btn {
        font-size: 20px;
        color: #999;
        margin-left: auto;
        cursor: pointer;
      }
    }

    // 内容
    .content {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;

      .left-box {
        width: 20%;
        height: 574px;
        background: #FFFFFF;

        .search-box {
          position: relative;
          width: 100%;
          height: 36px;
          background: #FFFFFF;
          border: 1px solid #E8E9EC;
          box-sizing: border-box;

          .search-input {
            width: 100%;
            height: 100%;
            padding: 0 0 0 7px;
            border: none;
            outline: none;
            box-sizing: border-box;
          }

          input::-webkit-input-placeholder {
            font-size: 14px;
            color: #999;
          }

          .search-btn {
            position: absolute;
            right: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 55px;
            height: 100%;
            font-size: 20px;
            color: #CBCED4;
            border-left: 1px solid #E8E9EC;
            cursor: pointer;
          }
        }

        &:deep(.filter-tree) {
          height: 524px;
          margin-top: 14px;
          border: 1px solid #E9EAEC;
          box-sizing: border-box;
          overflow-y: scroll;
          scrollbar-width: none;

          .el-tree-node__content {
            padding-right: 10px;
          }

          .el-checkbox {
            margin-left: 12px;
          }

          .custom-tree-node {
            display: inline-block;
            max-width: 185px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
          }

          /* 谷歌隐藏滚动条 */
          &::-webkit-scrollbar {
            display: none;
          }

          /* 隐藏滚动条，当IE下溢出，仍然可以滚动 */
          /* IE隐藏滚动条 */
          -ms-overflow-style: none;
        }
      }

    }

    .foot-btn.btn-row {
      margin-top: 30px;
      text-align: right;
    }

  }

  .prods-select-body {
    margin-top: 0;
    height: 574px;
  }
}


.component-shop-process-category {
  .el-tree-node__expand-icon {
    position: absolute;
    right: 2%;
  }

  .el-tree-node__expand-icon.expanded {
    transform: rotate(-90deg);
  }
}

.right-boxs {
  position: relative;
  width: 80%;
  height: 574px;
  border: 1px solid #E9EAEC;
  box-sizing: border-box;
  margin-left: 21px;

  // 上传图片样式
  .business-qual {
    .mul-pic-upload .el-upload-list--picture-card .el-upload-list__item {
      width: 80px;
      height: 80px;
      line-height: 80px;
      margin-bottom: 0;

      img {
        vertical-align: top;
      }
    }

    .mul-pic-upload .el-upload--picture-card {
      width: 80px;
      height: 80px;
      line-height: 80px;
      background: #fff;
    }
  }

  .el-table__body-wrapper {
    max-height: 524px;
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }

  .el-table__body-wrapper::-webkit-scrollbar {
    width: 6px; // 横向滚动条
    height: 6px; // 纵向滚动条 必写
  }

  .el-table__body {
    width: 100%;
  }

  .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}

@media screen and (max-width: 1680px) {
  .right-boxs {
    position: relative;
    width: 80%;
    height: 574px;
    border: 1px solid #E9EAEC;
    box-sizing: border-box;
    margin-left: 20px;

    .el-table__body-wrapper {
      max-height: 490px;
      overflow-y: scroll;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE 10+ */
    }

  }
}

.item-bar {
  display: flex;
  align-items: center;
  .label {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
