// 基本信息
.component-shop-process-basic {
  display: block;
  width: 90%;
  margin: 0 auto;
  .ci-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    &:deep(.el-form-item) {
      margin-bottom: 22px;
    }
    // 图片框大小统一
    &:deep(.plugin-images) {
      display: block;
      width: 130px;
      height: 130px;
      img {
        vertical-align: top;
      }
    }
    &:deep(.el-upload--picture-card),
    &:deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 130px !important;
      height: 130px !important;
      line-height: 130px !important;
      margin: 0;
      background: #fff;
    }
    &:deep(.el-upload-list--picture-card) {
      display: block;
      width: auto;
      height: 100%;
    }
    .img-content {
      height: 130px;
      dev {
        height: 100%;
      }
    }
    // 上传图片提示
    .upload-tips {
      font-size: 12px;
      color: #999;
      line-height: 1.5em;
      margin-top: 5px;
    }
    .left-info {
      min-width: 692px;
      width: 45%;
    }
    .right-info {
      // 背景图
      .bg-img {
        display: flex;
        justify-content: flex-start;
        .form-item-content {
          margin-right: 30px;
          .img-tips {
            display: block;
            text-align: center;
            font-size: 12px;
            color: #666;
            line-height: 1.5em;
            margin-top: 8px;
            p {
              margin: 0;
              padding: 0;
            }
          }
        }
      }
      .license-content {
        display: flex;
      }
      .en-left {
        margin-left: 150px;
      }
      .zh-left  {
        margin-left: 150px;
      }

      // 图片示例
      .upload-example {
        display: flex;
        .example-box {
          margin-left: 0;
          &:nth-child(2) {
            margin-left: 30px;
          }
        }
      }
      // 示例框
      .example-box {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 130px;
        min-width: 130px;
        height: 130px;
        background: #FFFFFF;
        border: 1px solid #EAEAEA;
        border-radius: 3px;
        box-sizing: border-box;
        margin-left: 30px;
        img {
          display: block;
          width: auto;
          max-width: 100%;
          height: auto;
          max-height: 100%;
        }
        .tips {
          position: absolute;
          left: -1px;
          bottom: 0;
          width: 130px;
          height: 20px;
          line-height: 20px;
          font-size: 12px;
          color: #fff;
          background: rgba(51, 51, 51, 0.5);
          text-align: center;
          border-radius: 0 0 3px 3px;
        }
      }
      .id-box {
        .upload-content {
          .upload-img {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            div {
              &:nth-child(2) {
                margin-left: 30px;
              }
            }
            .disabled-upload {
              &:deep(.el-upload) {
                background: #f5f7fa;
              }
            }
            &:deep(.el-form-item__content) {
              height: 130px;
            }
            &:deep(.el-form-item) {
              margin-bottom: 10px;
            }
          }
          .upload-img.en-upload-img {
            &:deep(.el-form-item.is-error) {
              margin-bottom: 45px;
            }
          }
          .upload-img.zh-upload-img {
            &:deep(.el-form-item.is-error) {
              margin-bottom: 22px;
            }
          }
        }
      }
    }
    .detail-addr {
      &:deep(.el-input__inner) {
        border-radius: 2px 0 0 2px!important;
      }
    }
  }
  .map {
    width: 100%;
    height: 300px;
  }
}
.component-shop-process-basic .rotating-img {
  width: 130px;
  height: 130px;
}
