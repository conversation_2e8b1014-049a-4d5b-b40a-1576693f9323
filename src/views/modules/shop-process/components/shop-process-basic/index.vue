<template>
  <!-- 基本信息 -->
  <div class="shop-base-info component-shop-process-basic">
    <el-form
      ref="baseInfoFormRef"
      :model="baseInfoForm"
      :rules="baseInfoRule"
      label-width="150px"
      @submit.prevent
    >
      <div class="ci-wrapper">
        <div class="left-info">
          <el-form-item
            :label="$t('shopProcess.logo')"
            prop="shopLogo"
          >
            <div class="form-item-content">
              <div class="img-content">
                <img-upload
                  v-if="applyStatus !== 0"
                  v-model="baseInfoForm.shopLogo"
                  @input="imgChange(baseInfoForm.shopLogo, 'shopLogo')"
                />
                <el-image
                  v-if="applyStatus === 0 && baseInfoForm.shopLogo"
                  class="rotating-img"
                  :src="checkFileUrl(baseInfoForm.shopLogo)"
                  :preview-src-list="[checkFileUrl(baseInfoForm.shopLogo)]"
                />
              </div>
              <div class="upload-tips">
                {{ $t('shopProcess.shopLogoPicTips') + $t('shopProcess.logoTips') }}
              </div>
            </div>
          </el-form-item>

          <el-form-item
            :label="$t('shopProcess.merchantName')"
            prop="merchantName"
          >
            <el-input
              v-model="baseInfoForm.merchantName"
              :placeholder="$t('shopProcess.merchantNameInputTips')"
              maxlength="10"
              :disabled="isNotEdit"
              @blur="
                baseInfoForm.merchantName =
                  baseInfoForm.merchantName ?
                    removeHeadAndTailSpaces(baseInfoForm.merchantName) :
                    baseInfoForm.merchantName
              "
            />
          </el-form-item>
          <el-form-item
            :label="$t('shopProcess.shopName')"
            prop="shopName"
          >
            <el-input
              v-model="baseInfoForm.shopName"
              :placeholder="$t('shopProcess.shopNameInputTips')"
              maxlength="20"
              :disabled="isNotEdit"
              @blur="
                baseInfoForm.shopName =
                  baseInfoForm.shopName ?
                    removeHeadAndTailSpaces(baseInfoForm.shopName) :
                    baseInfoForm.shopName
              "
            />
          </el-form-item>
          <el-form-item
            :label="$t('shopProcess.tel')"
            prop="tel"
          >
            <el-input
              v-model="baseInfoForm.tel"
              oninput="value=value.replace(/[^\d*]/g,'')"
              :placeholder="$t('shopProcess.telInputTips')"
              maxlength="11"
              :disabled="isNotEdit"
            />
          </el-form-item>
          <el-form-item
            :label="$t('shopProcess.intro')"
            prop="intro"
          >
            <el-input
              v-model="baseInfoForm.intro"
              :disabled="isNotEdit"
              type="textarea"
              :rows="4"
              maxlength="200"
              :placeholder="$t('shopProcess.introInput')"
              @blur="
                baseInfoForm.intro =
                  baseInfoForm.intro ?
                    removeHeadAndTailSpaces(baseInfoForm.intro) :
                    baseInfoForm.intro
              "
            />
          </el-form-item>
        </div>
      </div>

      <div class="btn-row">
        <div
          v-if="applyStatus !== 0 && applyStatus !== 1"
          class="default-btn primary-btn"
          @click="saveBasicInfo"
        >
          {{ $t('shopProcess.submitAndNextStep') }}
        </div>
        <div
          v-if="applyStatus === 0 || applyStatus === 1"
          class="default-btn primary-btn"
          @click="viewNextStep"
        >
          {{ $t('shopProcess.seeNextStep') }}
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { isMobile, removeHeadAndTailSpaces, validNoEmptySpace } from '@/utils/validate'
import { ElMessage } from 'element-plus'

defineProps({
  // 申请步骤 1.基本信息
  applyStep: {
    default: 1,
    type: [String, Number]
  },
  // 是否不可以编辑信息, 当申请状态为待审核时不能编辑
  isNotEdit: {
    default: false,
    type: Boolean
  },
  // 店铺申请状态 0：未审核 1：已通过 -1：未通过 -2：未提交过申请
  applyStatus: {
    default: 0,
    type: [String, Number]
  }
})
const emit = defineEmits(['viewNextStep', 'toNextStep'])

const baseInfoForm = ref({
  merchantName: '',
  shopName: '',
  tel: '',
  shopLogo: '',
  intro: '',
  // 纬度
  shopLat: '',
  // 经度
  shopLng: ''
})

const validateMobile = (rule, value, callback) => {
  if (baseInfoForm.value.tel) {
    const mobile = /^(?:(?:\+|00)86)?1\d{2}([\d*]{4})\d{4}$/
    if (mobile.test(value)) {
      callback()
    } else {
      callback(new Error($t('shopProcess.telErrorTips')))
    }
  } else if (!isMobile(value)) {
    callback(new Error($t('shopProcess.telErrorTips')))
  } else {
    callback()
  }
}
const validEmptyTab = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}
// 基础信息验证
const baseInfoRule = reactive({
  merchantName: [
    { required: true, message: $t('shopProcess.merchantNameNotEmpty'), trigger: 'blur' },
    { min: 2, max: 10, message: $t('shopProcess.merchantNameInputTips'), trigger: 'blur' },
    { validator: validEmptyTab, trigger: 'blur' }
  ],
  shopName: [
    { required: true, message: $t('shopProcess.shopNameNotEmpty'), trigger: 'blur' },
    { min: 2, max: 20, message: $t('shopProcess.shopNameInputTips'), trigger: 'blur' },
    { validator: validEmptyTab, trigger: 'blur' }
  ],
  tel: [
    { required: true, message: $t('shopProcess.telNotEmpty'), trigger: 'blur' },
    { validator: validateMobile, trigger: 'blur' }
  ],
  intro: [
    { validator: validEmptyTab, trigger: 'blur' }
  ],
  shopLogo: [
    { required: true, message: $t('shopProcess.logoNotEmpty'), trigger: 'change' }
  ]
})

onMounted(() => {
  // 地址
  getBasicInfo()
})

/**
 * 获取店铺基本信息
 */
const getBasicInfo = () => {
  http({
    url: http.adornUrl('/shop/shopDetail/info'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (data) {
      baseInfoForm.value = data
    }
  })
}

const baseInfoFormRef = ref(null)
/**
 * 图片的值发生改变，重新校验对应表单字段
 */
const imgChange = (value, prop) => {
  if (value) {
    baseInfoFormRef.value?.validateField(prop)
  }
}

/**
 * 保存基本信息
 */
const saveBasicInfo = () => {
  baseInfoFormRef.value?.validate((valid) => {
    if (valid) {
      http({
        url: http.adornUrl('/shop/shopDetail'),
        method: 'PUT',
        data: http.adornData(baseInfoForm.value)
      }).then(() => {
        ElMessage({
          message: $t('shopProcess.baseSaveSuccess'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            toNextStep()
          }
        })
      })
    }
  })
}

/**
 * 查看下一步
 */
const viewNextStep = () => {
  emit('viewNextStep')
}

/**
 * 提交，下一步
 */
const toNextStep = () => {
  emit('toNextStep')
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
