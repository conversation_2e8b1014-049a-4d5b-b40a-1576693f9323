.page-shop-process {
  .sp-wrapper {
    display: block;
    width: 90%;
    min-width: 1044px;
    margin: 30px auto;
    /** 公共样式 **/
    &:deep(.btn-row) {
      display: block;
      text-align: center;
      margin-top: 30px;
    }

    /** 公共样式 end **/

    // 状态提示框
    .status-prompt-box {
      .status-item {
        display: block;
        width: 100%;
        padding: 10px;
        box-sizing: border-box;

        .text {
          padding-left: 5px;
          color: #666;
          line-height: 1.5em;
          vertical-align: middle;
          word-break: break-word;
        }

        .text-icon {
          font-size: 16px;
          line-height: 1.5em;
          vertical-align: middle;
        }

        &.apply {
          background: #FFF7DD;
          border: 1px solid #FFD888;

          .text-icon {
            color: #ffa900;
          }
        }

        &.auditing {
          background: rgba(24, 144, 255, 0.08);
          border: 1px solid #155bd4;

          .text-icon {
            color: #155bd4;
          }
        }

        &.audit-failed {
          background: rgba(255, 33, 32, 0.05);
          border: 1px solid #FF2120;

          .text-icon {
            color: #FF2120;
          }
        }
      }
    }

    // 店铺协议/信息
    .shop-opening-contents {
      display: block;
      width: 100%;
      margin: 30px 0;
      // 状态标题
      .con-title {
        text-align: center;
        font-size: 22px;
        margin-bottom: 30px;
      }

      // 0-协议
      .agreement-box {
        .sa-content {
          width: 100%;
          height: 517px;
          padding: 30px 33px 25px;
          border: 1px solid #EAEAEA;
          box-sizing: border-box;
          overflow-y: scroll;
          /* 谷歌隐藏滚动条 */
          &::-webkit-scrollbar {
            display: none;
          }

          /* 隐藏滚动条，当IE下溢出，仍然可以滚动 */
          /* IE隐藏滚动条 */
          -ms-overflow-style: none;
        }

        .sa-checkbox {
          display: flex;
          justify-content: center;
          margin-top: 30px;
          cursor: pointer;

          .text {
            font-size: 14px;
            color: #000;

            .flag {
              color: #155bd4;
            }
          }
        }
      }

      // 店铺信息数据
      .shop-content-box {
        display: block;
        width: 100%;
        border: 1px solid #EAEAEA;
        padding-bottom: 30px;

        .footer {
          width: 90%;
          margin: 0 auto;
        }

        // 进度条
        .progress-bar {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1010px;
          overflow: hidden;
          margin: 50px auto;

          .step {
            position: relative;
            width: 315px;

            .step-item {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 64px;

              .step-num {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 34px;
                height: 34px;
                font-size: 14px;
                font-weight: bold;
                color: #fff;
                background: #D2D2D2;
                text-align: center;
                border: 4px solid #EFEFEF;
                border-radius: 50%;
                margin: 0 auto;
                z-index: 10;
              }

              .step-text {
                width: 64px;
                font-size: 16px;
                color: #999;
                margin-top: 18px;
              }
            }

            &::after {
              position: absolute;
              top: 15px;
              left: -280px;
              content: '';
              display: block;
              width: 300px;
              height: 6px;
              background: #F2F2F2;
            }

            &:nth-child(1) {
              &::after {
                display: none;
              }
            }

            &:nth-child(4) {
              width: 64px;
            }
          }

          .active-step {
            .step-item {
              .step-text {
                color: #155bd4;
              }

              .step-num {
                background: #155bd4;
                border: 4px solid #DEEFFF;
              }
            }

            &::after {
              background: #C1E1FF;
            }
          }
        }

        // 国际化 -English
        .progress-bar.en-progress-bar {
          .step {
            width: 255px;

            &::after {
              left: -180px;
              width: 250px;
            }
          }

          .step-item {
            width: 150px;

            .step-text {
              width: 150px;
              text-align: center;
            }
          }
        }

        // 店铺信息
        .shop-content {
          width: 100%;
        }
      }
    }

  }
}

.sp-wrapper :deep(.btn-row) {
  display: block;
  text-align: center;
  margin-top: 30px;
}
