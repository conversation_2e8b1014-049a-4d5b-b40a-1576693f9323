<template>
  <!-- 申请开店流程 -->
  <div class="shop-process-container page-shop-process">
    <top-navbar ref="topNavbarRef" />

    <div class="sp-wrapper">
      <!-- 店铺状态提示框 -->
      <div class="status-prompt-box">
        <!-- 待申请开店状态 -->
        <div
          v-if="applyStatus === -2"
          class="status-item apply"
        >
          <el-icon
            class="el-icon-warning text-icon"
          >
            <Warning />
          </el-icon>
          <span class="text">{{ $t('shopProcess.notSubmitApplyTips') }}</span>
        </div>
        <!-- 店铺正在审核状态 -->
        <div
          v-if="applyStatus === 0"
          class="status-item auditing"
        >
          <el-icon
            class="el-icon-success text-icon"
          >
            <CircleCheck />
          </el-icon>
          <span class="text">{{ $t('shopProcess.applyAuditingTips') }}</span>
        </div>
        <!-- 审核未通过状态 -->
        <div
          v-if="applyStatus === -1"
          class="status-item audit-failed"
        >
          <el-icon
            class="el-icon-error text-icon"
          >
            <CircleClose />
          </el-icon>
          <span class="text">{{ $t('shopProcess.applyFailTips') }}{{ auditingInfo }}</span>
        </div>
      </div>

      <!-- 店铺协议/信息 -->
      <div class="shop-opening-contents">
        <!--
          状态标题
          applyStatus 0 未审核 1已通过 -1未通过 2平台下线 3 平台下线待审核, -2未开店
        -->
        <div class="con-title">
          <span v-if="applyStatus === -2">{{ $t('shopProcess.applyShop') }}</span>
          <span v-if=" applyStatus === 0">{{ $t('shopProcess.applyAuditing') }}</span>
          <span v-if=" applyStatus === -1">{{ $t('shopProcess.applyFail') }}</span>
        </div>

        <!-- 第0步 开店协议 -->
        <div
          v-if="applyStep === applyStepConstants.shopAgreement"
          class="agreement-box"
        >
          <div class="sa-content">
            <div v-rich="shopProtocol" />
          </div>
          <div class="sa-checkbox">
            <el-checkbox v-model="isAgreen">
              <span class="text">{{ $t('homes.readConsent') }}<span class="flag">《{{ $t('homes.shopProtocol') }}》</span></span>
            </el-checkbox>
          </div>
          <div class="btn-row">
            <div
              class="default-btn primary-btn"
              @click="toNextStep"
            >
              {{ $t('shopProcess.nextStep') }}
            </div>
          </div>
        </div>

        <!-- 店铺信息数据 -->
        <div
          v-if="applyStep !== applyStepConstants.shopAgreement"
          class="shop-content-box"
        >
          <!-- 步骤进度条 -->
          <div
            class="progress-bar"
            :class="lang === 'en' ? 'en-progress-bar' : 'zh-progress-bar'"
          >
            <div
              class="first-step step"
              :class="{'active-step':applyStep > applyStepConstants.shopAgreement}"
            >
              <div class="step-item">
                <div class="si-wrapper">
                  <div class="step-num">
                    1
                  </div>
                  <div class="step-text">
                    {{ $t('shopProcess.basicInfo') }}
                  </div>
                </div>
              </div>
            </div>
            <div
              class="second-step step"
              :class="{'active-step':applyStep > applyStepConstants.shopBasicInfo}"
            >
              <div class="step-item">
                <div class="si-wrapper">
                  <div class="step-num">
                    2
                  </div>
                  <div class="step-text">
                    {{ $t('shopProcess.businessInfo') }}
                  </div>
                </div>
              </div>
            </div>
            <div
              class="third-step step"
              :class="{'active-step':applyStep > applyStepConstants.shopBusinessInfo}"
            >
              <div class="step-item">
                <div class="si-wrapper">
                  <div class="step-num">
                    3
                  </div>
                  <div class="step-text">
                    {{ $t('shopProcess.signUpInfo') }}
                  </div>
                </div>
              </div>
            </div>
            <div
              class="fourth-step step"
              :class="{'active-step':applyStep > applyStepConstants.shopSigningInfo}"
            >
              <div class="step-item">
                <div class="si-wrapper">
                  <div class="step-num">
                    4
                  </div>
                  <div class="step-text">
                    {{ $t('shopProcess.financeInfo') }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第一步 基本信息 -->
          <div
            v-if="applyStep === applyStepConstants.shopBasicInfo"
            class="shop-content"
          >
            <shop-process-basic
              :apply-step="applyStep"
              :is-not-edit="isNotEdit"
              :apply-status="applyStatus"
              @to-next-step="toNextStep"
              @view-next-step="viewNextStep"
            />
          </div>

          <!-- 第二步 工商信息 -->
          <div
            v-if="applyStep === applyStepConstants.shopBusinessInfo"
            class="shop-content"
          >
            <shop-process-business
              :apply-step="applyStep"
              :is-not-edit="isNotEdit"
              :apply-status="applyStatus"
              @to-next-step="toNextStep"
              @to-prev-step="toPrevStep"
              @view-next-step="viewNextStep"
            />
          </div>

          <!-- 第三步 签约信息 -->
          <div
            v-if="applyStep === applyStepConstants.shopSigningInfo"
            class="shop-content"
          >
            <!-- 类目 -->
            <shop-signing-list
              :signing-type="1"
              :apply-step="applyStep"
              :is-not-edit="isNotEdit"
              :apply-status="applyStatus"
              @get-categories="getCategories"
            />
            <!-- 品牌 -->
            <shop-signing-list
              :signing-type="2"
              :apply-step="applyStep"
              :is-not-edit="isNotEdit"
              :apply-status="applyStatus"
            />

            <div class="footer">
              <div
                v-if="applyStatus !== 0 && applyStatus !== 1"
                class="btn-row"
              >
                <div
                  class="default-btn"
                  @click="toPrevStep"
                >
                  {{ $t('shopProcess.previousStep') }}
                </div>
                <div
                  class="default-btn primary-btn"
                  @click="toNextStep"
                >
                  {{ $t('shopProcess.nextStep') }}
                </div>
              </div>
              <div
                v-if="applyStatus === 0 || applyStatus === 1"
                class="btn-row"
              >
                <div
                  class="default-btn"
                  @click="toPrevStep"
                >
                  {{ $t('shopProcess.seePreviousStep') }}
                </div>
                <div
                  class="default-btn primary-btn"
                  @click="viewNextStep"
                >
                  {{ $t('shopProcess.seeNextStep') }}
                </div>
              </div>
            </div>
          </div>

          <!-- 第四步 财务信息 -->
          <div
            v-if="applyStep === applyStepConstants.shopFinanceInfo"
            class="shop-content"
          >
            <shop-process-finance
              :apply-step="applyStep"
              :is-not-edit="isNotEdit"
              :apply-status="applyStatus"
              @back-to-first-step="backToFirstStep"
              @to-prev-step="toPrevStep"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import TopNavbar from './components/top-navbar/index.vue'
import shopProcessBasic from './components/shop-process-basic/index.vue'
import shopProcessBusiness from './components/shop-process-business/index.vue'
import shopSigningList from './components/shop-signing-list/index.vue'
import shopProcessFinance from './components/shop-process-finance/index.vue'
import { ElMessage } from 'element-plus'

// 语言
const lang = reactive(window.localStorage.getItem('bbcLang'))
// 申请步骤
const applyStepConstants = reactive({
  // 开店协议
  shopAgreement: 0,
  // 基本信息
  shopBasicInfo: 1,
  // 工商信息
  shopBusinessInfo: 2,
  // 签约信息
  shopSigningInfo: 3,
  // 财务信息
  shopFinanceInfo: 4
})
// 开店协议
const shopProtocol = ref('')

// 申请步骤
const applyStep = ref(0)
onMounted(() => {
  // 获取店铺审核状态
  getAuditingInfo()
})

// 是否开启开店协议开关
let shopProtocolSwitch = true
// 是否已阅读开店协议
const isAgreen = ref(false)
/**
 * 获取开店协议
 */
const getShopProtocol = () => {
  http({
    url: http.adornUrl('/shop/shopUserRegister/getShopProtocol'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (data) {
      shopProtocol.value = JSON.parse(data).content
    }
    if (applyStep.value === 0) {
      if (!shopProtocolSwitch) {
        isAgreen.value = true
        toNextStep()
      }
    }
  })
}

// 店铺申请状态 0：未审核 1：已通过 -1：未通过 -2：未提交过申请
const applyStatus = ref(-2)
// 申请不通过理由
const auditingInfo = ref($t('shopProcess.incompleteInformation'))
// 是否不可以编辑信息, 当申请状态为待审核时不能编辑
const isNotEdit = ref(false)
/**
 * 获取店铺审核状态信息
 */
const getAuditingInfo = () => {
  http({
    url: http.adornUrl('/shop/shopDetail/getAuditingInfo'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (data) {
      // 审核状态status 0 未审核 1已通过 -1未通过 2平台下线 3 平台下线待审核
      applyStatus.value = data.status
      auditingInfo.value = data.remarks
      isNotEdit.value = data.status === 0 || data.status === 1
      shopProtocolSwitch = data.shopProtocolSwitch
    } else {
      // 未开店
      applyStatus.value = -2
    }
    if (applyStatus.value === 0) {
      applyStep.value = 1
    }
    if (applyStep.value === 0) {
    // 开店协议
      getShopProtocol()
    }
  })
}

// 已签约类目列表
let signedCategories = []
/**
 * 获取已签约分类列表
 */
const getCategories = (list) => {
  signedCategories = list
}

/**
 * 提交申请后
 */
const backToFirstStep = () => {
  // 获取店铺审核信息
  getAuditingInfo()
}

/**
 * 上一步
 */
const toPrevStep = () => {
  applyStep.value -= 1
}
// 查看下一步
const viewNextStep = () => {
  applyStep.value += 1
}

const topNavbarRef = ref(null)
// 下一步
const toNextStep = () => {
  if (applyStep.value === 0 && !isAgreen.value) {
    ElMessage({
      message: $t('shopProcess.readShopProtocolTips'),
      type: 'error',
      duration: 1500
    })
    return
  }
  if (applyStep.value === 3 && !signedCategories.length) {
    ElMessage({
      message: $t('shopProcess.categorySigningNotEmpty'),
      type: 'error',
      duration: 1500
    })
    return
  }
  applyStep.value += 1
  if (applyStep.value === 2) {
    topNavbarRef.value?.getUserInfo()
  }
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
