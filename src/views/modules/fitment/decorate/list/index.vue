<template>
  <div class="app-container-10 container-f0f2f5 app-main feature-list-container page-decorate-list">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="pageName"
            :label="$t('shopFeature.list.pageName')"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.pageName"
              type="text"
              clearable
              :placeholder="$t('shopFeature.list.pageName')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch()"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="feature-list-content container-fff app-container-15 app-ele-border-radius-0">
      <!-- <div class="default-btn primary-btn" @click="goCreatePage">{{$t('shopFeature.list.newMicroPage')}}</div> -->
      <div
        v-if="isAuth('shop:shopRenovation:savePC')"
        class="default-btn primary-btn"
        @click="newPageSelect"
      >
        {{ $t('shopFeature.list.newMicroPage') }}
      </div>
      <!--微页面列表-->
      <div class="content-box table-con">
        <el-table
          :data="miniPageList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            :label="$t('shopFeature.list.pageName')"
          >
            <template #default="scope">
              <div>
                {{ scope.row.name }}
                <el-tag
                  v-if="scope.row.homeStatus===1"
                  effect="dark"
                  size="small"
                  style="margin-left: 10px;"
                >
                  {{ $t('shopFeature.list.shopHomePage') }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="createTime"
            :label="$t('shopFeature.list.createTime')"
          />
          <el-table-column
            align="center"
            prop="updateTime"
            :label="$t('shopFeature.list.updateTime')"
          />
          <el-table-column
            align="center"
            :label="$t('shopFeature.list.oper')"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  class="default-btn text-btn"
                  @click="handleSelect(scope.row)"
                >
                  {{ $t('shopFeature.list.view') }}
                </div>
                <div
                  v-if="isAuth('shop:shopRenovation:updatePC')"
                  class="default-btn text-btn"
                  @click="handleEdit(scope.row)"
                >
                  {{ $t('shopFeature.list.modify') }}
                </div>
                <div
                  v-if="isAuth('shop:shopRenovation:deletePC')"
                  class="default-btn text-btn"
                  @click="handleDelete(scope.row)"
                >
                  {{ $t('shopFeature.list.delete') }}
                </div>
                <div
                  v-if="isAuth('shop:shopRenovation:updateHomePC')"
                  class="default-btn text-btn update-home-page"
                  @click="handleSetHomePage(scope.row.renovationId)"
                >
                  {{ scope.row.homeStatus!=1 ? $t('shopFeature.list.setHomePage'): $t('shopFeature.list.cancelHomePage') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="perProps.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="perProps.pageSize"
        :total="perProps.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
    </div>

    <template-select
      v-if="isShowtemplateSelect"
      ref="templateSelectRef"
      @select-template="goCreatePage"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import templateSelect from '../template/components/template-select/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const miniPageList = ref([]) // 列表页
const perProps = reactive({
  pageNum: 1, // 当前页
  pageSize: 20, // 每页显示多少条
  total: 0 // 总数
})
const searchForm = reactive({
  pageName: ''
})

onMounted(() => {
  // 获取微页面列表
  getMiniPageList()
})

// 获取微页面列表
const getMiniPageList = () => {
  const { pageNum, pageSize } = perProps
  http({
    url: http.adornUrl('/shop/shopRenovation/pagePC'),
    methods: 'get',
    params: http.adornParams({
      current: pageNum, // 当前页
      size: pageSize, // 每页显示多少条
      renovationType: 1, // 1表示pc端，2表示移动端
      name: searchForm.pageName
    })
  }).then(({ data }) => {
    miniPageList.value = data.records
    perProps.total = data.total
  }).catch(() => {})
}

const router = useRouter()
// 新建微页面
const goCreatePage = (id) => {
  // 跳转到装修页面
  const obj = {
    type: 'add',
    template: '0'
  }
  if (id > -1) {
    obj.templateId = id
  }
  const newPage = router.resolve({
    path: '/fitment/decorate/create/edit/index',
    query: obj
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

const templateSelectRef = ref(null)
const isShowtemplateSelect = ref(false)
const newPageSelect = () => {
  isShowtemplateSelect.value = true
  nextTick(() => {
    templateSelectRef.value.init()
  })
}

// 每页数
const sizeChangeHandle = (val) => {
  perProps.pageSize = val
  getMiniPageList()
}

// 当前页
const currentChangeHandle = (val) => {
  perProps.pageNum = val
  getMiniPageList()
}

// 编辑
const handleEdit = (row) => {
  const newPage = router.resolve({
    path: '/fitment/decorate/create/edit/index',
    query: {
      renovationId: row.renovationId,
      type: 'edit',
      template: '0'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

// 删除
const handleDelete = (item) => {
  ElMessageBox.confirm($t('shopFeature.list.deleteTips'), $t('shopFeature.list.tips'), {
    confirmButtonText: $t('shopFeature.edit.confirm'),
    cancelButtonText: $t('shopFeature.edit.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/shopRenovation/deletePC/' + item.renovationId),
      method: 'delete'
    }).then(() => {
      const totalPage = Math.ceil((perProps.total - 1) / perProps.pageSize)
      const currentPage = perProps.pageNum > totalPage ? totalPage : perProps.pageNum
      perProps.pageNum = currentPage < 1 ? 1 : currentPage
      ElMessage.success($t('shopFeature.list.deleteSuccess'))
      getMiniPageList()
    }).catch(() => {})
  }).catch(() => {})
}

// 设置为主页
const handleSetHomePage = (renovationId) => {
  http({
    url: http.adornUrl('/shop/shopRenovation/updateHomePagePC/' + renovationId),
    method: 'put'
  }).then(() => {
    ElMessage.success($t('shopFeature.list.operSuccess'))
    getMiniPageList()
  }).catch(() => {})
}

// 查看
const handleSelect = (item) => {
  const newPage = router.resolve({
    path: '/fitment/decorate/create/select-decorate/index',
    query: {
      renovationId: item.renovationId,
      type: 'detail'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

const searchFormRef = ref(null)
/**
 * 重置表单
 */
const onResetSearch = () => {
  searchFormRef.value.resetFields()
  getMiniPageList()
}

const onSearch = () => {
  perProps.pageIndex = 1
  perProps.pageSize = 10
  getMiniPageList()
}

</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
