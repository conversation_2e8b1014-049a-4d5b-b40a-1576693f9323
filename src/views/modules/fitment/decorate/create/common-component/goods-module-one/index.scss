.component-goods-module-one {
  width: 100%;
  height: 442px;
  box-sizing: border-box;
  background: #fff;
  overflow: hidden;
  padding: 20px;
  .top-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    .main-title {
      display: flex;
      align-items: center;
      span {
        font-size: 24px;
        color: #000;
        font-family: Microsoft YaHei;
        margin-right: 8px;
      }
      img {
        width: 16px;
        height: 16px;
      }
    }
    .sub-title {
      color: #999;
      font-size: 14px;
      margin-top: 8px;
    }
  }
  .bottom-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    .content-items {
      display: flex;
      margin-bottom: 14px;
      .left-img {
        position: relative;
        .el-image {
          width: 100px;
          height:100px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #dbdfe6;
          font-size: 19px;
          background: rgba(243, 245, 247, 0.39);
          border: 1px solid rgba(153, 153, 153, 0.1);
        }
        .imgs_shelves {
          position: absolute;
          width: 100px;
          height: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(153, 153, 153, 0.6);
          top: 0;
          left: 0;
          img {
            width: 80px;
          }
        }
      }
      .right-text {
        width: calc(100% - 100px);
        padding-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .name {
          font-size: 14px;
          color: #000;
          font-family: Microsoft YaHei;
          display:-webkit-box;
          -webkit-box-orient:vertical;
          -webkit-line-clamp:2;
          word-break: break-all;
          overflow:hidden;
          text-overflow:ellipsis;
          padding-top: 2px;
        }
        .totals {
          display: flex;
          align-items: center;
          padding-bottom: 4px;
          .actual {
            span {
              color: rgba(225, 37, 27, 1);
              font-family: Microsoft YaHei;
              &:nth-child(1) {
                font-size: 12px;
              }
              &:nth-child(2) {
                font-size: 16px;
              }
              &:nth-child(3) {
                font-size: 12px;
              }
            }
          }
          .del-price {
            margin-left: 12px;
            color: #999;
            font-family: Microsoft YaHei;
            font-size: 12px;
            text-decoration: line-through;
            padding-top: 1px;
            max-width: calc(50% - 6px);
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
