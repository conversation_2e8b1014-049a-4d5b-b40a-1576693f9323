<template>
  <div class="pick-color-component component-pick-color">
    <el-color-picker
      v-model="pickForm.color"
      :show-alpha="true"
      @change="handleChangeColor"
    />
    <span @click="handleReset">{{ $t(`pcdecorate.axinterval.reset`) }}</span>
    <span>{{ pickForm.color }}</span>
  </div>
</template>
<script setup>

const props = defineProps({
  defineColor: { // 选择的颜色
    type: String,
    default: () => ''
  },
  resetColor: { // 重置的颜色
    type: String,
    default: () => ''
  }
})

const emit = defineEmits(['handleChangeColor'])

const pickForm = {
  color: 'rgba(51, 51, 51, 1)'
}

watch(() => props.defineColor, (newVal) => {
  if (newVal) {
    pickForm.color = newVal
  }
}, {
  immediate: true,
  deep: true
})

// 当颜色改变
const handleChangeColor = () => {
  emit('handleChangeColor', pickForm.color)
}
// 重置
const handleReset = () => {
  if (props.resetColor === '') {
    pickForm.color = 'rgba(51, 51, 51, 1)'
  } else {
    pickForm.color = props.resetColor
  }
  handleChangeColor()
}

</script>

<style lang="scss" scoped>
.component-pick-color {
  display: flex;
  align-items: center;

  span {
    font-size: 14px;
    font-family: Microsoft YaHei;
    color: #333333;

    &:nth-child(2) {
      color: #155BD4;
      cursor: pointer;
      width: 15%;
      text-align: center;
      display: block;
      margin: 0 5px;
      white-space: nowrap;
    }

    &:nth-child(3) {
      width: calc(100% - 15% - 160px);
      justify-content: center;
      white-space: nowrap;
      font-size: 13px;
      display: flex;
      align-items: center;
    }
  }
  &:deep(.el-color-picker) {
    height: 32px;
    .el-color-picker__trigger {
      width: 160px!important;
      height: 32px!important;
    }
  }
}

</style>
