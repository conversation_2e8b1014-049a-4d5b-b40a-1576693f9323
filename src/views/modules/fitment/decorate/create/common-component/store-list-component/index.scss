$maxWidth: 1150px; // 设计图整个宽度
$contentWidth: 886px; // 设计内容宽度
$currentMaxWidth: 1145px; // 当前页面的实际宽度
$currentContentWidth: calc(($contentWidth * $currentMaxWidth) / $maxWidth) ; // 当前页面内容宽度
$marginRight: calc(($currentContentWidth * 10px) / $contentWidth);
$marginLeft: calc(($currentContentWidth * 14px) / $contentWidth);
.component-store-list {
  width: 100%;
  background: #fff;
  display: flex;
  flex-wrap: wrap;
  .store-list-items {
    width: calc((100% - 36px ) / 4);
    height: 100%;
    border: 1px solid #F5F5F5;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin-right: 12px;
    margin-top: 12px;
    overflow: hidden;
    &:nth-child(1),&:nth-child(2),&:nth-child(3),&:nth-child(4) {
      margin-top: 0;
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    .images {
      width: calc(($currentContentWidth * 73px) / $contentWidth);
      height: calc(($currentContentWidth * 73px) / $contentWidth);
      margin: calc(($currentContentWidth * 10px) / $contentWidth) calc(($currentContentWidth * 14px) / $contentWidth) calc(($currentContentWidth * 10px) / $contentWidth) calc(($currentContentWidth * 10px) / $contentWidth);
      .el-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f6f7fa;
      }
    }
    .right-content {
      width: calc(100% - ($currentContentWidth * 73px) / $contentWidth - $marginLeft - $marginRight);
      height: calc(($currentContentWidth * 73px) / $contentWidth);
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      span {
        white-space: nowrap;
        overflow: hidden;
        display: block;
        text-overflow: ellipsis;
        word-break: break-all;
        font-size: 12px;
        font-family: Microsoft YaHei;
        color: #999999;
        &:nth-child(3) {
          width: calc(($currentContentWidth * 72px) / $contentWidth);
          height: calc(($currentContentWidth * 23px) / $contentWidth);
          display: flex;
          align-items: center;
          justify-content: center;
          background: #E1251B;
          border-radius: 12px;
          color: #fff;
          font-size: 12px;
        }
      }
    }
  }
}
