.component-goods-module-two {
  width: 100%;
  height: 305px;
  background: #fff;
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .top-header {
    display: flex;
    align-items: center;
    span {
      font-size: 24px;
      font-family: Microsoft YaHei;
      color: #000;
    }
    img {
      width: 16px;
      height: 16px;
      margin-left: 12px;
    }
  }
  .bottom-content {
    margin-top: 20px;
    display: flex;
    .left-content {
      width: 170px;
      height: 214px;
      background: rgba(255, 255, 255, 1);
      border-radius: 2px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      .left-content-img {
        position: relative;
        .imgs_shelves {
          width: 116px;
          height: 116px;
          position: absolute;
          left: 17px;
          top: 27px;
          border-radius: 50%;
          background: rgba(153, 153, 153, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 80px;
          }
        }
      }
      .el-image {
        width: 116px;
        height: 116px;
        background: rgba(255, 255, 255, 1);
        border-radius: 50%;
        margin: 27px 17px 17px 17px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #dbdfe6;
      }
      .title {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        color: #000;
        font-size: 12px;
        font-family: Microsoft YaHei;
        padding: 0 10px;
        margin-bottom: 10px;
        text-align: center;
      }
      .totals {
        text-align: center;
        width: calc(100% - 24px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        span {
          color: #E1251B;
          font-family: Microsoft YaHei;
          &:nth-child(1) {
            font-size: 12px;
          }
          &:nth-child(2) {
            font-size: 16px;
          }
          &:nth-child(3) {
            font-size: 12px;
          }
        }
      }
    }
    .right-content {
      width: calc(100% - 190px);
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .right-top-content,.right-bottom-content {
        width: 100%;
        height: 100px;
        display: flex;
        .right-items {
          width: calc((100% - 60px) / 3);
          height: 100px;
          margin-right: 20px;
          display: flex;
          .right-content-img {
            position: relative;
            .imgs_shelves {
              width: 100px;
              height: 100px;
              background: rgba(153, 153, 153, 0.6);
              position: absolute;
              display: flex;
              align-items: center;
              justify-content: center;
              top: 0;
              left: 0;
              img {
                width: 80px;
              }
            }
          }
          .el-image {
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 19px;
            color: #dbdfe6;
            background: #fafbfc;
          }
          .goods-message {
            width: calc(100% - 100px);
            padding-left: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .name {
              width: 100%;
              text-align: justify;
              word-break: break-all;
              font-size: 14px;
              color: #000;
              font-family: Microsoft YaHei;
              display:-webkit-box;
              -webkit-box-orient:vertical;
              -webkit-line-clamp:2;
              overflow:hidden;
              text-overflow:ellipsis;
              margin-top: 4px;
            }
            .totals {
              display: flex;
              align-items: center;
              font-size: 12px;
              margin-bottom: 4px;
              .actual {
                white-space: nowrap;
                span {
                  color: #E1251B;
                  font-family: Microsoft YaHei;
                  &:nth-child(1) {
                    font-size: 12px;
                  }
                  &:nth-child(2) {
                    font-size: 16px;
                  }
                  &:nth-child(3) {
                    font-size: 12px;
                  }
                }
              }
              .del-price {
                text-decoration: line-through;
                font-size: 12px;
                margin-left: 12px;
                padding-top: 2px;
                color: #999;
                max-width: calc(50% - 5px);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                word-break: break-all;
              }
            }
          }
        }
      }
    }
  }
}
