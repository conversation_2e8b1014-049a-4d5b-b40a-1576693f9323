$currentContentWidth: 1200px; // 当前页面内容宽度
.component-goods-list {
  width: 100%;
  overflow: hidden;
}

.component-goods-list {
  .goods_list_component {
    width: $currentContentWidth;
    display: flex;
    flex-wrap: wrap;
    margin: 0 auto;
    .goods-list-items {
      margin-right: 20px;
      margin-bottom: 20px;
      overflow: hidden;
      background: #fff;
      .goods-img-content {
        position: relative;
      }
      &.three {
        &:nth-child(3n) {
          margin-right: 0;
        }
        .el-image {
          height: 386.27px;
        }
        .imgs_shelves {
          position: absolute;
          width: 100%;
          height: 386.27px;
          background: rgba(153, 153, 153, 0.6);
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          .been_imgs {
            width: 180px;
          }
        }
      }
      &.four {
        &:nth-child(4n) {
          margin-right: 0;
        }
        .el-image {
          height: 285px;
        }
        .imgs_shelves {
          position: absolute;
          width: 100%;
          height: 285px;
          background: rgba(153, 153, 153, 0.6);
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          .been_imgs {
            width: 180px;
          }
        }
      }
      &.five {
        &:nth-child(5n) {
          margin-right: 0;
        }
        .el-image {
          height: 224px;
        }
        .imgs_shelves {
          position: absolute;
          width: 100%;
          height: 224px;
          background: rgba(153, 153, 153, 0.6);
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          .been_imgs {
            width: 140px;
          }
        }
      }

      .el-image {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(243, 245, 247, 0.39);
      }
      .image-slot {
        width: 100%;
        height: 100%;
        background: rgba(243, 245, 247, 0.39);
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .goods-text {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        overflow: hidden;
        margin-top: 15px;
        margin-bottom: 15px;
        .top {
          width: 90%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          text-align: center;
          margin-bottom: 9px;
          word-break: break-all;
          color: #333333;
          font-size: 13px;
          font-family: Microsoft YaHei;
        }
        .price {
          width: 90%;
          .count {
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            color: #E1251B;
            span {
              &:nth-child(1) {
                font-size: 12px;
              }
              &:nth-child(2) {
                font-size: 16px;
              }
              &:nth-child(3) {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

