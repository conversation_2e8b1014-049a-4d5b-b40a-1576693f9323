<template>
  <div class="add-bg-components component-add-bg">
    <template v-if="imgUrl != ''">
      <div class="bg-img">
        <el-image
          :src="checkFileUrl(imgUrl)"
          fit="cover"
        />
        <el-icon
          class="el-icon-delete"
          @click="handleRemmoveImage"
        >
          <Delete />
        </el-icon>
      </div>
    </template>
    <div
      v-show="imgUrl === ''"
      class="btns"
      @click="handleAddImage"
    >
      <span>+</span>
      <span>{{ $t(`pcdecorate.goodsModule5.addImage`) }}</span>
    </div>
  </div>
</template>
<script setup>

defineProps({
  imgUrl: {
    type: String,
    default: () => ''
  }
})

const emit = defineEmits(['handleAddImage', 'handleRemmoveImage'])

// 添加图片
const handleAddImage = () => {
  emit('handleAddImage')
}

// 移除图片
const handleRemmoveImage = () => {
  emit('handleRemmoveImage')
}

</script>
<style lang="scss" scoped>
@use "index";
</style>
