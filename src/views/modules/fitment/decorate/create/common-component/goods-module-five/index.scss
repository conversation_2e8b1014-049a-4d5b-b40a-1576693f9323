.component-goods-module-five {
  width: 100%;
  overflow: hidden;
  display: flex;
  .left-bg {
    width: 232px;
    height: 320px;
    margin-right: 20px;
    background: #fff;
    .el-image {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #dbdfe6;
    }
  }
  .right-goods {
    width: calc(100% - 252px);
    height: 320px;
    display: flex;
    .goods-items {
      width: calc((100% - 60px) / 4);
      height: 100%;
      background: #fff;
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
      padding: 12px;
      .goods-items-imgs {
        position: relative;
        .imgs_shelves {
          width: 100%;
          height: 203.19px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          left: 0;
          top: 0;
          background: rgba(153, 153, 153, 0.6);
          img {
            width: 140px;
          }
        }
      }
      .el-image {
        width: 100%;
        height: 203.19px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        background: rgba(243, 245, 247, 0.39);
        color: #dbdfe6;
      }
      .name {
        width: 100%;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        font-size: 14px;
        font-family: Microsoft YaHei;
        color: #000;
        margin: 15px 0;
      }
      .price {
        padding: 0 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        text-align: center;
        color: #E1251B;
        font-family: Microsoft YaHei;
        span {
          font-size: 12px;
          &:nth-child(2) {
            font-size: 16px;
          }
          &:nth-child(3) {
            font-size: 12px;
          }
        }
      }
    }
  }
}
