$currentContentWidth: 1200px; // 当前页面内容宽度
.component-goods-module-four {
  width: 100%;
  overflow: hidden;
  .goods-module-top {
    width: 100%;
    height: 300px;
    .el-image {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      background: rgba(243, 245, 247, 0.39);
      color: #dbdfe6;
    }
  }
  .bottom-content {
    width: 100%;
    padding: 12px 12px 0 12px;
    display: flex;
    overflow: hidden;
    background: #fff;
    .bottom-items {
      width: calc((100% - (25px * 3)) / 4);
      margin-right: 25px;
      &:last-child {
        margin-right: 0;
      }
      .bottom-items-imgs {
        position: relative;
        .imgs_shelves {
          width: 100%;
          position: absolute;
          height: 275px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(153, 153, 153, 0.6);
          left: 0;
          top: 0;
          img {
            width: 140px;
          }
        }
      }
      .el-image {
        width: 100%;
        height: 275px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #dbdfe6;
        background: rgba(243, 245, 247, 0.39);
      }
      .text-content {
        padding-bottom: 20px;
        .name {
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          font-size: 14px;
          color: #000;
          font-family: Microsoft YaHei;
          text-align: center;
          margin: 15px 0;
        }
        .price {
          padding: 0 15px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          text-align: center;
          span {
            font-size: 12px;
            color: #E1251B;
            font-family: Microsoft YaHei;
            &:nth-child(2) {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
