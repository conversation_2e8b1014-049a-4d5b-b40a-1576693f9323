$currentContentWidth: 1200px; // 当前页面内容宽度
.component-floor-title {
  width: 100%;
  overflow: hidden;
}

.component-floor-title {
  .floor_titles_content {
    width: 100%;
  }
  .common_floor_titles {
    width: $currentContentWidth;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    .left-content {
      display: flex;
      width: 80%;
      white-space: nowrap;
      overflow: hidden;
      .main-title {
        font-family: Microsoft YaHei;
      }
      .other-title {
        margin-left: 12px;
        font-family: Microsoft YaHei;
        display: flex;
        align-items: flex-end;
      }
    }
    .right-content {
      .see-more {
        display: flex;
        align-items: center;
        span {
          font-size: 12px;
          color: #999;
          font-family: Microsoft YaHei;
        }
        .el-icon-arrow-right {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

}
