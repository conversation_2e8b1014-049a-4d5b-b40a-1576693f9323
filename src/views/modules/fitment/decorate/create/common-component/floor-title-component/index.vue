<template>
  <!-- 这种楼层标题的公共组件 -->
  <div
    class="floor-title-container component-floor-title"
    :style="{'background': configMessage.background}"
  >
    <div
      class="floor_titles_content"
      :style="setMarginStyle()"
    >
      <div class="common_floor_titles">
        <div class="left-content">
          <div
            class="main-title"
            :style="setMainStyle()"
          >
            {{ configMessage.mainContent.title }}
          </div>
          <div
            v-show="configMessage.showSubTitle === 0"
            class="other-title"
            :style="setSubStyle()"
          >
            {{ configMessage.subContent.title }}
          </div>
        </div>
        <div class="right-content">
          <div
            v-show="configMessage.showMore === 0"
            class="see-more"
          >
            <span :style="{color: configMessage.moreTextColor}">{{ $t(`pcdecorate.floorTitle.more`) }}</span>
            <el-icon
              :style="{color: configMessage.moreTextColor}"
              class="el-icon-arrow-right"
            >
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  config: { // 配置信息
    type: Object,
    default: () => {}
  }
})

const configMessage = ref({
  mainContent: { // 主标题配置内容
    title: $t('pcdecorate.floorTitle.mainTitle'),
    fontSize: '16px',
    color: 'rgba(51, 51, 51, 1)'
  },
  background: 'rgba(244, 244, 244, 1)', // 背景颜色
  marginTop: '18px', // 上边距
  marginBottom: '18px', // 下边距
  showMore: 0, // 查看更多是否展示
  path: {
    link: '',
    name: '',
    type: ''
  }, // 查看更多的链接
  moreTextColor: 'rgba(153, 153, 153, 1)', // 查看更多的文字颜色
  showSubTitle: 0, // 是否展示副标题
  subContent: { // 副标题配置内容
    title: $t('pcdecorate.floorTitle.subTitle'),
    fontSize: '12px',
    color: 'rgba(153, 153, 153, 1)'
  }
})

// 监听配置信息的改变
watch(() => props.config, (newVal) => {
  if (JSON.stringify(newVal) != '{}') {
    configMessage.value = newVal
  }
}, {
  immediate: true,
  deep: true
})

// 主标题的样式
const setMainStyle = () => {
  const { fontSize, color } = configMessage.value.mainContent
  return {
    'font-size': fontSize,
    color
  }
}

// 副标题的样式
const setSubStyle = () => {
  const { fontSize, color } = configMessage.value.subContent
  return {
    'font-size': fontSize,
    color
  }
}

// 上下边距的样式
const setMarginStyle = () => {
  const { marginTop, marginBottom } = configMessage.value
  return {
    'margin-top': marginTop,
    'margin-bottom': marginBottom
  }
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
