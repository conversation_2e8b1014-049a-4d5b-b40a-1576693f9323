$currentContentWidth: 1200px; // 当前页面内容宽度
.component-goods-module-three {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20px;
  background: #fff;
  overflow: hidden;
  .top-header {
    display: flex;
    align-items: center;
    span {
      font-size: 24px;
      font-family: Microsoft YaHei;
      color: #000;
    }
    img {
      width: 16px;
      height: 16px;
      margin-left: 12px;
    }
  }
  .bottom-content {
    display: flex;
    margin-top: 20px;
    .bottom-items {
      width: calc((100% - 40px) / 3);
      display: flex;
      flex-direction: column;
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
      .bottom-items-imgs {
        position: relative;
        .imgs_shelves {
          width: 170px;
          height: 170px;
          background: rgba(153, 153, 153, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 0;
          left: 0;
          img {
            width: 80px;
          }
        }
      }
      .el-image {
        width: 170px;
        height: 170px;
        display: flex;
        background: rgba(248, 248, 249, 0.39);
        align-items: center;
        justify-content: center;
        color: #dbdfe6;
        font-size: 19px;
      }
      .bottom-text {
        font-size: 14px;
        padding: 0 4px;
        margin-top: 15px;
        .goods-name {
          width: 100%;
          margin: 0 auto;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: break-all;
          overflow: hidden;
          text-align: center;
          display: inline-block;
          color: #000;
        }
        .price-content {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 12px;
          .price-text {
            padding: 0 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: break-all;
            span {
              color: #E1251B;
              font-family: Microsoft YaHei;
              &:nth-child(1) {
                font-size: 12px;
              }
              &:nth-child(2) {
                font-size: 16px;
              }
              &:nth-child(3) {
                font-size: 12px;
              }
            }
          }
          .del-price {
            text-decoration: line-through;
            padding-top: 2px;
            margin-left: 12px;
            color: #999;
          }
        }
      }
    }
  }
}
