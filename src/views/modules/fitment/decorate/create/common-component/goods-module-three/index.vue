<template>
  <div class="goods-three-items component-goods-module-three">
    <div class="top-header">
      <span>{{ config.title }}</span>
      <img
        src="@/assets/img/pc-micro-page/goods_recommand.png"
        alt
      >
    </div>
    <div class="bottom-content">
      <template v-if="config.goodsList && config.goodsList.length > 0">
        <template
          v-for="(item, index) in config.goodsList"
          :key="index"
        >
          <div class="bottom-items">
            <div class="bottom-items-imgs">
              <el-image
                :src="checkFileUrl(item.imgs)"
                fit="cover"
              >
                <template #error>
                  <div class="image-slot">
                    <img
                      style="width: 24px"
                      src="@/assets/img/pc-micro-page/show-default.png"
                      alt
                    >
                  </div>
                </template>
              </el-image>
              <!-- 下架商品蒙版 start -->
              <div
                v-if="item.status != 1"
                class="imgs_shelves"
              >
                <img
                  class="been_imgs"
                  src="@/assets/img/pc-micro-page/been_shelves.png"
                  alt
                >
              </div>
              <!-- 下架商品蒙版 end -->
            </div>
            <div class="bottom-text">
              <span class="goods-name">{{ item.name }}{{ item.description }}</span>
              <div class="price-content">
                <div class="price-text">
                  <span>￥</span>
                  <span>{{ getPrice(item.price, 'left') }}.</span>
                  <span>{{ getPrice(item.price, 'right') }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>
      <template v-else>
        <template
          v-for="(item, index) in defaultArr"
          :key="index"
        >
          <div class="bottom-items">
            <el-image
              src=""
              fit="cover"
            >
              <template #error>
                <div class="image-slot">
                  <img
                    style="width: 24px"
                    src="@/assets/img/pc-micro-page/show-default.png"
                    alt
                  >
                </div>
              </template>
            </el-image>
            <div class="bottom-text">
              <span class="goods-name">{{ $t(`pcdecorate.goodsList.goodsName`) }}{{ $t(`pcdecorate.goodsList.goodsDescription`) }}</span>
              <div class="price-content">
                <div class="price-text">
                  <span>￥</span>
                  <span>{{ $t(`pcdecorate.goodsList.price`) }}</span>
                  <span />
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup>
defineProps({
  config: { // 配置信息
    type: Object,
    default: () => {}
  }
})

const defaultArr = new Array(3)

// 得到价格显示
const getPrice = computed(() => {
  return (price, type) => {
    if (!price) return
    const point = price.toString().indexOf('.') // 如果为-1则表示没找到
    let leftPrice
    let rightPrice
    if (point === -1) { // 当前是整数
      leftPrice = price
      rightPrice = '00'
    } else {
      leftPrice = price.toString().slice(0, point)
      rightPrice = price.toString().slice(point + 1)
    }
    switch (type) {
      case 'left':
        return leftPrice
      case 'right':
        return rightPrice
      default:
        break
    }
  }
})

</script>
<style lang="scss" scoped>
@use "index";
</style>
