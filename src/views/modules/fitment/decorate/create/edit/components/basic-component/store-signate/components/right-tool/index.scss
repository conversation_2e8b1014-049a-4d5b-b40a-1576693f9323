.component-store-signate-right-tool {
  height: 100%;
  overflow-y: auto;
  top: 50px;
  right: 0;
  z-index: 99;
  background: #fff;
  padding-bottom: 45px;

  .header-title {
    font-size: 16px;
    font-family: Microsoft YaHei;
    color: #333;
    position: relative;
    margin-left: 10px;

    &::before {
      content: '';
      position: absolute;
      width: 3px;
      height: 15px;
      left: -10px;
      top: 2px;
      background: rgba(21, 91, 212, 1);
    }

    .el-icon-close {
      color: #8C8C8C;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .title-border {
    width: 100%;
    height: 1px;
    background: #EDEDF2;
    margin: 20px 0;
  }

  .config-items {
    margin-bottom: 25px;
    display: flex;

    .title {
      width: 90px;
      line-height: 32px;
    }

    .add-navs {
      width: 102px;
      height: 28px;
      box-sizing: border-box;
      border: 1px solid #155BD4;
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      span {
        font-size: 12px;
        color: #155BD4;
        font-family: Microsoft YaHei;
      }
    }

    .tips {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #999999;
      font-family: Microsoft YaHei;
      margin-left: 10px;
    }

    .navs-config {
      display: flex;
      margin-bottom: 15px;
      margin-left: 6px;
      align-items: center;

      &:nth-child(1) {
        margin-top: 15px;
      }

      .title {
        width: 40px;
        white-space: nowrap;
        font-size: 12px;
      }
    }

    .navs-items {
      width: 100%;
      height: 50px;

      .nav-close {
        background: #155BD4;
        color: #fff;
        border-radius: 50%;
        font-size: 18px;
        padding: 3px;
        position: absolute;
        top: 7px;
        right: 3px;
        cursor: pointer;
      }
    }

    .special-water {
      display: flex;
      height: 40px;
      justify-content: center;
      align-items: center;
      color: rgba(153, 153, 153, 0.6);
    }
  }

  .config-items.nav-solid {
    min-height: 40px;
    border: 1px dashed #DCDFE6;
    display: flex;
    flex-direction: column;
  }

  .config-items.config-bg {
    display: flex;
    flex-direction: column;

    .b-title {
      font-size: 14px;
      color: #333;
      font-family: Microsoft YaHei;
    }

    .b-tips {
      font-size: 12px;
      color: #999999;
      font-family: Microsoft YaHei;
      margin: 8px 0 15px 0;
    }

    .b-btns {
      width: 100%;
      height: 42px;
      background: rgba(243, 245, 247, 0.39);
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      cursor: pointer;

      span {
        font-size: 14px;
        font-family: Microsoft YaHei;
        color: #155BD4;

        &:nth-child(1) {
          font-size: 25px;
          margin-right: 5px;
        }

        &:nth-child(2) {
          padding-top: 2px;
        }
      }
    }
  }
}

.component-store-signate-right-tool {
  &:deep(.el-color-picker) {
    .el-color-picker__trigger {
      width: 80px;
      height: 32px;
    }
  }
}

.special-radio {
  .el-radio {
    margin-right: 45px;
  }
}

.navs-items {
  .el-input__inner {
    height: 28px;
    padding: 0 5px;
  }

  input::-webkit-input-placeholder { /* WebKit browsers */
    color: #999;
    font-size: 12px;
  }

  input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #999;
    font-size: 12px;
  }

  input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #999;
    font-size: 12px;
  }

  input:-ms-input-placeholder { /* Internet Explorer 10+ */
    color: #999;
    font-size: 12px;
  }
}
