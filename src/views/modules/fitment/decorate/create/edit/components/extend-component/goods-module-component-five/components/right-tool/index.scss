.component-right-tool {
  .config-items {
    margin-bottom: 20px;
    .items-content {
      width: 100%;
      height: 100px;
      display: flex;
      background: rgba(255, 255, 255, 0.39);
      border: 1px solid #EAEAF2;
      border-radius: 2px;
      padding: 20px 15px;
      .item-left {
        .item-operation {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 60px;
          background: rgba(234, 234, 242, 0.39);
          position: relative;
          cursor: pointer;
          .el-image {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          span {
            font-size: 12px;
            font-family: Microsoft YaHei;
            color: #9FA4B1;
            &:nth-child(1) {
              font-size: 20px;
            }
            &:nth-child(2) {
              transform: scale(0.9);
            }
          }
          .icon-error {
            position: absolute;
            top: -6px;
            right: -6px;
            z-index: 99;
            display: flex;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            color: #fff;
            font-size: 12px;
            align-items: center;
            justify-content: center;
            background: #155bd4;
          }
        }
      }
      .item-right {
        width: 100%;
        display: flex;
        align-items: center;
        margin-left: 15px;
        .title {
          white-space: nowrap;
          margin-right: 15px;
        }
        .link-list {
          position: relative;
          width: 100%;
          height: 32px;
          border: 1px solid #DCDFE6;
          border-radius: 3px;
          .link-img {
            position: absolute;
            right: 2px;
            top: 5px;
            cursor: pointer;
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
}
