.component-right-config-message {
    width: 450px;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    top: 50px;
    right: 0;
    z-index: 99;
    padding: 20px;
    background: #fff;
    .title {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-family: Microsoft YaHei;
        color: #333333;
        position: relative;
        margin-left: 10px;
        &::before {
            content: '';
            position: absolute;
            width: 3px;
            height: 15px;
            left: -10px;
            top: 2px;
            background: rgba(21, 91, 212, 1);
        }

        .title-icon-box {
            .el-icon {
                cursor: pointer;
                :hover {
                  color: var(--el-color-primary);
                }
            }
            .el-icon + .el-icon {
                margin-left: 15px;
            }
        }
    }
    .title-border {
        width: 100%;
        height: 1px;
        background: #EDEDF2;
        margin: 20px 0;
    }
}
