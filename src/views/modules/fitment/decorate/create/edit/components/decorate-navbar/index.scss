.component-decorate-navbar {
  .site-navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 99;
    height: 50px;
    background-color: #fff;
  }

  .site-navbar__brand {
    font-size: 16px;
    white-space: normal;
    display: flex;
    align-items: center;
    height: 50px;
    font-weight: bold;
    padding: 5px 0;
  }

  .site-navbar__brand-lg {
    margin: 0 auto;
    word-break: break-all; /* 按字符截断换行 支持IE和chrome，FF不支持*/
    word-wrap: break-word; /* 按英文单词整体截断换行  以上三个浏览器均支持 */
    color: #333333;
  }

  .site-navbar__brand-lg:hover {
    color: #333;
  }

  .site-navbar__brand-mini {
    color: #333333;
  }

  .site-navbar :deep(.el-menu.el-menu--horizontal) {
    border-bottom: none;
  }

  .site-navbar :deep(.el-menu--horizontal > .el-menu-item) {
    height: 50px;
    line-height: 47px;
    padding: 0 !important;
  }

  .site-navbar__header {
    margin-right: 20px;
    float: left;
  }

  .site-navbar__menu {
    float: right;
  }

  div :deep(.site-navbar__menu--right:not(:first-child)) {
    padding-right: 25px;
  }

  div :deep(.site-navbar__menu--right:not(:first-child)::after) {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    content: '';
    display: block;
    width: 1px;
    height: 30px;
    background: #DCDFE6;
  }

  .site-navbar {
    overflow: hidden;
    border-bottom: 1px solid #EBEDF0;
  }

  .site-navbar__content {
    width: 100%;
  }

  .site-navbar__content .top-tip {
    float: right;
    height: 50px;
    line-height: 50px;
    color: #858585;
    margin-right: 20px;
  }

  .decorate-menu {
    font-size: 14px;
    color: #666666;
    padding: 0 18px;
  }

  .is-active:hover {
    color: #155BD4;
  }

  .is-active:hover .el-icon-menu {
    color: #155BD4;
  }

  .el-icon-menu {
    color: #909399;
    transform: rotate(45deg);
  }

  .img-upload {
    display: flex;
    align-items: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 22px;
  }

  :deep(.el-dialog) {
    padding: 30px 20px;

    .el-dialog__body {
      padding: 30px 0;
    }
  }

}
