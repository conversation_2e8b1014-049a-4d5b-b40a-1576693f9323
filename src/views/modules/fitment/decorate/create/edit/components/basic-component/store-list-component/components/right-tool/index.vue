<template>
  <div class="store-config-content component-store-list-right-tool">
    <div class="config-items">
      <div class="title">
        {{ $t(`pcdecorate.storeList.showSubTitle`) }}
      </div>
      <div class="right-select">
        <el-radio-group v-model="storeConfigForm.showSubTitle">
          <el-radio :label="0">
            {{ $t(`pcdecorate.storeList.show`) }}
          </el-radio>
          <el-radio :label="1">
            {{ $t(`pcdecorate.storeList.hide`) }}
          </el-radio>
        </el-radio-group>
      </div>
      <el-input
        v-model="storeConfigForm.subTitle"
        class="subtitle-input"
      />
    </div>
    <div class="config-items">
      <div class="title">
        {{ $t(`pcdecorate.storeList.mainTileColor`) }}
      </div>
      <pick-color-component
        :define-color="storeConfigForm.mainTextColor"
        @handle-change-color="handleMainTitleColor"
      />
    </div>
    <div class="config-items">
      <div class="title">
        {{ $t(`pcdecorate.storeList.subTitleColor`) }}
      </div>
      <pick-color-component
        :define-color="storeConfigForm.subTextColor"
        @handle-change-color="handleSubTitleColor"
      />
    </div>
    <div class="config-items">
      <div class="title">
        {{ $t(`pcdecorate.storeList.titleBgColor`) }}
      </div>
      <pick-color-component
        :define-color="storeConfigForm.bgColor"
        @handle-change-color="handleBgColor"
      />
    </div>
    <div class="config-items">
      <div class="items-content slider-content">
        <div class="title">
          {{ $t(`pcdecorate.storeList.titleSize`) }}
        </div>
        <div class="right-content">
          <el-slider
            v-model="storeConfigForm.mainFontSize"
            :min="0"
            :max="100"
            show-input
          />
        </div>
      </div>
    </div>
    <div class="config-items">
      <div class="items-content slider-content">
        <div class="title">
          {{ $t(`pcdecorate.storeList.subTitleSize`) }}
        </div>
        <div class="right-content">
          <el-slider
            v-model="storeConfigForm.subFontSize"
            :min="0"
            :max="100"
            show-input
          />
        </div>
      </div>
    </div>
    <div class="config-items">
      <div class="items-content slider-content">
        <div class="title">
          {{ $t(`pcdecorate.storeList.marginTop`) }}
        </div>
        <div class="right-content">
          <el-slider
            v-model="storeConfigForm.marginTop"
            :min="0"
            :max="100"
            show-input
          />
        </div>
      </div>
    </div>
    <div class="config-items">
      <div class="items-content slider-content">
        <div class="title">
          {{ $t(`pcdecorate.storeList.marginBottom`) }}
        </div>
        <div class="right-content">
          <el-slider
            v-model="storeConfigForm.marginBottom"
            :min="0"
            :max="100"
            show-input
          />
        </div>
      </div>
    </div>
    <div class="config-items">
      <div class="title">
        {{ $t(`pcdecorate.storeList.addStore`) }}
      </div>
      <!-- 选择店铺 start -->
      <select-goods-component
        :goods-list="storeConfigForm.storeList"
        @handle-add-click="handleAddClick"
        @handle-remove="handleRemove"
      />
      <!-- 选择店铺 end -->
    </div>
  </div>
</template>

<script setup>
import selectGoodsComponent from '../../../../../../common-component/select-goods-component/index.vue' // 选择商品组件
import pickColorComponent from '../../../../../../common-component/pick-color/index.vue' // 颜色选择器
import { bigNumberTransform } from '@/utils/index.js'

const props = defineProps({
  currentRef: { // 当前组件的ref
    type: String,
    default: () => ''
  },
  currentItem: { // 点击当前组件的显示配置信息
    type: Object,
    default: () => {}
  },
  editItem: { // 当前组件的配置信息
    type: Object,
    default: () => {}
  }
})
const emit = defineEmits(['handleUpdateMessage'])

const storeConfigForm = ref({
  showSubTitle: 0, // 是否显示副标题
  subTitle: '', // 副标题文字
  mainTextColor: 'rgba(51, 51, 51, 1)', // 主标题文字颜色
  subTextColor: 'rgba(153, 153, 153, 1)', // 副标题文字颜色
  bgColor: '', // 标题的背景颜色
  mainFontSize: 16, // 主标题文字大小
  subFontSize: 12, // 副标题文字大小
  marginTop: 0, // 上边距
  marginBottom: 0, // 下边距
  storeList: [] // 店铺信息
})

watch(() => storeConfigForm.value, (newVal) => {
  const obj = {
    type: 'store_list',
    ref: props.currentRef,
    config: newVal
  }
  emit('handleUpdateMessage', obj)
})

watch(() => props.currentItem, (newVal) => {
  if (newVal.currentConfigType === 'store_list') {
    if (JSON.stringify(newVal.config) != '{}') {
      storeConfigForm.value = { ...newVal.config }
    }
  }
}, { deep: true })

// 标题的文字颜色
const handleMainTitleColor = (color) => {
  storeConfigForm.value.mainTextColor = color
}

// 副标题文字颜色
const handleSubTitleColor = (color) => {
  storeConfigForm.value.subTextColor = color
}

// 标题背景色
const handleBgColor = (color) => {
  storeConfigForm.value.bgColor = color
}

// 删除店铺
const handleRemove = (index) => {
  storeConfigForm.value.storeList.splice(index, 1)
}

// 弹窗确定
const handleDialogSubmit = ({ type, value }) => {
  if (type === '3') { // 当前选择的是店铺
    storeConfigForm.value.storeList.push({
      shopId: value.storeItem.shopId, // 店铺id
      shoplogo: value.storeItem.shopLogo, // 店铺logo
      imgs: value.storeItem.shopLogo, // 店铺logo
      shopName: value.storeItem.shopName, // 店铺名称
      shopFocusNumber: value.storeItem.fansCount === null ? 0 : bigNumberTransform(value.storeItem.fansCount) // 店铺关注人数
    })
  }
}

// 验证信息
const handleValidate = () => {
  let status
  let message = ''
  if (JSON.stringify(props.editItem) === '{}') {
    status = false
    message = $t('pcdecorate.storeList.warning1')
  } else if (props.editItem.showSubTitle == 0 && props.editItem.subTitle === '') {
    status = false
    message = $t('pcdecorate.storeList.warning2')
  } else if (props.editItem.storeList.length === 0) {
    status = false
    message = $t('pcdecorate.storeList.warning3')
  } else {
    status = true
  }
  return {
    status,
    message
  }
}
// 提交信息
const handleSubmitMessage = () => {
  return storeConfigForm.value
}

defineExpose({
  handleSubmitMessage,
  handleValidate,
  handleDialogSubmit
})

</script>

<style lang="scss" scoped>
.component-store-list-right-tool {
  .config-items {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    .title {
      display: flex;
      margin-bottom: 20px;
      align-items: center;
      position: relative;
    }

    .right-select {
      margin: 0 0 20px 0;
      width: 100%;
    }

    .items-content {
      display: flex;
      align-items: center;
      overflow: hidden;

      .title {
        width: 90px;
        height: 100%;
        display: flex;
        align-items: center;
        padding-top: 15px;
      }

      .right-content {
        width: 100%;
        padding-bottom: 3px;
      }
    }
  }
  &:deep(.subtitle-input) {
    .el-input__inner {
      height: 28px;
    }
  }
}
</style>
