// 基础组件
import storeSignate from '../basic-component/store-signate/index.vue' // 商家招牌组件
import pictureShuffling from '../basic-component/picture-shuffling/index.vue' // 图片轮播组件
import intervalComponent from '../basic-component/interval-component/index.vue' // 辅助间隔组件
import floorTitleComponent from '../basic-component/floor-title-component/index.vue' // 楼层标题组件
import goodListComponent from '../basic-component/good-list-component/index.vue' // 商品列表组件
import hotSpotComponent from '../basic-component/hot-spot-component/index.vue' // 万能热区组件
// // 营销活动
import limitedSkillComponent from '../marketing-activities/limited-skill-component/index.vue' // 限时秒杀组件
import discountCouponComponent from '../marketing-activities/discount-coupon-component/index.vue' // 优惠团购组件
// // 扩展组件
import goodsModuleComponentOne from '../extend-component/goods-module-component-one/index.vue' // 商品模块1组件
import goodsModuleComponentTwo from '../extend-component/goods-module-component-two/index.vue' // 商品模块2组件
import goodsModuleComponentThree from '../extend-component/goods-module-component-three/index.vue' // 商品模块3组件
import goodsModuleComponentFour from '../extend-component/goods-module-component-four/index.vue' // 商品模块4组件
import goodsModuleComponentFive from '../extend-component/goods-module-component-five/index.vue' // 商品模块5组件间

import pcShopSignsPng from '@/assets/img/pc-micro-page/pc_shop_signs.png'
import pcShopSignsActivePng from '@/assets/img/pc-micro-page/pc_shop_signs_active.png'
import pcPictureByPng from '@/assets/img/pc-micro-page/pc_picture_by.png'
import pcPictureByActivePng from '@/assets/img/pc-micro-page/pc_picture_by_active.png'
import pcAuxiliaryIntervalPng from '@/assets/img/pc-micro-page/pc_auxiliary_interval.png'
import pcAuxiliaryIntervalActivePng from '@/assets/img/pc-micro-page/pc_auxiliary_interval_active.png'
import pcFloorTitlePng from '@/assets/img/pc-micro-page/pc_floor_title.png'
import pcFloorTitleActivePng from '@/assets/img/pc-micro-page/pc_floor_title_active.png'
import pcStoreListPng from '@/assets/img/pc-micro-page/pc_store_list.png'
import pcStoreListActivePng from '@/assets/img/pc-micro-page/pc_store_list_active.png'
import pcUniversalHotspotPng from '@/assets/img/pc-micro-page/pc_universal_hotspot.png'
import pcUniversalHotspotActivePng from '@/assets/img/pc-micro-page/pc_universal_hotspot_active.png'
import pcLimitedSkillPng from '@/assets/img/pc-micro-page/pc_limited_skill.png'
import pcLimitedSkillActivePng from '@/assets/img/pc-micro-page/pc_limited_skill_active.png'
import pcDiscountCouponPng from '@/assets/img/pc-micro-page/pc_discount_coupon.png'
import pcDiscountCouponActivePng from '@/assets/img/pc-micro-page/pc_discount_coupon_active.png'
import pcGoodsModulePng from '@/assets/img/pc-micro-page/pc_goods_module.png'
import pcGoodsModuleActivePng from '@/assets/img/pc-micro-page/pc_goods_module_active.png'

export const configComponentList = [
  {
    type: 'shop_signs', // 当前组件类型(唯一)
    currentType: 'basic', // 当前组件属于哪个栏目(大类)
    title: 'businessSigns', // 组件的标题文字
    pic: pcShopSignsPng, // 组件的默认图片
    picActive: pcShopSignsActivePng, // 组件的激活图片
    components: 'storeSignate', // 组件所对应的组件名
    rightConfigTitle: 'businessSigns', // 组件所对应的配置信息标题
    Ref: 'shopSignsTools', // 这个组件每个对应ref
    rightConfigMessage: {},
    routerPath: shallowRef(storeSignate) // 路由地址
  },
  {
    type: 'picture_by',
    currentType: 'basic',
    title: 'pictureBy',
    pic: pcPictureByPng,
    picActive: pcPictureByActivePng,
    components: 'pictureShuffling',
    rightConfigTitle: 'pictureBy',
    Ref: 'pictureByTools', // 这个组件每个对应ref
    rightConfigMessage: {},
    routerPath: shallowRef(pictureShuffling) // 路由地址
  },
  {
    type: 'auxiliary_interval',
    currentType: 'basic',
    title: 'AuxiliaryInterval',
    pic: pcAuxiliaryIntervalPng,
    picActive: pcAuxiliaryIntervalActivePng,
    components: 'intervalComponent',
    rightConfigTitle: 'AuxiliaryInterval',
    Ref: 'auxiliaryIntervalTools', // 这个组件每个对应ref
    rightConfigMessage: {},
    routerPath: shallowRef(intervalComponent) // 路由地址
  },
  {
    type: 'floor_title',
    currentType: 'basic',
    title: 'floorTitle',
    pic: pcFloorTitlePng,
    picActive: pcFloorTitleActivePng,
    components: 'floorTitleComponent',
    rightConfigTitle: 'floorTitle',
    Ref: 'floorTitleTools', // 这个组件每个对应ref
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(floorTitleComponent) // 路由地址
  },
  {
    type: 'goods_list',
    currentType: 'basic',
    title: 'goodsList',
    pic: pcStoreListPng,
    picActive: pcStoreListActivePng,
    components: 'goodListComponent',
    rightConfigTitle: 'goodsList',
    Ref: 'goodsListTools', // 这个组件每个对应ref
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(goodListComponent) // 路由地址
  },
  {
    type: 'universal_hotspot',
    currentType: 'basic',
    title: 'UniversalHotspot',
    pic: pcUniversalHotspotPng,
    picActive: pcUniversalHotspotActivePng,
    components: 'hotSpotComponent',
    rightConfigTitle: 'UniversalHotspot',
    Ref: 'universalHotspotTools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(hotSpotComponent) // 路由地址
  },
  {
    type: 'limited_skill',
    currentType: 'mark_activity',
    title: 'limitedKill',
    pic: pcLimitedSkillPng,
    picActive: pcLimitedSkillActivePng,
    components: 'limitedSkillComponent',
    rightConfigTitle: 'limitedKill',
    Ref: 'limitedSkillTools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(limitedSkillComponent) // 路由地址
  },
  {
    type: 'discount_coupon',
    currentType: 'mark_activity',
    title: 'discountCoupon',
    pic: pcDiscountCouponPng,
    picActive: pcDiscountCouponActivePng,
    components: 'discountCouponComponent',
    rightConfigTitle: 'discountCoupon',
    Ref: 'discountCouponTools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(discountCouponComponent) // 路由地址
  },
  {
    type: 'goods_module1',
    currentType: 'extend_component',
    title: 'goodsModule1',
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    components: 'goodsModuleComponentOne',
    rightConfigTitle: 'goodsModule1',
    Ref: 'goodsModule1Tools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(goodsModuleComponentOne) // 路由地址
  },
  {
    type: 'goods_module2',
    currentType: 'extend_component',
    title: 'goodsModule2',
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    components: 'goodsModuleComponentTwo',
    rightConfigTitle: 'goodsModule2',
    Ref: 'goodsModule2Tools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(goodsModuleComponentTwo) // 路由地址
  },
  {
    type: 'goods_module3',
    currentType: 'extend_component',
    title: 'goodsModule3',
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    components: 'goodsModuleComponentThree',
    rightConfigTitle: 'goodsModule3',
    Ref: 'goodsModule3Tools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(goodsModuleComponentThree) // 路由地址
  },
  {
    type: 'goods_module4',
    currentType: 'extend_component',
    title: 'goodsModule4',
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    components: 'goodsModuleComponentFour',
    rightConfigTitle: 'goodsModule4',
    Ref: 'goodsModule4Tools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(goodsModuleComponentFour) // 路由地址
  },
  {
    type: 'goods_module5',
    currentType: 'extend_component',
    title: 'goodsModule5',
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    components: 'goodsModuleComponentFive',
    rightConfigTitle: 'goodsModule5',
    Ref: 'goodsModule5Tools',
    rightConfigMessage: {}, // 右边配置信息
    routerPath: shallowRef(goodsModuleComponentFive) // 路由地址
  }
]
