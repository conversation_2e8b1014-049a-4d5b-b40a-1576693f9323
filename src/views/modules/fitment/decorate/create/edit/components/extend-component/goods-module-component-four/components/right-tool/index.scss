.component-right-tool {
  .config-items {
    margin-bottom: 20px;
    .bg-pic {
      width: 100%;
      height: 99px;
      background: rgba(243, 245, 247, 0.39);
      border-radius: 2px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: relative;
      .el-image {
        width: 100%;
        height: 100%;
      }
      .bg-icon {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        span {
          color: #155BD4;
          &:nth-child(1) {
            font-size: 30px;
            margin-right: 10px;
          }
          &:nth-child(2) {
            font-size: 14px;
            font-family: Microsoft YaHei
          }
        }
      }
      .bg-title {
        font-size: 12px;
        color: #9FA4B1;
        text-align: center;
      }
      .update-img {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 20px;
        background: rgba(0, 0, 0, 0.3);
        color: #fff;
        line-height: 20px;
        text-align: center;
        font-size: 12px;
      }
      .close-icon {
        position: absolute;
        right: -5px;
        top: -5px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: rgba(21, 91, 212, 1);
        color: #fff;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
      }
      &.has-bg {
        width: 100%;
        height: 99px;
      }
    }
    .title {
      margin-bottom: 8px;
    }
    &:nth-child(2) {
      margin-bottom: 34px;
    }
  }
}
