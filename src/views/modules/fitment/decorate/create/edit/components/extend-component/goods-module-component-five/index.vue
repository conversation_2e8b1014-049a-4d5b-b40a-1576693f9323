<template>
  <div class="goods-module-component component-goods-module-component-five">
    <goods-module-five :config="config" />
  </div>
</template>

<script setup>
import goodsModuleFive from '../../../../common-component/goods-module-five/index.vue'

const props = defineProps({
  itemComponent: { // 组件的信息
    type: Object,
    default: () => {}
  }
})

const config = ref({})

watch(() => props.itemComponent, (newVal) => {
  if (JSON.stringify(newVal.rightConfigMessage) != '{}') {
    config.value = {
      bgImg: newVal.rightConfigMessage.bgImg,
      goodsList: newVal.rightConfigMessage.goodsList
    }
  } else {
    config.value = {
      bgImg: '',
      goodsList: []
    }
  }
}, {
  immediate: true,
  deep: true
})

</script>
<style lang="scss" scoped>
$currentContentWidth: 1200px; // 当前页面内容宽度
.component-goods-module-component-five {
  width: $currentContentWidth;
  margin: 0 auto;
}
</style>
