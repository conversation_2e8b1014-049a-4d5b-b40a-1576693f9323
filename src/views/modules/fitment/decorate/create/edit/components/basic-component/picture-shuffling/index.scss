.component-picture-shutting {
  width: 100%;
  overflow: hidden;
  position: relative;
  .picture-shutting-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    display: flex;
    .picture-items {
      max-width: 1920px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      .el-image {
        width: 100%;
        height: 100%;
      }
    }
  }
  .page-container {
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    bottom: 10px;
    width: 100%;
    height: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    .page-items {
      width: 31px;
      height: 2px;
      background: rgba(255, 255, 255, 0.39);
      margin-right: 10px;
      &.active {
        background: #fff;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.picture-shutting-container {
  .image-slot {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    i {
      color: rgba(220, 223, 230, 0.39);
    }
    .advise {
      font-size: 13px;
      color: #9FA4B1;
      margin-top: 23px;
    }
  }
}
