.component-right-tool {
  .config-items {
    margin-bottom: 20px;
    .items-content {
      width: 100%;
      height: 108px;
      display: flex;
      .items-rows {
        width: 33.3%;
        height: 100%;
        box-sizing: border-box;
        border: 1px solid #DCDFE6;
        background: rgba(255, 255, 255, 0.39);
        cursor: pointer;
        &:nth-child(1) {
          border-right: none;
        }
        &:nth-child(2) {
          border-right: none;
        }
        &:hover {
          background: rgba(21, 91, 212, 0.04);
          border: 1px solid #155BD4;
        }
        &.active {
          background: rgba(21, 91, 212, 0.04);
          border: 1px solid #155BD4;
        }
      }
    }
    .link-list {
      position: relative;
      width: 100%;
      height: 32px;
      border: 1px solid #DCDFE6;
      border-radius: 3px;
      .link-img {
        position: absolute;
        right: 2px;
        top: 5px;
        cursor: pointer;
        width: 20px;
        height: 20px;
      }
    }
  }
}

.goods-moduleOne {
  .el-input__inner {
    height: 32px;
  }
}
