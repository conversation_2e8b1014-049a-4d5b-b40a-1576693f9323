<template>
  <div class="hot-spot-container component-hot-spot">
    <div
      :class="{'hot-spot-content': true, 'two': config.width == '100%' ? false : true }"
      :style="{width: config.width ? config.width : '100%'}"
    >
      <template v-if="config.imgList && config.imgList.length > 0">
        <template
          v-for="(item, index) in config.imgList"
          :key="index"
        >
          <el-image
            class="hotimg-items"
            :src="checkFileUrl(item.url)"
            fit="fill"
          >
            <template #error>
              <div class="image-slot">
                <img
                  src="@/assets/img/pc-micro-page/show-default.png"
                  alt
                >
              </div>
            </template>
          </el-image>
        </template>
      </template>
      <template v-else>
        <div class="hot-spot-default">
          <el-image
            src=""
            fit="fill"
          >
            <template #error>
              <div class="image-slot">
                <img
                  src="@/assets/img/pc-micro-page/show-default.png"
                  alt
                >
              </div>
            </template>
          </el-image>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  itemComponent: { // 组件信息
    type: Object,
    default: () => {}
  }
})

const config = ref({})

watch(() => props.itemComponent, (newVal) => {
  if (JSON.stringify(newVal.rightConfigMessage) != '{}') {
    let contentWidth
    if (newVal.rightConfigMessage.size === 1200) {
      contentWidth = '1200px'
    } else {
      contentWidth = '100%'
    }
    config.value = {
      width: contentWidth,
      imgList: newVal.rightConfigMessage.imgList
    }
  } else {
    let contentWidth
    if (newVal.rightConfigMessage.size === 1200) {
      contentWidth = '1200px'
    } else {
      contentWidth = '100%'
    }
    config.value = {
      width: contentWidth,
      imgList: []
    }
  }
}, {
  deep: true,
  immediate: true
})

</script>

<style lang="scss" scoped>
.component-hot-spot {
  margin: 0 auto;
  width: 100%;

  .hot-spot-content {
    margin: 0 auto;
  }

  &:deep(.el-image) {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .hot-spot-default {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-slot {
    font-size: 60px;
    color: rgba(220, 223, 230, 0.39);
  }
}

</style>
