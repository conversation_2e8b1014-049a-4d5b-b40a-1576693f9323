<template>
  <div class="component-decorate-navbar">
    <nav
      class="site-navbar"
      :class="'site-navbar--' + navbarLayoutType"
    >
      <div
        class="site-navbar__header"
        :style="{ 'margin-right': '20px'}"
      >
        <div
          class="site-navbar__brand"
          style="width: auto;"
        >
          <img
            v-if="configuration.bsTopBarIcon"
            style="height: 18px;width:59px;margin-right: 10px;object-fit: contain;"
            :src="checkFileUrl(configuration.bsTopBarIcon)"
            alt=""
          >
          <a
            class="site-navbar__brand-lg"
            style="text-transform:none;"
            href="javascript:;"
          >{{ configuration.bsMenuTitleOpen }}</a>
          <a
            v-if="!configuration.bsTopBarIcon"
            class="site-navbar__brand-mini"
            style="text-transform:none;"
            href="javascript:;"
            :style="fontCloseSize"
          >{{ configuration.bsMenuTitleClose }}</a>
        </div>
      </div>
      <div
        class="site-navbar__content clearfix"
        @click="closeRight;visible = false"
      >
        <el-menu
          class="site-navbar__menu site-navbar__menu--right"
          mode="horizontal"
          style="margin-right: 20px"
          :ellipsis="false"
        >
          <el-menu-item
            class="site-navbar__avatar"
            index="4"
          >
            <div
              class="decorate-menu is-active"
              @click.stop="setMenu(4);handleSave();visible=false"
            >
              {{ $t('crud.saveBtn') }}
            </div>
          </el-menu-item>
        </el-menu>
        <el-menu
          v-show="isTemplate === '0'"
          class="site-navbar__menu site-navbar__menu--right"
          mode="horizontal"
          :ellipsis="false"
          style="margin-right: 20px"
        >
          <el-menu-item
            class="site-navbar__avatar"
            index="3"
          >
            <div
              class="decorate-menu is-active"
              @click.stop="setMenu(3); viewDialog = true;visible=false"
            >
              {{ $t('pcdecorate.decorateNavbar.setTemplate') }}
            </div>
          </el-menu-item>
        </el-menu>
        <el-menu
          class="site-navbar__menu site-navbar__menu--right"
          mode="horizontal"
          :ellipsis="false"
          style="margin-right: 20px"
        >
          <el-menu-item
            class="site-navbar__avatar"
            index="2"
          >
            <div
              class="decorate-menu is-active"
              @click.stop="setMenu(2);view();visible=false"
            >
              {{ $t('pcdecorate.decorateNavbar.preview') }}
            </div>
          </el-menu-item>
        </el-menu>
        <el-menu
          class="site-navbar__menu site-navbar__menu--right"
          mode="horizontal"
          :ellipsis="false"
          style="margin-right: 20px"
        >
          <el-menu-item
            class="site-navbar__avatar"
            index="1"
          >
            <div
              class="decorate-menu is-active"
              @click.stop="setMenu(1);handleClickComponent();visible=false"
            >
              {{ $t('pcdecorate.commonModal.component') }}
            </div>
          </el-menu-item>
        </el-menu>
        <el-menu
          class="site-navbar__menu site-navbar__menu--right"
          mode="horizontal"
          :ellipsis="false"
          style="margin-right: 20px"
        >
          <el-menu-item
            class="site-navbar__avatar"
            index="0"
          >
            <el-popover
              v-model="visible"
              placement="bottom"
              width="400"
              trigger="click"
            >
              <all-can-use-components
                ref="allCanUseComponentsRef"
                @add-component="addComponent"
              />
              <template #reference>
                <div
                  class="decorate-menu is-active"
                  @click.stop="visible = !visible;setMenu(0);closeComponentManage()"
                >
                  <el-icon class="el-icon-menu">
                    <Menu />
                  </el-icon>
                  {{ $t('pcdecorate.decorateNavbar.addComponent') }}
                </div>
              </template>
            </el-popover>
          </el-menu-item>
        </el-menu>
        <div class="top-tip">
          {{ $t('pcdecorate.decorateNavbar.moveTip') }}
        </div>
      </div>
    </nav>

    <!-- 模板设置 -->
    <el-dialog
      v-model="viewDialog"
      :title="$t('pcdecorate.decorateNavbar.templateInfo')"
      width="30%"
    >
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="dataFormRef"
        :model="dataForm"
        :rules="dataRule"
        label-width="100px"
        @submit.prevent
      >
        <el-form-item
          :label="$t('transport.name') + ':'"
          prop="name"
          :label-width="labelWidth"
        >
          <!-- native modifier has been removed, please confirm whether the function has been affected  -->
          <el-input
            v-model="dataForm.name"
            maxlength="20"
            @keyup.enter="onSubmit()"
          />
        </el-form-item>
        <el-form-item
          :label="$t('pcdecorate.decorateNavbar.templateRemark') + ':'"
          prop="remark"
          :label-width="labelWidth"
        >
          <el-input
            v-model="dataForm.remark"
            type="textarea"
            rows="5"
            :placeholder="$t('pcdecorate.decorateNavbar.templateTip')"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :label="$t('pcdecorate.decorateNavbar.templateImg') + ':'"
          prop="imgUrl"
          :label-width="labelWidth"
        >
          <div class="img-upload">
            <img-upload v-model="dataForm.imgUrl" />
            <span
              v-if="dataForm.imgUrl"
              class="default-btn text-btn"
              @click="delTagItemPic()"
            >
              {{ $t('text.delBtn') }}
            </span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="primary-btn default-btn"
            @click="viewDialog = false"
          >
            {{ $t("order.cancel") }}
          </div>
          <div
            class="primary-btn default-btn"
            @click="onSubmit"
          >
            {{ $t("order.confirm") }}
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import allCanUseComponents from '../all-can-use-components/index.vue' // 左边组件列表
import { formatConfigInfo } from '@/utils/website-config.js'

const props = defineProps({
  isTemplate: {
    type: String,
    default: '0'
  },
  dataFormL: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits([
  'view',
  'handleSaveTemplate',
  'addComponent',
  'handleClickComponent',
  'handleSave',
  'closeRight',
  'closeComponentManage'
])

const configuration = ref({
  bsLoginLogoImg: null,
  bsLoginBgImg: null,
  bsCopyright: null,
  bsTitleContent: null,
  bsTitleImg: null,
  bsMenuTitleOpen: null,
  bsMenuTitleClose: null
})
const fontCloseSize = reactive({
  fontSize: '18px'
})
const fontOpenSize = reactive({
  fontSize: '16px'
})
const visible = ref(false)
const dataForm = ref(props.dataFormL)

const validateName = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('pcdecorate.decorateNavbar.templateNameNotNull')))
  } else {
    callback()
  }
}
const dataRule = reactive({
  name: [
    { required: true, message: $t('pcdecorate.decorateNavbar.templateNameNotNull'), trigger: 'blur' },
    { validator: validateName, trigger: 'blur' }
  ]
})
const labelWidth = localStorage.getItem('bbcLang') === 'en' ? '140px' : '100px'

const commonStore = useCommonStore()
const navbarLayoutType = computed(() => {
  return commonStore.navbarLayoutType
})

watch(() => props.dataFormL, (val) => {
  dataForm.value = val
})

onMounted(() => {
  updateWebConfigData()
})

const delTagItemPic = () => {
  dataForm.value.imgUrl = ''
}

// 预览
const view = () => {
  emit('view')
}

const dataFormRef = ref(null)
const viewDialog = ref(false)
// 模板保存
const onSubmit = () => {
  dataFormRef.value?.validate((valid) => {
    if (!valid) {
      return
    }
    emit('handleSaveTemplate', dataForm.value)
    viewDialog.value = false
  })
}

const allCanUseComponentsRef = ref(null)
let menuActive = 1
// 菜单选择
const setMenu = (index) => {
  if (index === 0) {
    allCanUseComponentsRef.value.currentActiveIndex = ''
  }
  if (index !== menuActive) {
    menuActive = index
  } else {
    menuActive = -1
  }
}

// 添加组件
const addComponent = (item) => {
  emit('addComponent', item)
}

// 组件管理
const handleClickComponent = () => {
  emit('handleClickComponent')
}

// 保存
const handleSave = () => {
  if (props.isTemplate === '0') {
    emit('handleSave', '2')
  } else {
    viewDialog.value = true
  }
}

// 关闭组件设置
const closeRight = () => {
  emit('closeRight')
}

// 关闭组件管理
const closeComponentManage = () => {
  emit('closeComponentManage')
}

const webConfigStore = useWebConfigStore()
// TODO -lang 适配各语言
// 更新网站配置信息
const updateWebConfigData = () => {
  http({
    url: http.adornUrl('/sys/webConfig/getActivity'),
    method: 'get'
  }).then(({ data }) => {
    data = formatConfigInfo(data)
    webConfigStore.addData(data)
    configuration.value = data
    if (data.bsMenuTitleOpen.length >= 17) {
      fontOpenSize.fontSize = '16px'
    }

    if (data.bsMenuTitleClose.length >= 6) {
      fontCloseSize.fontSize = '18px'
    }
  })
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
