.ad-hot-box {
  position: relative;
  width: 500px;
  margin: 0 auto;
  overflow: hidden;
  .box-img {
    position: relative;
    height: auto;
    width: 500px;
    vertical-align: middle;
    border: 0;
  }
}
.ad-drag {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
  user-select: auto;
  touch-action: none;
  cursor: move;
  border: 1px solid #38f;
  background: rgba(51, 136, 255, .5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  .el-icon-close {
    position: absolute;
    top: 0;
    right: 0;
  }
  &:hover {
    .el-icon-close {
      display: block;
      cursor: pointer;
    }
  }
  .hot-item-title {
    max-width: 100%;
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    margin-top: 5px;
  }
  * {
    user-select: auto;
    touch-action: none;
  }
  .title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
}
.component-hot-spot-right-tool {
  .config-items {
    width: 100%;
    margin-bottom: 25px;
    display: flex;
    position: relative;
    .title {
      width: 90px;
      display: flex;
      align-items: center;
      position: relative;
    }
    .right-select {
      width: 100%;
      &.tips {
        color: #999;
        font-size: 12px;
        font-family: Microsoft YaHei;
        display: flex;
        align-items: flex-end;
      }
    }
    .b-btns {
      width: 100%;
      height: 42px;
      background: rgba(243, 245, 247, 0.39);
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
      span {
        font-size: 14px;
        font-family: Microsoft YaHei;
        color: #155BD4;
        &:nth-child(1) {
          font-size: 25px;
          margin-right: 5px;
        }
        &:nth-child(2) {
          padding-top: 2px;
        }
      }
    }
  }
  .close-icon {
    position: absolute;
    top: 0px;
    right: 0px;
    font-size: 20px;
    display: none;
  }
  .ad-image-hot {
    position: relative;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
    .ad-image-hot-box {
      width: 350px;
      position: relative;
      &:hover {
        .close-icon {
          cursor: pointer;
          display: block;
        }
        .icon-error {
          display: block;
        }
      }
    }
    .add-ad-image {
      display: inline-block;
      margin-bottom: 15px;
      width: 90px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      border: 1px solid #155BD4;
      cursor: pointer;
      color: #155BD4;
      margin-right: 10px;
      font-size: 12px;
      border-radius: 2px;
    }
    img {
      width: 100%;
      user-select: none;
    }
    .ad-image-hot-content {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      cursor: all-scroll;
      overflow: hidden;
      span.ad-hot-box-item {
        position: absolute;
        border: 1px solid #38f;
        background: rgba(51, 136, 255, .5);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        cursor: pointer;
        max-width: 100%;
        max-height: 100%;
        &:hover {
          .el-icon-close {
            display: block;
            cursor: pointer;
          }
          .icon-error {
            display: block;
          }
        }
        .hot-item-title {
          max-width: 100%;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          margin-top: 5px;
        }
      }
    }
    .pic-close-icon {
      position: absolute;
      top: -13px;
      right: -8px;
    }
    .icon-error {
      position: absolute;
      top: -13px;
      right: -8px;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      text-align: center;
      line-height: 18px;
      background: #155bd4;
      color: #fff;
      font-size: 12px;
      cursor: pointer;
      display: none;
    }
  }
}
.ad-image-list {
  display: flex;
  background: #fff;
  padding: 10px;
  border: 1px dashed #e5e5e5;
  margin-top: 10px;
  align-items: center;
  &:hover {
    border: 1px dashed #38f;
  }
  > div {
    &:first-child {
      position: relative;
      width: 80px;
      height: 80px;
      cursor: all-scroll;

      .re-choose-img {
        position: absolute;
        margin: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        background: rgba(0, 0, 0, 0.52);
        text-align: center;
        padding: 2px 0;
        cursor: pointer;
        color: #fff;
      }
    }
    &:last-child {
      flex: 1;
    }
  }
  .ad-image-list-img {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-color: #ccc;
  }
  .ad-image-list-content {
    position: relative;
    padding-left: 10px;
    display: flex;
    align-items: center;
    .custom-link-input {
      margin: 10px auto;
      width: 100%!important;
    }
    .close-icon {
      top: -10px;
      right: -10px;
    }
    &:hover {
      .close-icon {
        cursor: pointer;
        display: block;
      }
    }
    .el-input {
      width: 200px;
      margin-left: 10px;
    }
    .ad-image-link {
      margin-top: 10px;
      display: flex;
      align-items: center;
      .el-dropdown-selfdefine {
        margin-left: 10px;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.custom-path-con {
  margin-top: 10px;
  max-width: 100%;
  display: flex;
  align-items: center;
  span {
    white-space: nowrap;
    margin-right: 10px;
  }
}

.redirect-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
