.component-picture-shutting-right-tool {
  .config-items {
    margin-bottom: 25px;
    display: flex;
    position: relative;
    .title {
      width: 90px;
      display: flex;
      align-items: center;
    }

    &:nth-child(1) {
      margin-bottom: 10px;
    }
    .right-select {
      width: 100%;
    }
  }
  .tips {
    color: #999;
    font-size: 12px;
    font-family: Microsoft YaHei;
    margin-bottom: 20px;
  }
  .picture-styles {
    .picture-items {
      position: relative;
      margin-bottom: 20px;
      .el-image {
        width: 100%;
        height: 100px;
        object-fit: cover;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
      .picture-text {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        span {
          display: flex;
          align-items: center;
        }
        .link-styles {
          width: 88%;
          height: 32px;
          background: rgba(255, 255, 255, 0.39);
          border: 1px solid #DCDFE6;
          border-radius: 3px;
          position: relative;
          cursor: pointer;
          .link-placeholder {
            position: absolute;
            left: 11px;
            top: 9px;
            color: #999;
            font-size: 12px;
            font-family: Microsoft YaHei;
          }
          .link-imgicon {
            position: absolute;
            width: 18px;
            height: 18px;
            right: 7px;
            top: 6px;
          }
        }
      }
      .el-icon-delete {
        position: absolute;
        top: 35%;
        color: #fff;
        right: 45%;
        transform: translate(-50%, -50%);
        font-size: 16px;
        display: none;
        cursor: pointer;
      }
      .update-pic {
        position: absolute;
        width: 100%;
        height: 20px;
        color: #fff;
        background: rgba(0, 0, 0 ,0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        top: 80px;
        font-size: 12px;
        border-radius: 4px 4px 0 0;
        cursor: pointer;
      }
      &:hover {
        .el-image {
          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.39);
          }
        }
        .el-icon-delete {
          display: block;
        }
      }
    }
    .b-btns {
      width: 100%;
      height: 42px;
      background: rgba(243, 245, 247, 0.39);
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      cursor: pointer;
      span {
        font-size: 14px;
        font-family: Microsoft YaHei;
        color: #155BD4;
        &:nth-child(1) {
          font-size: 25px;
          margin-right: 5px;
        }
        &:nth-child(2) {
          padding-top: 2px;
        }
      }
    }
  }
}
