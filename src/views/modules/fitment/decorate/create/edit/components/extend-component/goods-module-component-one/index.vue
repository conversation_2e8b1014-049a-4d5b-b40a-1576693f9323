<template>
  <div class="goods-module-one-component component-goods-module-component-one">
    <div class="one-items">
      <goods-module-one :config="config.leftConfigMessage" />
    </div>
    <div class="one-items">
      <goods-module-one :config="config.centerConfigMessage" />
    </div>
    <div class="one-items">
      <goods-module-one :config="config.rightConfigMessage" />
    </div>
  </div>
</template>
<script setup>
import goodsModuleOne from '../../../../common-component/goods-module-one/index.vue'

const props = defineProps({
  itemComponent: { // 组件信息展示
    type: Object,
    default: () => {}
  }
})

const config = reactive({ // 配置信息
  leftConfigMessage: {
    mainTitle: $t('pcdecorate.floorTitle.mainTitle'),
    subTitle: $t('pcdecorate.floorTitle.subTitle')
  }, // 左边配置信息
  centerConfigMessage: {
    mainTitle: $t('pcdecorate.floorTitle.mainTitle'),
    subTitle: $t('pcdecorate.floorTitle.subTitle')
  }, // 中间配置信息
  rightConfigMessage: {
    mainTitle: $t('pcdecorate.floorTitle.mainTitle'),
    subTitle: $t('pcdecorate.floorTitle.subTitle')
  } // 右边配置信息
})

const init = () => {
  config.leftConfigMessage = {
    currentIndex: 1, // 当前选择哪列
    mainTitle: $t('pcdecorate.goodsModule1.mainTitleCon'), // 当前主标题
    subTitle: $t('pcdecorate.goodsModule1.subTitleCon'), // 副标题
    titleLink: {
      name: '', // 跳转的名字
      link: '' // 跳转的链接
    }, // 标题跳转的链接
    goodsList: [] // 商品
  }
  config.centerConfigMessage = {
    currentIndex: 3, // 当前选择哪列
    mainTitle: $t('pcdecorate.goodsModule1.mainTitleCon'), // 当前主标题
    subTitle: $t('pcdecorate.goodsModule1.subTitleCon'), // 副标题
    titleLink: {
      name: '', // 跳转的名字
      link: '' // 跳转的链接
    }, // 标题跳转的链接
    goodsList: [] // 商品
  }
  config.rightConfigMessage = {
    currentIndex: 2, // 当前选择哪列
    mainTitle: $t('pcdecorate.goodsModule1.mainTitleCon'), // 当前主标题
    subTitle: $t('pcdecorate.goodsModule1.subTitleCon'), // 副标题
    titleLink: {
      name: '', // 跳转的名字
      link: '' // 跳转的链接
    }, // 标题跳转的链接
    goodsList: [] // 商品
  }
}

watch(() => props.itemComponent, (newVal) => {
  if (JSON.stringify(newVal.rightConfigMessage) != '{}') {
    config.leftConfigMessage = { ...newVal.rightConfigMessage.leftConfig }
    config.centerConfigMessage = { ...newVal.rightConfigMessage.centerConfig }
    config.rightConfigMessage = { ...newVal.rightConfigMessage.rightConfig }
  } else {
    init()
  }
}, {
  immediate: true,
  deep: true
})

</script>

<style lang="scss" scoped>
$currentContentWidth: 1200px; // 当前页面内容宽度
.component-goods-module-component-one {
  width: $currentContentWidth;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .one-items{
    width: calc((100% - 40px) / 3)
  }
}
</style>
