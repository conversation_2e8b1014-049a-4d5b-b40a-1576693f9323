.component-manage-container {
  width: 450px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  top: 50px;
  right: 0;
  z-index: 99;
  padding: 20px;
  background: #fff;

  .title {
    color: #333;
    font-size: 16px;
    margin-top: 0;
    position: relative;
    margin-left: 10px;

    &::before {
      content: '';
      position: absolute;
      width: 3px;
      height: 15px;
      left: -10px;
      top: 2px;
      background: rgba(21, 91, 212, 1);
    }

    .el-icon-close {
      color: #8C8C8C;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      :hover {
        color: var(--el-color-primary);
      }
    }
  }

  .title-border {
    width: 100%;
    height: 1px;
    background: #EDEDF2;
    margin: 20px 0;
  }

  .config-container {
    .titles {
      color: #666;
      margin-bottom: 15px;
    }

    .config-items {
      margin-bottom: 25px;
    }
  }

  .items-header {
    height: 40px;
    border: 1px dashed rgba(21, 91, 212, 1);
    background: #fff;
    z-index: 99;
    display: flex;
    padding: 0 10px;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .items-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }

  .top-header {
    display: flex;
    justify-content: space-between;

    span {
      color: #333;

      &:nth-child(2) {
        cursor: pointer;
        color: #155bd4;
      }
    }
  }

  .component-list {
    margin-top: 15px;

    .component-list-item {
      width: 100%;
      height: 40px;
      margin-bottom: 10px;
      border: 1px solid #EAEAF2;
      background: #fff;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      z-index: 99;
      color: #333;

      .item-drag-box {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 100;
        top: 0;
        left: 0;
        cursor: all-scroll;
      }

      .items-content {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
        .icon-con {
          display: flex;
          align-items: center;
          justify-content: space-between;

          :deep(.el-icon) {
            cursor: pointer;
            z-index: 999;
          }

          .el-icon + .el-icon {
            margin-left: 10px;
          }
        }
      }

      &:hover {
        border-color: #155bd4;
        color: #155bd4;
      }
    }
  }

  .empty-template-pc {
    margin-top: 20%;
    text-align: center;
    font-size: 14px;
    color: #c0c4cc;
  }
}

.drag-class {
  width: 400px;
  height: 40px;
  border: 1px solid #EAEAF2;
  background: #fff;
  z-index: 99;
  display: flex;
  padding: 0 20px;
  align-items: center;
  justify-content: space-between;

  .items-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
}

.config-container {
  .el-input {
    .el-input__inner {
      height: 32px !important;
    }
  }
}
