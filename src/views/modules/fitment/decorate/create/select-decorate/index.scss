.page-select-decorate {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background: #f4f4f4;
  left: 0;
  top: 0;
  z-index: 1030;
  bottom: 0;
  right: 0;
  overflow-y: auto;
  // 头部样式
  .store-signate-page-container .store-top.style1 {
    width: 1200px !important;
  }

  .store-signate-page-container .store-top.style2 {
    .store-top-content {
      width: 1200px !important;
    }
  }

  .store-signate-page-container .store-bottom .bottom-content {
    width: 1200px !important;

    .navs-list {
      padding: 0 5px;

      li {
        margin-right: 50px;
      }
    }
  }

  // 轮播样式
  .picture-shutting-com.two {
    width: 1200px !important;
  }

  // 标题样式
  .common_floor_titles {
    width: 1200px !important;
  }

  // 商品列表样式
  .goods_list_component {
    width: 1200px !important;

    .goods-list-items.five {
      .el-image {
        height: 224px !important;
      }

      .imgs_shelves {
        height: 224px !important;
      }
    }
  }

  // 店铺列表
  .store-list-container .store-list-pages {
    width: 1200px !important;
  }

  // 热区样式
  .hot-spot-container .hot-spot-content.two {
    width: 1200px !important;
  }

  // 秒杀专区
  .limited-skill-pages {
    width: 1200px !important;

    .goods-list-items.four {
      .el-image {
        height: 285px !important;
      }

      .imgs_shelves {
        height: 285px !important;
      }
    }
  }

  .goods-module-one-component {
    width: 1200px !important;
  }

  .goods-module-two-component {
    width: 1200px !important;

    .actual {
      span {
        &:nth-child(2) {
          font-size: 16px !important;
        }
      }
    }
  }

  // 商品模块3的样式
  .goods-module-three {
    width: 1200px !important;

    .goods-three-items {
      height: 336px !important;

      .bottom-items {
        width: calc((100% - 40px) / 3) !important;
        margin-right: 20px !important;

        .el-image {
          width: 170px !important;
          height: 170px !important;
        }

        .imgs_shelves {
          width: 170px !important;
          height: 170px !important;

          img {
            width: 140px !important;
          }
        }
      }
    }
  }

  .goods-module-component {
    width: 1200px !important;

    .bottom-items {
      .el-image {
        height: 275px !important;
      }

      .imgs_shelves {
        height: 275px !important;

        img {
          width: 180px !important;
        }
      }
    }

    .goods-module5 {
      .left-bg {
        width: 232px !important;
        height: 320px !important;
      }

      .right-goods {
        width: calc(100% - 232px) !important;
        height: 320px !important;

        .goods-items {
          height: 320px !important;

          .el-image {
            height: 203.19px !important;
          }

          .imgs_shelves {
            height: 203.19px !important;

            img {
              width: 140px !important;
            }
          }
        }
      }
    }
  }
}
