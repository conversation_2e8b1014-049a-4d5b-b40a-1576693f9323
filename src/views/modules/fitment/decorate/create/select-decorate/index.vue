<template>
  <div
    class="select-decorate-page page-select-decorate"
    :style="{background: pageBackground}"
  >
    <div class="select-decorate-content">
      <div
        v-for="(item, index) in configArr"
        :key="index"
      >
        <component
          :is="componentList.find(x=>x.type === item.type) ? componentList.find(x=>x.type === item.type).routerPath : ''"
          :item-component="item"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { configComponentList } from '../edit/components/all-can-use-components/configComponent.js'

const route = useRoute()
const pageBackground = ref('') // 页面背景颜色
onMounted(() => {
  const { renovationId } = route.query
  if (renovationId) {
    getDetail(renovationId)
  } else if (localStorage.getItem('bbcViewContent')) {
    viewContent(JSON.parse(localStorage.getItem('bbcViewContent')))
  }
})

const componentList = ref(configComponentList)

const configArr = ref([])
// 预览
const viewContent = async (data) => {
  document.title = data.name
  const useArr = []
  let contentArr = JSON.parse(data.content)
  // 匹配商品
  contentArr = await matchproducts(contentArr)
  contentArr.forEach(ele => {
    if (ele.type === 'page_background') {
      pageBackground.value = ele.dataField.background
    }
    componentList.value.forEach(item => {
      if (ele.type === item.type) {
        useArr.push({
          ...item,
          rightConfigMessage: ele.dataField
        })
      }
    })
  })
  configArr.value = useArr
}

// 获取详情
const getDetail = (id) => {
  http({
    url: http.adornUrl(`/shop/shopRenovation/info/${id}`),
    method: 'get'
  }).then(async ({ data }) => {
    document.title = data.name
    const useArr = []
    let contentArr = JSON.parse(data.content)
    // 匹配商品
    contentArr = await matchproducts(contentArr)
    contentArr.forEach((ele) => {
      if (ele.type === 'page_background') {
        pageBackground.value = ele.dataField.background
      }
      componentList.value.forEach(item => {
        if (ele.type === item.type) {
          useArr.push({
            ...item,
            rightConfigMessage: ele.dataField
          })
        }
      })
    })
    configArr.value = useArr
  })
}

// 匹配商品
const matchproducts = async (arr) => {
  const res = await searchGoodsMessage(arr) // 根据现有的id查询所有的商品信息
  arr.forEach(item => {
    if (item.type === 'goods_list') { // 商品列表
      item.dataField.goodsList = handleScreenGoods(item.dataField.goodsList, res, 'id')
    } else if (item.type === 'discount_coupon') {
      const discountParams = {
        // 商品名称
        status: 'status',
        price: 'price',
        imgs: 'pic',
        description: 'brief' // 商品描述
      }
      item.dataField.goodsList = handleScreenGoods(item.dataField.goodsList, res, 'id', discountParams)
    } else if (item.type === 'limited_skill') { // 秒杀商品
      const limitedParams = {
        // 商品名称
        status: 'status',
        price: 'price',
        imgs: 'pic',
        description: 'brief' // 商品描述
      }
      item.dataField.goodsList = handleScreenGoods(item.dataField.goodsList, res, 'prodId', limitedParams)
    } else if (item.type === 'goods_module1') { // 商品模块1
      item.dataField.leftConfig.goodsList = handleScreenGoods(item.dataField.leftConfig.goodsList, res, 'id')
      item.dataField.centerConfig.goodsList = handleScreenGoods(item.dataField.centerConfig.goodsList, res, 'id')
      item.dataField.rightConfig.goodsList = handleScreenGoods(item.dataField.rightConfig.goodsList, res, 'id')
    } else if (item.type === 'goods_module2') { // 商品模块2
      item.dataField.maingoodsList = handleScreenGoods(item.dataField.maingoodsList, res, 'id')
      item.dataField.othergoodsList = handleScreenGoods(item.dataField.othergoodsList, res, 'id')
    } else if (item.type === 'goods_module3') { // 商品模块3
      item.dataField.leftConfig.goodsList = handleScreenGoods(item.dataField.leftConfig.goodsList, res, 'id')
      item.dataField.rightConfig.goodsList = handleScreenGoods(item.dataField.rightConfig.goodsList, res, 'id')
    } else if (item.type === 'goods_module4' || item.type === 'goods_module5') { // 商品模块4
      item.dataField.goodsList = handleScreenGoods(item.dataField.goodsList, res, 'id')
    }
  })
  return arr
}

// 查询商品信息
const searchGoodsMessage = (arr) => {
  let goodsArr = []
  arr.forEach(item => {
    if (item.type === 'limited_skill') { // 秒杀商品
      item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.prodId)
      })
    } else if (item.type === 'discount_coupon') { // 优惠团购
      item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goods_list') { // 商品信息
      item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goods_module1') { // 商品模块1
      item.dataField.leftConfig && item.dataField.leftConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
      item.dataField.centerConfig && item.dataField.centerConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
      item.dataField.rightConfig && item.dataField.rightConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goods_module2') { // 商品模块2
      item.dataField.maingoodsList && item.dataField.maingoodsList.forEach(v => {
        goodsArr.push(v.id)
      })
      item.dataField.othergoodsList && item.dataField.othergoodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goods_module3') { // 商品模块3
      item.dataField.leftConfig && item.dataField.leftConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
      item.dataField.rightConfig && item.dataField.rightConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goods_module4') { // 商品模块4
      item.dataField.goodsList && item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goods_module5') { // 商品模块5
      item.dataField.goodsList && item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    }
  })
  goodsArr = Array.from(new Set(goodsArr))
  return new Promise((resolve) => {
    http({
      url: http.adornUrl('/admin/search/prod/renovationPage'),
      method: 'get',
      params: http.adornParams({
        prodIds: goodsArr,
        current: 1,
        size: goodsArr.length,
        getDelete: true
      })
    }).then(({ data }) => {
      resolve(data.records)
    })
  })
}

// 筛选商品回显 currentArr: 表示当前装修的商品，backReturnArr: 表示根据现有id查询后端返回的商品
const handleScreenGoods = (currentArr, backReturnArr, type, otherParams) => {
  let params = {}
  if (otherParams) {
    params = {
      ...otherParams
    }
  } else {
    params = {
      // 商品名称
      status: 'status', // 商品状态
      price: 'price', // 商品价格
      imgs: 'pic', // 商品图片
      description: 'brief' // 商品描述
    }
  }
  const arr = []
  for (let i = 0; i < currentArr.length; i++) {
    for (let j = 0; j < backReturnArr.length; j++) {
      if (currentArr[i][type] == backReturnArr[j].prodId) {
        for (const key in params) {
          currentArr[i][key] = backReturnArr[j][params[key]]
        }
        currentArr[i].price = currentArr[i].price ? backReturnArr[j].price : ''
        currentArr[i].orignPrice = currentArr[i].orignPrice ? backReturnArr[j].oriPrice : ''
        currentArr[i].prodType = currentArr[i].prodType ? backReturnArr[j].prodType : ''
        arr.push(currentArr[i])
      }
    }
  }
  return arr
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
