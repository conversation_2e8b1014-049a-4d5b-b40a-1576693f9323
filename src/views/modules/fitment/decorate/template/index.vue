<template>
  <div class="template-list page-decorate-template">
    <div
      v-if="isAuth('shop:shopTemplate:savePC')"
      class="default-btn primary-btn new-page"
      @click="goCreatePage"
    >
      {{ $t('shopFeature.template.newTemplate') }}
    </div>
    <div
      class="default-btn primary-btn new-page"
      @click="getMiniPageList"
    >
      {{ $t('shopFeature.template.refresh') }}
    </div>
    <div class="main-container">
      <div
        v-if="templateList.length"
        style="display: flex;flex-wrap: wrap;"
      >
        <div
          v-for="(item, index) in templateList"
          :key="index"
          class="card-item"
        >
          <div class="template-item">
            <img
              v-if="item.image"
              :src="checkFileUrl(item.image)"
              alt
            >
            <img
              v-else
              src="@/assets/img/def.png"
              alt
            >
            <div class="operation is-active">
              <div
                v-if="isAuth('shop:shopTemplate:deletePC')"
                class="operation-item"
                @click="handleDelete(item.templateId)"
              >
                <el-icon><Delete /></el-icon>{{ $t('shopFeature.template.delete') }}
              </div>
              <div
                v-if="isAuth('shop:shopTemplate:copyPC')"
                class="operation-item"
                @click="copyTemplatePage(item.templateId)"
              >
                <el-icon><CopyDocument /></el-icon>{{ $t('shopFeature.template.copy') }}
              </div>
              <div
                v-if="isAuth('shop:shopTemplate:updatePC')"
                class="operation-item"
                @click="handleEdit(item.templateId)"
              >
                <el-icon><Edit /></el-icon>{{ $t('shopFeature.template.edit') }}
              </div>
            </div>
          </div>
          <div class="title">
            {{ item.name }}
          </div>
        </div>
      </div>
      <el-empty
        v-else
        class="empty-form"
        :description="$t('shop.noData')"
      />
    </div>
    <div style="clear: both;" />
    <el-pagination
      v-if="templateList.length"
      style="margin-right: 5%"
      :current-page="perProps.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="perProps.pageSize"
      :total="perProps.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const templateList = ref([]) // 列表页

const perProps = reactive({
  pageNum: 1, // 当前页
  pageSize: 10, // 每页显示多少条
  total: 0 // 总数
})

onMounted(() => {
  // 获取微页面列表
  getMiniPageList()
})

// 获取微页面列表
const getMiniPageList = () => {
  const { pageNum, pageSize } = perProps
  http({
    url: http.adornUrl('/shop/shopTemplate/pagePC'),
    methods: 'get',
    params: http.adornParams({
      current: pageNum, // 当前页
      size: pageSize, // 每页显示多少条
      type: 1 // 1表示pc端，2表示移动端
    })
  }).then(({ data }) => {
    templateList.value = data.records
    perProps.total = data.total
  }).catch(() => {})
}

// 复制模板
const copyTemplatePage = (templateId) => {
  http({
    url: http.adornUrl(`/shop/shopTemplate/copyPC/${templateId}`),
    method: 'post'
  }).then(() => {
    ElMessage.success($t('shopFeature.template.copySuccess'))
    getMiniPageList()
  }).catch(() => {})
}

const router = useRouter()
// 新建微页面
const goCreatePage = () => {
  // 跳转到装修页面
  const newPage = router.resolve({
    path: '/fitment/decorate/create/edit/index',
    query: {
      type: 'add',
      template: '1'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

// 每页数
const sizeChangeHandle = (val) => {
  perProps.pageSize = val
  getMiniPageList()
}

// 当前页
const currentChangeHandle = (val) => {
  perProps.pageNum = val
  getMiniPageList()
}

// 编辑
const handleEdit = (templateId) => {
  const newPage = router.resolve({
    path: '/fitment/decorate/create/edit/index',
    query: {
      templateId,
      type: 'edit',
      template: '1'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

// 删除
const handleDelete = (renovationId) => {
  ElMessageBox.confirm($t('shopFeature.list.deleteTips'), $t('shopFeature.list.tips'), {
    confirmButtonText: $t('shopFeature.edit.confirm'),
    cancelButtonText: $t('shopFeature.edit.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/shopTemplate/deletePC/' + renovationId),
      method: 'delete'
    }).then(() => {
      const totalPage = Math.ceil((perProps.total - 1) / perProps.pageSize)
      const currentPage = perProps.pageNum > totalPage ? totalPage : perProps.pageNum
      perProps.pageNum = currentPage < 1 ? 1 : currentPage
      ElMessage.success($t('shopFeature.list.deleteSuccess'))
      getMiniPageList()
    }).catch(() => {})
  }).catch(() => {})
}

</script>

<style lang="scss" scoped>
.page-decorate-template {
  .new-page:first-child {
    margin-left: 40px;
  }
  .new-page {
    margin-left: 20px;
  }
  .main-container {
    margin-left: 30px;
    width: 100%;
    .template-item {
      position: relative;
      width: 100%;
    }
    .template-item:hover .operation {
      display: flex;
    }
    .card-item {
      background: #FFFFFF;
      margin: 20px 0.63%;
      width: 17.34%;
      img {
        width: 100%;
        height: 18.1vh;
        border-radius: 4px;
      }
      .operation {
        position: absolute;
        bottom: 0;
        display: none;
        background-color: #000;
        opacity: .7;
        height: 20%;
        width: 100%;
        color: #fff;
        border-radius: 0 0 4px 4px;
        .operation-item {
          flex: 1;
          display: flex;
          position: relative;
          font-size: 14px;
          cursor: pointer;
          justify-content: center;
          align-items: center;
        }
        .operation-item:hover {
          color: #155BD4;
        }
        .operation-item:not(:last-child)::after {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          content: '';
          display: block;
          width: 1px;
          height: 30px;
          background: #fff;
        }
      }
      .title {
        color: #000000;
        font-size: 14px;
        margin-top: 16px;
        text-align: center;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        max-width: 276px;
      }
    }
  }

}
  .empty-form {
    margin-top: 150px;
  }
</style>
