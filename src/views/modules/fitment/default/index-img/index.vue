<template>
  <div class="page-index-img">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"

        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="status"
            :label="$t('product.status')+':'"
          >
            <el-select
              v-model="searchForm.status"
              clearable
              :placeholder="$t('product.status')"
            >
              <el-option
                v-for="item in status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="imgType"
            :label="$t('platform.platform')+':'"
          >
            <el-select
              v-model="searchForm.imgType"
              clearable
              :placeholder="$t('platform.platform')"
            >
              <el-option
                v-for="item in imgTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetForm()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="operation-bar">
        <el-checkbox
          v-if="isAuth('admin:indexImg:delete')"
          v-model="selectAll"
          class="all-check-btn"
          :disabled="dataList.length===0"
          @change="onSelectAll"
        >
          {{ $t('publics.selectAll') }}
        </el-checkbox>
        <span
          v-if="dataListSelections.length"
          class="had-selected"
        >{{ $t('publics.selected') }} {{ dataListSelections.length }}</span>
        <div
          v-if="isAuth('admin:indexImg:save')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate(0)"
        >
          {{ $t('crud.addBtn') }}
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('publics.maxIndexImgNumOfPlatform')"
            placement="top"
          >
            <el-icon><WarningFilled /></el-icon>
          </el-tooltip>
        </div>
        <div
          v-if="isAuth('admin:indexImg:delete')"
          :class="[!dataListSelections.length ? 'disabled-btn':'','default-btn']"
          @click="onDelete()"
        >
          {{ $t('sys.batchDelete') }}
        </div>
      </div>
      <div class="table-con">
        <el-table
          ref="imgTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          :row-style="{ height:'120px'}"
          style="width: 100%"
          @selection-change="selectionChange"
          @select-all="clearAllSelection"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            :label="$t('temp.rotaImg')"
            align="center"
            prop="imgUrl"
            width="350"
          >
            <template #default="scope">
              <div class="table-cell-image banner-img">
                <ImgShow :src="scope.row.imgUrl" />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="seq"
            :label="$t('temp.sequence')"
          />
          <el-table-column
            align="left"
            :label="$t('publics.status')"
          >
            <template #default="scope">
              <span>{{ [$t('publics.disable'),$t('publics.normal')][scope.row.status] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="imgType"
            :label="$t('platform.platform')"
          >
            <template #default="scope">
              <span>{{ [$t('platform.mobile'),$t('platform.pc')][scope.row.imgType] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="220"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('admin:indexImg:update')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.imgId)"
                >
                  {{ $t('text.updateBtn') }}
                </div>
                <div
                  v-if="isAuth('admin:indexImg:delete')"
                  class="default-btn text-btn"
                  @click="onDelete(scope.row.imgId)"
                >
                  {{ $t('text.delBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="onRefreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import AddOrUpdate from './add-or-update.vue'
import { isAuth } from '@/utils/index.js'

const imgTypes = [
  {
    label: $t('platform.mobile'),
    value: 0
  }, {
    label: $t('platform.pc'),
    value: 1
  }
]
const status = [
  {
    label: $t('publics.disable'),
    value: 0
  }, {
    label: $t('publics.normal'),
    value: 1
  }
]

onMounted(() => {
  getDataList(page)
})

// 清空全选框选中状态
const dataList = ref([])
const clearAllSelection = () => {
  if (!dataList.value.length) {
    imgTableRef.value?.clearSelection()
  }
}
// 获取数据列表
let tempSearchForm = null // 保存上次点击查询的请求条件
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  status: null,
  imgType: null
})
const getDataList = (pageParam, newData = false) => {
  if (page) {
    const size = Math.ceil(page.total / page.pageSize)
    page.currentPage = (page.currentPage > size ? size : page.currentPage) || 1
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/admin/indexImg/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}
/**
 * 全选按钮
 */
const imgTableRef = ref(null)
const selectAll = ref(false)
const onSelectAll = () => {
  if (imgTableRef.value.selection?.length < dataList.value?.length) {
    selectAll.value = true
  } else {
    selectAll.value = false
  }
  imgTableRef.value?.toggleAllSelection()
}
// 新增 / 修改
const addOrUpdateRef = ref(null)
const addOrUpdateVisible = ref(false)
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id)
  })
}
// 删除
const dataListSelections = ref([])
const onDelete = (id) => {
  const ids = id ? [id] : dataListSelections.value.map(item => {
    return item.imgId
  })
  if (!ids.length) {
    return
  }
  ElMessageBox.confirm(`${$t('sys.makeSure')}[${id ? $t('text.delBtn') : $t('sys.batchDelete')}]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/admin/indexImg'),
      method: 'delete',
      data: http.adornData(ids, false)
    }).then(() => {
      page.total = page.total - ids.length
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          onRefreshChange()
        }
      })
    })
  })
}
// 条件查询
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}
// 刷新回调
const onRefreshChange = () => {
  getDataList(page)
}
// 多选变化
const selectionChange = (val) => {
  dataListSelections.value = val
  selectAll.value = val.length === dataList.value.length
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}

const searchFormRef = ref(null)
const onResetForm = () => {
  searchFormRef.value.resetFields()
}

</script>

<style lang="scss" scoped>
.page-index-img{
  .banner-img {
    width: auto;
    height: 94px;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
  }
}

</style>
