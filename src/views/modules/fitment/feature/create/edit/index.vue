<template>
  <div class="micro-create">
    <decorate-navbar
      v-show="currentOperationType!='detail'"
      ref="decorateNavbarRef"
      :is-template="isTemplate"
      :data-form-l="dataForm"
      @handle-save="save"
      @view="view"
      @handle-save-template="handleSaveTemplate"
      @handle-click-component="handleClickComponent"
    />

    <div
      class="micro-box-content"
      :style="{backgroundColor:setOther.bgColor}"
    >
      <div class="design-page">
        <div class="design-page-content">
          <!-- 左侧所有装修组件 -->
          <div
            v-show="currentOperationType!='detail'"
            class="all-use-components"
            :class="{'border-left': !sidebarFold}"
          >
            <!--所有可以选择的组件-->
            <all-can-use-components
              :is-show-all-components="isShowAllComponents"
              :hide-these-components="hideTheseComponents"
              :show-these-components="showTheseComponents"
              @add-component="addComponent"
              @ret-all-can-use-components="getUseComponentList"
            />
            <!--end 所有可以选择的组件-->
          </div>
          <!-- 界面预览 -->
          <div
            id="scrollbar"
            class="design-editor"
            :class="currentOperationType === 'detail' ? 'view-class' : ''"
          >
            <div
              ref="designBoxRef"
              class="design-box"
            >
              <div
                ref="designPreviewRef"
                class="design-preview"
                :style="{margin: currentOperationType!='detail' ? '30px 350px 0 0' : '0'}"
              >
                <div
                  v-if="componentLists.length"
                  ref="PreviewRef"
                  class="item-list"
                >
                  <!--头部-->
                  <div
                    v-for="(item,index) in configComponents"
                    id="headBox"
                    :key="index"
                    class="preview-item"
                    @click.stop="clickComponent(index,1)"
                  >
                    <component
                      :is="componentLists.find(x=>x.type === item.type) ? componentLists.find(x=>x.type === item.type).routerPath : ''"
                      :current="index"
                      :is-show-edit="currentEditConfigComponentsShow === index && configComponentsShow && currentOperationType!='detail'"
                      :index-key="item.type+'_'+index"
                      :data-field="item.dataField || {}"
                      :current-component="item"
                      :is-check-my-self="item.isCheckMySelf"
                      :is-start-check-field-rules="isStartCheckFieldRules"
                      @on-error-message-tip="onErrorMessageTip"
                      @my-check-result="everyComponentsCheckResultFun"
                      @components-value-chance="componentsValueChanceFromConfig"
                      @save="save"
                    />
                  </div>

                  <!-- 商家招牌 start -->
                  <template v-if="HeaderComponent.length > 0">
                    <div
                      v-for="(item, index) in HeaderComponent"
                      :key="item.type"
                      @click.stop="clickHeaderComponent(item)"
                    >
                      <div class="preview-item">
                        <component
                          :is="componentLists.find(x=>x.type === item.type) ? componentLists.find(x=>x.type === item.type).routerPath : ''"
                          :current="index"
                          :is-show-edit="headerEdit &&isShowEdit && currentOperationType!='detail'"
                          :current-use-components="HeaderComponent"
                          :index-key="item.type+'_'+index"
                          :data-field="item.dataField || {}"
                          :current-component="item"
                          :is-check-my-self="item.isCheckMySelf"
                          :is-start-check-field-rules="isStartCheckFieldRules"
                          @top-fixed="topFixed"
                          @on-error-message-tip="onErrorMessageTip"
                          @my-check-result="everyComponentsCheckResultFun"
                          @components-value-chance="HeaderComponentValueChance"
                          @save="save"
                        />
                        <!--边框、删除、添加-->
                        <div
                          v-if="currentOperationType!='detail'"
                          :style="{borderColor:setOther.viewTheme,borderStyle:'solid'}"
                          class="preview-item-border design-hide-class"
                          :class="headerEdit ? 'show':''"
                        >
                          <div
                            v-handle
                            class="drag-box"
                            :style="{cursor:setOther.dragCursor}"
                          />
                        </div>
                        <!--边框、删除、添加-->
                      </div>
                    </div>
                  </template>
                  <!-- 商家招牌 end -->

                  <!--组件-->
                  <slick-list
                    v-model:list="currentUseComponents"
                    :use-drag-handle="true"
                    lock-axis="y"
                    :press-delay="500"
                    :transition-duration="500"
                    class="slickList-scroll"
                    @update:list="sortInput"
                    @sort-start="sortStart"
                    @sort-move="sortMove"
                    @sort-end="sortEnd"
                  >
                    <slick-item
                      v-for="(item,index) in currentUseComponents"
                      :id="'previewItem_'+index"
                      :key="item.id"
                      :class="['preview-box',{isMoveIng:isMoveIng}]"
                      :index="index"
                    >
                      <div
                        class="preview-item"
                        @click.stop="clickComponent(index)"
                      >
                        <component
                          :is="componentLists.find(x=>x.type === item.type) ? componentLists.find(x=>x.type === item.type).routerPath : ''"
                          :current="index"
                          :is-show-edit="currentEditComponent === index && isShowEdit && currentOperationType!='detail'"
                          :current-use-components="currentUseComponents"
                          :index-key="item.type+'_'+index"
                          :data-field="item.dataField || {}"
                          :current-component="item"
                          :is-check-my-self="item.isCheckMySelf"
                          :is-start-check-field-rules="isStartCheckFieldRules"
                          @top-fixed="topFixed"
                          @on-error-message-tip="onErrorMessageTip"
                          @my-check-result="everyComponentsCheckResultFun"
                          @set-current-use-components="setCurrentUseComponents"
                          @components-value-chance="componentsValueChance"
                          @save="save"
                        />
                        <!--边框、删除、添加-->
                        <div
                          v-if="currentOperationType!='detail'"
                          :style="{borderColor:setOther.viewTheme,borderStyle:'solid'}"
                          class="preview-item-border design-hide-class"
                          :class="currentEditComponent === index ? 'show':''"
                        >
                          <div
                            v-handle
                            class="drag-box"
                            :style="{cursor:setOther.dragCursor}"
                          />
                        </div>
                        <!--边框、删除、添加-->
                      </div>
                    </slick-item>

                    <!-- 商品瀑布 start -->
                    <template v-if="goodsWaterfallComponent.length > 0">
                      <div
                        v-for="(item, index) in goodsWaterfallComponent"
                        :key="item.type"
                        class="preview-box"
                        @click.stop="clickgoodsWaterfallComponent(item)"
                      >
                        <div class="preview-item">
                          <component
                            :is="componentLists.find(x=>x.type === item.type) ? componentLists.find(x=>x.type === item.type).routerPath : ''"
                            :current="index"
                            :is-show-edit="goodsWEdit &&isShowEdit"
                            :current-use-components="goodsWaterfallComponent"
                            :index-key="item.type+'_'+index"
                            :data-field="item.dataField || {}"
                            :current-component="item"
                            :is-check-my-self="item.isCheckMySelf"
                            :is-start-check-field-rules="isStartCheckFieldRules"
                            @top-fixed="topFixed"
                            @components-value-chance="goodsWaterfallComponentValueChance"
                            @save="save"
                          />
                          <!--边框、删除、添加-->
                          <div
                            v-if="currentOperationType!='detail'"
                            :style="{borderColor:setOther.viewTheme,borderStyle:'solid'}"
                            class="preview-item-border design-hide-class"
                            :class="goodsWEdit ? 'show':''"
                          >
                            <div
                              v-handle
                              class="drag-box"
                              :style="{cursor:setOther.dragCursor}"
                            />
                          </div>
                          <!--边框、删除、添加-->
                        </div>
                      </div>
                    </template>
                    <!-- 商品瀑布 end -->
                  </slick-list>
                  <!--插入-->
                  <div
                    v-if="isShowEditorAllComponents"
                    class="design-editor-item append-component-box"
                  >
                    <div class="design-config-editor">
                      <all-can-use-components
                        :hide-these-components="hideTheseComponents"
                        @add-component="addComponent"
                        @ret-all-can-use-components="getUseComponentList"
                      />
                    </div>
                  </div>
                  <!--插入-->
                </div>
              </div>
            </div>
            <!--右侧管理列表-->
            <div
              v-if="currentOperationType!='detail'"
              class="preview-wrap"
            >
              <div
                class="preview-wrap-item"
                :style="setOther.componentOperation ? 'background-color:'+setOther.viewTheme+';color:#fff;' : ''"
                @click="setOther.componentOperation = true"
              >
                <el-icon>
                  <Coin />
                </el-icon>
                {{ $t('shopFeature.edit.componentManagement') }}
              </div>
            </div>
          </div>
          <!-- 组件管理 -->
          <div
            v-if="setOther.componentOperation"
            class="design-editor-item"
          >
            <div class="design-config-editor">
              <div class="design-editor-component-title">
                {{ $t('shopFeature.edit.componentManagement') }}
              </div>
              <div class="components-list-edit">
                <p
                  class="clear-current-component"
                  :style="[{color:setOther.viewTheme}]"
                >
                  <span>{{ $t('shopFeature.edit.componentSorting') }}</span>
                  <span
                    @click="HeaderComponent = []; currentUseComponents = []; goodsWaterfallComponent = []"
                  >{{ $t('shopFeature.edit.removeAll') }}</span>
                </p>

                <!-- 商家招牌 start -->
                <template v-if="HeaderComponent.length > 0">
                  <div
                    v-for="(item, index) in HeaderComponent"
                    :key="item.type"
                    class="components-list-edit-item"
                  >
                    <div
                      v-handle
                      class="drag-box"
                    >
                      {{ componentLists.find(x=>x.type === item.type) ?
                        componentLists.find(x=>x.type === item.type).title : '' }}
                    </div>
                    <div class="drag-box-icon">
                      <el-icon
                        :title="$t('shopFeature.edit.removeComponent')"
                        class="el-icon-delete"
                        @click="popBtn(0, item,'pop_'+index,index)"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                </template>
                <!-- 商家招牌 end -->

                <slick-list
                  v-model:list="currentUseComponents"
                  :use-drag-handle="true"
                  axis="y"
                  helper-class="drag-class"
                  :transition-duration="500"
                  @update:list="componentsSortInput"
                >
                  <slick-item
                    v-for="(item,index) in currentUseComponents"
                    :key="index"
                    class="components-list-edit-item"
                    :index="index"
                  >
                    <div
                      v-handle
                      class="drag-box"
                    >
                      {{ componentLists.find(x=>x.type === item.type) ?
                        componentLists.find(x=>x.type === item.type).title : '' }}
                      <span class="drag-box-remark">{{ item.customRemark }}</span>
                    </div>
                    <div class="drag-box-icon">
                      <custom-remark-edit-popover
                        :index="index"
                        :current-edit-component="currentEditComponent"
                        @set-current-component="setCurrentComponent"
                        @save-edit="saveCustomRemark"
                      />
                      <el-icon
                        :size="18"
                        :title="$t('shopFeature.edit.removeComponent')"
                        @click="popBtn(2, item,'pop_'+index,index)"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </slick-item>
                </slick-list>

                <!-- 商品瀑布 start -->
                <template v-if="goodsWaterfallComponent.length > 0">
                  <div
                    v-for="(item, index) in goodsWaterfallComponent"
                    :key="item.type"
                    class="components-list-edit-item"
                  >
                    <div
                      v-handle
                      class="drag-box"
                    >
                      {{ componentLists.find(x=>x.type === item.type) ?
                        componentLists.find(x=>x.type === item.type).title : '' }}
                    </div>
                    <div class="drag-box-icon">
                      <el-icon
                        :title="$t('shopFeature.edit.removeComponent')"
                        class="el-icon-delete"
                        @click="popBtn(1, item,'pop_'+index,index)"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                </template>
                <!-- 商品瀑布 end -->

                <div
                  v-if="currentUseComponents.length === 0"
                  class="empty-template"
                >
                  {{ $t('shopFeature.edit.emptyTemplate') }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { SlickList, SlickItem, HandleDirective } from 'vue-slicksort'
import allCanUseComponents from './components/all-can-use-components/index.vue'
import { ElMessage } from 'element-plus'
// 顶边栏
import decorateNavbar from './components/decorate-navbar/index.vue'
import customRemarkEditPopover from '../../../components/custom-remark-edit-poppver/index.vue'
import { Debounce } from '@/utils/debounce.js'

const vHandle = HandleDirective

const props = defineProps({
  isShowPushAdd: { // 是否显示插入的模式
    type: Boolean,
    default: true
  },
  isShowClose: { // 是否关闭的模式
    type: Boolean,
    default: true
  },
  hideTheseComponents: { // 隐藏这些组件 结构如：[{type: 'config',isHide:true}]
    type: Array,
    default: () => []
  },
  showTheseComponents: { // 显示这些组件 结构如：[{type: 'config',isHide:true}]
    type: Array,
    default: () => []
  },
  isShowAllComponents: { // 是否隐藏所有组件
    type: Boolean,
    default: true
  },
  saveBtnTitle1: { // 按钮1的文本
    type: String,
    default: $t('shopFeature.edit.save')
  },
  saveBtnTitle2: { // 按钮2的文本
    type: String,
    default: $t('shopFeature.edit.saveAndCon')
  },
  saveBtnTitle3: { // 按钮3的文本
    type: String,
    default: $t('shopFeature.edit.moreOper')
  },
  isShowSaveBtn1: { // 是否显示 上架 按扭
    type: Boolean,
    default: true
  },
  isShowSaveBtn2: { // 是否显示 保存并继续 按扭
    type: Boolean,
    default: true
  },
  isShowSaveBtn3: { // 是否显示 更多操作 按扭
    type: Boolean,
    default: true
  },
  // 是否需要返回最后保存的值，在当前页面被用作组件的时候可使用
  isNeedReturnSaveData: {
    type: Boolean,
    default: false
  },
  /** 配置顶部项的组件 */
  currentConfigComponents: {
    type: Array,
    default: () => [{ type: 'config' }] // 配置项不能删除、排序
  },
  /** 其他的组件传过来只能使用的组件 */
  currentCanUseComponents: {
    type: Array,
    default: () => [] //
  },
  microId: { // 主要运用于其他的调用微页面组件获取详情使用，具体可参考：公告广告
    type: Number,
    default: null
  },
  microKeyWord: { // 微页面 主要运用于其他的调用微页面组件获取详情使用，具体可参考：微集合
    type: String,
    default: ''
  }
})

const emit = defineEmits(['currentUseComponentsChange', 'returnData'])

const route = useRoute()
const router = useRouter()

const commonStore = useCommonStore()
const sidebarFold = computed(() => {
  return commonStore.sidebarFold
})

const id = ref(null)
watch(() => props.microId, (val) => {
  if (val) {
    id.value = val
    getInfo()
  }
})

const currentUseComponents = ref([])
watch(() => props.microKeyWord, (val) => {
  if (val) {
    id.value = null
    currentUseComponents.value = []
    getInfo()
  }
})

watch(() => currentUseComponents.value, (val) => {
  nextTick(() => {
    emit('currentUseComponentsChange', val)
  })
})

const isTemplate = ref('0') // 是否为模板编辑
let renovationId = route.query.renovationId || null
let templateId = route.query.templateId || null
let pageType = ''
/** 配置顶部项的组件 */
const configComponents = ref([{ type: 'config' }])
onMounted(() => {
  isTemplate.value = route.query.template
  const renovationIdPar = route.query.renovationId
  const templateIdPar = route.query.templateId
  pageType = route.query.type
  handleInit()
  if (pageType === 'edit') {
    renovationId = renovationIdPar
    templateId = templateIdPar
    if (isTemplate.value === '0') {
      getInfo()
    } else {
      getTemplateDetail()
    }
    localStorage.removeItem('bbcViewContent')
  } else if (pageType === 'add' && templateIdPar) {
    templateId = templateIdPar
    getTemplateDetail()
    localStorage.removeItem('bbcViewContent')
  } else {
    configComponents.value = JSON.parse(JSON.stringify(props.currentConfigComponents))
  }
  if (pageType === 'detail' && renovationIdPar) {
    getInfo()
  } else if (pageType === 'detail' && localStorage.getItem('bbcViewContent')) {
    viewContent(JSON.parse(localStorage.getItem('bbcViewContent')))
  }

  nextTick(() => {
    commonStore.updateSidebarFold(true)
  })
})

onActivated(() => {
  if (route.query.renovationId && route.query.renovationId !== renovationId) {
    renovationId = route.query.renovationId
    getInfo()
  }
})

onDeactivated(() => {
  configComponents.value = []
  configComponents.value = JSON.parse(JSON.stringify(props.currentConfigComponents))
})

const currentOperationType = ref('') // 当前操作类型
const handleInit = () => {
  if (route.query.type === 'detail') {
    currentOperationType.value = 'detail'
  }
}

let allCheckResult = []
const isStartCheckFieldRules = ref(false) // 是否开始校验所有组件的规则
let isView = false // 是否预览
// 预览
const view = () => {
  allCheckResult = []
  isStartCheckFieldRules.value = !isStartCheckFieldRules.value// 开始校验
  isView = true
}

const HeaderComponent = ref([]) // 头部组件
const goodsWaterfallComponent = ref([]) // 底部瀑布组件
const toView = () => {
  const $data = configComponents.value.concat(HeaderComponent.value, currentUseComponents.value, goodsWaterfallComponent.value)
  const par = {
    content: JSON.stringify($data),
    name: $data[0].dataField.title || $t('shopFeature.header.microTitle')
  }
  localStorage.setItem('bbcViewContent', JSON.stringify(par))
  const newPage = router.resolve({
    path: '/fitment/feature/create/edit/index',
    query: {
      type: 'detail'
    }
  })
  isView = false
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

const isShowEditorAllComponents = ref(false) // 是否显示插入组件区域，永远与isShowEdit相反
const setOther = reactive({ // 设置
  viewTheme: '#155BD4',
  bgColor: '#fff', // 背景
  dragCursor: 'all-scroll', // 显示的图标 all-scroll
  componentOperation: false// 是否操作组件
})
// 组件管理
const handleClickComponent = () => {
  setOther.componentOperation = !setOther.componentOperation
  isShowEditorAllComponents.value = false
}

/** 关闭组件管理 */
const componentOperation = () => {
  setOther.componentOperation = false
}

const componentLists = ref([]) // 所有可用的组件
/**
 * 获取所有可用的组件
 * */
const getUseComponentList = ($list) => {
  if ($list) {
    componentLists.value = $list
  } else {
    ElMessage.success($t('shopFeature.edit.noneUseableCom'))
  }
}

let scrollTopHeight = 0
/** 正在编辑的组件 */
const currentEditComponent = ref(0)
const goodsWEdit = ref(false) // 是否修改瀑布组件
const headerEdit = ref(false) // 是否修改头部组件
const appendPosition = {
  index: '',
  position: 'bottom', // 默认底部
  theRefs: null
} // 插入组件的位置
/**
 *  添加组件
 *  */
const addComponent = (item) => {
  // 判断当前配置的组件是否已经有头部组件
  if (item.type === 'storeSignate' && HeaderComponent.value.length === 1) {
    return ElMessage.warning($t('shopFeature.businessSigns.tip2'))
  }
  const HeaderComponentPar = HeaderComponent.value
  if (item.type === 'goodsWaterfall' && goodsWaterfallComponent.value.length === 1) {
    return ElMessage.warning($t('shopFeature.goodsWaterfall.tip2'))
  }
  const goodsWaterfallComponentPar = goodsWaterfallComponent.value
  const scrollBox = document.getElementById('scrollbar')
  scrollTopHeight = scrollBox.scrollTop
  const allCurrentUseComponents = currentUseComponents.value
  const $par = {
    type: item.type,
    id: Math.floor(Math.random() * ***********)
  }
  const $appendPositionIndex = appendPosition.index
  if (item.type != 'goodsWaterfall' && item.type != 'storeSignate') {
    if ($appendPositionIndex !== '') { // 插入模式
      const $in = appendPosition.position === 'top' ? $appendPositionIndex : $appendPositionIndex + 1
      allCurrentUseComponents.splice($in, 0, item)
      currentEditComponent.value = $in
    } else {
      allCurrentUseComponents.push($par)
      currentEditComponent.value = allCurrentUseComponents.length - 1
    }
    goodsWEdit.value = false
    headerEdit.value = false
  } else if (item.type == 'storeSignate') {
    HeaderComponentPar.push($par)
    headerEdit.value = true
  } else {
    currentEditComponent.value = -1
    goodsWEdit.value = true
    goodsWaterfallComponentPar.push($par)
  }
  setTimeout(() => {
    // 重置插入位置为空
    currentUseComponents.value = allCurrentUseComponents
    HeaderComponent.value = HeaderComponentPar // 头部组件
    goodsWaterfallComponent.value = goodsWaterfallComponentPar // 瀑布组件
    appendPosition.index = ''
    reSetIsShowEdit()
    scrollBox.scrollTop = scrollTopHeight
  }, 5)
}

const configComponentsShow = ref(true)
const isShowEdit = ref(false) // 是否显示组件的编辑区
/**
 * 重置isShowEdit
 * */
const reSetIsShowEdit = ($is = true, isReSetOperation = true) => {
  configComponentsShow.value = false
  isShowEdit.value = $is
  isShowEditorAllComponents.value = !isShowEdit.value
  if (isReSetOperation) {
    componentOperation()
  }
}

/** 正在编辑的组件 */
const currentEditConfigComponentsShow = ref(0)
/**
 * 点击某个组件
 * $isConfigComponents =  1 时为顶部
 * */
// eslint-disable-next-line no-unused-vars
const clickComponent = ($index, $isConfigComponents = 0) => {
  goodsWEdit.value = false
  if ($isConfigComponents === 1) {
    currentEditConfigComponentsShow.value = $index
    configComponentsShow.value = true
    isShowEdit.value = false
    isShowEditorAllComponents.value = false
  } else {
    headerEdit.value = false
    currentEditComponent.value = $index
    reSetIsShowEdit()
  }
  componentOperation()
}

// 点击商家招牌组件
const clickHeaderComponent = () => {
  currentEditComponent.value = -2
  headerEdit.value = true
  goodsWEdit.value = false
  reSetIsShowEdit()
  componentOperation()
}

// 点击商品瀑布组件
const clickgoodsWaterfallComponent = () => {
  currentEditComponent.value = -1
  goodsWEdit.value = true
  headerEdit.value = false
  reSetIsShowEdit()
  componentOperation()
}

/**
 * 移除某个组件
 * */
// eslint-disable-next-line no-unused-vars
const popBtn = ($type, item, ref, index) => {
  if ($type === 0) {
    HeaderComponent.value = []
  }
  if ($type === 1) {
    goodsWaterfallComponent.value = []
  }
  if ($type === 2) { // 确定
    const $currentUseComponents = currentUseComponents.value
    $currentUseComponents.splice(index, 1)
    setTimeout(() => {
      currentUseComponents.value = $currentUseComponents
    }, 5)
  }
  isShowEditorAllComponents.value = false
}

const setCurrentComponent = (index) => {
  currentEditComponent.value = currentUseComponents.value[index]
}
const saveCustomRemark = (remark) => {
  currentEditComponent.value.customRemark = remark
}

// 监听某个组件的变化
const componentsValueChanceFromConfig = ($val) => {
  if ($val) {
    configComponents.value[$val.current].dataField = $val.dataField
  }
}

const componentsValueChance = ($val) => {
  if ($val) {
    currentUseComponents.value[$val.current].dataField = $val.dataField
  }
}

const HeaderComponentValueChance = ($val) => {
  if ($val) {
    HeaderComponent.value[$val.current].dataField = $val.dataField
  }
}

const goodsWaterfallComponentValueChance = ($val) => {
  if ($val) {
    goodsWaterfallComponent.value[$val.current].dataField = $val.dataField
  }
}

const isMoveIng = ref(false) // 是否正在移动
let elementArr = []
// 拖拽
const sortStart = () => {
  // 开始拖拽时隐藏编辑区
  elementArr = document.getElementsByClassName('design-hide-class')
  for (let i = 0; i < elementArr.length; i++) {
    // 隐藏样式
    elementArr[i].style.visibility = 'hidden'
  }
  setOther.dragCursor = 'all-scroll'
  isMoveIng.value = true
}

// 拖动过程，改变鼠标样式
const sortMove = () => {}

// 结束拖拽
const sortEnd = (val) => {
  // 结束拖拽时显示编辑区
  for (let i = 0; i < elementArr.length; i++) {
    // 显示样式
    elementArr[i].style.visibility = 'visible'
  }
  headerEdit.value = false
  currentEditComponent.value = val.newIndex
  isMoveIng.value = false
}

let isDrag = false // 是否拖动了位置
// 拖拽之后的结果
const sortInput = (val, isReSet = true) => {
  if (isDrag) {
    for (let i = 0; i < val.length; i++) {
      if (val[i].type === 'storeSignate') {
        if (val[0].type != 'storeSignate') {
          ElMessage.warning($t('shopFeature.businessSigns.tip1'))
        }
      }
      if (val[i].type === 'goodsWaterfall') {
        if (val[val.length - 1].type != 'goodsWaterfall') {
          ElMessage.warning($t('shopFeature.goodsWaterfall.tip1'))
        }
      }
    }
    // 始终保证招牌在顶部
    const arr = []
    const HeaderComponentPar = []
    const goodsWaterfallComponentPar = []
    val.forEach(item => {
      if (item.type === 'shop_signs') {
        HeaderComponentPar.push(item)
      } else if (item.type === 'goodsWaterfall') {
        goodsWaterfallComponentPar.push(item)
      } else {
        arr.push(item)
      }
    })
    setTimeout(() => {
      currentUseComponents.value = arr
    }, 5)
    appendPosition.index = ''
  }
  isDrag = false
  configComponentsShow.value = false
  reSetIsShowEdit(true, isReSet)
}

/** 右边组件的拖拽 */
const componentsSortInput = (val) => {
  isDrag = true
  sortInput(val, false)
}

let savePageType = '0' // 保存类型
// 所有每次校验的结果
let saveType = 2 // 保存的类型=> 2保存 1上架 3更多操作
let dontShowMessage = false
/**
 * 保存事件 先校验
 * */
const save = ($type, dontShowMessagePar) => {
  savePageType = '0'
  saveType = $type
  dontShowMessage = dontShowMessagePar
  allCheckResult = []
  isStartCheckFieldRules.value = !isStartCheckFieldRules.value// 开始校验
}

const dataForm = ref({
  name: '',
  remark: '',
  imgUrl: ''
}) // 模板信息
/**
 * 保存模板 先校验
 * */
const handleSaveTemplate = (dataFormPar) => {
  savePageType = '1'
  dataForm.value = dataFormPar
  allCheckResult = []
  isStartCheckFieldRules.value = !isStartCheckFieldRules.value// 开始校验
}

let errorMessageTipList = []
/**
 * 获取所有组件的错误提示，集中处理
 * 利用防抖函数，每次只展示一条报错信息
 */
const onErrorMessageTip = Debounce((errorTip) => {
  errorMessageTipList.push(errorTip)
  if (errorMessageTipList.length > 0) {
    for (let i = 0; i < 1; i++) {
      const obj = errorMessageTipList[0]
      const tip = obj.rightConfigTitle + (obj.customRemark ? (' ' + obj.customRemark) : '') + '：' + obj.errorMessage
      ElMessage.error(tip)
    }
    errorMessageTipList = []
  }
}, 1000)

/** 所有的正在编辑的校验结果 */
const everyComponentsCheckResultFun = (ev) => {
  allCheckResult.push(ev.data)
  /** 统计当前所有正在使用组件的数量 */
  const currentUserAllComponentsList = currentUseComponents.value.length + configComponents.value.length
  if (allCheckResult.length === currentUserAllComponentsList) {
    const $allCheckResult = allCheckResult
    for (let i = 0; i < $allCheckResult.length; i++) {
      const cur = $allCheckResult[i]
      if (!cur.isPass) {
        let $type = 0
        if (cur.isHeader) { // 头部的验证规则不通过
          configComponents.value[cur.current].isCheckMySelf = !configComponents.value[cur.current].isCheckMySelf
          $type = 1
        } else { // 底部的不通过
          currentUseComponents.value[cur.current].isCheckMySelf = !currentUseComponents.value[cur.current].isCheckMySelf
        }
        clickComponent(cur.current, $type)
        isStartCheckFieldRules.value = false
        break
      }
      if (i === $allCheckResult.length - 1) { // 验证完成，可以保存了
        if (isView) {
          toView()
        } else {
          savePush()
        }
      }
    }
  }
}

// 验证完毕之后发布push
const savePush = () => {
  const $parameter = configComponents.value.concat(HeaderComponent.value, currentUseComponents.value, goodsWaterfallComponent.value)
  $parameter.forEach(re => { // 移除isCheckMySelf字段，不能上传数据库
    delete re.isCheckMySelf
  })
  if (props.isNeedReturnSaveData) { // 返回最后保存的值  当前微页面被使用成组件时
    emit('returnData',
      {
        typeDecs: saveType === 1 ? props.saveBtnTitle1 : saveType === 2 ? props.saveBtnTitle2 : props.saveBtnTitle3,
        type: saveType, // 按钮类型
        data: $parameter
      }
    )
    return
  }
  if (savePageType === '0') {
    saveData($parameter, saveType)
  } else {
    saveTemplateData($parameter, saveType)
  }
}

/** 保存到数据库 */
const saveData = async ($data) => {
  const par = {
    content: JSON.stringify($data),
    name: $data[0].dataField.title || $t('shopFeature.header.microTitle')
  }
  if (renovationId) {
    par.renovationId = renovationId
  }
  let url
  let method
  if (renovationId) {
    url = http.adornUrl('/shop/shopRenovation/updateMove')
    method = 'put'
  } else {
    url = http.adornUrl('/shop/shopRenovation/saveMove')
    method = 'post'
  }
  http({
    url,
    method,
    data: http.adornData(par)
  }).then(({ data }) => {
    if (!renovationId) {
      renovationId = data
      router.replace({
        path: '/fitment/feature/create/edit/index',
        query: {
          renovationId,
          type: 'edit',
          template: '0'
        }
      })
    }
    if (!dontShowMessage) {
      ElMessage({
        message: $t('shopFeature.edit.saveSuccess'),
        type: 'success',
        duration: 1500,
        onClose: () => {}
      })
    } else {
      dontShowMessage = false
    }
  })
}

/** 保存模板到数据库 */
const saveTemplateData = async ($data) => {
  const par = {
    content: JSON.stringify($data),
    remark: dataForm.value.remark,
    image: dataForm.value.imgUrl,
    name: dataForm.value.name, // 模板名称
    type: 2 // 装修类型
  }
  if (templateId) {
    par.templateId = templateId
  }
  let url
  let method
  if (templateId) {
    url = http.adornUrl('/shop/shopTemplate/updateMove')
    method = 'put'
  } else {
    url = http.adornUrl('/shop/shopTemplate/saveMove')
    method = 'post'
  }
  http({
    url,
    method,
    data: http.adornData(par)
  }).then(({ data }) => {
    if (!templateId) {
      templateId = data
    }
    if (!dontShowMessage) {
      ElMessage({
        message: $t('shopFeature.edit.saveSuccess'),
        type: 'success',
        duration: 1500
      })
    } else {
      dontShowMessage = false
    }
  })
}

const viewContent = async (data) => {
  configComponents.value = []
  let detail = {
    keyword: 'other',
    pageData: data.content
  }
  if (props.microKeyWord === 'productCatalog') {
    detail = {
      keyword: 'productCatalog',
      pageData: data.content
    }
  }
  let $data = JSON.parse(detail.pageData)
  $data = await matchproducts($data)
  if ($data.length) {
    configComponents.value = []
    $data.forEach((re, index) => {
      re.customRemark = re.customRemark || index
      if (re.type.indexOf('config') !== -1) {
        configComponents.value.push(re)
      } else {
        currentUseComponents.value.push(re)
      }
    })
  }
}

/**
 * 获取详情
 */
const getInfo = () => {
  http({
    url: http.adornUrl('/shop/shopRenovation/info/' + renovationId),
    method: 'GET'
  }).then(async ({ data }) => {
    configComponents.value = []
    const par = {}
    if (props.microKeyWord) {
      par.keyword = props.microKeyWord
    }
    if (renovationId) {
      par.renovationId = renovationId
    }
    let detail = {
      keyword: 'other',
      pageData: data.content
    }
    if (props.microKeyWord === 'productCatalog') {
      detail = {
        keyword: 'productCatalog',
        pageData: data.content
      }
    }
    let $data = JSON.parse(detail.pageData)
    $data = await matchproducts($data)
    if ($data.length) {
      configComponents.value = []
      $data.forEach(re => {
        re.id = Math.floor(Math.random() * ***********)
        if (re.type.indexOf('config') !== -1) {
          configComponents.value.push(re)
        } else if (re.type.indexOf('storeSignate') !== -1) {
          HeaderComponent.value.push(re)
        } else if (re.type.indexOf('goodsWaterfall') !== -1) {
          goodsWaterfallComponent.value.push(re)
        } else {
          currentUseComponents.value.push(re)
        }
      })
    }
  }).catch(() => {})
}

// 获取模板详情
const getTemplateDetail = () => {
  http({
    url: http.adornUrl('/shop/shopTemplate/info/' + templateId),
    method: 'GET'
  }).then(async ({ data }) => {
    configComponents.value = []
    if (pageType === 'edit') {
      dataForm.value = {
        name: data.name || '',
        remark: data.remark || '',
        imgUrl: data.image || ''
      }
    }
    if (pageType === 'add' && isTemplate.value === '0') {
      templateId = null
    }
    const par = {}
    if (props.microKeyWord) {
      par.keyword = props.microKeyWord
    }
    if (templateId) {
      par.templateId = templateId
    }
    let detail = {
      keyword: 'other',
      pageData: data.content
    }
    if (props.microKeyWord === 'productCatalog') {
      detail = {
        keyword: 'productCatalog',
        pageData: data.content
      }
    }
    let $data = JSON.parse(detail.pageData)
    $data = await matchproducts($data)
    if ($data.length) {
      configComponents.value = []
      $data.forEach(re => {
        re.id = Math.floor(Math.random() * ***********)
        if (re.type.indexOf('config') !== -1) {
          configComponents.value.push(re)
        } else if (re.type.indexOf('storeSignate') !== -1) {
          HeaderComponent.value.push(re)
        } else if (re.type.indexOf('goodsWaterfall') !== -1) {
          goodsWaterfallComponent.value.push(re)
        } else {
          currentUseComponents.value.push(re)
        }
      })
    }
  }).catch(() => {})
}

// 匹配商品
const matchproducts = async (arr) => {
  const res = await searchGoodsMessage(arr)
  arr.forEach(item => {
    if (item.type === 'goods') { // 商品列表
      const goodsParams = {
        prodName: 'prodName',
        status: 'status',
        price: 'price',
        pic: 'pic',
        brief: 'brief'
      }
      item.dataField.goods = handleScreenGoods(item.dataField.goods, res, 'prodId', goodsParams)
    } else if (item.type === 'promotionalActivities') { // 促销活动
      const promoParams = {
        prodName: 'prodName',
        prodType: 'prodType',
        status: 'status',
        price: 'activityPrice',
        pic: 'pic',
        brief: 'brief',
        oriPrice: 'oriPrice',
        groupNumber: 'groupNumber',
        activityPrice: 'activityPrice'
      }
      item.dataField.prodIds = handleScreenGoods(item.dataField.prodIds, res, 'prodId', promoParams)
    } else if (item.type === 'goodsModule1') { // 商品模块1
      item.dataField.leftConfig.goodsList = handleScreenGoods(item.dataField.leftConfig.goodsList, res, 'id')
      item.dataField.rightConfig.goodsList = handleScreenGoods(item.dataField.rightConfig.goodsList, res, 'id')
    } else if (item.type === 'goodsModule3' || item.type === 'goodsModule4' || item.type === 'goodsModule5') { // 商品模块3 4 5
      item.dataField.goodsList = handleScreenGoods(item.dataField.goodsList, res, 'id')
    }
  })
  return arr
}

// 查询商品信息
const searchGoodsMessage = (arr) => {
  let goodsArr = []
  arr.forEach(item => {
    if (item.type === 'goods') { // 商品信息
      item.dataField?.goods.forEach(v => {
        goodsArr.push(v.prodId)
      })
    } else if (item.type === 'promotionalActivities') { // 促销和活动
      item.dataField.prodIds.forEach(v => {
        goodsArr.push(v.prodId)
      })
    } else if (item.type === 'goodsModule1') { // 商品模块1
      item.dataField.leftConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
      item.dataField.rightConfig.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goodsModule3') { // 商品模块3
      item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goodsModule4') { // 商品模块4
      item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    } else if (item.type === 'goodsModule5') { // 商品模块5
      item.dataField.goodsList.forEach(v => {
        goodsArr.push(v.id)
      })
    }
  })
  goodsArr = Array.from(new Set(goodsArr))
  return new Promise((resolve) => {
    http({
      url: http.adornUrl('/admin/search/prod/renovationPage'),
      method: 'get',
      params: http.adornParams({
        prodIds: goodsArr,
        current: 1,
        size: goodsArr.length,
        getDelete: true
      })
    }).then(({ data }) => {
      data.records.forEach(item => {
        if (item.groupActivitySearchVO) {
          item.groupNumber = item.groupActivitySearchVO.groupNumber
        }
      })
      resolve(data.records)
    })
  })
}

// 筛选商品回显 currentArr: 表示当前装修的商品，backReturnArr: 表示根据现有id查询后端返回的商品
const handleScreenGoods = (currentArr, backReturnArr, type, otherParams) => {
  let params
  if (otherParams) {
    params = {
      ...otherParams
    }
  } else {
    params = {
      // 商品名称
      prodType: 'prodType', // 商品类型
      status: 'status', // 商品状态
      price: 'price', // 商品价格
      imgs: 'pic', // 商品图片
      orignPrice: 'oriPrice', // 商品原价
      description: 'brief' // 商品描述
    }
  }
  const arr = []
  for (let i = 0; i < currentArr.length; i++) {
    for (let j = 0; j < backReturnArr.length; j++) {
      if (currentArr[i][type] == backReturnArr[j].prodId) {
        for (const key in params) {
          currentArr[i][key] = backReturnArr[j][params[key.toString()]]
        }
        currentArr[i].orignPrice = backReturnArr[j].oriPrice ? backReturnArr[j].oriPrice : currentArr[i].orignPrice
        currentArr[i].price = backReturnArr[j].price ? backReturnArr[j].price : currentArr[i].price
        arr.push(currentArr[i])
      }
    }
  }
  return arr
}

const setCurrentUseComponents = (current) => {
  currentUseComponents.value.unshift(currentUseComponents.value[current])
  currentUseComponents.value.splice(current + 1, 1)
}

const topFixed = () => {
  currentEditComponent.value = 0
}

</script>

<style lang="scss" scoped>
@use "edit";
</style>
