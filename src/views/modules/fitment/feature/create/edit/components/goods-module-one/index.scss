.component-goods-module-one {
  // 预览样式
  .design-preview-controller {
    width: 100%;
    padding: 12px 12px 0 12px;
    box-sizing: border-box;
    border-radius: 6px;
    overflow: hidden;
    display: flex;

    .left-items {
      padding: 0 13px;
      width: 50%;
      background: #fff;
    }

    .right-items {
      padding: 0 13px;
      width: 50%;
      background: #fff;
    }
  }

  // 编辑样式
  .config-item {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .title {
      font-size: 14px;
      color: #666;
      margin-top: 20px;
      margin-bottom: 12px;
    }

    &.special-item {
      flex-direction: row;
      align-items: center;
      margin: 20px 0;

      &:nth-child(1) {
        margin-top: 24px;
      }

      .title {
        width: 80px;
        margin: 0;
      }

      :deep(.el-slider) {
        width: calc(100% - 80px);
      }
    }

    .bottom-edit {
      width: 100%;
      display: flex;
      background: rgba(255, 255, 255, 0.39);
      border: 1px solid #dcdfe6;
      border-radius: 2px;

      .edit-item {
        width: 50%;
        height: 108px;

        &:nth-child(2) {
          border-left: 1px solid #dcdfe6;
        }

        &.active {
          border: 1px solid #155bd4;
          background: rgba(21, 91, 212, 0.04);
        }
      }
    }
  }
}

.config-item {
  .bottom-config :deep(.el-input__inner) {
    height: 28px;
  }
}
