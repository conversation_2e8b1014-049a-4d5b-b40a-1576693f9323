<template>
  <div class="micro-title-text component-store-signate">
    <div class="shop">
      <div class="shopInfo">
        <div class="sl">
          <div class="shopLogo">
            <img
              v-if="shopInfo.shopLogo"
              :src="checkFileUrl(shopInfo.shopLogo)"
              alt
              @error="shopInfo.shopLogo = ''"
            >
            <img
              v-else
              src="@/assets/img/pc-micro-page/show-default.png"
              alt
            >
          </div>
          <div class="shopTitle">
            <div class="shopname-box">
              <div class="shopname">
                {{ shopInfo.shopName }}
              </div>
              <div
                v-if="shopInfo.shopId == 1 || shopInfo.type === 1"
                class="shop-tap type-tag-wrap"
              >
                <div
                  v-if="shopInfo.shopId == 1"
                  class="self-operate"
                >
                  {{ $t('shopFeature.businessSigns.selfEmployed') }}
                </div>
                <div
                  v-if="shopInfo.type === 1"
                  class="preferred-tag"
                >
                  {{ $t('shopFeature.businessSigns.preferred') }}
                </div>
              </div>
            </div>
            <div class="shopIntro">
              <div
                v-if="shopInfo.shopScore"
                class="shop-tap score"
              >
                {{ shopInfo.shopScore.toFixed(1) }}{{ $t('shopFeature.businessSigns.fen') }}
              </div>
              <div
                v-if="formData.isShowStoreProd"
                class="shop-tap fol"
              >
                {{ $t('shopFeature.businessSigns.fans') }} {{
                  shopInfo.fansCount < 10000
                    ? shopInfo.fansCount
                    : shopInfo.fansCount / 10000 + $t('shopFeature.businessSigns.wan')
                }}
              </div>
              <view class="shop-tap">
                {{ $t('pcdecorate.storeSignate.qualifications') }}
                <img
                  src="@/assets/img/arrow-right2.png"
                  style="width: 8px;height: 8px;"
                  alt
                >
              </view>
            </div>
          </div>
        </div>
        <div class="follow-btn">
          <div class="fol">
            <img
              class="col-icon"
              src="@/assets/img/micro-page/follow.png"
              alt
            >
            <div>{{ $t('shopFeature.businessSigns.follow') }}</div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-show="isShowEdit"
      class="design-editor-item design-hide-class"
    >
      <div class="design-config-editor">
        <div class="design-editor-component-title">
          {{ $t('pcdecorate.componentTitle.businessSigns') }}
        </div>
        <div class="design-editor-component-container">
          <!-- native modifier has been removed, please confirm whether the function has been affected  -->
          <el-form
            ref="formDataRef"
            class="edit-form"
            :model="formData"
            @submit.prevent
          >
            <el-form-item :label="$t('shopFeature.businessSigns.fans')">
              <el-radio-group v-model="formData.isShowStoreProd">
                <el-radio :label="1">
                  {{ $t('pcdecorate.storeSignate.show') }}
                </el-radio>
                <el-radio :label="0">
                  {{ $t('pcdecorate.storeSignate.hide') }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  currentComponent: { // 当前组件
    type: Object,
    default: () => {}
  },
  current: {
    type: Number,
    default: null
  },
  isShowEdit: { // 是否为编辑状态
    type: Boolean,
    default: false
  },
  dataField: { // 所有上传到后端的字段
    type: Object,
    default: () => {
    }
  },
  isStartCheckFieldRules: { // 是否开始校验规则
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['componentsValueChance'])

const shopInfo = ref({})
const formData = reactive({
  // 默认显示粉丝数量
  isShowStoreProd: 1
})

/** 设置或者重设formData的数据 */
const setFormData = () => {
  if (props.dataField) {
    for (const el in props.dataField) {
      formData[el] = props.dataField[el]
    }
  }
}

watch(() => props.dataField, (val) => {
  if (val) {
    setFormData()
  }
})

watch(() => formData, (val) => {
  if (val) {
    emit('componentsValueChance', {
      indexKey: props.indexKey,
      current: props.current,
      dataField: val
    })
  }
}, {
  deep: true
})

onMounted(() => {
  getHeader()
  setFormData()
})

// 请求头部
const getHeader = () => {
  http({
    url: http.adornUrl('/shop/shopDetail/getShopInfo'),
    method: 'get'
  }).then(res => {
    const shopInfoPar = res.data
    if (!shopInfoPar.shopLogo) {
      shopInfoPar.shopLogo = ''
    }
    shopInfo.value = shopInfoPar
  })
}

</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
