import header from '../header/index.vue'
import goods from '../goods/index.vue'
import goodsWaterfall from '../goods-waterfall/index.vue'
import goodsModuleFive from '../goods-module-five/index.vue'
import goodsModuleFour from '../goods-module-four/index.vue'
import goodsModuleThree from '../goods-module-three/index.vue'
import goodsModuleTwo from '../goods-module-two/index.vue'
import goodsModuleOne from '../goods-module-one/index.vue'
import headerAd from '../header_ad/index.vue'
import titleText from '../title-text/index.vue'
import notice from '../notice/index.vue'
import search from '../search/index.vue'
import imageAd from '../image_ad/index.vue'
import tabNav from '../tab-nav/index.vue'
import promotionalActivities from '../promotional-activities/index.vue'
import hotArea from '../hot-area/index.vue'
import storeSignate from '../store-signate/index.vue'

import pTextPng from '@/assets/img/micro-page/p-text.png'
import pTextActivePng from '@/assets/img/micro-page/p-text-active.png'
import pNoticePng from '@/assets/img/micro-page/p-notice.png'
import pNoticeActivePng from '@/assets/img/micro-page/p-notice-active.png'
import pSearchPng from '@/assets/img/micro-page/p-search.png'
import pSearchActivePng from '@/assets/img/micro-page/p-search-active.png'
import pProdListPng from '@/assets/img/micro-page/p-prod-list.png'
import pProdListActivePng from '@/assets/img/micro-page/p-prod-list-active.png'
import pImageAdPng from '@/assets/img/micro-page/p-image-ad.png'
import pImageAdActivePng from '@/assets/img/micro-page/p-image-ad-active.png'
import goodsWaterfallPng from '@/assets/img/micro-page/goods-waterfall.png'
import goodsWaterfallActivePng from '@/assets/img/micro-page/goods-waterfall-active.png'
import pTagsPng from '@/assets/img/micro-page/p-tabs.png'
import pTagsActivePng from '@/assets/img/micro-page/p-tabs-active.png'
import pPromotionPng from '@/assets/img/micro-page/p-promotion.png'
import pPromotionActivePng from '@/assets/img/micro-page/p-promotion-active.png'
import pHotareaPng from '@/assets/img/micro-page/p-hotarea.png'
import pHotareaActivePng from '@/assets/img/micro-page/p-hotarea-active.png'
import pcGoodsModulePng from '@/assets/img/pc-micro-page/pc_goods_module.png'
import pcGoodsModuleActivePng from '@/assets/img/pc-micro-page/pc_goods_module_active.png'
import pcShopSignsPng from '@/assets/img/pc-micro-page/pc_shop_signs.png'
import pcShopSignsActivePng from '@/assets/img/pc-micro-page/pc_shop_signs_active.png'

export const componentLists = ref([// 所有可用的组件
  {
    type: 'config', // 组件名称标识
    title: $t('shopFeature.allCanUse.config'),
    isHide: true, // 是否隐藏选择
    isHeader: true, // 是否为头部
    routerPath: shallowRef(header) // 路由地址
  },
  { // 公共广告头部配置
    type: 'config_common_ad',
    title: $t('shopFeature.allCanUse.config'),
    isHide: true, // 是否隐藏选择
    isHeader: true, // 是否为头部
    routerPath: shallowRef(headerAd)
  },
  { // 商家招牌
    type: 'storeSignate',
    title: $t('shopFeature.allCanUse.businessSigns'),
    pic: pcShopSignsPng,
    picActive: pcShopSignsActivePng,
    routerPath: shallowRef(storeSignate)
  },
  // 标题文本
  {
    type: 'titleText',
    title: $t('shopFeature.allCanUse.titleText'),
    pic: pTextPng,
    picActive: pTextActivePng,
    routerPath: shallowRef(titleText)
  },
  // 公告
  {
    type: 'notice',
    title: $t('shopFeature.allCanUse.notice'),
    pic: pNoticePng,
    picActive: pNoticeActivePng,
    routerPath: shallowRef(notice)
  },
  // 搜索
  {
    type: 'search',
    title: $t('shopFeature.searchBar.searchBar'),
    pic: pSearchPng,
    picActive: pSearchActivePng,
    routerPath: shallowRef(search)
  },
  // 商品列表
  {
    type: 'goods',
    title: $t('shopFeature.allCanUse.goodsList'),
    pic: pProdListPng,
    picActive: pProdListActivePng,
    routerPath: shallowRef(goods)
  },
  // 商品瀑布流
  {
    type: 'goodsWaterfall',
    title: $t('shopFeature.allCanUse.goodsWaterfall'),
    pic: goodsWaterfallPng,
    picActive: goodsWaterfallActivePng,
    routerPath: shallowRef(goodsWaterfall)
  },
  // 图片广告
  {
    type: 'imageAd',
    title: $t('shopFeature.allCanUse.imgAd'),
    pic: pImageAdPng,
    picActive: pImageAdActivePng,
    routerPath: shallowRef(imageAd)
  },
  // 导航栏
  {
    type: 'tabNav',
    title: $t('shopFeature.allCanUse.navigationBar'),
    pic: pTagsPng,
    picActive: pTagsActivePng,
    routerPath: shallowRef(tabNav)
  },
  // 促销活动
  {
    type: 'promotionalActivities',
    title: $t('shopFeature.allCanUse.activities'),
    pic: pPromotionPng,
    picActive: pPromotionActivePng,
    routerPath: shallowRef(promotionalActivities)
  },
  // 热区
  {
    type: 'hotArea',
    title: $t('shopFeature.imageAd.hotAreaTit'),
    pic: pHotareaPng,
    picActive: pHotareaActivePng,
    routerPath: shallowRef(hotArea)
  },
  {
    type: 'goodsModule1',
    title: $t('shopFeature.allCanUse.goodsModule1'),
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    routerPath: shallowRef(goodsModuleOne)
  },
  {
    type: 'goodsModule2',
    title: $t('shopFeature.allCanUse.goodsModule2'),
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    routerPath: shallowRef(goodsModuleTwo)
  },
  {
    type: 'goodsModule3',
    title: $t('shopFeature.allCanUse.goodsModule3'),
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    routerPath: shallowRef(goodsModuleThree)
  },
  {
    type: 'goodsModule4',
    title: $t('shopFeature.allCanUse.goodsModule4'),
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    routerPath: shallowRef(goodsModuleFour)
  },
  {
    type: 'goodsModule5',
    title: $t('shopFeature.allCanUse.goodsModule5'),
    pic: pcGoodsModulePng,
    picActive: pcGoodsModuleActivePng,
    routerPath: shallowRef(goodsModuleFive)
  }
])
