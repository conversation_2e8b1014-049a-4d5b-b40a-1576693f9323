<template>
  <div class="micro-goods-box component-goods-waterfall">
    <div class="design-preview-controller">
      <div class="goods-list">
        <template v-if="goodsList.length">
          <div
            v-for="(item, index) in goodsList"
            :key="index"
            class="goods-li"
            :class="{isGoodCell3:formData.size === 3, isGoodCell1:formData.size === 1}"
          >
            <div
              class="goods-li-box"
              :class="{'no-goods-price':!formData.showContent.find(x=>x===3)&&formData.showContent.find(x=>x===4)}"
            >
              <div
                class="goods-item"
                :class="{goodsItem1:formData.size === 1}"
              >
                <!--图片-->
                <el-image
                  class="goods-img-one"
                  :style="{width: formData.size===3 ? 90 + 'px' : 152 + 'px', height: formData.size!==1 ? (formData.size===3 ? 90 + 'px' : 152 + 'px') : '', margin: formData.size!==1?'0 auto' : ''}"
                  :class="{goodsImgOne1:formData.size === 1, 'goods-empty': !goodsList[0].prodId}"
                  :src="checkFileUrl(item.pic)"
                  fit="fill"
                >
                  <template #error>
                    <div class="image-slot">
                      <img
                        src="@/assets/img/pc-micro-page/show-default.png"
                        alt
                      >
                    </div>
                  </template>
                </el-image>
                <!-- 下架商品蒙版 start -->
                <div
                  v-if="item.status != 1"
                  class="imgs_shelves"
                >
                  <img
                    class="been_imgs"
                    src="@/assets/img/pc-micro-page/been_shelves.png"
                    alt
                  >
                </div>

                <div
                  class="goods-box-info"
                  :class="{goodsBoxInfo1:formData.size === 1}"
                >
                  <div
                    v-if="formData.showContent.find(x=>x===1)"
                    class="goods-info-title"
                  >
                    {{ item.prodName }}
                  </div>
                  <div
                    v-if="formData.showContent.find(x=>x===2)"
                    class="goods-info-desc"
                  >
                    {{ item.brief }}
                  </div>
                  <div
                    v-if="formData.showContent.find(x=>x===3)||formData.showContent.find(x=>x===4)"
                    class="goods-info-price "
                    :class="{'goods-cell-3':formData.showContent.find(x=>x===4)}"
                  >
                    <div
                      v-if="formData.showContent.find(x=>x===3)"
                      class="price-info"
                    >
                      <span>¥</span>{{ item.price }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div
            v-for="(item,index) in goodsList"
            :key="index"
            class="goods-li"
            :class="{isGoodCell3:formData.size === 3, isGoodCell1:formData.size === 1}"
          >
            <div
              class="goods-li-box"
              :class="{'no-goods-price':!formData.showContent.find(x=>x===3)&&formData.showContent.find(x=>x===4)}"
            >
              <div
                class="goods-item"
                :class="{goodsItem1:formData.size === 1}"
              >
                <!--图片-->
                <div
                  class="goods-img-one"
                  :class="{goodsImgOne1:formData.size === 1, 'goods-empty': !goodsList[0].prodId}"
                  :style="{backgroundImage:'url('+(item.pic || defPng)+')'}"
                />
                <!--end 图片-->
                <div
                  class="goods-box-info"
                  :class="{goodsBoxInfo1:formData.size === 1}"
                >
                  <div
                    v-if="formData.showContent.find(x=>x===1)"
                    class="goods-info-title"
                  >
                    {{ item.prodName }}
                  </div>
                  <div
                    v-if="formData.showContent.find(x=>x===2)&&item.brief"
                    class="goods-info-desc"
                  >
                    {{ item.brief }}
                  </div>
                  <div
                    v-if="formData.showContent.find(x=>x===3)||formData.showContent.find(x=>x===4)"
                    class="goods-info-price "
                    :class="{'goods-cell-3':formData.showContent.find(x=>x===4)}"
                  >
                    <div
                      v-if="formData.showContent.find(x=>x===3)"
                      class="price-info"
                    >
                      <span>¥</span>{{ item.price }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div
      v-show="isShowEdit"
      class="design-editor-item design-hide-class"
    >
      <div class="design-config-editor">
        <div class="design-editor-component-title">
          {{ $t('shopFeature.allCanUse.goodsWaterfall') }}
        </div>
        <!--选择商品-->
        <div class="design-editor-component-container">
          <el-form
            ref="formDataRef"
            :model="formData"
            label-position="left"
            class="goods-select-form"
            @submit.prevent
          >
            <el-form-item :label="$t('shopFeature.goods.listStyle')">
              <el-radio-group v-model="formData.size">
                <el-radio
                  v-for="(count, index) in lineSize"
                  :key="index"
                  :label="count.value"
                >
                  {{ count.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              :label="$t('shopFeature.goods.showContent')"
              class="goods-show-container"
            >
              <div class="goods-show-content">
                <el-checkbox-group v-model="formData.showContent">
                  <el-checkbox
                    v-for="(showItem, index) in goodsShowContent"
                    :key="index"
                    :label="showItem.value"
                  >
                    {{ showItem.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
            <el-form-item :label="$t('shopFeature.goodsWaterfall.sortType')">
              <el-radio-group
                v-model="formData.sortType"
                @change="changeSortType"
              >
                <el-radio
                  v-for="(item, index) in sortTypeList"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formData.sortType !== 0"
              :label="$t('shopFeature.goodsWaterfall.timeType')"
              class="goods-show-container"
            >
              <el-radio-group
                v-model="formData.timeType"
                @change="getGoodsInfo"
              >
                <el-radio
                  v-for="(item, index) in timeList"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('shopFeature.goodsWaterfall.timeStatus')">
              <el-radio-group
                v-model="formData.sortStatus"
                @change="getGoodsInfo"
              >
                <el-radio
                  v-for="(item, index) in sortStatus"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import defPng from '@/assets/img/def.png'

const props = defineProps({
  currentComponent: { // 当前组件
    type: Object,
    default: () => {}
  },
  indexKey: { // 当前组件排序索引 type_ + key
    type: String,
    default: ''
  },
  current: { // 当前排序索引 key
    type: Number,
    default: 0
  },
  isShowEdit: { // 是否为编辑状态
    type: Boolean,
    default: false
  },
  dataField: { // 所有上传到后端的字段
    type: Object,
    default: () => {
    }
  },
  isStartCheckFieldRules: { // 是否开始校验规则
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['myCheckResult', 'componentsValueChance'])

// 商品显示内容
const goodsShowContent = [
  {
    value: 1,
    label: $t('shopFeature.goods.prodName')
  },
  {
    value: 3,
    label: $t('shopFeature.goods.prodPrice')
  }
]
// 一行几个
const lineSize = [
  {
    value: 1,
    label: $t('shopFeature.goods.oneLineItem1')
  },
  {
    value: 2,
    label: $t('shopFeature.goods.oneLineItem2')
  },
  {
    value: 3,
    label: $t('shopFeature.goods.oneLineItem3')
  }
]
const sortTypeList = [
  {
    value: 0,
    label: $t('shopFeature.goodsWaterfall.shelfTime')
  },
  {
    value: 1,
    label: $t('shopFeature.goodsWaterfall.salesVolume')
  },
  {
    value: 2,
    label: $t('shopFeature.goodsWaterfall.commentCount')
  }
]
const timeList = [
  {
    value: 0,
    label: $t('stock.all')
  },
  {
    value: 1,
    label: $t('shopFeature.goodsWaterfall.lastYear')
  },
  {
    value: 2,
    label: $t('shopFeature.goodsWaterfall.threeMonths')
  },
  {
    value: 3,
    label: $t('shopFeature.goodsWaterfall.lastMonth')
  },
  {
    value: 4,
    label: $t('shopFeature.goodsWaterfall.lastWeek')
  }
]
const sortStatus = [
  {
    value: 0,
    label: $t('shopFeature.goodsWaterfall.ascendingOrder')
  },
  {
    value: 1,
    label: $t('shopFeature.goodsWaterfall.descendingOrder')
  }
]

const formData = reactive({
  size: 2, // 一行多少个
  showContent: [1, 3],
  sortType: 0,
  timeType: 0,
  sortStatus: 1,
  categoryShow: 2
})
onMounted(() => {
  // 变更数据，保存初始装修数据
  formData.sortStatus = 0
  getGoodsInfo()
})

watch(() => props.dataField, (val) => {
  if (val) {
    setFormData()
  }
})
watch(() => props.isStartCheckFieldRules, () => {
  startCheckFieldRulesCommonFun()
})
watch(() => formData, (val) => {
  if (val) {
    emit('componentsValueChance', {
      indexKey: props.indexKey,
      current: props.current,
      dataField: val
    })
  }
}, {
  deep: true
})

onMounted(() => {
  // 变更数据，保存初始装修数据
  formData.sortStatus = props.dataField.sortStatus || 0
  getGoodsInfo()
  setFormData()
})

const changeSortType = (val) => {
  if (val === 0) {
    formData.timeType = 0
  }
  getGoodsInfo()
}
/** 批量获取商品详情 */
const goodsList = ref([]) // 获取的接口数据
const getGoodsInfo = () => {
  http({
    url: http.adornUrl('/admin/search/prod/renovationPage'),
    method: 'get',
    params: http.adornParams({
      current: 1,
      size: 20,
      mustNotProdTypes: 3,
      status: 1,
      isActive: 1,
      esRenovationSpuSort: dealSortType(formData.sortType, formData.sortStatus),
      showSpuType: 1, // 展示商品类型 0.展示指定的商品ids 1.展示瀑布流商品集合
      esTimeRange: formData.timeType
    })
  }).then(({ data }) => {
    goodsList.value = data.records
  })
}

// 处理升降序
const dealSortType = (type, status) => {
  let res = 0
  switch (type) {
    case 0:
      res = status === 0 ? 0 : 1
      break
    case 1:
      res = status === 0 ? 3 : 2
      break
    case 2:
      res = status === 0 ? 5 : 4
      break
  }
  return res
}

/* 校验 */
const checkData = () => {
  myCheckResult(true)
}
/** 设置或者重设formData的数据 */
const setFormData = () => {
  if (props.dataField) {
    for (const el in props.dataField) {
      formData[el] = props.dataField[el]
    }
  }
}

/**
 * 从父级传过来默认开始验证规格的时候
 * 一般为保存时
 * */
const startCheckFieldRulesCommonFun = () => {
  checkData()
}
/**
 * 返回校验结果
 * isHeader 是否为顶部 特殊情况
 * 结果：Boolean true false
 * */
const myCheckResult = ($isPass) => {
  emit('myCheckResult', {
    data: {
      isPass: $isPass,
      dataField: props.dataField,
      isHeader: (props.currentComponent.type.indexOf('config') !== -1),
      current: props.current
    }
  })
}
</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
