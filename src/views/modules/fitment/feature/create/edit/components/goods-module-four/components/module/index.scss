.component-goods-four-module {
  width: 100%;
  height: 100%;
  min-height: 286px;
  position: relative;
  overflow: hidden;
  .top-image {
    width: 100%;
    height: 98px;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
    &:deep(.el-image) {
      width: 100%;
      height: 100%;
      background: rgba(235, 237, 240, 0.39);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .bottom-content {
    width: 100%;
    min-height: 198px;
    background: #fff;
    display: flex;
    flex-wrap: wrap;
    border-radius: 10px;
    padding: 10px 8px;
    position: relative;
    margin-top: -10px;
    .bottom-items {
      width: calc((100% - 14px) / 3);
      margin-right: 7px;
      margin-bottom: 7px;
      &:nth-child(3n) {
        margin-right: 0;
      }
      .bottom-items-img {
        position: relative;
        .imgs_shelves {
          position: absolute;
          width: 107px;
          height: 100px;
          background: rgba(0, 0, 0, 0.6);
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 80px;
          }
        }
      }
      &:deep(.el-image) {
        width: 107px;
        height: 100px;
        background: rgba(243, 245, 247, 0.39);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .goods-description {
        font-size: 12px;
        height: 34px;
        line-height: 17px;
        padding: 0 8px;
        text-align: center;
        color: #333333;
        font-family: PingFang SC;
        margin: 10px 0 4px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .bottom-actions {
        display: flex;
        justify-content: space-between;
        padding: 0 8px;
        align-items: center;
        .real-price {
          color: #e43130;
          width: calc(100% - 18px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          span {
            font-size: 10px;
            font-weight: bold;
            font-family: PingFang SC;
            &:nth-child(2) {
              font-size: 13px;
            }
          }
        }
        .add-carts {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 18px;
          padding-top: 1px;
          background: linear-gradient(180deg, #ff6a37 0%, #ff302a 100%);
        }
      }
    }
  }
}
