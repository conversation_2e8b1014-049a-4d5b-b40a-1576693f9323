.component-prods-select {
  .el-input.el-input--small,
  .el-cascader.el-cascader--small {
    width: 180px;
  }

  .prods-select-body {
    height: 420px;
  }

  .prods-select-radio {
    .el-radio__label {
      display: none;
    }
  }

  .prod-info {
    display: flex;
    align-items: center;

    img {
      object-fit: contain;
    }

    .prod-name {
      flex: 1;
      margin-left: auto;
      padding: 0 30px 0 10px;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
      -webkit-text-overflow: ellipsis;
      -moz-text-overflow: ellipsis;
      word-break: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      /* autoprefixer: ignore next */
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 20px;
    }
  }

  :deep(.el-table__body-wrapper::-webkit-scrollbar) {
    width: 6px;
    height: 439px;
    background: #F7F8FA;
    opacity: 1;
    border-radius: 4px;
  }

  // 滚动条的滑块
  :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
    width: 6px;
    height: 150px;
    background: #E9ECF3;
    opacity: 1;
    border-radius: 4px;
  }
}
