/**可选择的组件*/
.component-all-can-use-components {
  &.all-grouped {
    .add-component-grouped {
      box-sizing: border-box;

      .add-component-grouped-item {
        padding: 0 5px 0 10px;

        .add-grouped-item-title {
          font-size: 14px;
          font-family: Microsoft YaHei;
          margin: 15px 0 0 0;
          color: #333;
        }

        .add-grouped-item-list {
          div {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }

          .add-grouped-item-list-btn {
            box-sizing: border-box;
            width: 45%;

            .add-grouped-item-list-btn-title {
              margin-top: 10px;
              width: 80px;
              height: 80px;
              text-align: center;
              font-size: 12px;
              border-radius: 4px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              cursor: pointer;

              .item-pic-container {
                width: 25px;
                margin-bottom: 8px;

                img {
                  display: inline-block;
                  max-width: 100%;
                }
              }
            }

            .add-grouped-item-list-btn-title.active {
              background: #155bd4;
              color: #fff;
            }
          }
        }
      }
    }
  }
}
