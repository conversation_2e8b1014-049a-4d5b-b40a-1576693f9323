.component-goods-five-module {
  width: 100%;
  .goods-top {
    display: flex;
    :deep(.el-image) {
      width: calc((100% - 9px) / 2);
      height: 98px;
      margin-right: 9px;
      display: flex;
      align-items: center;
      background: rgba(235, 237, 240, 0.39);
      justify-content: center;
      border-radius: 4px;
      overflow: hidden;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .goods-bottom {
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    .bottom-items {
      width: calc((100% - 27px) / 4);
      margin-right: 9px;
      height: 120px;
      background: #fff;
      border-radius: 4px;
      padding: 9px 8px;
      margin-bottom: 9px;
      &:nth-child(4n) {
        margin-right: 0;
      }
      .bottom-items-imgs {
        position: relative;
        .imgs_shelves {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 61px;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 50px;
          }
        }
      }
      :deep(.el-image) {
        width: 100%;
        height: 61px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(243, 245, 247, 0.39);
        border-radius: 2px;
        overflow: hidden;
      }
      .name {
        width: 100%;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 0 8px;
        font-size: 12px;
        color: #333;
        font-family: PingFang SC;
        margin: 7px 0 4px 0;
      }
      .real-price {
        color: #e43130;
        font-weight: bold;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        span {
          font-size: 12px;
          &:nth-child(2) {
            font-size: 13px;
          }
        }
      }
    }
  }
}
