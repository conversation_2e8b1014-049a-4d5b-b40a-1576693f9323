.component-store-signate {
  position: relative;
  .shop {
    padding: 12px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 0 0 6px 6px;

    .shopInfo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .shopInfo .sl {
      display: flex;
      align-items: center;
    }

    .shopInfo .shopLogo {
      display: inline-block;
      width: 42px;
      height: 42px;
      border-radius: 6px;
      margin-right: 15px;
    }

    .shopInfo .shopLogo img {
      display: block;
      width: 42px;
      height: 42px;
      border-radius: 6px;
    }
    .shopInfo .shopTitle {
      display: inline-block;
      box-sizing: border-box;
      font-size: 12px;
    }
    .shopInfo .shopTitle .shopname {
      vertical-align: middle;
      padding-right: 4px;
      font-size: 14px;
      color: #333;
      font-weight: bold;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 150px;

    }
    .shopInfo .shopTitle .shopIntro {
      display: flex;
      align-items: center;
      color: #5b5b5b;
      font-size: 12px;
      margin-top: 8px;
    }
    .shopInfo .shopTitle .shopIntro .shop-tap {
      padding: 0 4px;
      border-right: 1px solid #e1e1e1;
      img {
        vertical-align: baseline;
      }
    }
    .shopInfo .shopTitle .shopIntro .shop-tap:last-child {
      border-right: none;
    }
    .shopInfo .shopTitle .shopIntro .shop-tap:first-child {
      padding-left: 0;
    }
    .shopInfo .shopTitle .shopIntro-en {
      display: block;
      margin-top: 5px;
    }
    .shopInfo .shopTitle .shopIntro-en .fol {
      margin-top: 5px;
    }
    .shopInfo .shopTitle .shopname-box {
      display: flex;
      align-items: center;
    }
    .shopInfo .shopTitle .shopname-box .self-operate {
      display: inline-block;
      background-color: #F81A1A;
      background-image: linear-gradient(to bottom, #F81A1A, #f86323);
      color: #fff;
      font-size: 15px;
      padding: 1px 3px;
      border-radius: 3px;
      min-width: 30px;
    }
    .shopInfo .shopTitle .shopname-box .preferred-tag {
      display: inline-block;
      background: linear-gradient(106deg, #F6D18D 0%, #F6CE8D 100%);
      color: #5E3D00;
      font-size: 15px;
      padding: 1px 3px;
      border-radius: 3px;
      min-width: 30px;
    }
    .shopInfo .shopTitle .shopname-box .preferred-tag,
    .shopInfo .shopTitle .shopname-box .self-operate {
      font-size: 12px;
      margin-right: 4px;
    }
    .shopInfo .shopTitle .shopIntro .type-tag-wrap {
      padding-right: 0;
      display: flex;
      display: -webkit-flex; /* Safari */
      justify-content: space-evenly;
      -webkit-justify-content: space-evenly;
    }
    .shopInfo .shopTitle .shopIntro .score {
      line-height: 12px;
      font-size: 11px;
      color: #FE3A26;
      font-weight: 600;
    }
    .shopInfo .follow-btn {
      justify-content: flex-end;
      background-color: #F81A1A;
      line-height: 15px;
      padding: 5px 10px;
      font-size: 12px;
      border-radius: 50px;
      letter-spacing: 1px;
      color: #fff;
    }
    .shopInfo .follow-btn.followed {
      background: #f5f5f5;
      color: #999;
    }
    .shopInfo .follow-btn .fol {
      display: flex;
      align-items: center;
    }
    .shopInfo .follow-btn .col-icon {
      display: inline-block;
      width: 15px;
      height: 15px;
      margin-right: 2px;
    }

    /* 店铺首页 */
    .shop-index .shopInfo .follow-btn.followe {
      color: #fff;
    }
  }
}

.edit-form {
  padding: 0;
  .el-radio {
    margin-bottom: 10px;

    .el-radio__label {
      font-size: 14px;
    }
  }
}

.view-more-item {
  display: flex;
  justify-content: space-between;
  span {
    font-size: 12px;
    color: #606266;
  }
}
.el-radio-group {
  vertical-align: unset;
}

.btn-style {
  background-color: #f7f8fa;
  padding: 20px 16px;
  margin-top: 20px;
  transition: all .2s;
  font-size: 14px;
  .btn-styles-con {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .btn-type-input {
    display: flex;
    align-items: center;
    vertical-align: center;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .cursor {
      .type-sign {
        background-color: #ceefff;
        border: #02a1e9 1px solid;
        color: #02a1e9;
        padding: 2px 4px;
        font-size: 12px;
        margin-right: 10px;
      }
    }


    .item-label {
      width: 80px;
      margin-top: 2px;
    }
    .goods-name {
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0 0 0 10px !important;
      cursor: pointer;
    }
    .goods-name:hover {
      color: #e43130;
    }
    .el-dropdown {
      cursor: pointer;
    }
    .el-input {
      width: unset;
    }
    input {
      height: 30px;
    }
    .mini-programs-path {
      margin-left: 80px;
      width: 240px !important;
    }
  }
  .btn-type-input:nth-child(3n) {
    margin-bottom: 0;
  }
}

.custom-path-con {
  margin-top: 10px;
  max-width: 100%;
  display: flex;
  align-items: center;
  span {
    white-space: nowrap;
    margin-right: 10px;
  }
}
.el-dialog__title {
  font-size: 16px;
}
