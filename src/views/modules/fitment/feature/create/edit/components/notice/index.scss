.component-notice {
  .notice-content {
    height: 40px;
    line-height: 40px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 12px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .el-icon-arrow-right {
      font-size: 18px;
    }

    span {
      display: inline-block;
      margin-right: 4px;
      vertical-align: top;
      font-size: 18px;
    }
  }

  &:deep(.el-color-picker) {
    vertical-align: middle;
  }
}

.add-notice {
  background: #fff;
  padding: 16px 12px;
  line-height: 16px;
  border: 1px solid #EAEAF2;
  margin-top: 10px;
}
