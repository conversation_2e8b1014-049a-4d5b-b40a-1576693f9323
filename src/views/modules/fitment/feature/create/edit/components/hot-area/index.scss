.component-hot-area {
  img {
    width: 100%;
    user-select: none;
  }
  .ad-view-box {
    position: relative;
    overflow: hidden;

    &.ad-view-2, &.ad-view-3 {
      white-space: nowrap;
      display: -webkit-box;

      .image-ad-view {
        width: 100%;
      }
    }
    &.ad-view-3 {
      .image-ad-view {
        width: 40%;
      }
    }
  }
  .close-icon {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 20px;
    display: none;
  }
  .image-ad-view {
    position: relative;
    height: 135px;
    overflow: hidden;
    font-size: 12px;
    background-color: #f3f5f7;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    color: #88c4dc;
    text-align: center;

    .image-ad-title {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #9FA4B1;
      > img {
        width: 57px;
        object-fit: contain;
        margin-bottom: 12px;
      }
    }
  }
  .image-ad-edit {
    .ad-edit-item-title {
      font-size: 14px;
      color: #666666;
      margin: 20px 0;
      > span.tips {
        font-size: 12px;
        color: #AAAAAA;
        margin-left: 15px;
      }
    }
  }
  .ad-hot-box {
    position: relative;
    width: 500px;
    margin: 0 auto;
    overflow: hidden;
    :deep(.box-img) {
      position: relative;
      height: auto;
      width: 500px;
      vertical-align: middle;
      border: 0;
    }
  }
  .ad-drag {
    position: absolute;
    width: 100px;
    height: 100px;
    top: 0;
    left: 0;
    user-select: auto;
    touch-action: none;
    cursor: move;
    border: 1px solid #38f;
    background: rgba(51, 136, 255, .5);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      .el-icon-close {
        display: block;
        cursor: pointer;
      }
    }
    * {
      user-select: auto;
      touch-action: none;
    }
    :deep(.el-dropdown-link) {
      color: #fff;
    }
  }
  .ad-image-hot {
    position: relative;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
    .ad-image-hot-box {
      position: relative;
      &:hover {
        .close-icon {
          cursor: pointer;
          display: block;
        }
      }
    }
    .add-ad-image {
      display: inline-block;
      margin-bottom: 10px;
      padding: 4px 6px;
      border: 1px solid #459ae9;
      cursor: pointer;
      color: #459ae9;
      margin-right: 10px;
    }
    img {
      width: 100%;
      user-select: none;
    }
    .ad-image-hot-content {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      cursor: all-scroll;
      overflow: hidden;
      span.ad-hot-box-item {
        position: absolute;
        border: 1px solid #38f;
        background: rgba(51, 136, 255, .5);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        cursor: pointer;
        max-width: 100%;
        max-height: 100%;
        &:hover {
          .el-icon-close {
            display: block;
            cursor: pointer;
          }
        }
      }
    }
  }
  :deep(.el-dialog__title) {
    font-size: 16px;
  }
}
