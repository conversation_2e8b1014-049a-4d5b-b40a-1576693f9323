.component-goods {
  position: relative;
  .goods-list {
    display: flex;
    flex-wrap: wrap;
    .goods-li {
      width: 50%;
      &.isGoodCell3 {
        width: 33.33%;
      }
      &.isGoodCell1 {
        width: 100%;
      }
      .goods-li-box {
        height: 100%;
        padding: 6px 5px;
        &.no-goods-price {
          padding-bottom: 32px;
        }
        .goodsItem1 {
          height: 100%;
          display: flex;
        }
        .goods-item {
          position: relative;
          height: 100%;
          background: #fff;
          box-shadow: 0 1px 17px 1px rgb(0 0 0 / 4%);
          padding: 8px;
          box-sizing: border-box;
          border-radius: 5px;
          .goods-img-box{
            position: relative;
            margin-right: 10px;
          }
          .goods-img-one {
            width: 100%;
            background-repeat: no-repeat;
            background-size: contain;
            background-position: center;
            flex: 1;
            display: block;
            &::before {
              content: "";
              padding-top: 100%;
              float: left;
            }
            &::after {
              content: "";
              display: block;
              clear: both;
            }
            .image-slot {
              display: flex;
              align-items: center;
              img {
                width: 100%;
              }
            }
          }
          .goods-img-one.goods-empty {
            background-size: 26px;
            background-color: #f3f5f7;
          }
          .goodsImgOne1 {
            max-width: 33%;
            height: 100% !important;
            margin-right: 10px;
          }
          .goodsBoxInfo1 {
            max-width: 65%;
            display: flex;
            height: 100% !important;
            flex-direction: column;
            justify-content: center;
          }
          .goods-box-info {
            height: 30%;
            margin-top: 10px;
            .goods-info-title {
              width: 100%;
              font-size: 12px;
              line-height: 14px;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              word-break: break-word;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            .goods-info-desc {
              width: 100%;
              font-size: 12px;
              height: 16px;
              color: #999;
              overflow: hidden;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              white-space: nowrap;
              margin-top: 5px;
            }
            .goods-info-price {
              position: relative;
              margin-top: 10px;
              &.goods-cell-3 {
                padding-right: 20px;
              }
              .price-info {
                width: 100%;
                font-size: 14px;
                color: #ff4444;
                overflow: hidden;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .imgs_shelves {
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        top: 0;
        left: 0;
        img {
          max-width: 100%;
        }
      }
    }
  }

  .design-config-editor {
    .design-editor-component-container {
      padding: 10px 0 !important;
      .goods-select-form.el-form {
        .el-form-item {
          display: flex;
          flex-direction: column;
          margin-bottom: 15px;
        }
      }
      .goods-select-con {
        background: #fff;
        .goods-select-list {
          display: flex;
          flex-wrap: wrap;
          padding: 12px 0 0 20px;
          border: 1px solid #eaeaf2;
          .goods-select-item {
            position: relative;
            display: inline-block;
            margin: 0 16px 12px 0;
            background-color: #fff;
            &:hover {
              .close-item {
                display: block;
              }
            }
            &:nth-child(5n) {
              margin-right: 0;
            }
            .goods-select-item-img {
              width: 50px;
              height: 50px;
              line-height: 50px;
              text-align: center;
              background-repeat: no-repeat;
              background-position: center;
              background-size: cover;
              font-size: 10px;
              border-radius: 2px;
            }
            .goods-select-item-img.goods-empty {
              background-size: 11px;
              background-color: #f3f5f7;
            }
            .goods-select-item-img.add-btn {
              border: 1px solid #eaeaf2;
              cursor: pointer;
            }
            .close-item {
              position: absolute;
              width: 20px;
              height: 20px;
              border-radius: 20px;
              line-height: 20px;
              text-align: center;
              top: -10px;
              right: -10px;
              z-index: 15;
              font-size: 14px;
              cursor: pointer;
              color: #fff;
              background: #bbb;
              display: none;
            }
          }
        }
      }
      .goods-show-container.el-form-item {
        :deep(.el-form-item__content) {
          line-height: 0;
          .goods-show-content {
            width: 100%;
            border-radius: 2px;
            padding: 15px 20px;
            border: 1px solid #eaeaf2;
            .el-checkbox-group {
              display: flex;
              justify-content: space-around;
            }
          }
        }
      }
    }
  }
}
