.component-goods-waterfall {
  position: relative;
  padding: 0 6px;

  .goods-list {
    display: flex;
    flex-wrap: wrap;

    .goods-li {
      width: 50%;
      position: relative;

      &.isGoodCell3 {
        width: 33.33%;
      }

      &.isGoodCell1 {
        width: 100%;
      }

      .goods-li-box {
        padding: 6px 5px;

        &.no-goods-price {
          padding-bottom: 32px;
        }

        .goodsItem1 {
          display: flex;
        }

        .goods-item {
          position: relative;
          background: #fff;
          box-shadow: 0 1px 17px 1px rgb(0 0 0 / 4%);
          padding: 8px;
          box-sizing: border-box;
          border-radius: 5px;

          .goods-img-one {
            width: 100%;
            background-repeat: no-repeat;
            background-size: contain;
            background-position: center;
            position: relative;
            flex: 1;
            display: block;

            &::before {
              content: "";
              padding-top: 100%;
              float: left;
            }

            &::after {
              content: "";
              display: block;
              clear: both;
            }

            .image-slot {
              display: flex;
              align-items: center;
              img {
                width: 100%;
              }
            }
          }

          .goods-img-one.goods-empty {
            background-size: 26px;
            background-color: #f3f5f7;
          }

          .goodsImgOne1 {
            max-width: 33%;
            margin-right: 10px;
          }

          .goodsBoxInfo1 {
            max-width: 65%;
            display: flex;
            flex-direction: column;
            justify-content: center;
          }

          .goods-box-info {
            margin-top: 10px;

            .goods-info-title {
              width: 100%;
              font-size: 12px;
              line-height: 14px;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              word-break: break-word;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }

            .goods-info-desc {
              width: 100%;
              font-size: 12px;
              height: 16px;
              color: #999;
              overflow: hidden;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              white-space: nowrap;
              margin-top: 5px;
            }

            .goods-info-price {
              position: relative;
              margin-top: 10px;

              &.goods-cell-3 {
                padding-right: 20px;
              }

              .price-info {
                width: 100%;
                font-size: 14px;
                color: #ff4444;
                overflow: hidden;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }

      .imgs_shelves {
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        top: 0;
        left: 0;

        img {
          width: 140px;
        }
      }
    }
  }

  .design-config-editor {
    .design-editor-component-container {
      padding: 10px 0 !important;

      .goods-select-form.el-form {
        :deep(.el-form-item) {
          display: flex;
          flex-direction: column;
          margin-bottom: 15px;
        }
      }

      .goods-show-container {
        &.el-form-item {
          :deep(.el-form-item__content) {
            line-height: 30px;

            .goods-show-content {
              width: 100%;
              border-radius: 2px;
              padding: 15px 20px;
              border: 1px solid #eaeaf2;

              .el-checkbox-group {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-around;
              }
            }

            .el-radio {
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }
}
