.component-classification-list {
  .design-preview-controller {
    overflow: hidden;

    .el-carousel__button {
      width: 10px;
    }

    img {
      display: block;
      width: 100%;
      height: 100%;
      margin: 0 auto;
      border: 0;
    }

    .advImgBoxlist {
      width: 100%;
      -webkit-overflow-scrolling: touch;
      white-space: nowrap;
      display: -webkit-box;

      .adv-li {
        width: 125px;
        height: 125px;

        img {
          height: 100%;
          width: 100%;
          margin-right: 0;
          margin-left: 0;
        }
      }

    }

    .advImgBoxlist::-webkit-scrollbar {
      background-color: transparent;
    }
  }

  .typelabel {
    border: 1px solid rgba(51, 136, 255, 0.3);
    background: #e2f3ff;
    color: #666666;
    padding: 1px 4px;
    text-align: center;
    margin-right: 10px;
  }

  .up-video-compent {

    .material-list-images .category-list {
      margin-bottom: 10px;
      max-height: 350px !important;
    }

    .el-pager li {
      min-width: 18.5px;
    }

    .attachment-container {

      .image-meta {
        position: absolute;
        width: 105px;
        height: 25px;
        font-size: 12px;
        line-height: 25px;
        color: #fff;
        text-align: center;
        background: rgba(0, 0, 0, .2);
        bottom: 0;
      }

      .image-item,
      .image-box {
        width: 105px !important;
        height: 105px !important;
      }

      .image-title {
        padding: 0 !important;
        font-size: 12px;
        width: 105px;
        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        -webkit-text-overflow: ellipsis;
        -moz-text-overflow: ellipsis;
        white-space: nowrap;
        margin: 5px 0;
      }

      .image-item {
        margin: 0 0 30px 20px;
      }
    }
  }

  .editor {
    //:not(:last-child)
    margin-bottom: 20px;

    .editor-container {
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: -moz-box;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      -moz-box-align: center;
      align-items: center;

      .editor__control-group-control {
        width: 185px;
      }

      img {
        width: auto \9
      ;
        height: auto;
        max-width: 100%;
        vertical-align: middle;
        border: 0;
        -ms-interpolation-mode: bicubic;
        position: relative;
      }

      .editor-card {
        position: relative;
        //添加的图 item

        .class-edit-box:first-child {
          margin-top: 0;
        }

        .class-edit-box {
          position: relative;
          background-color: #fff;
          margin: 15px 0;
          padding: 15px;
          border: 1px dashed #e5e5e5;
        }

        //热区item
        .editor_subentry-item {
          position: relative;

          .image-editor {
            float: left;
          }

          .subentry-item-editor {
            float: left;
            margin-left: 10px;
            max-width: 245px;

            .editor-container {
              margin-bottom: 15px;

              .zent-input-wrapper {
                display: -webkit-box;
                display: -ms-flexbox;
                display: -webkit-flex;
                display: -moz-box;
                display: flex;
                position: relative;
                height: 30px;
                max-height: 36px;
              }

              .control-group-label {
                margin-right: 10px;
              }
            }

            .subentry-control-group {
              margin-bottom: 0;
            }
          }
        }

        .image-component {
          display: inline-block;

          .has-choosed-image-box {
            position: relative;
            width: 80px;
            height: 80px;
            border: 1px solid #e5e5e5;
            text-align: center;

            .thumb-image {
              min-height: 80px;
              -webkit-box-sizing: border-box;
              -moz-box-sizing: border-box;
              box-sizing: border-box;
              vertical-align: bottom;
              max-height: 100%;
              max-width: 100%;
              height: auto;
              width: auto;
              object-fit: cover;
            }

            .modify-image {
              position: absolute;
              bottom: 0;
              left: 0;
              width: 80px;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              color: #fff;
              background: rgba(0, 0, 0, .5);
              cursor: pointer;
            }
          }
        }

        .clearfix {
          zoom: 1;
        }

        .class-edit-box:hover .item-delete {
          display: block;
        }

      }

      .editor-card-item:first-child {
        margin-top: 0;
      }

      .editor-label {
        font-size: 12px;
        width: auto !important;
        -ms-flex-negative: 0;
        -webkit-flex-shrink: 0;
        flex-shrink: 0;
        text-align: right;
        margin-right: 10px;
      }

      .editor-label--top {
        margin-top: 0;
        -ms-flex-item-align: start;
        -webkit-align-self: flex-start;
        align-self: flex-start;
      }

      .editor-control {
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        -webkit-flex-grow: 1;
        -moz-box-flex: 1;
        flex-grow: 1;

        .templates.active {
          border-color: #38f;
        }

        .templates {
          width: 100px;
          height: 100px;
          display: inline-block;
          border: 1px solid #e5e5e5;
          margin: 0 10px 15px 0;
          padding-top: 5px;
          background-color: #fff;
          text-align: center;
          cursor: pointer;

          .templates__image-block {
            width: 100px;
            height: 64px;
          }

          .templates .templates__title {
            margin-top: 10px;
          }
        }
      }

      .rc-design-editor-card {
        position: relative;
      }
    }
  }

  .add-ads-desc {
    margin-bottom: 5px;
    color: #999;

    .label-name {
      color: #222;
    }
  }

  .default-preview {
    .default-preview__title {
      margin-top: 50px;
      font-size: 14px;
    }
  }

  .rc-design-vue-preview {
    position: relative;
    overflow: hidden;
  }

  .rc-design-vue-preview:before {
    display: block;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .clearfix:after {
    content: "";
    display: table;
    clear: both;
  }

  .drag_box {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    height: 75%;
    cursor: move;
  }

  //添加一个背景图 addbtn
  .editor-card-add {
    display: flex;
    align-items: center;
    background: #fff;
    margin: 10px 0;
    padding: 20px 10px;
    border: 1px dashed #e5e5e5;
    cursor: pointer;
    color: #38f;

    .add-image {
      width: 100%;
      height: 60px;
      text-align: center;

      .add-image-text {
        margin-top: 13px;

        .editor-card-add-icon {
          margin-right: 10px;
          color: #38f;
        }
      }

      .add-image-tip {
        color: #999;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .collapse-box {
    position: relative;
    border: 1px dashed #e5e5e5;
    background: #fff;
    margin-bottom: 10px;

    &:hover {
      .all-del {
        display: block;
      }
    }

    .title-header {
      position: relative;
      padding: 10px 0;
      border-bottom: 1px dashed #e5e5e5;

      .iconRight {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }

      .inputTitle {
        width: 150px;
        height: 30px;

        .el-input__inner {
          height: 30px;
          line-height: 30px;
        }
      }
    }

    .collapse-con {
      padding-top: 10px;
      max-height: 100vh;
      overflow: hidden;
      transition: all .2s;

      &.hide {
        max-height: 0;
      }
    }

    .collapse-item {
      padding: 0 10px;
    }

  }

  .up-video-dialog {
    .el-dialog__body {
      padding-top: 10px;
    }

    .el-dialog__header {
      padding: 10px;
      border-bottom: 1px solid #e5e5e5;

      .el-dialog__title {
        padding: 8px 15px;
        text-align: center;
        min-width: 90px;
        font-size: 12px;
      }

      .el-dialog__headerbtn {
        top: 10px;
      }
    }

    .up-video-compent {
      overflow-y: auto;
      max-height: 80vh;
    }
  }

  .item-delete {
    display: none;
    position: absolute;
    cursor: pointer;
    font-size: 20px;
    right: -10px;
    top: -10px;
    color: #bbb;
    background: #fff;
    border-radius: 50%;
  }

  .all-class {
    display: flex;
    min-height: 400px;

    .all-item-left {
      width: 78px;
      background: #f8f8f8;
      color: #7d7e80;

      .all-item-list {
        display: block;
        overflow: hidden;
        font-size: 12px;
        line-height: 1.4;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        color: #7d7e80;
        word-break: break-all;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        padding: 20px 12px 20px 9px;
        background-color: #f8f8f8;
        border-left: 3px solid transparent;

        &.active {
          background: #fff;
          font-weight: 500;
          color: #323233;
          border-left: 3px solid #ff4444;
        }
      }
    }

    .all-item-right {
      flex: 1;

      .banner-wrapper {
        padding: 12px 15px 7px;

        .banner-wrapper-cont {
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
          display: block;
          width: 100%;
          padding-top: 37%;
          background-repeat: no-repeat;
          background-size: cover;
          background-position: 50%;
        }
      }

      .container_group {
        padding: 0 15px;

        .container_group-title {
          margin: 0;
          font-size: 12px;
          font-weight: 400;
          line-height: 28px;
          color: #3a3a3a;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .container_group-list {
          -webkit-flex-wrap: wrap;
          -ms-flex-wrap: wrap;
          flex-wrap: wrap;
          -webkit-box-pack: justify;
          -moz-box-pack: justify;
          -ms-flex-pack: justify;
          margin-top: 2px;
          display: flex;

          .container_group-list-item {
            -webkit-box-flex: 1;
            -webkit-flex: 1 1 30%;
            -moz-box-flex: 1;
            -ms-flex: 1 1 30%;
            flex: 1 1 30%;
            max-width: 30%;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            -moz-box-orient: vertical;
            -moz-box-direction: normal;
            -ms-flex-direction: column;
            flex-direction: column;
            margin-bottom: 15px;
            padding: 0 3px;

            .cap-category-container__img {
              width: 100%;
              padding-top: 100%;
              background-size: cover;
              background-repeat: no-repeat;
              background-position: 50%;
            }

            .cap-category-container__title {
              display: block;
              margin-top: 10px;
              font-size: 12px;
              line-height: 18px;
              color: #3a3a3a;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}

/**1.2.3*/
.class-edit-box {
  position: relative;
  background-color: #fff;
  margin: 15px 0;
  padding: 15px;
  border: 1px dashed #e5e5e5;
  z-index: 99;

  .item-delete {
    display: none;
    position: absolute;
    cursor: pointer;
    font-size: 20px;
    right: -10px;
    top: -10px;
    color: #bbb;
    background: #fff;
    border-radius: 50%;
  }

  .editor-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-box;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    align-items: center;

    .editor__control-group-control {
      width: 185px;
    }

    img {
      width: auto \9
    ;
      height: auto;
      max-width: 100%;
      vertical-align: middle;
      border: 0;
      -ms-interpolation-mode: bicubic;
      position: relative;
    }

    .editor-card {
      position: relative;
      //添加的图 item

      .class-edit-box:first-child {
        margin-top: 0;
      }

      .class-edit-box {
        position: relative;
        background-color: #fff;
        margin: 15px 0;
        padding: 15px;
        border: 1px dashed #e5e5e5;
      }

      //热区item
      .editor_subentry-item {
        position: relative;

        .image-editor {
          float: left;
        }

        .subentry-item-editor {
          float: left;
          width: 260px;
          margin-left: 10px;

          .editor-container {
            margin-bottom: 15px;

            .zent-input-wrapper {
              display: -webkit-box;
              display: -ms-flexbox;
              display: -webkit-flex;
              display: -moz-box;
              display: flex;
              position: relative;
              height: 30px;
              max-height: 36px;
            }

            .control-group-label {
              margin-right: 10px;
            }
          }

          .subentry-control-group {
            margin-bottom: 0;
          }
        }
      }

      .image-component {
        display: inline-block;

        .has-choosed-image-box {
          position: relative;
          width: 80px;
          height: 80px;
          border: 1px solid #e5e5e5;
          text-align: center;

          .thumb-image {
            min-height: 80px;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            vertical-align: bottom;
            max-height: 100%;
            max-width: 100%;
            height: auto;
            width: auto;
            object-fit: cover;
          }

          .modify-image {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 20px;
            line-height: 20px;
            font-size: 12px;
            color: #fff;
            background: rgba(0, 0, 0, .5);
            cursor: pointer;
          }
        }
      }

      .clearfix {
        zoom: 1;
      }

    }

    //添加一个背景图 addbtn
    .editor-card-add {
      display: -webkit-box;
      display: -webkit-flex;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -webkit-align-items: center;
      -moz-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      background: #fff;
      margin: 10px 0;
      padding: 10px;
      border: 1px dashed #e5e5e5;
      cursor: pointer;
      color: #38f;

      .add-image {
        width: 100%;
        height: 60px;
        text-align: center;

        .add-image-text {
          margin-top: 13px;

          .editor-card-add-icon {
            margin-right: 10px;
            color: #38f;
          }
        }

        .add-image-tip {
          color: #999;
        }
      }
    }

    .editor-card-item:first-child {
      margin-top: 0;
    }

    .editor-card-add:last-child {
      margin-bottom: 0;
    }

    .editor-label {
      font-size: 12px;
      width: auto !important;
      -ms-flex-negative: 0;
      -webkit-flex-shrink: 0;
      flex-shrink: 0;
      text-align: right;
      margin-right: 10px;
    }

    .editor-label--top {
      margin-top: 0;
      -ms-flex-item-align: start;
      -webkit-align-self: flex-start;
      align-self: flex-start;
    }

    .editor-control {
      -webkit-box-flex: 1;
      -ms-flex-positive: 1;
      -webkit-flex-grow: 1;
      -moz-box-flex: 1;
      flex-grow: 1;

      .templates.active {
        border-color: #38f;
      }

      .templates {
        width: 100px;
        height: 100px;
        display: inline-block;
        border: 1px solid #e5e5e5;
        margin: 0 10px 15px 0;
        padding-top: 5px;
        background-color: #fff;
        text-align: center;
        cursor: pointer;

        .templates__image-block {
          width: 100px;
          height: 64px;
        }

        .templates .templates__title {
          margin-top: 10px;
        }
      }
    }

    .rc-design-editor-card {
      position: relative;
    }
  }

  &:hover .item-delete {
    display: block;
  }

  .editor_subentry-item {
    position: relative;

  }

  .zent-input-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-box;
    display: flex;
    position: relative;
    height: 30px;
    max-height: 36px;
  }

  .subentry-control-group {
    margin-bottom: 0;
  }

  .control-group-label {
    margin-right: 10px;
    font-size: 12px;
  }

  .image-editor {
    float: left;
  }

  .has-choosed-image-box {
    position: relative;
    width: 80px;
    height: 80px;
    border: 1px solid #e5e5e5;
    text-align: center;
    overflow: hidden;

    .thumb-image {
      min-height: 80px;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      vertical-align: bottom;
      max-height: 100%;
      max-width: 100%;
      height: auto;
      width: auto;
      object-fit: cover;
    }

    .modify-image {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 80px;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      background: rgba(0, 0, 0, .5);
      cursor: pointer;
    }
  }

  .subentry-item-editor {
    float: left;
    margin-left: 10px;

    .editor-container {
      margin-bottom: 15px;
    }
  }

  .zent-input-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-box;
    display: flex;
    position: relative;
    height: 30px;
    max-height: 36px;
  }

  .subentry-control-group {
    margin-bottom: 0;
  }

  .choose_ed {
    font-size: 12px;
    cursor: pointer;
    color: #459ae9;

    &.active {
      color: #333;

    }
  }

}

