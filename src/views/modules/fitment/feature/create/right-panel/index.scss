.showRightPanel {
  overflow: hidden;
  position: relative;
  width: calc(100% - 15px);
}

.rightPanel-background {
  opacity: 0;
  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);
  background: rgba(0, 0, 0, .2);
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  position: fixed;
  z-index: -1;
}

.rightPanel {
  background: #fff;
  position: fixed;
  height: 100vh;
  width: 100%;
  max-width: 260px;
  top: 0;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, .05);
  transition: all .25s cubic-bezier(.7, .3, .1, 1);
  transform: translate(100%);
  z-index: 200;
  left: auto;
  right: 0;
}

.show {
  transition: all .3s cubic-bezier(.7, .3, .1, 1);

  .rightPanel-background {
    z-index: 100;
    opacity: 1;
    width: 100%;
    height: 100%;
  }

  .rightPanel {
    transform: translate(0);
  }
}

.handle-button {
  position: absolute;
  border-radius: 6px 0 0 6px !important;
  pointer-events: auto;
  z-index: 0;
  cursor: pointer;
  pointer-events: auto;
  font-size: 24px;
  text-align: center;
  color: #fff;
  line-height: 48px;
  left: -22px;
  width: 22px;
  height: 52px;
  transition: all .2s;

  &:hover {
    left: -48px;
    width: 48px;
  }

  i {
    font-size: 16px;
    line-height: 48px;
  }
}
