<template>
  <div class="template-list page-feature-template">
    <div
      v-if="isAuth('shop:shopTemplate:saveMove')"
      class="default-btn primary-btn new-page"
      @click="goCreatePage"
    >
      {{ $t('shopFeature.template.newTemplate') }}
    </div>
    <div
      class="default-btn primary-btn new-page"
      @click="getMiniPageList"
    >
      {{ $t('shopFeature.template.refresh') }}
    </div>
    <div class="main-container">
      <div
        v-if="templateList.length"
        style="display: flex;flex-wrap: wrap;"
      >
        <div
          v-for="(item, index) in templateList"
          :key="index"
          class="card-item"
        >
          <div class="template-item">
            <img
              v-if="item.image"
              :src="checkFileUrl(item.image)"
              alt
            >
            <img
              v-else
              src="@/assets/img/def.png"
              alt
            >
            <div class="operation is-active">
              <div
                v-if="isAuth('shop:shopTemplate:deleteMove')"
                class="operation-item"
                @click="handleDelete(item.templateId)"
              >
                <el-icon><Delete /></el-icon>{{ $t('shopFeature.template.delete') }}
              </div>
              <div
                v-if="isAuth('shop:shopTemplate:copyMove')"
                class="operation-item"
                @click="copyTemplatePage(item.templateId)"
              >
                <el-icon><DocumentCopy /></el-icon>{{ $t('shopFeature.template.copy') }}
              </div>
              <div
                v-if="isAuth('shop:shopTemplate:updateMove')"
                class="operation-item"
                @click="handleEdit(item.templateId)"
              >
                <el-icon><Edit /></el-icon>{{ $t('shopFeature.template.edit') }}
              </div>
            </div>
          </div>
          <div class="title">
            {{ item.name }}
          </div>
        </div>
      </div>
      <el-empty
        v-else
        class="empty-form"
        :description="$t('shop.noData')"
      />
    </div>
    <div style="clear: both;" />
    <el-pagination
      v-if="templateList.length"
      style="margin-right: 5%"
      :current-page="perProps.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="perProps.pageSize"
      :total="perProps.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const perProps = reactive({
  pageNum: 1, // 当前页
  pageSize: 10, // 每页显示多少条
  total: 0 // 总数
})

onMounted(() => {
  getMiniPageList()
})

const templateList = ref([]) // 列表页
// 获取微页面列表
const getMiniPageList = () => {
  const { pageNum, pageSize } = perProps
  http({
    url: http.adornUrl('/shop/shopTemplate/pageMove'),
    methods: 'get',
    params: http.adornParams({
      current: pageNum, // 当前页
      size: pageSize, // 每页显示多少条
      type: 2 // 1表示pc端，2表示移动端
    })
  }).then(({ data }) => {
    templateList.value = data.records
    perProps.total = data.total
  }).catch(() => {})
}

// 复制模板
const copyTemplatePage = (templateId) => {
  http({
    url: http.adornUrl(`/shop/shopTemplate/copyMove/${templateId}`),
    method: 'POST'
  }).then(() => {
    ElMessage.success($t('shopFeature.template.copySuccess'))
    getMiniPageList()
  }).catch(() => {})
}

const router = useRouter()
// 新建微页面
const goCreatePage = () => {
  const newPage = router.resolve({
    path: '/fitment/feature/create/edit/index',
    query: {
      type: 'add',
      template: '1'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

// 每页数
const sizeChangeHandle = (val) => {
  perProps.pageSize = val
  getMiniPageList()
}

// 当前页
const currentChangeHandle = (val) => {
  perProps.pageNum = val
  getMiniPageList()
}
// 编辑
const handleEdit = (templateId) => {
  const newPage = router.resolve({
    path: '/fitment/feature/create/edit/index',
    query: {
      templateId,
      type: 'edit',
      template: '1'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

// 删除
const handleDelete = (renovationId) => {
  ElMessageBox.confirm($t('shopFeature.list.deleteTips'), $t('shopFeature.list.tips'), {
    confirmButtonText: $t('shopFeature.edit.confirm'),
    cancelButtonText: $t('shopFeature.edit.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/shopTemplate/deleteMove/' + renovationId),
      method: 'delete'
    }).then(() => {
      const totalPage = Math.ceil((perProps.total - 1) / perProps.pageSize)
      const currentPage = perProps.pageNum > totalPage ? totalPage : perProps.pageNum
      perProps.pageNum = currentPage < 1 ? 1 : currentPage
      ElMessage.success($t('shopFeature.list.deleteSuccess'))
      getMiniPageList()
    }).catch(() => {})
  }).catch(() => {})
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
