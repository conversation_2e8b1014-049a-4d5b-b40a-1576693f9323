.page-feature-template {
  .new-page:first-child {
    margin-left: 40px;
  }

  .new-page {
    margin-left: 20px;
  }

  .main-container {
    margin-left: 30px;

    .template-item {
      width: 100%;
      position: relative;
    }

    .template-item:hover .operation {
      display: flex;
    }

    .card-item {
      width: 14.7%;
      margin: 1.28%;
      background: #FFFFFF;

      img {
        width: 100%;
        border-radius: 4px;
        height: 37.4vh;
        object-fit: fill;
      }

      .operation {
        position: absolute;
        bottom: 0;
        display: none;
        background-color: #000;
        opacity: .7;
        height: 10%;
        width: 100%;
        color: #fff;
        border-radius: 0 0 4px 4px;

        .operation-item {
          flex: 1;
          display: flex;
          position: relative;
          font-size: 14px;
          cursor: pointer;
          justify-content: center;
          align-items: center;
        }

        .operation-item:hover {
          color: #155BD4;
        }

        .operation-item:not(:last-child)::after {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          content: '';
          display: block;
          width: 1px;
          height: 30px;
          background: #fff;
        }
      }

      .title {
        color: #000000;
        font-size: 14px;
        margin-top: 16px;
        text-align: center;
        display: -webkit-box;
        word-break: break-all;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        max-width: 229.5px;
      }
    }
  }

}

.empty-form {
  margin-top: 150px;
}
