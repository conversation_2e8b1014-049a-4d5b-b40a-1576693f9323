.component-feature-template-select {
  .template-list {
    .new-page {
      margin-left: 10px;
    }

    .main-container {
      display: flex;
      flex-wrap: wrap;

      .card-item-card {
        margin: 20px 10px 20px 10px;
        cursor: pointer;
        width: 150px;
      }

      .card-item-card:not(:first-child) {
        margin-left: 10px;
      }

      .template-item-first {
        display: flex;
        border: 1px solid #EBEEF5;
        align-items: center;
        justify-content: center;
      }

      .template-item-first:hover {
        border: 1px solid #155BD4;
      }

      .template-item {
        width: 153px;
        height: 204px;
        border-radius: 4px;
      }

      .template-item:hover img {
        border: 1px solid #155BD4;
      }

      .card-item-first {
        width: 150px;
        position: relative;
      }

      .card-item {
        float: left;
        background: #FFFFFF;

        img {
          width: 100%;
          height: 204px;
          border-radius: 4px;
        }

        .title-first {
          text-align: center;
          width: 100%;
        }

        .title {
          color: #000000;
          font-size: 14px;
          margin-top: 16px;
          text-align: center;
          display: -webkit-box;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          max-width: 153px;
        }
      }
    }

  }

  .empty-form {
    margin-top: 150px;
  }

}
