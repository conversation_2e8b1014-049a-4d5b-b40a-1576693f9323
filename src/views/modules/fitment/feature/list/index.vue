<template>
  <div class="app-container-10 container-f0f2f5 app-main feature-list-container page-feature-list">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="pageName"
            :label="$t('shopFeature.list.pageName')"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.pageName"
              type="text"
              clearable
              :placeholder="$t('shopFeature.list.pageName')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch()"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="feature-list-content container-fff app-container-15 app-ele-border-radius-0">
      <div
        v-if="isAuth('shop:shopRenovation:saveMove')"
        class="default-btn primary-btn"
        @click="newPageSelect"
      >
        {{ $t('shopFeature.list.newMicroPage') }}
      </div>
      <!--微页面列表-->
      <div class="table-con content-box">
        <el-table
          :data="miniPageList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          :row-style="{ height:'70px'}"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            align="left"
            :label="$t('shopFeature.list.pageName')"
          >
            <template #default="scope">
              <div>
                {{ scope.row.name }}
                <el-tag
                  v-if="scope.row.homeStatus===1"
                  effect="dark"
                  size="small"
                  style="margin-left: 10px;"
                >
                  {{ $t('shopFeature.list.shopHomePage') }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="createTime"
            :label="$t('shopFeature.list.createTime')"
          />
          <el-table-column
            align="left"
            prop="updateTime"
            :label="$t('shopFeature.list.updateTime')"
          />
          <el-table-column
            align="center"
            :label="$t('shopFeature.list.oper')"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  class="default-btn text-btn"
                  @click="handleSelect(scope.row)"
                >
                  {{ $t('shopFeature.list.view') }}
                </div>
                <div
                  v-if="isAuth('shop:shopRenovation:updateMove')"
                  class="default-btn text-btn"
                  @click="handleEdit(scope.row)"
                >
                  {{ $t('shopFeature.list.edit') }}
                </div>
                <div
                  v-if="isAuth('shop:shopRenovation:deleteMove')"
                  class="default-btn text-btn"
                  @click="handleDelete(scope.row)"
                >
                  {{ $t('shopFeature.list.delete') }}
                </div>
                <div
                  v-if="isAuth('shop:shopRenovation:updateHomeMove')"
                  class="default-btn text-btn update-home-page"
                  @click="handleSetHomePage(scope.row.renovationId)"
                >
                  {{ scope.row.homeStatus!=1 ? $t('shopFeature.list.setHomePage'): $t('shopFeature.list.cancelHomePage') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
    </div>

    <template-select
      v-if="isShowtemplateSelect"
      ref="templateSelectRef"
      @select-template="goCreatePage"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TemplateSelect from '../template/components/template-select/index.vue'

const searchForm = reactive({
  pageName: ''
})

onMounted(() => {
  getMiniPagesList()
})

const pageSize = ref(10)
const pageIndex = ref(1)
// 每页数
const sizeChangeHandle = (val) => {
  pageSize.value = val
  pageIndex.value = 1
  getMiniPagesList()
}

// 当前页
const currentChangeHandle = (val) => {
  pageIndex.value = val
  getMiniPagesList()
}

const totalPage = ref(0)
const miniPageList = ref([])
/**
 * 获取微页面列表
 */
const getMiniPagesList = () => {
  if (pageIndex.value) {
    const size = Math.ceil(totalPage.value / pageSize.value)
    pageIndex.value = (pageIndex.value > size ? size : pageIndex.value) || 1
  }
  http({
    url: http.adornUrl('/shop/shopRenovation/pageMove'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageIndex.value,
          size: pageSize.value,
          renovationType: 2, // 1表示pc端，2表示移动端
          name: searchForm.pageName
        }
      )
    )
  }).then(({ data }) => {
    miniPageList.value = data.records
    totalPage.value = data.total
  }).catch(() => {})
}

const isShowtemplateSelect = ref(false)
const templateSelectRef = ref(null)
// 选择模板
const newPageSelect = () => {
  isShowtemplateSelect.value = true
  nextTick(() => {
    templateSelectRef.value.init()
  })
}

const router = useRouter()
/** 跳转编辑页面 */
const goCreatePage = (id) => {
  const obj = {
    type: 'add',
    template: '0'
  }
  if (id > -1) {
    obj.templateId = id
  }
  const newPage = router.resolve({
    path: '/fitment/feature/create/edit/index',
    query: obj
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

/** 点击编辑 */
const handleEdit = (item) => {
  const newPage = router.resolve({
    path: '/fitment/feature/create/edit/index',
    query: {
      renovationId: item.renovationId,
      type: 'edit',
      template: '0'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

/** 点击删除 */
const handleDelete = (item) => {
  ElMessageBox.confirm($t('shopFeature.list.deleteTips'), $t('shopFeature.list.tips'), {
    confirmButtonText: $t('shopFeature.edit.confirm'),
    cancelButtonText: $t('shopFeature.edit.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/shopRenovation/deleteMove/' + item.renovationId),
      method: 'delete'
    }).then(() => {
      totalPage.value = totalPage.value - 1
      const totalPagePar = Math.ceil((totalPage.value - 1) / pageSize.value)
      const currentPage = pageIndex.value > totalPagePar ? totalPagePar : pageIndex.value
      pageIndex.value = currentPage < 1 ? 1 : currentPage
      ElMessage.success($t('shopFeature.list.deleteSuccess'))
      getMiniPagesList()
    })
  }).catch(() => {})
}

/**
 * 设置主页
 */
const handleSetHomePage = (renovationId) => {
  http({
    url: http.adornUrl('/shop/shopRenovation/updateHomePageMove/' + renovationId),
    method: 'put'
  }).then(() => {
    ElMessage.success($t('shopFeature.list.operSuccess'))
    getMiniPagesList()
  }).catch(() => {})
}

// 查看
const handleSelect = (item) => {
  const newPage = router.resolve({
    path: '/fitment/feature/create/edit/index',
    query: {
      renovationId: item.renovationId,
      type: 'detail'
    }
  })
  window.open(newPage.href, '_blank', 'noopener,noreferrer')
}

const searchFormRef = ref(null)
/**
 * 重置表单
 */
const onResetSearch = () => {
  searchFormRef.value.resetFields()
  getMiniPagesList()
}

const onSearch = () => {
  pageIndex.value = 1
  pageSize.value = 10
  getMiniPagesList()
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
