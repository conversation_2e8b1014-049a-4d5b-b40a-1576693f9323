<template>
  <div class="app-container component-popup-a">
    <!-- 搜索相关区域 -->
    <div class="search-bar">
      <el-form
        :inline="true"
        :model="searchForm"
        class="demo-form-inline"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item :label="$t('popupAd.popupName')">
            <el-input
              v-model="searchForm.popupName"
              :placeholder="$t('popupAd.popupName')"
              clearable
            />
          </el-form-item>
          <!-- status 表示状态  1.未开始 2.投放中 3.已结束 -->
          <el-form-item :label="$t('popupAd.popupState')">
            <el-select
              v-model="searchForm.status"
              :placeholder="$t('popupAd.popupState')"
              clearable
            >
              <el-option
                :label="$t('popupAd.notStarted')"
                value="1"
              />
              <el-option
                :label="$t('popupAd.release')"
                value="2"
              />
              <el-option
                :label="$t('popupAd.finished')"
                value="3"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="onSearch()"
            >
              {{ $t('crud.searchBtn') }}
            </el-button>
            <el-button
              @click="onReset()"
            >
              {{ $t('user.reset') }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 列表相关区域 -->
    <div class="main-container">
      <div class="operation-bar">
        <el-button
          v-if="isAuth('shop:popupAd:save')"
          type="primary"
          class="filter-item"
          @click="onAddOrUpdate()"
        >
          {{ $t('popupAd.addPopup') }}
        </el-button>
      </div>
      <div class="table-con">
        <el-table
          v-loading="pageLoading"
          :data="dataList"
          row-key="popupId"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          style="width: 100%;"
        >
          <!-- 名称 -->
          <el-table-column
            :label="$t('popupAd.popupName')"
            prop="popupName"
            align="center"
            width="300"
          />
          <!-- 开始时间 -->
          <el-table-column
            :label="$t('time.startTime')"
            prop="startTime"
            align="center"
          />
          <!-- 结束时间 -->
          <el-table-column
            :label="$t('time.endTime')"
            prop="endTime"
            align="center"
          />
          <!-- 状态 -->
          <el-table-column
            :label="$t('popupAd.popupState')"
            prop="status"
            align="center"
          >
            <template #default="{row}">
              <span>{{ ['',$t('popupAd.notStarted'),$t('popupAd.release'),$t('popupAd.finished')][row.status] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('crud.menu')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="{row}">
              <el-button
                v-if="isAuth('shop:popupAd:update') && row.status !== 3"
                link
                type="primary"
                @click="onAddOrUpdate(row.popupId,1)"
              >
                {{ $t('crud.editBtn') }}
              </el-button>
              <el-button
                v-if="isAuth('shop:popupAd:view') && row.status === 3"
                link
                type="primary"
                @click="onAddOrUpdate(row.popupId,2)"
              >
                {{ $t('crud.viewBtn') }}
              </el-button>
              <el-button
                v-if="isAuth('shop:popupAd:invalid') && row.status !== 3"
                link
                type="primary"
                @click="onSetInvalid(row.popupId)"
              >
                {{ $t('groups.invalidActivity') }}
              </el-button>
              <el-button
                v-if="isAuth('shop:popupAd:delete')"
                link
                type="primary"
                @click="onDelete(row.popupId)"
              >
                {{ $t('crud.delBtn') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页条 -->
    <div class="pagination-box">
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import { ElMessageBox, ElMessage } from 'element-plus'

// 查询的参数
const searchForm = reactive({
  popupName: null,
  status: null
})
let tempSearchForm = null
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})

onMounted(() => {
  getDataList()
})

const dataList = ref([])
const pageLoading = ref(true)
const getDataList = (newData = false) => {
  pageLoading.value = true
  if (newData || !tempSearchForm) {
    tempSearchForm = { ...searchForm }
  }
  http({
    url: http.adornUrl('/costPerPopup/page'),
    method: 'get',
    params: http.adornParams({
      ...tempSearchForm,
      current: page.currentPage,
      size: page.pageSize
    })
  }).then(({ data }) => {
    pageLoading.value = false
    dataList.value = data.records
    page.total = data.total
  }).catch(() => {
    pageLoading.value = false
  })
}

const router = useRouter()
const onAddOrUpdate = (popupId, pageType) => {
  router.push({
    path: '/fitment/feature/popup-ad/add-or-update',
    query: {
      popupId,
      pageType
    }
  })
}

// 失效弹窗
const onSetInvalid = (popupId) => {
  ElMessageBox.confirm($t($t('popupAd.invalidConfirmTips')), $t('text.tips'), {
    confirmButtonText: $t('user.confirm'),
    cancelButtonText: $t('user.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl(`/costPerPopup/invalid?popupId=${popupId}`),
      method: 'put'
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => getDataList()
      })
    })
  }).catch()
}

// 删除
const onDelete = (popupId) => {
  ElMessageBox.confirm($t('admin.isDeleOper'), $t('text.tips'), {
    confirmButtonText: $t('user.confirm'),
    cancelButtonText: $t('user.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/costPerPopup'),
      method: 'delete',
      params: {
        popupId
      }
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          if (dataList.value.length === 1 && page.currentPage > 1) {
            page.currentPage--
          }
          getDataList()
        }
      })
    })
  }).catch()
}

// 查询
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList()
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList()
}

const onSearch = () => {
  page.currentPage = 1
  getDataList(true)
}
const onReset = () => {
  searchForm.popupName = null
  searchForm.status = null
}

</script>

<style lang="scss" scoped>
.component-popup-a {
  .pagination-box {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
