<template>
  <el-dialog
    v-model="visible"
    :title="!form.voucherId ? $t('voucher.add') : $t('voucher.update')"
    destroy-on-close
    :close-on-click-modal="false"
    style="width: 600px;"
  >
    <el-form
      ref="dataFormRef"
      :model="form"
      label-width="80px"
      :rules="voucherNameRules"
      @submit.prevent
      @keyup.enter="onSubmit()"
    >
      <!-- 卡券名称 -->
      <el-form-item
        prop="name"
        :label="$t('product.voucherName')"
        :required="true"
      >
        <el-input
          v-model.trim="form.name"
          maxlength="20"
          show-word-limit
          style="width: 300px"
          :placeholder="$t('voucher.inputTips')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn"
          @click="visible = false"
        >
          {{ $t("crud.filter.cancelBtn") }}
        </div>
        <div
          class="default-btn primary-btn"
          @click="onSubmit()"
        >
          {{ $t("crud.filter.submitBtn") }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { Debounce } from '../../../../utils/debounce.js'

const emit = defineEmits(['refreshDataList'])

// 表单
const form = reactive({
  voucherId: null,
  name: ''
})

/**
 * 检查 是否包含特殊字符 :;
 * @param rule
 * @param value
 * @param callback
 */
const validateWord = (rule, value, callback) => {
  const reg = /[\\;\\:\\；\\：]/g
  if (reg.test(value)) {
    callback(new Error($t('product.voucher') + $t('product.specialWordSymbolTips') + ':;'))
  }
  callback()
}

/**
 * 规格名称 校验
 */
const voucherNameRules = reactive({
  name: [
    { required: true, message: $t('product.voucherNameNoNull'), trigger: 'blur' },
    { validator: validateWord, trigger: 'blur' }
  ]
})

const visible = ref(false)
const init = (id = null) => {
  if (id) {
    form.voucherId = id
    http({
      url: http.adornUrl(`/multishop/voucher/info/${form.voucherId}`),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      form.name = data.name
      visible.value = true
    })
  } else {
    form.name = ''
    form.voucherId = null
    visible.value = true
  }
}

const dataFormRef = ref(null)
/**
 * 表单提交
 */
const onSubmit = Debounce(() => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      http({
        url: http.adornUrl('/multishop/voucher'),
        method: form.voucherId ? 'put' : 'post',
        data: http.adornData(form)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }
  })
}, 1500)

defineExpose({
  init
})

</script>
