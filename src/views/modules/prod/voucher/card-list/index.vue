<template>
  <div class="mod-prod page-voucher-card-list">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        inline
        :model="searchForm"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="name"
            :label="$t('order.number')"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.orderNumber"
              type="text"
              clearable
              :placeholder="$t('order.number')"
            />
          </el-form-item>
          <el-form-item
            prop="dateRange"
            :label="$t('voucher.exportDate') + ':' "
          >
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              clearable
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
            />
            <div
              class="default-btn"
              style="margin-left: 20px;"
              :class="{ 'is-active': timeActive === 1 }"
              @click="setDateRange(1)"
            >
              {{
                $t("time.t")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 2 }"
              @click="setDateRange(2)"
            >
              {{
                $t("time.y")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 3 }"
              @click="setDateRange(3)"
            >
              {{
                $t("time.n")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 4 }"
              @click="setDateRange(4)"
            >
              {{
                $t("temp.m")
              }}
            </div>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="handleSearch"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container main">
      <div class="operation-bar">
        <el-checkbox
          v-model="selectAll"
          :disabled="tableList.length===0"
          class="all-check-btn"
          @change="onSelectAll"
        >
          {{ $t('publics.selectAll') }}
        </el-checkbox>
        <div style="display:inline-block;">
          <el-dropdown
            class="batch-setting-dropdown"
            :hide-on-click="true"
            trigger="click"
            @command="handleBatchSetting"
            @visible-change="handleVisibleChange"
          >
            <div
              :class="[showBatchSetting? 'active' : '','default-btn batch-setting-btn']"
            >
              {{ $t('publics.batchSetting') }}
              <i class="arrow" />
            </div>
            <!-- 批量设置功能列表 -->
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="isAuth('multishop:voucherItem:import')"
                  command="1"
                >
                  {{ $t('stock.batchImport') }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="isAuth('multishop:voucherItem:export')"
                  command="0"
                >
                  {{ $t('stock.batchExport') }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="isAuth('multishop:voucherItem:batchDelete')"
                  command="-1"
                >
                  {{ $t('publics.batchDelete') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="table-con spec-table content">
        <!-- 导航 -->
        <div class="order-status-nav clearfix">
          <ul class="nav-list clearfix">
            <li
              :class="['nav-item', activeName === 99 ? 'selected' : '']"
              data-sts="99"
              @click="selectNav($event)"
            >
              {{ $t('time.a') }}
            </li>
            <li
              :class="['nav-item', activeName === 1 ? 'selected' : '']"
              data-sts="1"
              @click="selectNav($event)"
            >
              {{ $t('product.startUsing') }}
            </li>
            <li
              :class="['nav-item', activeName === 2 ? 'selected' : '']"
              data-sts="2"
              @click="selectNav($event)"
            >
              {{ $t('product.notEnabled') }}
            </li>
            <li
              :class="['nav-item', activeName === 0 ? 'selected' : '']"
              data-sts="0"
              @click="selectNav($event)"
            >
              {{ $t("product.expired") }}
            </li>
          </ul>
          <ul class="nav-right" />
        </div>
        <el-table
          ref="multipTableRef"
          :data="tableList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
          @selection-change="onSelectSome"
        >
          <el-table-column
            type="selection"
            width="55"
          />
          <el-table-column
            prop="cardNumber"
            :label="$t('voucher.cardNum')"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="cardPwd"
            :label="$t('voucher.cardPWD')"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="couponCode"
            :label="$t('voucher.cardCode')"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="status"
            :label="$t('product.status')"
            align="center"
            show-overflow-tooltip
          >
            <!-- 0：已失效 1已发送 2未发送 -->
            <template #default="scope">
              <span
                v-if="scope.row.status === 0"
              >
                {{ $t('product.expired') }}
              </span>
              <span
                v-else
              >
                {{ scope.row.status === 1 ? $t('product.startUsing') : $t('product.notEnabled') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="importTime"
            :label="$t('voucher.exportDate')"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="expirationTime"
            :label="$t('coupon.endTime')"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="orderNumber"
            :label="$t('chat.orderNumber')"
            align="center"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.orderNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            fixed="right"
            width="200"
            :label="$t('publics.operating')"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('multishop:voucherItem:delete')"
                  class="default-btn text-btn"
                  @click.stop="onDelete(scope.row.voucherItemId)"
                >
                  {{ $t('text.delBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        style="margin-top: 12px; text-align: right;"
        :current-page="perProps.page"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="perProps.perPage"
        layout="->, total, sizes, prev, pager, next, jumper"
        :total="perProps.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>

    <!-- 导出入库明细 -->
    <el-dialog
      v-model="exportVisible"
      :title="$t('product.exportVoucherDetails')"
      width="600px"
    >
      <el-radio
        v-model="exportRadio"
        :label="1"
      >
        {{ $t('product.exportVoucherOfSearch') }}
      </el-radio>
      <el-radio
        v-model="exportRadio"
        :label="2"
      >
        {{ $t('product.exportVoucherOfSelect') }}
      </el-radio>
      <template #footer>
        <div
          class="default-btn"
          @click="exportVisible = false"
        >
          {{ $t('crud.filter.cancelBtn') }}
        </div>
        <div
          class="default-btn primary-btn"
          @click="confirmExport"
        >
          {{ $t('crud.filter.submitBtn') }}
        </div>
      </template>
    </el-dialog>

    <voucher-item-import
      v-if="uploadVisible"
      ref="voucherUploadRef"
      :voucher-id="searchForm.voucherId"
      @refresh-data-list="getWaitingExcel"
    />
  </div>
</template>
<script setup>
import { isAuth } from '@/utils/index.js'
import VoucherItemImport from '../components/voucher-item-import.vue'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import { Debounce } from '@/utils/debounce'

const visible = ref(false)
const tableList = ref([]) // 商品列表

const perProps = reactive({ // 分页
  perPage: 10,
  page: 1,
  total: 0
})

const searchForm = reactive({
  voucherId: '',
  cardNumber: '',
  orderNumber: null
})
const route = useRoute()
onMounted(() => {
  searchForm.voucherId = route.query.voucherId
  getDataList()
})

const getDataList = () => {
  const { page, perPage } = perProps
  http({
    url: http.adornUrl('/multishop/voucherItem/page'),
    method: 'get',
    params: http.adornParams({
      voucherId: searchForm.voucherId,
      orderNumber: searchForm.orderNumber || undefined,
      current: page,
      size: perPage,
      status: status.value,
      startTime: dateRange.value === null ? null : dateRange.value[0], // 开始时间
      endTime: dateRange.value === null ? null : dateRange.value[1] // 结束时间
    })
  }).then(({ data }) => {
    tableList.value = data.records
    perProps.total = data.total
    visible.value = true
  }).catch(() => {
    visible.value = true
  })
}

// 分页每页多少条
const onPageSizeChange = (val) => {
  perProps.perPage = val
  getDataList()
}

// 分页当前第几页
const onPageChange = (val) => {
  perProps.page = val
  getDataList()
}

// 搜索
const handleSearch = () => {
  getDataList()
}

const dateRange = ref([])
const timeActive = ref(null)
/**
 * 根据选项设置时间
 * 1:今天 2:昨天 3: 近七天 4:近30天 5:近60天
 */
const setDateRange = (val) => {
  timeActive.value = val
  let startDay = null
  let endDay = null
  if (val === 1) {
    startDay = 0
    endDay = 0
  } else if (val === 2) {
    startDay = -1
    endDay = -1
  } else if (val === 3) {
    startDay = -7
    endDay = -1
  } else if (val === 4) {
    startDay = -30
    endDay = -1
  } else {
    return
  }
  // 开始时间
  const startTime = moment().add(startDay, 'days').startOf('days').format('LL')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days').format('LL')
  dateRange.value = [startTime, endTime]
}

const activeName = ref(99)
const status = ref(null)
/**
 * 导航选择状态
 * 状态 99：全部 0：正常 1：已发放 2：已失效
 */
const selectNav = (e) => {
  const sts = e.currentTarget.dataset.sts
  activeName.value = Number(sts)
  status.value = activeName.value === 99 ? null : parseInt(sts)
  perProps.page = 1
  getDataList(perProps)
}

// 打开导入弹窗
const voucherUploadRef = ref(null)
const uploadVisible = ref(false)
const getUpload = () => {
  uploadVisible.value = true
  nextTick(() => {
    voucherUploadRef.value?.init()
  })
}

// 删除
const onDelete = (id) => {
  ElMessageBox.confirm(`${$t('sys.makeSure')}[${id ? $t('text.delBtn') : $t('sys.batchDelete')}]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  })
    .then(() => {
      http({
        url: http.adornUrl('/multishop/voucherItem'),
        method: 'delete',
        data: http.adornData([id], false)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            refreshChange()
          }
        })
      })
    }).catch(() => { })
}

// 刷新回调用
const refreshChange = () => {
  getDataList(perProps)
}

const getWaitingExcel = () => {
  getDataList()
}

const selectAll = ref(false) // 是否全选
const onSelectSome = (val) => {
  dataListSelections.value = val
  selectAll.value = val.length === tableList.value.length
  if (tableList.value.length === 0) {
    selectAll.value = false
  }
}

// 全选按钮
const multipTableRef = ref(null)
const onSelectAll = () => {
  multipTableRef.value?.toggleAllSelection()
}

const showBatchSetting = ref(false)
/**
 * 批量设置菜单显隐
 */
const handleVisibleChange = (val) => {
  showBatchSetting.value = val
}

const dataListSelections = ref([])
/**
 * 批量设置
 */
const handleBatchSetting = (command) => {
  const commands = Number(command)
  if (commands === 1) {
    getUpload()
  } else if (commands === 0) {
    exportVoucherItem()
  } else {
    if (!dataListSelections.value.length) {
      ElMessage.warning($t('marketing.pleaseSelectAProduct'))
      return
    }
    batchDeleteHandle()
  }
}

// 批量删除
const batchDeleteHandle = () => {
  const ids = dataListSelections.value.map(item => {
    return item.voucherItemId
  })
  ElMessageBox.confirm(`${$t('sys.makeSure')}【${$t('sys.batchDelete')}】${$t('publics.operating')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/multishop/voucherItem'),
      method: 'delete',
      data: http.adornData(ids, false)
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          refreshChange()
        }
      })
    })
  }).catch(() => { })
}

// 重置表单
const searchFormRef = ref(null)
const onResetSearch = () => {
  dateRange.value = []
  searchForm.orderNumber = null
}

const exportVoucherItem = () => {
  exportVisible.value = true
}

/**
 * 根据当前搜索条件批量导出
 */
const getExportExcel = (params) => {
  const loading = ElLoading.service({
    lock: true,
    target: '.mod-supplier-supplier',
    customClass: 'export-load',
    background: 'transparent',
    text: $t('formData.exportIng')
  })
  exportVisible.value = false
  http({
    url: http.adornUrl('/multishop/voucherItem/exportExcel'),
    method: 'get',
    params: http.adornParams(params),
    responseType: 'blob' // 解决文件下载乱码问题
  }).then(({ data }) => {
    loading.close()
    const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
    const fileName = $t('product.voucherItemExcel')
    const elink = document.createElement('a')
    if ('download' in elink) { // 非IE下载
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  }).catch((e) => {
    loading.close()
  })
}

const exportVisible = ref(false)
const exportRadio = ref(1) //  1 按搜索条件导出 2 按选择项导出
/**
 * 确定导出
 */
const confirmExport = Debounce(() => {
  if (exportRadio.value !== 1 && exportRadio.value !== 2) {
    ElMessage({
      message: $t('stock.exportRadioEmptyTips'),
      type: 'error',
      duration: 1500
    })
    return
  }
  // 准备参数
  const params = {
    voucherId: searchForm.voucherId,
    status: status.value,
    startTime: null, // 开始时间
    endTime: null, // 结束时间
    voucherItemIds: null
  }
  if (exportRadio.value === 1) {
    // 导出模式为按搜索条件导出
    if (perProps.total === 0) {
      ElMessage({
        message: $t('stock.exportStockLogSearchEmptyTips'),
        type: 'error',
        duration: 1500
      })
      return
    }
    params.startTime = dateRange.value === null ? null : dateRange.value[0]
    params.endTime = dateRange.value === null ? null : dateRange.value[1]
  } else {
    if (dataListSelections.value.length === 0) {
      ElMessage({
        message: $t('stock.exportStockLogSelectEmptyTips'),
        type: 'error',
        duration: 1500
      })
      return
    }
    params.voucherItemIds = dataListSelections.value.map(item => {
      return item.voucherItemId
    })
  }
  getExportExcel(params)
}, 1500)

</script>

<style scoped lang="scss">
@use "index";
</style>
