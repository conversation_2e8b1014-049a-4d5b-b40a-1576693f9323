<template>
  <div class="mod-prod">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"

        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="name"
            :label="$t('product.voucherName')"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.name"
              type="text"
              clearable
              :placeholder="$t('product.voucherName')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('prod:spec:save')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <div class="table-con spec-table">
        <el-table
          ref="specListTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
          @cell-mouse-enter="cellMouseEnter"
          @cell-mouse-leave="cellMouseLeave"
        >
          <el-table-column
            align="left"
            prop="name"
            :label="$t('product.voucherName')"
          >
            <template #default="scope">
              <div class="table-cell-text voucher-name-box">
                <div>{{ scope.row.name }}</div>
                <div
                  v-show="nowHoverVoucherId === scope.row.voucherId"
                  class="edit-box"
                  @click.stop="onAddOrUpdate(scope.row)"
                >
                  <el-icon><Edit /></el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="prodName"
            :label="$t('product.relatedProducts')"
          >
            <template #default="scope">
              <div class="table-cell-text">
                {{ scope.row.prodName || '-' }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            align="left"
            prop="startUsingNum"
            :label="$t('product.startUsing')"
          >
            <template #default="scope">
              <div class="table-cell-text">
                {{ scope.row.startUsingNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="notEnabledNum"
          >
            <template #header>
              <div>
                {{ $t('product.notEnabled') }}
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="$t('product.notEnabledTips')"
                  placement="top"
                >
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div class="table-cell-text">
                {{ scope.row.notEnabledNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="expiredNum"
            :label="$t('product.expired')"
          >
            <template #default="scope">
              <div class="table-cell-text">
                {{ scope.row.expiredNum }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            fixed="right"
            width="200"
            :label="$t('publics.operating')"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  class="default-btn text-btn"
                  @click.stop="openDetailList(scope.row)"
                >
                  {{ $t('voucher.voucherDetail') }}
                </div>
                <div
                  v-if="isAuth('prod:spec:delete')"
                  class="default-btn text-btn"
                  @click.stop="onDelete(scope.row.voucherId)"
                >
                  {{ $t('text.delBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddOrUpdate from './add-or-update.vue'

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})

onMounted(() => {
  getDataList()
})

// 获取数据列表
const searchForm = reactive({
  name: ''
})
let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
const getDataList = (pageParam, newData = false) => {
  if (page) {
    const size = Math.ceil(page.total / page.pageSize)
    page.currentPage = (page.currentPage > size ? size : page.currentPage) || 1
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/multishop/voucher/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize,
          name: searchForm.name
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 删除
const onDelete = (id) => {
  ElMessageBox.confirm(`${$t('sys.makeSure')}[${id ? $t('text.delBtn') : $t('sys.batchDelete')}]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  })
    .then(() => {
      http({
        url: http.adornUrl(`/multishop/voucher/${id}`),
        method: 'delete',
        data: http.adornData()
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            refreshChange()
          }
        })
      })
    }).catch(() => { })
}

// 刷新回调用
const refreshChange = () => {
  getDataList(page)
}

const onSearch = (newData = false) => {
  page.currentPage = 1
  page.pageSize = 10
  getDataList(page, newData)
}

// 重置表单
const searchFormRef = ref(null)
const onResetSearch = () => {
  searchFormRef.value.resetFields()
  getDataList()
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList()
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList()
}

const addOrUpdateRef = ref(null)
const addOrUpdateVisible = ref(false)
const onAddOrUpdate = (val) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(val?.voucherId || null)
  })
}

const router = useRouter()
const openDetailList = (val) => {
  router.push({
    path: '/prod/voucher/card-list/index',
    query: { voucherId: val.voucherId }
  })
}

const nowHoverVoucherId = ref(null)
const cellMouseEnter = (row) => {
  nowHoverVoucherId.value = row.voucherId
}

const cellMouseLeave = () => {
  nowHoverVoucherId.value = null
}

</script>
<style scoped lang="scss">
.voucher-name-box {
  display: flex !important;
  align-items: center;
  .edit-box {
    margin-left: 3px;
    padding-top: 3px;
    &:hover {
      cursor: pointer;
    }
  }
}
</style>
