<template>
  <el-dialog
    v-model="visible"
    :title="$t('product.relatedItems')"
    :before-close="closeDialog"
    width="950px"
  >
    <div
      class="default-btn primary-btn"
      @click="selectSingleProds()"
    >
      {{
        $t("product.chooSingPro")
      }}
    </div>
    <div class="main-container">
      <div class="table-con">
        <el-table
          :data="retailProdTable"
          :header-cell-style="{height: '42px', background: '#F6F7FA', color:'#666666','font-weight': '500'}"
          :cell-style="{height: '64px', padding: '8px 0', color:'#000'}"
          style="width: 100%"
          height="420"
        >
          <el-table-column
            prop="name"
            :label="$t('product.itemName')"
            width="180"
          >
            <template #default="scope">
              <div class="table-cell-text">
                {{ scope.row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="spec"
            :label="$t('product.singleProdSpe')"
            width="180"
          >
            <template #default="scope">
              <div class="table-cell-text">
                {{ scope.row.spec }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="retailNums"
            :label="$t('product.quantityInCom')"
          >
            <template #default="scope">
              <label>
                <input
                  v-model.number="scope.row.retailNums"
                  type="number"
                  :max="1000000"
                  :min="1"
                  :step="1"
                  class="tag-input-width"
                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                  @keyup="
                    scope.row.retailNums = String(scope.row.retailNums).match(/[^0-9]/) ? 0 : scope.row.retailNums
                  "
                  @blur="countTotal(scope.row, scope.$index)"
                >
              </label>
            </template>
          </el-table-column>
          <el-table-column
            prop="costPrice"
            :label="$t('product.singleProd')"
          />
          <el-table-column
            prop="total"
            :label="$t('product.total')"
          />
          <el-table-column
            prop="stocks"
            :label="$t('product.singleProdInve')"
          />
        </el-table>
      </div>
    </div>
    <template #footer>
      <div
        class="dialog-footer"
      >
        <div
          class="default-btn"
          @click="closeDialog"
        >
          {{
            $t("crud.filter.cancelBtn")
          }}
        </div>
        <div
          class="default-btn primary-btn"
          @click="confirm"
        >
          {{ $t("crud.filter.submitBtn") }}
        </div>
      </div>
    </template>
    <!-- 单品选择弹窗  测试完之后添加这个链接  dataUrl='/group/prod/getNotGroupProdPage'-->
    <single-prods-select
      v-if="sprodVisible"
      ref="singleProdsSelectRef"
      @refresh-select-single-prods="getRetailTable"
    />
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import Big from 'big.js'

const emit = defineEmits(['refreshSelectSingleProds'])

let oriIndex = null
const visible = ref(false)
const retailProdTable = ref([])
const init = (skuSingleProds, index) => {
  retailProdTable.value = []
  oriIndex = index
  if (skuSingleProds) {
    retailProdTable.value = JSON.parse(JSON.stringify(skuSingleProds))
  }
  visible.value = true
}
const getRetailTable = (prods) => {
  if (prods) {
    prods.forEach(item => {
      const total = new Big(item.costPrice)
      item.total = total.times(item.retailNums).toFixed(2)
    })
    retailProdTable.value = prods
  }
}

const singleProdsSelectRef = ref(null)
const sprodVisible = ref(false)
const selectSingleProds = () => {
  sprodVisible.value = true
  nextTick(() => {
    singleProdsSelectRef.value.init(retailProdTable.value)
  })
}
const countTotal = (rowData, index) => {
  const total = new Big(rowData.costPrice)
  if (!/^[0-9]\d*$/.test(rowData.retailNums) || parseInt(rowData.retailNums) <= 0) {
    ElMessage.error($t('retailProd.tipError'))
    retailProdTable.value[index].retailNums = 1
    return
  }
  rowData.retailNums = parseInt(rowData.retailNums)
  rowData.total = total.times(rowData.retailNums).toFixed(2)
}
// 关闭弹窗
const closeDialog = () => {
  visible.value = false
  retailProdTable.value = []
}
const confirm = () => {
  if (retailProdTable.value === null || retailProdTable.value === undefined || retailProdTable.value.length <= 0) {
    return ElMessage.error($t('retailProd.tip2Error'))
  }
  for (let i = 0; i < retailProdTable.value.length; i++) {
    if (!/^[0-9]\d*$/.test(retailProdTable.value[i].retailNums) || parseInt(retailProdTable.value[i].retailNums) === 0) {
      return ElMessage.error($t('retailProd.tipError'))
    }
  }
  emit('refreshSelectSingleProds', retailProdTable.value, oriIndex)
  visible.value = false
}

defineExpose({
  init
})

</script>

<style lang="scss" scoped>
.main-container {
  .table-con {
    padding-bottom: 0;
  }
  :deep(.el-table__body-wrapper::-webkit-scrollbar) {
    width: 6px;
    height: 439px;
    background: #F7F8FA;
    opacity: 1;
    border-radius: 4px;
  }
  // 滚动条的滑块
  :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
    width: 6px;
    height: 150px;
    background: #E9ECF3;
    opacity: 1;
    border-radius: 4px;
  }
}

// 表格输入框
.tag-input-width {
  width: 100%;
  padding-left: 5px;
  padding-right: 0;
  border: 1px solid #DCDCDC;
  border-radius: 2px;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
  &:focus {
    outline: 0;
  }
}
</style>
