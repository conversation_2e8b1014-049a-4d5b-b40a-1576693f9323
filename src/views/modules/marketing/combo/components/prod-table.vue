<template>
  <!-- 商品信息 -->
  <div class="component-combo-prod-table">
    <div
      class="default-btn"
      :class="{ 'disabled-btn': productItems.length === limit || pageType === 2 }"
      @click="selectProdHandle()"
    >
      {{ $t('shop.addProd') }}
    </div>
    <div class="count-desc-text">
      {{ type === 1 ? $t('combo.mainProdTips') : $t('combo.matchingProdTips') }}
    </div>
    <div
      v-if="!verifyFlag"
      class="error-tips"
    >
      {{ type === 1 ? $t('combo.mainProdErrorTips') : $t('combo.matchingProdErrorTips') }}
    </div>
    <div
      v-if="productItems && productItems.length > 0"
      class="prodItem-table"
    >
      <el-table
        :data="productItems"
        header-cell-class-name="table-header"
        row-class-name="table-row"
        :style="tableWidth"
      >
        <el-table-column
          :label="$t('group.prodInfo')"
          prop="reason"
          fixed="left"
          align="center"
          class-name="prod-info-container-cell"
          width="320px"
        >
          <template #default="scope">
            <div class="prod-info-container">
              <div class="prod-image">
                <ImgShow :src="scope.row.pic" />
              </div>
              <div class="prod-name">
                <div class="prod-name-txt">
                  {{ scope.row.prodName }}
                </div>
                <div
                  v-if="showDistributionTips(scope.row)"
                  class="distribution-tips"
                >
                  {{ $t('text.tips') + '：' + $t('combo.mainAndMatchProdNotDvyTips') }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('combo.participateSpec')"
          align="center"
          prop="skuName"
          width="200px"
        >
          <template #default="scope">
            <div
              v-for="(sku) in scope.row.skuList"
              :key="sku.skuId"
              class="items name"
            >
              <span class="sku-name">
                {{ sku.skuName || '-' }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('product.price')"
          align="center"
          prop="skuName"
          width="200px"
        >
          <template #default="scope">
            <div
              v-for="(sku) in scope.row.skuList"
              :key="sku.skuId"
            >
              <span class="sku-name">
                {{ sku.price }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="matchingPrice"
          :label="$t('combo.matchingPrice')"
          align="center"
          width="160px"
        >
          <template #default="scope">
            <div
              v-for="(sku, skuIndex) in scope.row.skuList"
              :key="sku.skuId"
              class="items name"
            >
              <div class="matching-price">
                <input
                  v-model.number="sku.matchingPrice"
                  :disabled="pageType === 2"
                  type="number"
                  :precision="2"
                  :max="sku.price"
                  :min="0.01"
                  :step="0.01"
                  style="width: 80%;"
                  class="tag-input-width"
                  @blur="inputMatchingPrice(sku, sku.matchingPrice, scope.$index, skuIndex)"
                >
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="matchingPrice"
          :label="$t('combo.leastNum')"
          align="center"
          width="160px"
        >
          <template #default="scope">
            <div class="custom-rate">
              <input
                v-model="scope.row.leastNum"
                :disabled="pageType === 2"
                type="number"
                :precision="0"
                :max="99999999"
                :min="1"
                :step="1"
                style="width: 80%;"
                class="tag-input-width"
                @keyup="
                  scope.row.leastNum = String(scope.row.leastNum).match(/[^0-9]/) ? 1 : scope.row.leastNum
                "
                @blur="inputValue(scope.row, scope.row.leastNum, 'leastNum', 1, 99999999)"
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="type === 2"
          prop="required"
          :label="$t('combo.isRequired')"
          align="center"
          width="140px"
        >
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.required"
              :disabled="pageType === 2 || showDistributionTips(scope.row)"
              :true-value="1"
              :false-value="0"
              @change="checkRequired(scope.row, scope.$index)"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="pageType === 1"
          align="center"
          :label="$t('crud.menu')"
          fixed="right"
          width="140px"
        >
          <template #default="scope">
            <div class="text-btn-con">
              <div
                class="default-btn text-btn"
                @click="editSpecHandle(scope.$index, scope.row)"
              >
                {{ $t('combo.editSpec') }}
              </div>
              <div
                class="default-btn text-btn"
                @click="deleteHandle(scope.$index, scope.row.prodId)"
              >
                {{ $t('text.delBtn') }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <sku-select
      v-if="addProdVisible"
      ref="ProdsSelectRef"
      :is-radio="true"
      :chosen-check-items="productItems"
      :limit="limit"
      :status="1"
      :sku-status="1"
      :is-active="isActive"
      :mold="mold"
      :pre-sell-status="preSellStatus"
      :main-prod-id="mainProdId"
      :not-mold="notMold"
      @refresh-select-prods="selectProdItem"
    />
    <prodSkuSelect
      v-if="prodSkuSelectDialogVisible"
      ref="prodSkuSelectRef"
      @refresh-select-skus="selectSkuItem"
    />
  </div>
</template>

<script setup>
import prodSkuSelect from './prod-sku-select.vue'
import { onMounted, reactive } from 'vue'
const emit = defineEmits(['refreshDeleteHandle', 'refreshSelectProds'])

const props = defineProps({
  type: {
    default: 1,
    type: Number // 1 主商品 2 搭配商品
  },
  mainProdId: {
    default: () => [],
    type: Array // 主商品Id
  },
  mainProdDeliveryMode: {
    default: () => { },
    type: Object // 主商品配送方式
  },
  comboId: {
    default: 0,
    type: Number // 套餐id
  },
  limit: {
    default: -1,
    type: Number // 限制数量 -1代表没有限制
  },
  pageType: {
    default: 1,
    type: Number // 1：新增/编辑 2：查看
  },
  mold: {
    default: null,
    type: Number // 商品类别 1.实物商品 2. 虚拟商品
  },
  preSellStatus: {
    default: 0,
    type: Number // 预售状态 1：开启 0：未开启 预售商品不能参与套餐，所以值固定为0
  },
  isActive: {
    default: null,
    type: Number // null: 普通商品 1：普通商品 + 活动商品
  },
  notMold: {
    default: null,
    type: Number
  },
  // 过滤指定商品类型集合
  notMolds: {
    default: () => [],
    type: Array
  }
})

const Data = reactive({
  addd: true,
  tableWidth: {
    width: '1180px'
  },
  productItems: [], // 已选择的商品项
  selectSkuProdIndex: null,
  addProdVisible: false, // 添加商品弹窗是否可见
  prodSkuSelectDialogVisible: false, // 商品规格选择弹窗是否可见
  verifyFlag: true, // 数据校验标记，true：正确，false：错误
  totalCount: 0, // 总出入库量
  failureCount: 0, // 失效的商品数量
  rowspan: {}// 表格合并数据
})

const { tableWidth, productItems, addProdVisible, prodSkuSelectDialogVisible, verifyFlag } = toRefs(Data)

onMounted(() => {
  init()
})

const init = (productItems) => {
  if (props.type === 1) {
    Data.tableWidth.width = props.pageType === 1 ? '1180px' : '1040px'
  } else {
    Data.tableWidth.width = props.pageType === 1 ? '1320px' : '1180px'
  }
  if (productItems) {
    computeRowspan(productItems)
    Data.productItems = productItems
  } else {
    Data.productItems = []
  }
}

const ProdsSelectRef = ref()
const selectProdHandle = () => {
  if (Data.productItems.length >= props.limit || props.pageType === 2) {
    return
  }
  Data.addProdVisible = true
  nextTick(() => {
    ProdsSelectRef.value.init(1)
  })
}

// 选择商品项回调
const selectProdItem = (prodItems) => {
  Data.productItems = []
  let requiredCount = 0
  prodItems.forEach(prod => {
    prod.required = 0
    prod.skuList.forEach(sku => {
      sku.matchingPrice = sku.matchingPrice ? sku.matchingPrice : sku.price
    })
    prod.required = prod.required === 1 ? 1 : 0
    requiredCount += prod.required ? 1 : 0
    prod.leastNum = prod.leastNum ? prod.leastNum : 1
  })
  computeRowspan(prodItems)
  emit('refreshSelectProds', prodItems)
  // 重置verifyFlag
  if (props.type === 1) {
    if (prodItems.length === 1) {
      Data.verifyFlag = true
    }
  } else {
    if (prodItems.length > 0 && prodItems.length < 5 && requiredCount > 0) {
      Data.verifyFlag = true
    }
  }
  Data.productItems = prodItems
}

// 选择sku项回调
const selectSkuItem = (skuList) => {
  if (!skuList || skuList.length === 0) {
    const prodId = Data.productItems[Data.selectSkuProdIndex].prodId
    Data.productItems.splice(Data.selectSkuProdIndex, 1)
    emit('refreshDeleteHandle', prodId)
  } else {
    skuList.forEach(sku => {
      sku.matchingPrice = sku.matchingPrice ? sku.matchingPrice : sku.price
    })
    Data.productItems[Data.selectSkuProdIndex].skuList = skuList
  }
  computeRowspan(Data.productItems)
}

const computeRowspan = (prodItems) => {
  Data.rowspan = {}
  let prodId = -1
  let firstIndex = -1
  for (let i = 0; i < prodItems.length; i++) {
    if (prodItems[i].prodId === prodId) {
      if (Data.rowspan[firstIndex]) {
        Data.rowspan[firstIndex] += 1
      } else {
        Data.rowspan[firstIndex] = 2
      }
    } else {
      firstIndex = i
      prodId = prodItems[i].prodId
      Data.rowspan[firstIndex] = 1
    }
  }
}

// 校验数据，校验成功返回数据项，不成功返回null
const verifyDataForm = () => {
  if (props.type === 1) {
    if (Data.productItems.length === 1) {
      Data.verifyFlag = true
      return Data.productItems
    } else {
      Data.verifyFlag = false
      return null
    }
  }
  if (props.type === 2) {
    if (Data.productItems.length <= 4) {
      Data.verifyFlag = true
      return Data.productItems
    } else {
      Data.verifyFlag = false
      return null
    }
  }
}

// 输入值校验
const inputMatchingPrice = (row, max, index1, index2) => {
  const con = JSON.parse(JSON.stringify(Data.productItems[index1]))
  const matchingPrice = String(row.matchingPrice).match(/\d+(\.\d{0,2})?/) ? Number.parseFloat(String(row.matchingPrice).match(/\d+(\.\d{0,2})?/)[0]) : null
  if (row !== undefined && row !== null) {
    if (matchingPrice > max) {
      row.matchingPrice = max
      con.skuList[index2].matchingPrice = max
    }
    if (matchingPrice < 0.01 || !matchingPrice) {
      row.matchingPrice = 0.01
      con.skuList[index2].matchingPrice = 0.01
    }
  } else {
    row.matchingPrice = 0.01
    con.skuList[index2].matchingPrice = 0.01
  }
  Data.oductItems[index1] = con
}

const prodSkuSelectRef = ref()
// 编辑商品规格
const editSpecHandle = (index, row) => {
  Data.selectSkuProdIndex = index
  Data.prodSkuSelectDialogVisible = true
  nextTick(() => {
    prodSkuSelectRef.value.init(row.prodId, row.skuList)
  })
}
const checkRequired = (row, index) => {
  Data.productItems[index] = row
}
// 删除已选择的商品项
const deleteHandle = (index, id) => {
  Data.productItems.splice(index, 1)
  emit('refreshDeleteHandle', id)
  computeRowspan(Data.productItems)
}

// 输入值校验
const inputValue = (row, val, dataFields, min, max) => {
  if (row !== undefined && row !== null) {
    if (val > max) {
      row[dataFields] = max
    }
    if (val < min || !val) {
      row[dataFields] = min
    }
  } else {
    row[dataFields] = min
  }
}

// 目前支持的配送方式
const supportDeliveryMode = computed(() => {
  const tempDeliveryMode = JSON.parse(JSON.stringify(props.mainProdDeliveryMode))
  productItems.value.forEach(matchingProd => {
    if (matchingProd.required) {
      for (const key in matchingProd.deliveryModeVo) {
        if (!matchingProd.deliveryModeVo[key]) {
          tempDeliveryMode[key] = false
        }
      }
    }
  })
  return tempDeliveryMode
})

// 判断显示配送信息提示
const showDistributionTips = (prod) => {
  if (props.type === 1 || !props.mainProdDeliveryMode || Object.keys(props.mainProdDeliveryMode).length === 0) {
    return false
  }

  for (const key in supportDeliveryMode.value) {
    if (supportDeliveryMode.value[key] && prod.deliveryModeVo[key]) {
      return false
    }
  }

  return true
}

defineExpose({
  verifyDataForm,
  init
})
</script>

<style lang="scss" scoped>
.component-combo-prod-table {
  width: 100%;
  .count-desc-text {
    margin-bottom: 4px;
    color: #888888;
    font-size: 14px;
  }
  .error-tips {
    color: #f56c6c;
    font-size: 12px;
    padding-bottom: 4px;
  }
  .disabled-btn {
    color: #C0C4CC;
    &:hover {
      cursor: not-allowed;
      color: #C0C4CC;
    }
  }
  .prodItem-table {
    .prod-info-container {
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      .prod-image {
        margin-right: 20px;
        width: 80px;
        height: 80px;
        flex-shrink:0;
      }
      .prod-name {
        flex: 1;
        text-align: left;
        .prod-name-txt {
          font-size: 14px;
          color: #333333;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          line-break: anywhere;
        }
        .distribution-tips{
          color:#ec1c24;
        }
      }
    }
    .sku-name {
      font-size: 14px;
      color: #333333;
      height: 40px;
      line-height: 40px;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow:ellipsis;
      width: 170px;
    }
    .matching-price,.custom-rate {
      height: 45px;
      line-height: 45px;
    }
    // 表格输入框
    .tag-input-width {
      width: 100%;
      padding-left: 5px;
      padding-right: 0;
      border: 1px solid #DCDCDC;
      border-radius: 2px;
      height: 32px;
      line-height: 32px;
      box-sizing: border-box;
      &:focus {
        outline: 0;
      }
    }
  }
}
</style>
