<template>
  <el-dialog
    v-model="visible"
    class="component-prod-sku-select"
    :title="$t('combo.editSpec')"
  >
    <el-table
      ref="dataListRef"
      :data="dataList"
      header-cell-class-name="table-header"
      row-class-name="table-row"
      :row-style="{ height: '70px' }"
      style="width: 100%"
    >
      <el-table-column
        prop="partyCode"
        header-align="center"
        align="center"
        width="60"
      >
        <template #header>
          <el-checkbox
            v-model="isCheckAll"
            :indeterminate="indeterminate"
            @change="checkAll"
          />
        </template>
        <template #default="scope">
          <el-checkbox
            v-model="scope.row.check"
            @change="checkSku(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="skuName"
        header-align="center"
        align="center"
        :label="$t('stock.spec')"
      >
        <template #default="scope">
          <div class="sku-name-txt">
            {{ scope.row.skuName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('coupon.stock')"
        prop="stocks"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span class="table-cell-text line-clamp-one">{{ scope.row.stocks }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('stock.price')"
        prop="price"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span class="table-cell-text line-clamp-one">{{ scope.row.price }}</span>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span>
        <div
          class="default-btn"
          @click="visible = false"
        >{{
          $t("crud.filter.cancelBtn")
        }}</div>
        <div
          class="default-btn primary-btn"
          type="primary"
          @click="submitProds()"
        >{{
          $t("crud.filter.submitBtn")
        }}</div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive } from 'vue'
const emit = defineEmits(['refreshSelectSkus'])

const Data = reactive({
  visible: false,
  dataList: [], // 数据列表
  isCheckAll: false, // 全部选择
  indeterminate: false, // 部分选择
  choseSkuMap: {}, // 已选择的sku对象Map
  choseSkuCount: 0, // 选择的sku
  searchForm: {
    prodId: null
  }
})
const { visible, dataList, isCheckAll, indeterminate } = toRefs(Data)

const init = (prodId, choseSkuList) => {
  Data.visible = true
  Data.dataList = []
  Data.choseSkuMap = {}
  Data.choseSkuCount = 0
  Data.searchForm.prodId = prodId
  choseSkuList.forEach(sku => {
    ++Data.choseSkuCount
    Data.choseSkuMap[sku.skuId] = sku
  })
  getDataList()
}

// 点击选择sku
const checkSku = (row) => {
  if (row.check) {
    ++Data.choseSkuCount
    row.check = true
  } else {
    --Data.choseSkuCount
    row.check = false
  }
  Data.isCheckAll = Data.choseSkuCount === Data.dataList.length
  Data.indeterminate = Data.choseSkuCount > 0 && (Data.choseSkuCount < Data.dataList.length)
}

// 点击全选按钮
const checkAll = () => {
  if (Data.isCheckAll) {
    Data.dataList.forEach(sku => {
      sku.check = true
    })
    Data.choseSkuCount = Data.dataList.length
  } else {
    Data.dataList.forEach(sku => {
      sku.check = false
    })
    Data.choseSkuCount = 0
  }
  Data.indeterminate = false
}

// 获取sku列表
const getDataList = () => {
  http({
    url: http.adornUrl('/sku/getEnableSkuList'),
    method: 'get',
    params: http.adornParams(
      Data.searchForm
    )
  }).then(({ data }) => {
    data.forEach(sku => {
      if (Data.choseSkuMap[sku.skuId]) {
        sku.check = true
      } else {
        sku.check = false
      }
    })
    Data.isCheckAll = Data.choseSkuCount === data.length
    Data.indeterminate = Data.choseSkuCount > 0 && (Data.choseSkuCount < data.length)
    Data.dataList = data
  }).catch(() => {
    Data.dataListLoading = false
  })
}

// 确认提交
const submitProds = () => {
  const skuList = []
  Data.dataList.forEach(sku => {
    if (sku.check) {
      const temp = Data.choseSkuMap[sku.skuId] ? Data.choseSkuMap[sku.skuId] : sku
      skuList.push(temp)
    }
  })
  emit('refreshSelectSkus', skuList)
  Data.visible = false
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped></style>
