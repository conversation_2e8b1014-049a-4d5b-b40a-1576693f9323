<template>
  <div class="page-combo">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="test-form"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('combo.name')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.name"
              clearable
              :placeholder="$t('combo.name')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('combo.comboStatus')+':'"
            class="search-form-item"
          >
            <el-select
              v-model="searchForm.status"
              clearable
              :placeholder="$t('combo.comboStatus')"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('combo.mainProd')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.mainProdName"
              clearable
              :placeholder="$t('combo.mainProd')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('combo.matchingProd')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.matchProdName"
              clearable
              :placeholder="$t('combo.matchingProd')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="searchChange(true)"
            >
              {{ $t('shopFeature.searchBar.search') }}
            </div>
            <div
              class="default-btn"
              @click="clearSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <!-- 表格主体 -->
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('combo:combo:add')"
          class="default-btn primary-btn"
          @click="addOrUpdateHandle(0, 1)"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-con seckill-table">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            :label="$t('combo.name')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('giveaway.mainProdInfo')"
            width="320"
          >
            <template #default="scope">
              <div class="table-cell-con">
                <div class="table-cell-image">
                  <ImgShow
                    :src="scope.row.mainProdPic"
                    :img-alt="scope.row.mainProdName"
                  />
                </div>
                <span class="table-cell-text">{{ scope.row.mainProdName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="name"
            :label="$t('order.amountOfGoods')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.prodCount }}{{ $t('marketing.item') }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            :label="$t('combo.comboStatus')"
          >
            <template #default="scope">
              <div class="tag-text">
                {{ [$t("group.expired"), $t("groups.processing"), $t("groups.hasNotStarted")]
                  [scope.row.status] }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="soldNum"
            :label="$t('combo.soldNum')"
          />

          <el-table-column
            prop="price"
            :label="$t('combo.totalAmount')"
          />

          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="250"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('combo:combo:edit') && (scope.row.status === 1||scope.row.status === 2)"
                  type="text"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row.comboId, 1)"
                >
                  {{ $t("groups.editEvent") }}
                </div>
                <div
                  v-if="scope.row.status === 0"
                  type="text"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row.comboId, 2)"
                >
                  {{ $t("crud.viewTitle") }}
                </div>
                <div
                  v-if="scope.row.status === 1"
                  class="default-btn text-btn"
                >
                  <div
                    v-if="isAuth('combo:combo:close')"
                    type="text"
                    class="default-btn text-btn"
                    @click="changeStatus(scope.row.comboId, $t('groups.invalidActivity'), 0)"
                  >
                    {{ $t("groups.invalidActivity") }}
                  </div>
                </div>
                <div
                  v-if="isAuth('combo:combo:delete')"
                  class="default-btn text-btn"
                  @click="changeStatus(scope.row.comboId, $t('combo.delete'), -1)"
                >
                  {{ $t("text.delBtn") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 表格主体end -->
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'

const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件

  dataList: [],
  pageIndex: 1,
  pageSize: 10,
  totalPage: 0,
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  // 头部搜索表单
  searchForm: {
    name: null,
    status: null
  },
  statusList: [
    {
      value: 0,
      label: $t('groups.expired')
    },
    {
      value: 1,
      label: $t('groups.processing')
    },
    {
      value: 2,
      label: $t('groups.hasNotStarted')
    }
  ],
  dataListLoading: false,
  offlineEventHandleVisible: false,
  dataListSelections: [],
  addOrUpdateVisible: false
})
const { dataList, page, searchForm, statusList } = toRefs(Data)

onMounted(() => {
  getDataList()
})

// 获取数据列表
const getDataList = (page, newData = false) => {
  Data.dataListLoading = true
  if (newData || !Data.theData) {
    Data.theData = JSON.parse(JSON.stringify(Data.searchForm))
  }
  http({
    url: http.adornUrl('/shop/combo/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize
        },
        Data.theData
      )
    )
  }).then(({ data }) => {
    Data.dataList = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  })
}

const router = useRouter()
// 新增 / 修改
const addOrUpdateHandle = (comboId, pageType) => {
  router.push({
    path: '/marketing/combo/add-or-update',
    query: {
      comboId,
      pageType
    }
  })
}

// 改变套餐状态
const changeStatus = (comboId, operDesc, status) => {
  ElMessageBox.confirm(`${$t('sys.makeSure')}[` + operDesc + `]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/combo/changeStatus'),
      method: 'put',
      params: http.adornParams({
        status,
        comboId
      })
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          getDataList(Data.page)
        }
      })
    })
  })
}

// 删除

// 条件查询
const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  Data.page.pageSize = 10
  getDataList(Data.page, newData)
}

const clearSearch = () => {
  Data.searchForm.name = null
  Data.searchForm.status = null
  Data.searchForm.mainProdName = null
  Data.searchForm.matchProdName = null
}

// 每页数量变更
const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

// 页数变更
const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}

</script>

<style lang="scss" scoped>

</style>
