<template>
  <div class="page-live-room">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="name"
            :label="$t('live.liveName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.name"
              type="text"
              clearable
              :placeholder="$t('live.liveName')"
            />
          </el-form-item>
          <el-form-item
            prop="nickName"
            :label="$t('live.anchorName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.nickName"
              type="text"
              clearable
              :placeholder="$t('live.anchorName')"
            />
          </el-form-item>
          <el-form-item
            prop="liveStatus"
            :label="$t('live.liveStatus')+':'"
            class="search-form-item"
          >
            <el-select
              v-model="searchForm.liveStatus"
              clearable
              :placeholder="$t('live.liveStatus')"
            >
              <el-option
                :label="$t('live.living')"
                :value="1"
              />
              <el-option
                :label="$t('live.noStart')"
                :value="0"
              />
              <el-option
                :label="$t('live.finished')"
                :value="2"
              />
              <el-option
                :label="$t('product.violation')"
                :value="-1"
              />
              <el-option
                :label="$t('live.stop')"
                :value="3"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('live.startTime')+':'">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              clearable
              :range-separator="$t('time.tip')"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              value-format="YYYY-MM-DD HH:mm:ss"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="searchChange(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="resetSearchForm('searchForm')"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('live:liveRoom:save')"
          class="default-btn primary-btn"
          @click="addOrUpdateHandle()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <div class="table-con live-room-table">
        <el-table
          ref="liveRoomTable"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            align="left"
            prop="name"
            :label="$t('live.liveName')"
            min-width="260"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="nickName"
            :label="$t('live.anchorName')"
            min-width="200"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.nickName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="userMobile"
            :label="$t('live.anchorMobile')"
            min-width="180"
          />
          <el-table-column
            align="left"
            prop="liveStatus"
            :label="$t('live.liveStatus')"
            min-width="120"
          >
            <template #default="scope">
              <span
                v-if="scope.row.liveStatus === 1"
                class="tag-text"
              >{{ $t("live.living") }}</span>
              <span
                v-if="scope.row.liveStatus === 0"
                class="tag-text"
              >{{
                $t("live.noStart")
              }}</span>
              <span
                v-if="scope.row.liveStatus === -1"
                class="tag-text"
              >{{
                $t("product.violation")
              }}</span>
              <span
                v-if="scope.row.liveStatus === 2"
                class="tag-text"
              >{{
                $t("live.finished")
              }}</span>
              <span
                v-if="scope.row.liveStatus === 3"
                class="tag-text"
              >{{
                $t("live.stop")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="startTime"
            :label="$t('live.liveStartTime')"
            min-width="160"
          />
          <el-table-column
            align="left"
            prop="endTime"
            :label="$t('live.liveEndTime')"
            min-width="160"
          />
          <el-table-column
            fixed="right"
            align="center"
            :label="$t('publics.operating')"
            min-width="230"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('live:liveRoom:update') && (scope.row.liveStatus === 0 || scope.row.liveStatus === 3)"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row)"
                >
                  {{ $t("live.edit") }}
                </div>
                <div
                  v-if="scope.row.liveStatus !== 0 && scope.row.liveStatus !== 3"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row)"
                >
                  {{ $t("live.view") }}
                </div>
                <div
                  v-if="scope.row.liveStatus === -1"
                  class="default-btn text-btn"
                  @click="getOfflineRemark(scope.row.remark)"
                >
                  {{ $t("components.reasonForOffline") }}
                </div>
                <div
                  v-if="isAuth('live:liveRoom:delete') && scope.row.liveStatus !== 1"
                  class="default-btn text-btn"
                  type="text"
                  @click="deleteHandle(scope.row.roomId)"
                >
                  {{ $t('resource.Delete') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { onActivated, onMounted, reactive, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'

const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件
  theParams: null, // 保存上次点击查询的请求条件

  dataList: [],
  prods: [],
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  searchForm: {
    name: '',
    nickName: '',
    liveStatus: '',
    roomTop: ''
  }, // 搜索
  dataListLoading: false,
  dateRange: []
})

const { dataList, page, searchForm, dateRange } = toRefs(Data)

onMounted(() => {
  getDataList(Data.page)
})

onActivated(() => {
  if (!Data.dataListLoading) {
    getDataList(Data.page)
  }
})

const getDataList = (page, newData = false) => {
  Data.dataListLoading = true
  if (newData || !Data.theData) {
    Data.theParams = JSON.parse(JSON.stringify(Data.searchForm))
    Data.theData = {
      current: page == null ? Data.page.currentPage : page.currentPage,
      size: page == null ? Data.page.pageSize : page.pageSize,
      startTime: Data.dateRange === null ? null : Data.dateRange[0], // 开始时间
      endTime: Data.dateRange === null ? null : Data.dateRange[1] // 结束时间
    }
  } else {
    Data.theData.current = page == null ? Data.page.currentPage : page.currentPage
    Data.theData.size = page == null ? Data.page.pageSize : page.pageSize
  }
  http({
    url: http.adornUrl('/multishop/live/liveRoom/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize,
          startTime: Data.dateRange === null ? null : Data.dateRange[0], // 开始时间
          endTime: Data.dateRange === null ? null : Data.dateRange[1] // 结束时间
        },
        Data.theParams
      )
    )
  }).then(({ data }) => {
    Data.dataList = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  }).catch(() => {
    Data.dataListLoading = false
  })
}

const router = useRouter()
// 新增 / 修改
const addOrUpdateHandle = (data) => {
  router.push({
    path: '/marketing/live-room/new-live-room'
  })
  sessionStorage.setItem('bbcLiveRoomData', JSON.stringify(data))
}

const deleteHandle = (id) => {
  ElMessageBox.confirm($t('admin.isDeleOper'), $t('resource.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/multishop/live/liveRoom/' + id),
      method: 'delete',
      data: http.adornData({})
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          refreshChange()
        }
      })
    })
  }).catch(() => { })
}

// 清空自定义数据
const searchReset = () => {
  Data.dateRange = []
}

/**
     * 刷新回调
     */
const refreshChange = () => {
  Data.page.currentPage = 1
  getDataList(Data.page)
}

const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  getDataList(Data.page, newData)
}

const searchFormRef = ref()
/**
     * 重置表单
     * @param {String} formName 表单名称
     */
const resetSearchForm = () => {
  searchFormRef.value.resetFields()
  searchReset()
  Data.prodName = ''
}

const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}

// 查看下线原因
const getOfflineRemark = (remark) => {
  ElMessageBox.confirm(remark, $t('components.reasonForOffline'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    showCancelButton: false
  })
}
</script>

<style lang="scss" scoped>

</style>
