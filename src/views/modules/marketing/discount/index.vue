<template>
  <div class="page-discount">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="test-form"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('groups.eventName') + ':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.discountName"
              clearable
              :placeholder="$t('groups.eventName')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('group.actStatus') + ':'"
            class="search-form-item"
          >
            <el-select
              v-model="searchForm.status"
              clearable
              :placeholder="$t('group.actStatus')"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="searchChange(true)"
            >
              {{ $t('shopFeature.searchBar.search') }}
            </div>
            <div
              class="default-btn"
              @click="clearSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <!-- 表格主体 -->
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('admin:discount:save')"
          class="default-btn primary-btn"
          @click="addOrUpdateHandle()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-con seckill-table">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            prop="discountName"
            :label="$t('groups.eventName')"
            width="300"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.discountName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            min-width="130"
            :label="$t('marketing.mobiltyDiagram')"
          >
            <template #default="scope">
              <div class="table-cell-image">
                <ImgShow
                  :src="scope.row.mobilePic"
                  :img-alt="scope.row.prodName"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="startTime"
            :label="$t('coupon.startTime')"
            min-width="160"
          />

          <el-table-column
            prop="endTime"
            :label="$t('coupon.endTime')"
            min-width="160"
          />

          <el-table-column
            prop="status"
            :label="$t('group.actStatus')"
          >
            <template #default="scope">
              <div class="tag-text">
                {{ [$t("station.close"), $t("station.open"), $t("product.violation"),
                    $t('coupon.waitReview')]
                  [scope.row.status] }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="250"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div class="default-btn text-btn">
                  <div
                    v-if="isAuth('admin:discount:update')"
                    type="text"
                    class="default-btn text-btn"
                    @click="addOrUpdateHandle(scope.row.discountId)"
                  >
                    {{ $t("groups.editEvent") }}
                  </div>
                </div>
                <div class="default-btn text-btn">
                  <div
                    v-if="isAuth('admin:discount:discountProd')"
                    type="text"
                    class="default-btn text-btn"
                    @click="showProds(scope.row.discountId)"
                  >
                    {{ $t("live.view") }}
                  </div>
                </div>
                <div
                  v-if="isAuth('admin:discount:auditApply') && scope.row.status > 1"
                  class="default-btn text-btn"
                >
                  <div
                    v-if="isAuth('admin:discount:auditApply') && scope.row.status > 1"
                    type="text"
                    class="default-btn text-btn"
                    @click="offlineEventHandle(scope.row.discountId)"
                  >
                    {{ scope.row.status === 2 ? $t("groups.applyForListing") : $t("coupon.waitReview") }}
                  </div>
                </div>
                <div
                  v-if="isAuth('admin:discount:delete')"
                  class="default-btn text-btn"
                  @click="deleteHandle(scope.row.discountId, scope.row.status)"
                >
                  {{ $t("text.delBtn") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 表格主体end -->

    <!-- 弹窗  新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
    />

    <!-- 下线管理弹窗-->
    <eventHandle
      v-if="offlineEventHandleVisible"
      ref="offlineEventHandleRef"
      select-url="/admin/discount/getOfflineHandleEventByDiscountId"
      apply-url="/admin/discount/auditApply"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import AddOrUpdate from './components/add-or-update.vue'
import eventHandle from '@/components/offline-event-handle/index.vue'

import { isAuth } from '@/utils'
import { onMounted } from 'vue'

const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件

  dataForm: {
    discountName: ''
  },
  dataList: [],
  pageIndex: 1,
  pageSize: 10,
  totalPage: 0,
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  // 头部搜索表单
  searchForm: {
    discountName: null,
    status: null
  },
  statusList: [
    {
      value: 0,
      label: $t('station.close')
    },
    {
      value: 1,
      label: $t('station.open')
    },
    {
      value: 2,
      label: $t('product.violation')
    },
    {
      value: 3,
      label: $t('coupon.waitReview')
    }
  ],
  dataListLoading: false,
  offlineEventHandleVisible: false,
  dataListSelections: [],
  addOrUpdateVisible: false
})

const { dataList, page, searchForm, statusList, offlineEventHandleVisible, addOrUpdateVisible } = toRefs(Data)

onMounted(() => {
  getDataList()
})

// 获取数据列表
const getDataList = (page, newData = false) => {
  if (Data.page) {
    const size = Math.ceil(Data.page.total / Data.page.pageSize)
    Data.page.currentPage = (Data.page.currentPage > size ? size : Data.page.currentPage) || 1
  }
  if (newData || !Data.theData) {
    Data.theData = JSON.parse(JSON.stringify(Data.searchForm))
  }
  Data.dataListLoading = true
  http({
    url: http.adornUrl('/admin/discount/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize
        },
        Data.theData
      )
    )
  }).then(({ data }) => {
    Data.dataList = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  })
}

const router = useRouter()
// 新增
const addOrUpdateHandle = (id) => {
  router.push({ path: '/marketing/discount/info', query: { discountId: id } })
}

const addOrUpdateRef = ref()
// 查看商品
const showProds = (id) => {
  Data.addOrUpdateVisible = true
  nextTick(() => {
    addOrUpdateRef.value.showProdInit(id, 1)
  })
}

// 删除
const deleteHandle = (id) => {
  const ids = id ? [id] : Data.dataListSelections.map(item => {
    return item.discountId
  })
  ElMessageBox.confirm(`${$t('sys.makeSure')}[${$t('text.delBtn')}]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/admin/discount/' + id),
      method: 'delete',
      data: http.adornData({}, false)
    }).then(() => {
      Data.page.total = Data.page.total - ids.length
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          getDataList(Data.page)
        }
      })
    })
  })
}

// 条件查询
const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  Data.page.pageSize = 10
  getDataList(Data.page, newData)
}

const offlineEventHandleRef = ref()
// 下线管理
const offlineEventHandle = (id) => {
  Data.offlineEventHandleVisible = true
  nextTick(() => {
    offlineEventHandleRef.value.init(id)
  })
}

const clearSearch = () => {
  Data.searchForm.discountName = null
  Data.searchForm.status = null
}

// 每页数量变更
const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

// 页数变更
const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}

/**
 * 刷新数据
 */
const refreshChange = () => {
  getDataList()
}
</script>

<style lang="scss" scoped></style>
