<template>
  <div class="page-discount-info">
    <div class="new-page-title">
      <div class="line" />
      <div class="text">
        {{
          !dataForm.discountId
            ? $t('marketing.newDiscount')
            : $t('marketing.viewDiscount')
        }}
      </div>
    </div>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="auto"
      class="form-box"
      @submit.prevent
      @keyup.enter="dataFormSubmit()"
    >
      <el-form-item
        :label="$t('group.actName') + ':'"
        prop="discountName"
      >
        <el-input
          v-model="dataForm.discountName"
          maxlength="50"
          show-word-limit
          style="width: 559px;padding-right:46px"
          :placeholder="$t('group.actName')"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.mobiltyDiagram') + ':'"
        prop="mobilePic"
      >
        <img-upload
          v-model="dataForm.mobilePic"
          @update:model-value="onValidateField('mobilePic')"
        />
        <span style="color: #999999">{{ $t("pictureManager.imageSize") }}710*260，{{ $t("marketing.suggest1") }}</span>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pcActivityListChart') + ':'"
        prop="pcPic"
      >
        <img-upload
          v-model="dataForm.pcPic"
          @update:model-value="onValidateField('pcPic')"
        />
        <span style="color: #999999">{{ $t("pictureManager.imageSize") }}590*240</span>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.pcActiviroundMap') + ':'"
        prop="pcBackgroundPic"
      >
        <img-upload
          v-model="dataForm.pcBackgroundPic"
          @update:model-value="onValidateField('pcBackgroundPic')"
        />
        <span style="color: #999999">{{ $t("pictureManager.imageSize") }}1200*360</span>
      </el-form-item>
      <el-form-item :label="$t('marketing.activTime') + ':'">
        <div class="date-picker">
          <el-form-item
            ref="startTimeRef"
            prop="startTime"
          >
            <el-date-picker
              v-model="dataForm.startTime"
              type="date"
              :placeholder="$t('live.chooseStartDate')"
              value-format="YYYY-MM-DD"
              style="width: 143px"
            />
            <el-time-select
              v-model="startTimeValue"
              start="00:00"
              step="00:30"
              end="23:30"
              :class="[startTimeValue ? 'select-time': '']"
              style="width: 102px"
              :placeholder="$t('time.startTime')"
            />
          </el-form-item>
          <span style="margin: 0 10px">-</span>
          <el-form-item
            ref="endTimeRef"
            prop="endTime"
          >
            <el-date-picker
              v-model="dataForm.endTime"
              type="date"
              :placeholder="$t('live.chooseEndDate')"
              value-format="YYYY-MM-DD"
              style="width: 143px"
            />
            <el-time-select
              v-model="endTimeValue"
              start="00:00"
              step="00:30"
              end="23:30"
              :class="[endTimeValue ? 'select-time': '']"
              style="width: 102px"
              :placeholder="$t('time.endTime')"
            />
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.typeOfActivity') + ':'"
        prop="discountRule"
      >
        <el-radio-group
          v-model="dataForm.discountRule"
          :disabled="!!dataForm.discountId"
          @change="changeDiscountRule"
        >
          <el-radio :label="0">
            {{ $t("marketing.fullMoneoney") }}
          </el-radio>
          <!-- <el-radio :label="1">满件减钱</el-radio> -->
          <el-radio :label="2">
            {{
              $t("marketing.fullMoneyDiscount")
            }}
          </el-radio>
          <el-radio :label="3">
            {{ $t("marketing.fullDiscount") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="dataForm.discountRule === 0 || dataForm.discountRule === 1"
        :label="$t('marketing.typeOfExemption') + ':'"
        prop="discountType"
      >
        <el-radio-group
          v-model="dataForm.discountType"
          @change="discountTypeChange"
        >
          <el-radio :label="0">
            {{
              $t("marketing.decreaseOestLevel")
            }}
          </el-radio>
          <el-radio :label="1">
            {{ $t("marketing.decreaeryTime") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 卡片 START -->
      <el-form-item :label="$t('marketing.offerContent') + ':'">
        <el-card
          v-for="(discountItem, index) in dataForm.discountItems"
          :key="index"
          class="box-card"
          shadow="never"
        >
          <template
            v-if="!dataForm.discountType"
            #header
          >
            <div
              class="box-card-header"
            >
              <span>{{ $t("marketing.activityLevel") }} {{ index + 1 }}</span>
              <div
                v-if="dataForm.discountItems.length > 1"
                class="default-btn text-btn"
                @click="deleteActivityClass(index)"
              >
                {{ $t("text.delBtn") }}
              </div>
            </div>
          </template>

          <el-form-item
            :label="$t('marketing.conditionsOfUse') + ':'"
            :label-width="$t('language') === 'English' ? '150px' : '100px'"
            :rules="[{ required: true, message: $t('publics.noNull') }]"
          >
            <el-input
              v-model="discountItem.needAmount"
              type="number"
              style="width: 200px"
              :min="1"
              @change="checkNumber(index, 1)"
            >
              <template
                v-if="dataForm.discountType === 1"
                #prepend
              >
                {{ $t("marketing.everyFull") }}
              </template>
              <template
                v-else
                #prepend
              >
                {{ $t("marketing.full") }}
              </template>
              <template #append>
                {{ discountItemTexts[0] }}
              </template>
            </el-input>
            <div class="pleaseEnteThan">
              {{ $t("formData.pleaseThan0") }}
              <el-tooltip
                v-if="dataForm.discountType === 0"
                class="item"
                effect="light"
                placement="top"
              >
                <template #content>
                  <div>
                    {{ $t("marketing.conditionsOfUseIn") }}
                  </div>
                </template>

                <span>
                  <el-icon><QuestionFilled /></el-icon>
                </span>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item
            :label="$t('marketing.discountedPrice') + ':'"
            :rules="[{ required: true, message: $t('publics.noNull') }]"
            :label-width="$t('language') === 'English' ? '150px' : '100px'"
          >
            <el-input
              v-model="discountItem.discount"
              type="number"
              style="width: 200px"
              @change="checkNumber(index, 2)"
            >
              <template
                v-if="dataForm.discountRule < 2"
                #prepend
              >
                {{ $t("marketing.reducea") }}
              </template>
              <template
                v-else
                #prepend
              >
                {{ $t("marketing.dozen") }}
              </template>
              <template #append>
                {{ discountItemTexts[1] }}
              </template>
            </el-input>
            <div class="pleaseEnteThan">
              {{
                dataForm.discountRule < 2 ? $t("marketing.pleaseThan0") : $t("marketing.pleaseEnterTheNumber010") }}
            </div>
          </el-form-item>
        </el-card>
      </el-form-item>
      <!-- 卡片 END -->
      <el-form-item
        v-if="dataForm.discountType === 0"
        :label="' '"
      >
        <div
          class="default-btn"
          @click="addActivityClass()"
        >
          {{ $t("addAct") }}
        </div>
      </el-form-item>
      <el-form-item
        v-if="dataForm.discountRule >= 2 || dataForm.discountType !== 0"
        :label="$t('marketing.maximumDiscountAmount') + ':'"
        prop="maxReduceAmount"
      >
        <el-input
          v-model="dataForm.maxReduceAmount"
          type="number"
          style="width: 520px"
          :placeholder="$t('marketing.maximumDiscountAmount')"
          :max="9999999999999"
          @change="maxReduceAmountChange"
        />
      </el-form-item>
      <el-form-item
        :label="$t('marketing.applicableProductType') + ':'"
        prop="suitableProdType"
        :rules="[{ required: true, message: $t('publics.noNull') }]"
      >
        <el-radio-group v-model="dataForm.suitableProdType">
          <el-radio :label="0">
            {{ $t("marketing.allProdsPar") }}
          </el-radio>
          <el-radio :label="1">
            {{ $t("marketing.participateInD") }}
          </el-radio>
          <el-radio :label="2">
            {{ $t("marketing.specifiedProduct") }}
          </el-radio>
        </el-radio-group>
        <div class="discount-tip">
          {{ $t("marketing.discountTip") }}
        </div>
      </el-form-item>
      <!-- 适用商品类型 -->
      <el-form-item
        v-if="dataForm.suitableProdType !== 0 && dataForm.discountProds.length"
        :label="' '"
      >
        <div class="prod-card">
          <div
            v-for="(discountProd, index) in dataForm.discountProds"
            :key="index"
          >
            <el-card
              :body-style="{ padding: '0px' }"
              style="height: 160px; width: 120px; margin-bottom: 15px;margin-right: 10px;"
            >
              <prod-pic
                height="104px"
                width="100%"
                :pic="discountProd.pic"
              />
              <div class="card-prod-bottom">
                <span class="card-prod-name">{{ discountProd.prodName }}</span>
                <el-button
                  type="text"
                  class="card-prod-name-button"
                  @click="deleteProd(index)"
                >
                  {{ $t("text.delBtn") }}
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
      </el-form-item>
      <!-- 选择商品按钮 -->
      <el-form-item
        v-if="dataForm.suitableProdType === 1 || dataForm.suitableProdType === 2"
        :label="' '"
      >
        <div
          class="default-btn"
          @click="prodsSelectHandle()"
        >
          {{ $t("product.select") }}
        </div>
      </el-form-item>
      <!-- 商品 -->
      <el-form-item
        v-if="dataForm.status === 1 || dataForm.status === 0"
        :label="$t('product.status') + ':'"
        prop="status"
      >
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">
            {{ $t("combo.open") }}
          </el-radio>
          <el-radio :label="0">
            {{ $t("station.close") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="' '">
        <div
          class="default-btn primary-btn"
          @click="dataFormSubmit()"
        >
          {{
            $t("crud.filter.submitBtn")
          }}
        </div>
        <div
          class="default-btn"
          @click="closeTag()"
        >
          {{
            $t("crud.filter.cancelBtn")
          }}
        </div>
      </el-form-item>
    </el-form>
    <!-- 商品选择弹窗-->
    <prods-select
      v-if="prodsSelectVisible"
      ref="prodsSelectRef"
      @refresh-select-prods="selectDiscountProds"
    />
  </div>
</template>

<script setup>
import { computed, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const validateTime = (rule, value, callback) => {
  if (rule.field === 'startTime' && (!Data.startTimeValue || !Data.dataForm.startTime)) {
    callback(new Error($t('formData.startTimeCannotBeEmpty')))
  }
  if (rule.field === 'endTime' && (!Data.endTimeValue || !Data.dataForm.endTime)) {
    callback(new Error($t('formData.endTimeCannotBeEmpty')))
  }
  const startTime = Data.dataForm.startTime + ' ' + Data.startTimeValue + ':00'
  const endTime = Data.dataForm.endTime + ' ' + Data.endTimeValue + ':00'
  if (Data.dataForm.status === 1 && rule.field === 'endTime' && new Date() > Date.parse(endTime)) {
    callback(new Error($t('groups.endTime')))
  }
  if (rule.field === 'startTime' && Date.parse(startTime) >= Date.parse(endTime)) {
    callback(new Error($t('product.dateErrTips1')))
  } else if (Data.dataForm.status === 1 && Data.dataForm.groupActivityId && Date.parse(Data.validEndTime) > Date.parse(endTime)) {
    callback(new Error($t('groups.extendedEndTime')))
  } else {
    callback()
  }
}

const validateName = (rule, value, callback) => {
  if (!value || !value.trim()) {
    callback(new Error($t('publics.noNull')))
  } else {
    callback()
  }
}

const validateAmount = (rule, value, callback) => {
  const reg = /^\d+(\.{0,2}\d+){0,2}$/
  if (!value || !reg.test(value)) {
    callback(new Error($t('marketing.pleaseThan0')))
  } else {
    callback()
  }
}

const Data = reactive({
  dataForm: {
    discountId: 0,
    discountName: '',
    mobilePic: '',
    pcPic: '',
    pcBackgroundPic: '',
    discountRule: 0,
    discountType: 0,
    suitableProdType: 0,
    maxReduceAmount: 0,
    shopId: '',
    startTime: '',
    endTime: '',
    status: 1,
    discountItems: [],
    discountProds: []
  },
  dataRule: {
    mobilePic: [
      { required: true, message: $t('marketing.pleaseUploadAPicture'), trigger: 'blur' }
    ],
    pcPic: [
      { required: true, message: $t('marketing.pleaseUploadAPicture'), trigger: 'blur' }
    ],
    pcBackgroundPic: [
      { required: true, message: $t('marketing.pleaseUploadAPicture'), trigger: 'blur' }
    ],
    startTime: [
      { required: true, message: $t('groups.startTimeCannotBeEmpty'), trigger: 'blur' },
      { validator: validateTime, trigger: 'blur' }
    ],
    endTime: [
      { required: true, message: $t('groups.endTimeCannotBeEmpty'), trigger: 'blur' },
      { validator: validateTime, trigger: 'blur' }
    ],
    discountName: [
      { required: true, message: $t('publics.noNull'), trigger: 'blur' },
      { validator: validateName, trigger: 'blur' }
    ],
    maxReduceAmount: [
      { required: true, message: $t('marketing.pleaseThan0'), trigger: 'blur' },
      { validator: validateAmount, trigger: 'blur' }
    ]
  },
  prodsSelectVisible: false,
  isTrue: false,
  errorValue: false,
  isSubmitting: false,
  startTimeValue: '',
  endTimeValue: ''
})

const { dataForm, dataRule, prodsSelectVisible, startTimeValue, endTimeValue } = toRefs(Data)

const discountItemTexts = computed(() => {
  const texts = []
  if (Data.dataForm.discountRule === 0 || Data.dataForm.discountRule === 2) {
    texts[0] = $t('admin.dollar')
  } else {
    texts[0] = $t('marketing.item')
  }
  if (Data.dataForm.discountRule === 0 || Data.dataForm.discountRule === 1) {
    texts[1] = $t('admin.dollar')
  } else {
    texts[1] = $t('marketing.fold')
  }
  return texts
})

const newStartTime = computed(() => {
  const startTime = Data.dataForm.startTime + ' ' + Data.startTimeValue + ':00'
  return JSON.parse(JSON.stringify(startTime))
})

const newEndTime = computed(() => {
  const endTime = Data.dataForm.endTime + ' ' + Data.endTimeValue + ':00'
  return JSON.parse(JSON.stringify(endTime))
})

const route = useRoute()
const commonStore = useCommonStore()
onMounted(() => {
  if (route.query.discountId !== undefined) {
    Data.dataForm.discountId = route.query.discountId
  }
  getDataList()
  const title = Data.dataForm.discountId ? $t('marketing.viewDiscount') : $t('marketing.newDiscount')
  commonStore.replaceSelectMenu(title)
})

watch(() => Data.dataForm.discountRule, (val) => {
  if (val === 2 || val === 3) {
    Data.dataForm.discountType = 0
  }
})

const endTimeRef = ref()
const startTimeRef = ref()
watch(newStartTime, () => {
  endTimeRef.value.clearValidate('endTime')
})
watch(newEndTime, () => {
  startTimeRef.value.clearValidate('startTime')
})

const dataFormRef = ref()
const router = useRouter()
const getDataList = () => {
  nextTick(() => {
    dataFormRef.value.resetFields()
    Data.dataForm.discountItems = [{ needAmount: 1, discount: 0.01 }]
    const datetimeRange = getDateTimeRange()
    Data.dataForm.startTime = datetimeRange.startTime
    Data.dataForm.endTime = datetimeRange.endTime
    Data.startTimeValue = datetimeRange.currentTime
    Data.endTimeValue = datetimeRange.currentTime
  })
  if (Data.dataForm.discountId) {
    http({
      url: http.adornUrl(`/admin/discount/info/${Data.dataForm.discountId}`),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      Data.dataForm = data
      Data.startTimeValue = Data.dataForm.startTime ? Data.dataForm.startTime.substring(11, Data.dataForm.startTime.length - 3) : ''
      Data.endTimeValue = Data.dataForm.endTime ? Data.dataForm.endTime.substring(11, Data.dataForm.endTime.length - 3) : ''
      Data.dataForm.startTime = getParseTime(Data.dataForm.startTime, '{y}-{m}-{d}')
      Data.dataForm.endTime = getParseTime(Data.dataForm.endTime, '{y}-{m}-{d}')
      Data.dataForm.maxReduceAmount = Math.ceil(Data.dataForm.maxReduceAmount)
    }).catch(() => {
      router.go(-1)
    })
  }
}

/**
  * 活动类型变化时，更新优惠内容的值
  */
const changeDiscountRule = () => {
  for (let i = 0; i < Data.dataForm.discountItems.length; i++) {
    checkNumber(i, 1)
    checkNumber(i, 2)
  }
}

/**
  * 输入框的数据改变时，对值进行校验
  * type 1:验证needAmount(满足使用条件需要达到的金额） 2：验证discount（优惠金额）
  */
const checkNumber = (index, type) => {
  const discountItem = Data.dataForm.discountItems[index]
  // item.needAmount <= item.discount
  if (type === 1) {
    if (discountItem.needAmount < 1) {
      discountItem.needAmount = 1
    }
    if (discountItem.needAmount > 9999999) {
      discountItem.needAmount = 9999999
    }
    discountItem.needAmount = Math.round(discountItem.needAmount)
  }
  if (type === 2) {
    // 如果小于最小值
    if (discountItem.discount > 9999999.99) {
      discountItem.discount = 9999999.99
    }
    if (discountItem.discount < 0.01 && Data.dataForm.discountRule < 2) {
      discountItem.discount = 0.01
    }
    if (discountItem.discount < 0.1 && Data.dataForm.discountRule >= 2) {
      discountItem.discount = 0.1
    }
    if (discountItem.discount >= 10 && Data.dataForm.discountRule >= 2) {
      // 如果折扣大于10
      discountItem.discount = 9.9
    } else if (Data.dataForm.discountRule < 2) {
      // 优惠金额保留两位小数
      discountItem.discount = parseFloat(discountItem.discount).toFixed(2)
    } else {
      // 保留一位小数
      discountItem.discount = parseFloat(discountItem.discount).toFixed(1)
      discountItem.discount = discountItem.discount >= 10 ? 9.9 : discountItem.discount
    }
  }
}

/**
  * 优惠金额上限校验
  */
const maxReduceAmountChange = () => {
  const maxReduceAmount = Math.round(Data.dataForm.maxReduceAmount)
  Data.dataForm.maxReduceAmount = maxReduceAmount < 1 ? 1 : maxReduceAmount > 9999999999999 ? 9999999999999 : maxReduceAmount
}

/**
  * 减免类型选择时，更新数据
  */
const discountTypeChange = () => {
  Data.dataForm.discountItems = [Data.dataForm.discountItems[0]]
}

// 添加活动层级
const addActivityClass = () => {
  if (Data.dataForm.discountItems.length >= 5) {
    ElMessage.error($t('marketing.maxActivityLevelsTips'))
    return
  }
  Data.dataForm.discountItems.push({})
}

// 删除活动层级
const deleteActivityClass = (index) => {
  Data.dataForm.discountItems.splice(index, 1)
}

// 删除指定商品
const deleteProd = (index) => {
  Data.dataForm.discountProds.splice(index, 1)
}

const prodsSelectRef = ref()
// 显示添加指定商品弹框
const prodsSelectHandle = () => {
  Data.prodsSelectVisible = true
  nextTick(() => {
    prodsSelectRef.value.init(Data.dataForm.discountProds)
  })
}

// 添加指定商品
const selectDiscountProds = (prods) => {
  Data.prodsSelectVisible = false
  if (prods) {
    Data.dataForm.discountProds = prods
  }
}

// 关闭当前标签页
const closeTag = () => {
  router.go(-1)
}

/**
  * 错误提示框
  */
const errorMsg = (message) => {
  ElMessage({
    message,
    type: 'error',
    duration: 1500
  })
}

/**
  * 校验优惠内容是否正确
  */
const checkDiscountItems = () => {
  Data.isTrue = false

  let minAmount = Data.dataForm.discountItems[0].discount
  let maxAmount = Data.dataForm.discountItems[0].discount
  let amount = 0
  let prevDiscount = 0
  let index = 1
  Data.dataForm.discountItems.forEach(item => {
    if (Data.dataForm.discountType === 0 && item.needAmount <= amount) {
      Data.isTrue = true
      Data.errorValue = $t('marketing.activityLevel') + index + $t('marketing.theConditierThan') + amount
      return true
    }
    // 限制层级高的优惠金额不得低于层级低的优惠金额
    if (Data.dataForm.discountType === 0 &&
      Data.dataForm.discountRule === 0 &&
      Number(item.discount) <= Number(prevDiscount)) {
      Data.isTrue = true
      Data.errorValue = $t('marketing.activityLevel') + index + $t('marketing.theOfferAmountThan') + prevDiscount
      return true
    }
    // 限制层级高的优惠折扣不得低于层级低的优惠折扣(折扣越低优惠金额越大)
    if (Data.dataForm.discountType === 0 &&
      Data.dataForm.discountRule !== 0 &&
      Number(item.discount) >= Number(prevDiscount) &&
      Number(prevDiscount) !== 0) {
      Data.isTrue = true
      Data.errorValue = $t('marketing.activityLevel') + index + $t('marketing.theOfferDiscountThan') + prevDiscount
      return true
    }
    amount = item.needAmount
    prevDiscount = item.discount
    index = index + 1
    if (Data.dataForm.discountRule === 0) {
      if (item.needAmount <= item.discount) {
        Data.isTrue = true
        Data.errorValue = $t('marketing.activimount')
        return true
      }
    }
    if (!item.needAmount) {
      Data.isTrue = true
      Data.errorValue = $t('marketing.activitytBeEmpty')
      return true
    }
    if (!item.discount) {
      Data.isTrue = true
      Data.errorValue = $t('marketing.promotioBeEmpty')
      return true
    }
    minAmount = item.discount < minAmount ? item.discount : minAmount
    maxAmount = item.discount > maxAmount ? item.discount : maxAmount
  })
  // 活动类型：满钱减钱 => 减免类型：按满足最高层减一次 => 优惠金额上限为最大金额
  if (Data.dataForm.discountRule === 0 && Data.dataForm.discountType === 0) {
    Data.dataForm.maxReduceAmount = maxAmount
  }
  if (Data.dataForm.discountRule < 2 && Data.dataForm.discountType === 0) {
    Data.dataForm.maxReduceAmount = maxAmount
  } else if (Data.dataForm.discountRule < 2 && Data.dataForm.discountType === 1) {
    if (Data.dataForm.maxReduceAmount < minAmount) {
      Data.isTrue = true
      Data.errorValue = $t('marketing.maximumDiscountAmountBig')
      return true
    }
  }
  if (Data.dataForm.suitableProdType !== 0 && Data.dataForm.discountProds.length < 1) {
    Data.isTrue = true
    Data.errorValue = $t('marketing.pleaseSelectAProduct')
    return true
  }
}

// 表单提交
const dataFormSubmit = () => {
  checkDiscountItems()
  if (Data.isTrue) {
    errorMsg(Data.errorValue)
    return
  }
  dataFormRef.value.validate((valid) => {
    if (valid) {
      const startTime = Data.dataForm.startTime
      const endTime = Data.dataForm.endTime
      Data.dataForm.startTime = Data.dataForm.startTime && Data.startTimeValue ? Data.dataForm.startTime + ' ' + Data.startTimeValue + ':00' : ''
      Data.dataForm.endTime = Data.dataForm.endTime && Data.endTimeValue ? Data.dataForm.endTime + ' ' + Data.endTimeValue + ':00' : ''
      if (Data.dataForm.endTime < Data.dataForm.startTime) {
        errorMsg($t('marketing.eventEndanStartTime'))
        return
      }
      if (Data.dataForm.discountItems.length > 5) {
        ElMessage.error($t('marketing.maxActivityLevelsTips'))
        return
      }
      if (Number(Data.dataForm.discountItems[0].discount) === 0.0 || Number(Data.dataForm.discountItems[0].discount) === 0) {
        ElMessage.error($t('marketing.discountNoZero'))
        return
      }
      if (Data.isSubmitting) {
        return
      }
      Data.isSubmitting = true
      http({
        url: http.adornUrl('/admin/discount'),
        method: Data.dataForm.discountId ? 'put' : 'post',
        data: http.adornData({
          discountId: Data.dataForm.discountId || undefined,
          shopId: Data.dataForm.shopId,
          discountName: Data.dataForm.discountName,
          mobilePic: Data.dataForm.mobilePic,
          pcPic: Data.dataForm.pcPic,
          pcBackgroundPic: Data.dataForm.pcBackgroundPic,
          discountRule: Data.dataForm.discountRule,
          discountType: Data.dataForm.discountType,
          suitableProdType: Data.dataForm.suitableProdType,
          maxReduceAmount: Data.dataForm.maxReduceAmount,
          startTime: Data.dataForm.startTime,
          endTime: Data.dataForm.endTime,
          status: Data.dataForm.status,
          discountItems: Data.dataForm.discountItems,
          discountProds: Data.dataForm.discountProds
        })
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            // Data.$store.commit('common/removeMainActiveTab')
            router.go(-1)
          }
        })
      }).catch(() => {
        Data.isSubmitting = false
      })
      Data.dataForm.startTime = startTime
      Data.dataForm.endTime = endTime
    }
  })
}

const onValidateField = (field) => {
  dataFormRef.value?.validateField(field)
}
</script>

<style lang="scss" scoped>
.page-discount-info {

  :deep(.el-form-item__content) {
    display: block;
  }

  .form-box {
    margin-left: 20px;
  }

  .date-picker {
    display: flex;
  }

  .pleaseEnteThan {
    word-break: break-word;
    color: #999;
  }

  .discount-tip {
    color: #a3a3a3;
    font-size: 14px;
    margin-left: 2%;
    display: inline-block;
  }

  .box-card {
    margin-bottom: 30px;
    width: 515px;

    &:last-child {
      margin-bottom: 10px;
    }

    :deep(.el-card__header) {
      padding: 15px;
    }

    .box-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 19px;
      line-height: 19px;
    }
  }

  .prod-card {
    display: flex;
    flex-wrap: wrap;
  }

  .card-prod-bottom .card-prod-name {
    text-align: center;
  }

  :deep(.el-select .el-input__suffix-inner){
      display: none;
    }
  .select-time:hover :deep(.el-input__suffix-inner){
    display: inline-flex;
  }
}
</style>
