<template>
  <div class="page-giveaway">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="test-form"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('group.actName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.name"
              clearable
              :placeholder="$t('group.actName')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('giveaway.mainProdName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.prodName"
              clearable
              :placeholder="$t('giveaway.mainProdName')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('giveaway.name')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.giveawayProdName"
              clearable
              :placeholder="$t('giveaway.name')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('combo.comboStatus')+':'"
            class="search-form-item"
          >
            <el-select
              v-model="searchForm.status"
              clearable
              :placeholder="$t('combo.comboStatus')"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="searchChange(true)"
            >
              {{ $t('shopFeature.searchBar.search') }}
            </div>
            <div
              class="default-btn"
              @click="clearSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <!-- 表格主体 -->
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('giveaway:giveaway:add')"
          class="default-btn primary-btn"
          @click="addOrUpdateHandle(0, 1)"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-con seckill-table">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            :label="$t('group.actName')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('giveaway.mainProdInfo')"
            prop="reason"
            width="320px"
          >
            <template #default="scope">
              <div class="table-cell-con">
                <div class="table-cell-image">
                  <prod-pic
                    :pic="scope.row.pic"
                  />
                </div>
                <span class="table-cell-text">{{ scope.row.prodName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="status"
            :label="$t('combo.comboStatus')"
          >
            <template #default="scope">
              <div class="tag-text">
                {{ [$t("group.expired"), $t("groups.processing"), $t('groups.notOpen')]
                  [scope.row.status] }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="startTime"
            :label="$t('coupon.startTime')"
          />

          <el-table-column
            prop="endTime"
            :label="$t('coupon.endTime')"
          />

          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="250"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('giveaway:giveaway:edit') && scope.row.status === 1 || scope.row.status === 2"
                  type="text"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row.giveawayId, 1)"
                >
                  {{ $t("groups.editEvent") }}
                </div>
                <div
                  v-if="scope.row.status === 0"
                  type="text"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row.giveawayId, 2)"
                >
                  {{ $t("crud.viewTitle") }}
                </div>
                <div
                  v-if="scope.row.status === 1"
                  class="default-btn text-btn"
                >
                  <div
                    v-if="isAuth('giveaway:giveaway:close')"
                    type="text"
                    class="default-btn text-btn"
                    @click="changeStatus(scope.row.giveawayId, $t('groups.invalidActivity'), 0)"
                  >
                    {{ $t("groups.invalidActivity") }}
                  </div>
                </div>
                <div
                  v-if="isAuth('giveaway:giveaway:delete')"
                  class="default-btn text-btn"
                  @click="changeStatus(scope.row.giveawayId, $t('combo.delete'), -1)"
                >
                  {{ $t("text.delBtn") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 表格主体end -->
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'

const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件

  dataList: [],
  pageIndex: 1,
  pageSize: 10,
  totalPage: 0,
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  // 头部搜索表单
  searchForm: {
    name: null,
    prodName: null,
    status: null
  },
  statusList: [
    {
      value: 0,
      label: $t('groups.expired')
    },
    {
      value: 1,
      label: $t('groups.processing')
    },
    {
      value: 2,
      label: $t('groups.notOpen')
    }
  ],
  dataListLoading: false,
  offlineEventHandleVisible: false,
  dataListSelections: [],
  addOrUpdateVisible: false
})

const { dataList, page, searchForm, statusList } = toRefs(Data)

onMounted(() => {
  getDataList()
})

// 获取数据列表
const getDataList = (page, newData = false) => {
  Data.dataListLoading = true
  if (newData || !Data.theData) {
    Data.theData = JSON.parse(JSON.stringify(Data.searchForm))
  }
  http({
    url: http.adornUrl('/shop/giveaway/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize
        },
        Data.theData
      )
    )
  }).then(({ data }) => {
    Data.dataList = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  })
}

const router = useRouter()
// 新增 / 修改
const addOrUpdateHandle = (giveawayId, pageType) => {
  router.push({
    path: '/marketing/giveaway/add-or-update',
    query: {
      giveawayId,
      pageType
    }
  })
}

// 改变套餐状态
const changeStatus = (giveawayId, operDesc, status) => {
  ElMessageBox.confirm(`${$t('sys.makeSure')}[` + operDesc + `]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  })
    .then(() => {
      http({
        url: http.adornUrl('/shop/giveaway/changeStatus'),
        method: 'put',
        params: http.adornParams({
          status,
          giveawayId
        })
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            getDataList(Data.page)
          }
        })
      })
    })
    .catch(() => {

    })
}

// 条件查询
const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  Data.page.pageSize = 10
  getDataList(Data.page, newData)
}

const clearSearch = () => {
  Data.searchForm.name = null
  Data.searchForm.prodName = null
  Data.searchForm.status = null
  Data.searchForm.giveawayProdName = null
}

// 每页数量变更
const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

// 页数变更
const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}
</script>

<style lang="scss" scoped>
</style>
