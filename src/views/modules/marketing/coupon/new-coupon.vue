<template>
  <div class="page-new-coupon">
    <div class="new-page-title">
      <div class="line" />
      <div class="text">
        {{
          dataForm.couponId
            ? $t('marketing.modifyCoupon')
            : $t('marketing.newCoupon')
        }}
      </div>
    </div>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      class="form-box"
      label-width="auto"
      @submit.prevent
      @keyup.enter="dataFormSubmit()"
    >
      <el-form-item
        :label="$t('marketing.couponName')"
        prop="couponName"
      >
        <el-input
          v-model="dataForm.couponName"
          maxlength="20"
          show-word-limit
          class="coupon-input"
          :placeholder="$t('marketing.couponName')"
          :disabled="dataForm.couponId !== 0"
        />
      </el-form-item>
      <el-form-item
        :label="$t('coupon.launchStatus')"
        prop="putonStatus"
      >
        <el-radio-group
          v-model="dataForm.putonStatus"
          :disabled="dataForm.putonStatus < 0"
        >
          <el-radio :label="0">
            {{ $t("coupon.waitAutoLaunch") }}
            <el-tooltip
              class="item"
              effect="light"
              placement="top"
            >
              <template #content>
                <div>
                  {{ $t("coupon.launchTip") }}
                </div>
              </template>

              <span>
                <el-icon><QuestionFilled /></el-icon>
              </span>
            </el-tooltip>
          </el-radio>
          <el-radio :label="1">
            {{ $t("coupon.launched") }}
          </el-radio>
          <el-radio :label="4">
            {{ $t("coupon.waitLaunch") }}
          </el-radio>
          <el-radio
            :label="-1"
            :disabled="true"
          >
            {{ $t("coupon.cancelLaunch") }}
            <el-tooltip
              class="item"
              effect="light"
              placement="top"
            >
              <template #content>
                <div>
                  {{ $t("coupon.launchTip1") }}
                </div>
              </template>

              <span>
                <el-icon><QuestionFilled /></el-icon>
              </span>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 投放时间 -->
      <el-form-item
        v-if="dataForm.putonStatus === 0"
        :label="$t('coupon.timeToMarket')"
        prop="launchTime"
      >
        <el-date-picker
          v-model="dataForm.launchTime"
          value-format="YYYY-MM-DD"
          type="date"
          :placeholder="$t('coupon.chooseLaunchTime')"
          style="width:140px"
        />
        <el-time-select
          v-model="launchTimeValue"
          start="00:00"
          step="00:30"
          end="23:30"
          style="width:106px"
          :placeholder="$t('time.startTime')"
        />
      </el-form-item>
      <el-form-item
        :label="$t('coupon.getWay')"
        prop="getWay"
      >
        <el-radio-group v-model="dataForm.getWay">
          <el-radio :label="0">
            {{ $t("coupon.receiveDirectly") }}
          </el-radio>
          <el-radio :label="1">
            {{ $t("coupon.exchangeOrSystemIssue") }}
          </el-radio>
          <!-- <el-radio :label="3">兑换券</el-radio> -->
        </el-radio-group>
      </el-form-item>
      <!-- 优惠券类型 -->
      <el-form-item
        :label="$t('coupon.couponType')"
        prop="couponType"
      >
        <el-radio-group
          v-model="dataForm.couponType"
          :disabled="dataForm.couponId !== 0"
        >
          <el-radio :label="1">
            {{ $t("coupon.voucher") }}
          </el-radio>
          <el-radio :label="2">
            {{ $t("coupon.discountCoupon") }}
          </el-radio>
          <!-- <el-radio :label="3">兑换券</el-radio> -->
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="$t('marketing.conditionsOfUse')"
        prop="cashCondition"
      >
        <el-input
          v-model="dataForm.cashCondition"
          class="coupon-input1"
          type="text"
          :placeholder="$t('marketing.conditionsOfUse')"
          style="width:240px"
          :disabled="dataForm.couponId !== 0"
          @blur="toFloat('cashCondition')"
        >
          <template #prepend>
            {{ $t("marketing.full") }}
          </template>
          <template #append>
            {{ $t("admin.dollar") }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        v-if="dataForm.couponType === 1"
        :label="$t('marketing.reductionAmount')"
        prop="reduceAmount"
      >
        <el-input
          v-model="dataForm.reduceAmount"
          class="coupon-input1"
          :placeholder="$t('marketing.reductionAmount')"
          type="number"
          :max="99999"
          :min="0.01"
          :step="0.01"
          style="width:240px"
          :disabled="dataForm.couponId !== 0"
          @blur="toFloat('reduceAmount')"
        >
          <template #append>
            {{ $t("admin.dollar") }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        v-if="dataForm.couponType === 2"
        :label="$t('marketing.discountAmount')"
        prop="couponDiscount"
      >
        <el-input
          v-model="dataForm.couponDiscount"
          :placeholder="$t('marketing.pleaseEnterDiscount')"
          type="text"
          class="coupon-input1"
          :disabled="dataForm.couponId !== 0"
          @blur="toFloat('couponDiscount')"
        >
          <template #append>
            {{ $t("marketing.fold") }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        :label="$t('coupon.effectiveType')"
        prop="validTimeType"
      >
        <el-radio-group
          v-model="dataForm.validTimeType"
          :disabled="dataForm.couponId !== 0"
        >
          <el-radio :label="1">
            {{ $t("marketing.fixedTime") }}
          </el-radio>
          <el-radio :label="2">
            {{
              $t("marketing.effectiveAfterReceipt")
            }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 固定时间数据 START -->
      <div v-if="dataForm.validTimeType === 1">
        <el-form-item
          v-if="dataForm.validTimeType === 1"
          :label="$t('coupon.startTime')"
          prop="startTime"
        >
          <el-date-picker
            v-model="dataForm.startTime"
            :disabled="dataForm.couponId !== 0"
            value-format="YYYY-MM-DD"
            type="date"
            style="width:140px"
            :placeholder="$t('marketing.chooseStartTime')"
          />
          <el-time-select
            v-model="startTimeValue"
            start="00:00"
            step="00:30"
            end="23:30"
            :class="[startTimeValue ? 'select-time': '']"
            style="width:106px"
            :disabled="dataForm.couponId !== 0"
            :placeholder="$t('time.startTime')"
            @blur="handleValidate('startTime')"
          />
        </el-form-item>
        <el-form-item
          v-if="dataForm.validTimeType === 1"
          :label="$t('coupon.endTime')"
          prop="endTime"
        >
          <el-date-picker
            v-model="dataForm.endTime"
            value-format="YYYY-MM-DD"
            type="date"
            style="width:140px"
            :placeholder="$t('marketing.chooseEndTime')"
            :disabled="dataForm.putonStatus < 0"
          />
          <el-time-select
            v-model="endTimeValue"
            start="00:00"
            step="00:30"
            end="23:30"
            :class="[endTimeValue ? 'select-time': '']"
            :disabled="dataForm.putonStatus < 0"
            style="width:106px"
            :placeholder="$t('time.endTime')"
            @blur="handleValidate('endTime')"
          />
        </el-form-item>
      </div>
      <!-- 固定时间数据 END -->
      <!-- 领取后生效数据 START -->
      <el-form-item
        v-if="dataForm.validTimeType === 2"
        :label="$t('marketing.afterReceivingTheCoupon')"
        prop="afterReceiveDays"
      >
        <el-input
          v-model="dataForm.afterReceiveDays"
          type="number"
          class="coupon-input1"
          oninput="if(value>3652)value=1"
          :max="3652"
          :min="0"
          onkeyup="value=value.replace(/[^\d.]/g,'');"
          style="width:240px"
          :disabled="dataForm.couponId !== 0"
        >
          <template #append>
            {{ $t("marketing.effectiveDays") }}
          </template>
        </el-input>
        <el-tooltip
          class="item"
          effect="dark"
          :content="$t('marketing.maxTimeTip')"
          placement="top"
        >
          <el-icon><InfoFilled /></el-icon>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        v-if="dataForm.validTimeType === 2"
        :label="$t('marketing.validDate')"
        prop="validDays"
      >
        <el-input
          v-model="dataForm.validDays"
          type="number"
          class="coupon-input1"
          oninput="if(value>3652)value=1"
          :max="3652"
          :min="0"
          onkeyup="value=value.replace(/[^\d.]/g,'');"
          style="width:240px"
          :disabled="dataForm.couponId !== 0"
        >
          <template #append>
            {{ $t("form.day") }}
          </template>
        </el-input>
        <el-tooltip
          class="item"
          effect="dark"
          :content="$t('marketing.maxTimeTip')"
          placement="top"
        >
          <el-icon><InfoFilled /></el-icon>
        </el-tooltip>
      </el-form-item>
      <!-- 领取后生效数据 END -->
      <!-- 每人限领 -->
      <el-form-item
        :label="$t('marketing.perPerson')"
        prop="limitNum"
      >
        <el-input
          v-model="dataForm.limitNum"
          :min="1"
          onkeyup="value=value.replace(/[^\d.]/g,'');"
          class="coupon-input1"
          type="number"
          :disabled="dataForm.putonStatus < 0"
          style="width:240px"
          @blur="toFloat('limitNum')"
        >
          <template #append>
            {{ $t("marketing.piece") }}
          </template>
        </el-input>
      </el-form-item>
      <!-- 库存 -->
      <el-form-item
        :label="$t('marketing.inventory')"
        prop="stocks"
      >
        <el-input
          v-model="dataForm.stocks"
          type="number"
          :min="1"
          onkeyup="value=value.replace(/[^\d.]/g,'');"
          class="coupon-input1"
          style="width:240px"
          :disabled="dataForm.couponId !== 0"
          @blur="toFloat('stocks')"
        >
          <template #append>
            {{ $t("marketing.piece") }}
          </template>
        </el-input>
      </el-form-item>
      <!-- 适用商品 -->
      <el-form-item
        :label="$t('marketing.applicableGoods')"
        prop="suitableProdType"
      >
        <el-radio-group
          v-model="dataForm.suitableProdType"
          :disabled="dataForm.putonStatus < 0"
        >
          <el-radio :label="0">
            {{ $t("marketing.allProdsPar") }}
          </el-radio>
          <el-radio :label="1">
            {{ $t("marketing.participateInD") }}
          </el-radio>
          <el-radio :label="2">
            {{ $t("marketing.specifiedProduct") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="dataForm.suitableProdType === 1 || dataForm.suitableProdType === 2"
        :label="' '"
      >
        <div
          plain
          class="default-btn"
          @click="prodsSelectHandle()"
        >
          {{ $t("product.select") }}
        </div>
      </el-form-item>
      <!-- 商品 -->
      <el-form-item
        v-if="dataForm.suitableProdType !== 0 && dataForm.couponProds.length > 0"
        :label="' '"
        style="width: 100%"
      >
        <el-row>
          <el-col
            v-for="(couponProd, index) in dataForm.couponProds"
            :key="index"
          >
            <el-card
              :body-style="{ padding: '0px' }"
              style="height: 160px; width: 120px"
            >
              <prod-pic
                height="104px"
                width="100%"
                :pic="couponProd.pic"
              />
              <div class="card-prod-bottom">
                <span class="card-prod-name">{{ couponProd.prodName }}</span>
                <el-button
                  type="text"
                  class="card-prod-name-button"
                  @click="deleteProd(index)"
                >
                  {{ $t("text.delBtn") }}
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="' '">
        <div
          class="default-btn"
          @click="back()"
        >
          {{
            $t("crud.filter.cancelBtn")
          }}
        </div>
        <div
          v-if="dataForm.putonStatus > -1"
          class="default-btn primary-btn"
          @click="dataFormSubmit()"
        >
          {{
            $t("crud.filter.submitBtn")
          }}
        </div>
      </el-form-item>
    </el-form>
    <!-- 商品选择弹窗-->
    <prods-select
      v-if="prodsSelectVisible"
      ref="prodsSelectRef"
      @refresh-select-prods="selectCouponProds"
    />
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const Data = reactive({
  dataForm: {
    couponId: 0,
    couponName: '',
    subTitle: '',
    couponType: 1,
    getWay: 0,
    reduceAmount: 0,
    couponDiscount: 0,
    cashCondition: 0,
    validTimeType: 1,
    launchTime: null,
    startTime: null,
    endTime: null,
    afterReceiveDays: 0,
    validDays: 1,
    stocks: 1,
    suitableProdType: 0,
    limitNum: 1,
    putonStatus: 0,
    couponProds: []
  },
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  putonStatus: 0,
  errorTip: false,
  dataListSelections: [],
  prodsSelectVisible: false,
  launchTimeValue: '',
  startTimeValue: '',
  endTimeValue: ''
})
const { dataForm, prodsSelectVisible, launchTimeValue, startTimeValue, endTimeValue } = toRefs(Data)

const validate = (rule, value, callback) => {
  if (!/^[1-9]\d*$|^[1-9]\d*\.\d\d?$|^0\.\d\d?$/.test(value)) {
    callback(new Error($t('live.pleaseEnteThan0')))
  } else if (value >= 10000000000000) {
    callback(new Error($t('live.numberLimit')))
  } else {
    callback()
  }
}
const validCouponName = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('marketing.couponNameCannotBeEmpty')))
  } else {
    callback()
  }
}
const validTittle = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('shop.titCanNoBlank')))
  } else {
    callback()
  }
}
const validateNumber = (rule, value, callback) => {
  if (!/^[1-9]\d*$/.test(value)) {
    callback(new Error($t('formData.pleaseThan0')))
  } else if (value >= 2147483600) {
    callback(new Error($t('formData.stockLimit')))
  } else {
    callback()
  }
}
const validateStocks = (rule, value, callback) => {
  if (value === 0 && Data.dataForm.couponId) {
    callback()
  } else if (!/^[1-9]\d*$/.test(value)) {
    callback(new Error($t('formData.pleaseThan0')))
  } else if (value >= 2147483600) {
    callback(new Error($t('formData.stockLimit')))
  } else {
    callback()
  }
}
const validateeDays = (rule, value, callback) => {
  if (!/^[0-9]\d*$/.test(value)) {
    callback(new Error($t('marketing.pleaseaTo0')))
  } else {
    callback()
  }
}
const validateeOneDays = (rule, value, callback) => {
  if (!/^[1-9]\d*$/.test(value)) {
    callback(new Error($t('marketing.pleaseaTo1')))
  } else {
    callback()
  }
}
const discountValidate = (rule, value, callback) => {
  if (!/^[1-9]\d*$|^[1-9]\d*\.\d\d?$|^0\.\d\d?$/.test(value)) {
    callback(new Error($t('marketing.pleaseTo10')))
  } else if (parseInt(value) >= 10 || parseInt(value) < 0) {
    callback(new Error($t('marketing.pleaseTo10')))
  } else {
    callback()
  }
}
const validateLaunchTime = (rule, value, callback) => {
  if (!Data.launchTimeValue) {
    callback(new Error($t('coupon.launchTimeTip1')))
  }
  callback()
}
// 是否已重新效验
let proven = false
const validateTime = (rule, value, callback) => {
  if (!proven) {
    proven = true
    dataFormRef.value.validateField(rule.field === 'startTime' ? 'endTime' : 'startTime')
  } else {
    proven = false
  }

  if (rule.field === 'startTime' && (!Data.startTimeValue || !Data.dataForm.startTime)) {
    callback(new Error($t('formData.startTimeCannotBeEmpty')))
  }
  if (rule.field === 'endTime' && (!Data.endTimeValue || !Data.dataForm.endTime)) {
    callback(new Error($t('formData.endTimeCannotBeEmpty')))
  }
  const launchTime = dataForm.value.launchTime + ' ' + launchTimeValue.value + ':00'
  const startTime = Data.dataForm.startTime + ' ' + Data.startTimeValue + ':00'
  const endTime = Data.dataForm.endTime + ' ' + Data.endTimeValue + ':00'
  if (rule.field === 'endTime' && dataForm.value.putonStatus === 0 && (Date.parse(launchTime) >= Date.parse(endTime))) {
    callback(new Error($t('coupon.launchTimeTip2')))
  }
  if (rule.field === 'startTime' && Date.parse(startTime) >= Date.parse(endTime)) {
    callback(new Error($t('marketing.timeCanThanOrEqualTo')))
  }
  if (Data.dataForm.couponId === 0 && rule.field === 'endTime' && new Date() > Date.parse(endTime)) {
    callback(new Error($t('groups.endTime')))
  } else {
    callback()
  }
}

const dataRule = reactive({
  couponName: [
    { required: true, message: $t('marketing.couponNameCannotBeEmpty'), trigger: 'blur' },
    { validator: validCouponName, trigger: 'blur' }
  ],
  launchTime: [
    { required: true, message: $t('coupon.launchTimeTip1'), trigger: 'blur' },
    { validator: validateLaunchTime, trigger: 'blur' }
  ],
  subTitle: [
    { required: true, message: $t('shop.titCanNoBlank'), trigger: 'blur' },
    { validator: validTittle }
  ],
  couponType: [
    { required: true, message: $t('marketing.couponTypeCannotBeEmpty'), trigger: 'blur' }
  ],
  reduceAmount: [
    { required: true, message: $t('marketing.theDedEmpty'), trigger: 'blur' },
    { validator: validate, trigger: 'blur' }
  ],
  limitNum: [
    { required: true, message: $t('marketing.theLimitetBeEmpty'), trigger: 'blur' },
    { validator: validateNumber, trigger: 'blur' }
  ],
  afterReceiveDays: [
    { required: true, message: $t('marketing.collectioeEmpty'), trigger: 'blur' },
    { validator: validateeDays, trigger: 'blur' }
  ],
  validDays: [
    { required: true, message: $t('marketing.effectiveEmpty'), trigger: 'blur' },
    { validator: validateeOneDays, trigger: 'blur' }
  ],
  couponDiscount: [
    { required: true, message: $t('marketing.theDiscouBeEmpty'), trigger: 'blur' },
    { validator: discountValidate, trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: $t('formData.startTimeCannotBeEmpty'), trigger: 'blur' },
    { validator: validateTime, trigger: 'blur' }
  ],
  endTime: [
    { required: true, message: $t('formData.endTimeCannotBeEmpty'), trigger: 'blur' },
    { validator: validateTime, trigger: 'blur' }
  ],
  cashCondition: [
    { required: true, message: $t('marketing.conditionBeEmpty'), trigger: 'blur' },
    { validator: validate, trigger: 'blur' }
  ],
  validTimeType: [
    { required: true, message: $t('marketing.effectiveotBeEmpty'), trigger: 'blur' }
  ],
  stocks: [
    { required: true, message: $t('marketing.invenEmpty'), trigger: 'blur' },
    { validator: validateStocks, trigger: 'blur' }
  ],
  suitableProdType: [
    { required: true, message: $t('marketing.applicabltBeEmpty'), trigger: 'blur' }
  ]
})

const route = useRoute()
const commonStore = useCommonStore()
onMounted(() => {
  const couponId = route.query.couponId
  init(couponId)
  const title = couponId ? $t('marketing.modifyCoupon') : $t('marketing.newCoupon')
  commonStore.replaceSelectMenu(title)
})

const dataFormRef = ref()
// 获取数据列表
const init = (couponId) => {
  Data.dataForm.couponId = couponId || 0
  Data.putonStatus = 0
  nextTick(() => {
    dataFormRef.value.resetFields()
    const datetimeRange = getDateTimeRange()
    Data.dataForm.launchTime = datetimeRange.startTime
    Data.dataForm.startTime = datetimeRange.startTime
    Data.dataForm.endTime = datetimeRange.endTime
    Data.launchTimeValue = datetimeRange.currentTime
    Data.startTimeValue = datetimeRange.currentTime
    Data.endTimeValue = datetimeRange.currentTime
    if (Data.dataForm.couponId) {
      getDataList()
    }
  })
}

const getDataList = () => {
  http({
    url: http.adornUrl(`/admin/coupon/info/${Data.dataForm.couponId}`),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    Data.dataForm = data
    Data.launchTimeValue = Data.dataForm.launchTime ? Data.dataForm.launchTime.substring(11, Data.dataForm.launchTime.length - 3) : ''
    Data.startTimeValue = Data.dataForm.startTime ? Data.dataForm.startTime.substring(11, Data.dataForm.startTime.length - 3) : ''
    Data.endTimeValue = Data.dataForm.endTime ? Data.dataForm.endTime.substring(11, Data.dataForm.endTime.length - 3) : ''
    Data.dataForm.launchTime = getParseTime(Data.dataForm.launchTime, '{y}-{m}-{d}')
    Data.dataForm.startTime = getParseTime(Data.dataForm.startTime, '{y}-{m}-{d}')
    Data.dataForm.endTime = getParseTime(Data.dataForm.endTime, '{y}-{m}-{d}')
    Data.putonStatus = Data.dataForm.putonStatus
  })
}

const toFloat = (type) => {
  const val = parseFloat(Data.dataForm[type])
  if (val) {
    Data.dataForm[type] = val

    // 限制减免金额最多5位数字 99999.99
    if (type === 'reduceAmount') {
      Data.dataForm[type] = formatNumber(val, 99999.99)
    }
    // 限制使用条件满元 9999999.99
    if (type === 'cashCondition') {
      Data.dataForm[type] = formatNumber(val, 9999999.99)
    }
  }
}

/**
     * 格式化输入的优惠券 减免金额 / 使用条件
     * @param {*} number 输入金额
     * @param {*} maxNumber 限制的最大值
     */
const formatNumber = (number, maxNumber) => {
  number = parseFloat(number.toFixed(2))
  if (!number) {
    return
  }
  if (number >= maxNumber) {
    return maxNumber
  }
  if (number < 0.01) {
    return 0.01
  }
  return number
}

const prodsSelectRef = ref()
// 显示添加指定商品弹框
const prodsSelectHandle = () => {
  Data.prodsSelectVisible = true
  nextTick(() => {
    prodsSelectRef.value.init(Data.dataForm.couponProds)
  })
}

// 删除指定商品
const deleteProd = (index) => {
  Data.dataForm.couponProds.splice(index, 1)
}

// 添加指定商品
const selectCouponProds = (prods) => {
  Data.prodsSelectVisible = false
  if (prods) {
    Data.dataForm.couponProds = prods
  }
}

// 表单提交
const dataFormSubmit = Debounce(() => {
  if (Data.errorTip) {
    ElMessage({
      message: $t('marketing.quantitssThan0'),
      type: 'error',
      duration: 1500
    })
    return
  }
  dataFormRef.value.validate((valid) => {
    if (valid) {
      if (Data.dataForm.putonStatus === 0) {
        if (Date.parse(Data.dataForm.launchTime + ' ' + Data.launchTimeValue + ':00') <= Date.now()) {
          ElMessage({
            message: $t('coupon.launchTimeTip'),
            type: 'error',
            duration: 1500
          })
          return
        } else if (Data.dataForm.launchTime === null || Data.launchTimeValue === null) {
          ElMessage({
            message: $t('coupon.launchTimeTip1'),
            type: 'error',
            duration: 1500
          })
          return
        }
      }
      const launchTime = Data.dataForm.launchTime
      const startTime = Data.dataForm.startTime
      const endTime = Data.dataForm.endTime
      Data.dataForm.launchTime = Data.dataForm.launchTime && Data.launchTimeValue ? Data.dataForm.launchTime + ' ' + Data.launchTimeValue + ':00' : ''
      Data.dataForm.startTime = Data.dataForm.startTime && Data.startTimeValue ? Data.dataForm.startTime + ' ' + Data.startTimeValue + ':00' : ''
      Data.dataForm.endTime = Data.dataForm.endTime && Data.endTimeValue ? Data.dataForm.endTime + ' ' + Data.endTimeValue + ':00' : ''
      if (Data.dataForm.couponType === 1 && (parseFloat(Data.dataForm.cashCondition) <= parseFloat(Data.dataForm.reduceAmount))) {
        ElMessage.error($t('marketing.amounnCannotBe'))
        return false
      }
      if (Data.dataForm.suitableProdType !== 0 && Data.dataForm.couponProds.length === 0) {
        ElMessage.error($t('marketing.pleaseSelectAProduct'))
        return false
      }
      http({
        url: http.adornUrl('/admin/coupon'),
        method: Data.dataForm.couponId ? 'put' : 'post',
        data: http.adornData({
          couponId: Data.dataForm.couponId || undefined,
          couponName: Data.dataForm.couponName,
          subTitle: Data.dataForm.subTitle,
          couponType: Data.dataForm.couponType,
          getWay: Data.dataForm.getWay,
          reduceAmount: Data.dataForm.reduceAmount,
          couponDiscount: Data.dataForm.couponDiscount,
          cashCondition: Data.dataForm.cashCondition,
          validTimeType: Data.dataForm.validTimeType,
          launchTime: Data.dataForm.putonStatus === 0 ? Data.dataForm.launchTime : null,
          startTime: Data.dataForm.startTime,
          endTime: Data.dataForm.endTime,
          afterReceiveDays: Data.dataForm.afterReceiveDays,
          validDays: Data.dataForm.validDays,
          stocks: Data.dataForm.stocks,
          suitableProdType: Data.dataForm.suitableProdType,
          limitNum: Data.dataForm.limitNum,
          putonStatus: Data.dataForm.putonStatus,
          couponProds: Data.dataForm.couponProds
        })
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            Data.dataForm.couponProds = []
            back()
          }
        })
      })
      Data.dataForm.launchTime = launchTime
      Data.dataForm.startTime = startTime
      Data.dataForm.endTime = endTime
    }
  })
}, 1500)

const router = useRouter()
const back = () => {
  router.push('/marketing/coupon/index')
}

const handleValidate = (props) => {
  nextTick(() => {
    dataFormRef.value.validateField(props)
  })
}
</script>

<style lang="scss" scoped>
.page-new-coupon {
  .coupon-input {
    width: 520px;
  }
  .coupon-input1 {
    width: 220px;
  }
  .form-box {
    margin-left: 40px;
  }

  :deep(.el-select .el-input__suffix-inner){
      display: none;
    }
  .select-time:hover :deep(.el-input__suffix-inner){
    display: inline-flex;
  }
}
:deep(.asterisk-left .el-row > .el-col) {
  flex: 0 0 auto;
  margin: 0 20px 10px 0;
  .el-card {
    width: 120px;
    height: 160px;
  }
}
</style>
