<template>
  <div class="page-seckill">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="test-form"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('product.prodName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.prodName"
              clearable
              :placeholder="$t('product.prodName')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('groups.eventName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.seckillName"
              clearable
              :placeholder="$t('groups.eventName')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('group.actStatus')+':'"
            class="search-form-item"
          >
            <el-select
              v-model="searchForm.status"
              clearable
              :placeholder="$t('group.actStatus')"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="searchChange(true)"
            >
              {{ $t('shopFeature.searchBar.search') }}
            </div>
            <div
              class="default-btn"
              @click="clearSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <!-- 表格主体 -->
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('seckill:seckill:save')"
          class="default-btn primary-btn"
          @click="addOrUpdateHandle()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-con seckill-table">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            prop="seckillName"
            :label="$t('groups.eventName')"
            width="100"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.seckillName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('product.prodInfo')"
            width="340"
          >
            <template #default="{row}">
              <div class="table-cell-con">
                <div class="table-cell-image">
                  <ImgShow :src="row.pic" />
                </div>
                <span class="table-cell-text">{{ row.prodName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="startTime"
            :label="$t('coupon.startTime')"
            min-width="100"
          />

          <el-table-column
            prop="endTime"
            :label="$t('coupon.endTime')"
            min-width="100"
          />

          <el-table-column
            prop="maxNum"
            :label="$t('product.maxNum')"
          />

          <el-table-column
            prop="status"
            :label="$t('group.actStatus')"
          >
            <template #default="scope">
              <div class="tag-text">
                {{ [$t("station.close"), $t("station.open"), $t("product.violation"),
                    $t('coupon.waitReview')]
                  [scope.row.status] }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('crud.menu')"
            align="center"
            width="200"
            fixed="right"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('seckill:seckill:info')"
                  class="default-btn text-btn"
                  @click="addOrUpdateHandle(scope.row.seckillId)"
                >
                  {{ $t("live.view") }}
                </div>
                <div
                  v-if="isAuth('seckill:seckill:invalid') && scope.row.status === 1"
                  class="default-btn text-btn"
                  @click="invalidHandle(scope.row.seckillId)"
                >
                  {{ $t("station.close") }}
                </div>
                <div
                  v-if="isAuth('seckill:seckill:auditApply') && scope.row.status > 1"
                  class="default-btn text-btn"
                  @click="auditEventHandle(scope.row.seckillId)"
                >
                  {{
                    scope.row.status === 2
                      ? $t("groups.applyForListing")
                      : $t("coupon.waitReview")
                  }}
                </div>
                <div
                  v-if="isAuth('seckill:seckill:delete')"
                  class="default-btn text-btn"
                  @click="deleteHandle(scope.row.seckillId, scope.row.status)"
                >
                  {{ $t("text.delBtn") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 表格主体end -->

    <!-- 下线管理弹窗-->
    <offlineEventHandle
      v-if="offlineEventHandleVisible"
      ref="offlineEventRef"
      select-url="/seckill/seckill/getOfflineHandleEventBySeckillId"
      apply-url="/seckill/seckill/auditApply"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'

const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件

  dataList: [],
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  // 头部搜索表单
  searchForm: {
    seckillName: null,
    status: null,
    prodName: ''
  },
  statusList: [
    {
      value: 0,
      label: $t('station.close')
    },
    {
      value: 1,
      label: $t('station.open')
    },
    {
      value: 2,
      label: $t('product.violation')
    },
    {
      value: 3,
      label: $t('coupon.waitReview')
    }
  ],
  dataListLoading: false,
  offlineEventHandleVisible: false
})

const { dataList, page, searchForm, statusList, offlineEventHandleVisible } = toRefs(Data)

onMounted(() => {
  getDataList()
})

const getDataList = (page, newData = false) => {
  if (Data.page) {
    const size = Math.ceil(Data.page.total / Data.page.pageSize)
    Data.page.currentPage = (Data.page.currentPage > size ? size : Data.page.currentPage) || 1
  }
  Data.dataListLoading = true
  if (newData || !Data.theData) {
    Data.theData = JSON.parse(JSON.stringify(Data.searchForm))
  }
  http({
    url: http.adornUrl('/seckill/seckill/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize
        },
        Data.theData
      )
    )
  }).then(({ data }) => {
    data.records.forEach(element => {
      if (element.maxNum === -1) {
        element.maxNum = $t('seckill.unlimited')
      }
    })
    Data.dataList = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  })
}

const router = useRouter()
// 新增 / 修改
const addOrUpdateHandle = (id) => {
  router.push({
    path: '/marketing/seckill/add-or-update',
    query: {
      seckillId: id
    }
  })
}

const deleteHandle = (id) => {
  const ids = id ? [id] : Data.dataListSelections.map(item => {
    return item.seckillId
  })
  ElMessageBox.confirm($t('admin.isDeleOper') + '?', $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/seckill/seckill/' + id),
      method: 'delete',
      data: http.adornData({})
    }).then(() => {
      Data.page.total = Data.page.total - ids.length
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          getDataList(Data.page)
        }
      })
    })
  }).catch(() => { })
}

const invalidHandle = (id) => {
  ElMessageBox.confirm($t('seckill.makeSurateTheAct'), $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/seckill/seckill/invalid/' + id),
      method: 'put',
      data: http.adornData({})
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          refreshChange()
        }
      })
    })
  }).catch(() => { })
}

// 刷新回调用
const refreshChange = () => {
  getDataList(Data.page)
}

const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  getDataList(Data.page, newData)
}

const offlineEventRef = ref()
// 下线管理
const auditEventHandle = (id) => {
  Data.offlineEventHandleVisible = true
  nextTick(() => {
    offlineEventRef.value.init(id)
  })
}

const clearSearch = () => {
  Data.searchForm.seckillName = null
  Data.searchForm.status = null
  Data.searchForm.prodName = ''
}

// 每页数量变更
const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

// 页数变更
const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}
</script>

<style lang="scss" scoped>
</style>
