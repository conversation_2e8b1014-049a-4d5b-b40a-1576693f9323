<template>
  <div class="page-distribution-prod-log">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="test-form"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('product.prodName')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.prodName"
              clearable
              :placeholder="$t('product.prodName')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('distribution.distributor')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.nickName"
              clearable
              :placeholder="$t('distribution.distributor')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('distribution.phoneNum')+':'"
            class="search-form-item"
          >
            <el-input
              v-model="searchForm.userMobile"
              clearable
              :placeholder="$t('distribution.phoneNum')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('product.status')+':'"
            class="search-form-item"
          >
            <el-select
              v-model="searchForm.state"
              clearable
              :placeholder="$t('product.status')"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('distribution.orderNumber') + ':'">
            <el-input
              v-model="searchForm.orderNumber"
              type="text"
              clearable
              :placeholder="$t('distribution.orderNumber')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="searchChange(true)"
            >
              {{ $t('shopFeature.searchBar.search') }}
            </div>
            <div
              class="default-btn"
              @click="clearSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <!-- 表格主体 -->
    <div class="main-container">
      <!-- 表格 -->
      <div class="table-con seckill-table">
        <el-table
          ref="prodListTable"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('product.prodInfo')"
            min-width="300"
          >
            <template #default="scope">
              <div class="table-cell-con">
                <div class="table-cell-image">
                  <ImgShow :src="scope.row.pic" />
                </div>
                <span class="table-cell-text">{{ scope.row.prodName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="incomeAmount"
            width="120"
            :label="$t('distribution.commAmount')"
            sortable
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.incomeAmount || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="nickName"
            :label="$t('distribution.distributor')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.nickName || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            width="110"
            prop="userMobile"
            :label="$t('distribution.phoneNum')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.userMobile || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="placeTime"
            width="200"
            :label="$t('order.createTime')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.placeTime || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            width="190"
            prop="orderNumber"
            :label="$t('chat.orderNumber')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.orderNumber || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            align="center"
            prop="state"
            :label="$t('distribution.distriStatus')"
          >
            <template #default="scope">
              <div
                v-if="scope.row.state>0"
                class="tag-text"
              >
                {{ ['', $t("distribution.waitSettle"), $t("distribution.settled")]
                  [scope.row.state] }}
              </div>
              <div
                v-else
                class="tag-text"
              >
                {{ $t('distribution.invOrder') }}
              </div>
            </template>
          </el-table-column>
          <!--          失效-->
          <el-table-column
            align="center"
            prop="reson"
            :label="$t('distributionProdLog.lapseCase')"
          >
            <template #default="scope">
              <!-- <span v-if="scope.row.reson === 0">{{ $t('distributionProdLog.lapse0') }}</span> -->
              <span v-if="scope.row.reson === 0">-</span>
              <span v-if="scope.row.reson === 1">{{ $t('distributionProdLog.lapse1') }}</span>
              <span v-if="scope.row.reson === 2">{{ $t('distributionProdLog.lapse2') }}</span>
              <span v-if="scope.row.reson === 3">{{ $t('distributionProdLog.lapse3') }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 表格主体end -->
  </div>
</template>

<script setup>
import { onMounted, reactive } from 'vue'

const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件

  dataForm: {
    prodName: ''
  },
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  dataListLoading: false,
  dataList: [],
  // 头部搜索表单
  searchForm: {
    prodName: null,
    state: null,
    userMobile: null,
    nickName: null,
    orderNumber: null
  },
  statusList: [
    {
      label: $t('distribution.waitSettle'),
      value: 1
    }, {
      label: $t('distribution.settled'),
      value: 2
    }, {
      label: $t('distribution.invOrder'),
      value: -1
    }
  ]
})
const { page, dataList, searchForm, statusList } = toRefs(Data)

onMounted(() => {
  getDataList()
})

// 获取数据列表
const getDataList = (page, newData = false) => {
  Data.dataListLoading = true
  if (newData || !Data.theData) {
    Data.theData = JSON.parse(JSON.stringify(Data.searchForm))
  }
  http({
    url: http.adornUrl('/distribution/distributionProd/getDistributionProdLogPage'),
    method: 'get',
    params: http.adornParams(Object.assign({
      current: page ? page.currentPage : Data.page.currentPage,
      size: page ? page.pageSize : Data.page.pageSize
    }, Data.theData))
  }).then(({ data }) => {
    Data.page.total = data.total
    Data.page.pageSize = data.size
    Data.page.currentPage = data.current
    Data.dataList = data.records
    Data.dataListLoading = false
  })
}

const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  getDataList(Data.page, newData)
}

const clearSearch = () => {
  Data.searchForm.prodName = null
  Data.searchForm.state = null
  Data.searchForm.nickName = ''
  Data.searchForm.userMobile = ''
  Data.searchForm.orderNumber = ''
}

// 每页数量变更
const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

// 页数变更
const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}
</script>

<style lang="scss" scoped>

</style>
