<template>
  <div class="page-group-team">
    <!-- 新版规范 -->
    <div class="group-list-mod">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form
          ref="test-form"
          :inline="true"
          class="search-form"
          :model="searchForm"
          label-width="auto"
          @submit.prevent
        >
          <div class="input-row">
            <el-form-item
              :label="$t('groups.eventName') + ':'"
              class="search-form-item"
            >
              <el-input
                v-model="searchForm.activityName"
                clearable
                :placeholder="$t('groups.eventName')"
              />
            </el-form-item>
            <el-form-item>
              <div
                class="default-btn primary-btn"
                @click="searchChange(true)"
              >
                {{ $t('shopFeature.searchBar.search') }}
              </div>
              <div
                class="default-btn"
                @click="clearSearch"
              >
                {{ $t('shop.resetMap') }}
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 搜索栏end -->
      <!-- 表格主体 -->
      <div class="main-container">
        <!-- 表格 -->
        <div class="table-con group-team">
          <el-table
            :data="dataList"
            header-cell-class-name="table-header"
            row-class-name="table-row-low"
            style="width: 100%"
          >
            <!-- <el-table-column align="left" type="index" :label="$t('formData.serialNumber')" width="85"></el-table-column> -->
            <el-table-column
              prop="activityName"
              :label="$t('groups.eventName')"
              width="200"
            >
              <template #default="scope">
                <div>
                  <span class="table-cell-text">{{ scope.row.activityName }}</span>
                </div>
              </template>
            </el-table-column>
            <!-- 商品信息 -->
            <el-table-column
              :label="$t('product.prodInfo')"
              min-width="240"
            >
              <template #default="{row}">
                <div class="table-cell-con">
                  <div class="table-cell-image">
                    <ImgShow :src="row.pic" />
                  </div>
                  <span class="table-cell-text">{{ row.prodName }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              min-width="120"
              prop="startTime"
              :label="$t('group.groupTime')"
            >
              <template #default="scope">
                <div>
                  <span class="table-cell-text">{{ scope.row.startTime ? scope.row.startTime : $t('group.notOpenGroup') }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              min-width="100"
              prop="groupNumber"
              :label="$t('groups.numberOfParticipants')"
              sortable
            >
              <template #default="scope">
                <div>
                  <span>{{ scope.row.joinNum }}/{{ scope.row.groupNumber }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              min-width="120"
              prop="totalPrice"
              :label="$t('group.totalOrderAmount')"
              sortable
            >
              <template #default="scope">
                <div>
                  <span>{{ scope.row.totalPrice }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="status"
              :label="$t('order.status')"
              width="100px"
            >
              <template #default="scope">
                <div class="tag-text">
                  {{ [$t("group.waitGroupUnpay"),
                      $t("group.inAGroup"),
                      $t("group.succ"),
                      $t("group.failGroup"),
                  ][scope.row.status] }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              min-width="240"
              align="center"
              :label="$t('order.operation')"
            >
              <template #default="scope">
                <div
                  v-if="isAuth('group:team:info')"
                  class="default-btn text-btn marginLe"
                  @click="viewGroupTeamInfo(scope.row.groupTeamId)"
                >
                  {{ $t("groups.viewGroupOrders") }}
                </div>
                <div
                  class="default-btn text-btn marginLBtn"
                  @click="viewGroupActivityInfo(scope.row.groupActivityId)"
                >
                  {{ $t("groups.viewGroupActivityInfo") }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 分页 -->
        <el-pagination
          v-if="dataList.length"
          style="margin-top:20px"
          :current-page="page.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- 表格主体end -->
    </div>
    <group-team-info
      v-if="groupTeamInfoVisible"
      ref="groupTeamInfoRef"
    />
    <group-activity-info
      v-if="groupActivityInfoVisible"
      ref="groupActivityInfoRef"
    />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { isAuth } from '@/utils/index.js'
import GroupTeamInfo from './components/info.vue'
import GroupActivityInfo from './components/activity-info.vue'
const Data = reactive({
  theData: null, // 保存上次点击查询的请求条件
  dataList: [],
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  searchForm: {},
  dataListLoading: false,
  groupTeamInfoVisible: false,
  groupActivityInfoVisible: false
})

const { dataList, page, searchForm, groupTeamInfoVisible, groupActivityInfoVisible } = toRefs(Data)

onMounted(() => {
  getDataList()
})

const getDataList = (page, newData = false) => {
  Data.dataListLoading = true
  if (newData || !Data.theData) {
    Data.theData = JSON.parse(JSON.stringify(Data.searchForm))
  }
  http({
    url: http.adornUrl('/group/team/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize
        },
        Data.theData
      )
    )
  }).then(({ data }) => {
    Data.dataList = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  })
}

const groupTeamInfoRef = ref()
// 查看同团订单
const viewGroupTeamInfo = (id) => {
  Data.groupTeamInfoVisible = true
  nextTick(() => {
    groupTeamInfoRef.value.init(id)
  })
}

const groupActivityInfoRef = ref()
// 查看活动详情
const viewGroupActivityInfo = (id) => {
  Data.groupActivityInfoVisible = true
  nextTick(() => {
    groupActivityInfoRef.value.init(id)
  })
}
// 每页数量变更
const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}
// 页数变更
const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}

const searchChange = (newData = false) => {
  Data.page.currentPage = 1
  Data.page.pageSize = 10
  getDataList(Data.page, newData)
}
const clearSearch = () => {
  Data.searchForm.activityName = null
  Data.searchForm.status = null
}
</script>

<style lang="scss" scoped>
@media (max-width: 1440px) {
  .marginLe {
    margin-left: -5px;
  }
  .marginLBtn {
    margin-left: 0 !important;
  }
}
</style>
