<template>
  <el-dialog
    v-model="dialogTableVisible"
    class="components-group-team-info"
    :title="$t('groups.groupOrderList')"
    width="60%"
    :close-on-click-modal="false"
  >
    <el-table
      :data="tableData"
      header-cell-class-name="table-header"
      row-class-name="table-row-low"
      style="width: 100%;paddingBottom: 20px;"
    >
      <el-table-column
        prop="orderNumber"
        :label="$t('order.number')"
        width="300"
      />
      <el-table-column
        prop="activityProdPrice"
        :label="$t('order.salesPrice')"
      />
      <el-table-column
        prop="groupPrice"
        :label="$t('groups.groupPrice')"
      />
      <el-table-column
        prop="payPrice"
        :label="$t('order.actualAmount')"
      />
      <el-table-column :label="$t('groups.membership')">
        <template #default="scope">
          <span v-if="scope.row.identityType === 1 && scope.row.userId !== 0">{{ $t("groups.head") }}</span>
          <span v-if="scope.row.identityType === 0 && scope.row.userId !== 0">{{ $t("groups.member") }}</span>
          <span v-if="scope.row.userId === '0'">{{ $t("groups.robot") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        width="180"
        :label="$t('order.createTime')"
      />
      <el-table-column
        :label="$t('text.menu')"
        align="center"
        width="180"
      >
        <template #default="scope">
          <el-button
            type="text"
            :disabled="scope.row.userId === '0' || !scope.row.userId"
            @click="viewOrder(scope.row.orderNumber)"
          >
            {{ $t("order.viewOrder") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="tableData.length"
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="page.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-dialog>
</template>

<script setup>
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const Data = reactive({
  tableData: [],
  page: {
    total: 0, // 总页数
    currentPage: 1, // 当前页数
    pageSize: 10 // 每页显示多少条
  },
  dialogTableVisible: false,
  dataListLoading: false,
  GroupOrder: {
    groupTeamId: null
  }
})

const { tableData, page, dialogTableVisible } = toRefs(Data)

const init = (groupTeamId) => {
  Data.dialogTableVisible = true
  nextTick(() => {
    if (groupTeamId) {
      Data.GroupOrder.groupTeamId = groupTeamId
      getDataList(Data.page)
    }
  })
}

const getDataList = (page) => {
  Data.dataListLoading = true
  http({
    url: http.adornUrl('/group/team/info'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: page == null ? Data.page.currentPage : page.currentPage,
          size: page == null ? Data.page.pageSize : page.pageSize
        },
        Data.GroupOrder
      )
    )
  }).then(({ data }) => {
    Data.tableData = data.records
    Data.page.total = data.total
    Data.dataListLoading = false
  })
}

const router = useRouter()
const routerStore = useRouterStore()
const viewOrder = (orderNumber, userId) => {
  if (Number(userId) === 0) {
    return
  }
  if (!isAuth('order:get:info')) {
    ElMessage.info($t('homes.noPermissionAccessPage'))
    return
  }
  Data.dialogTableVisible = false
  routerStore.updateIncludePageList('order-order')
  routerStore.pushHisPageList('order-order')
  router.push({
    path: '/order/order/index',
    query: { orderNumber }
  })
}

const handleSizeChange = (val) => {
  Data.page.pageSize = val
  getDataList()
}

const handleCurrentChange = (val) => {
  Data.page.currentPage = val
  getDataList()
}

defineExpose({
  init
})

</script>
