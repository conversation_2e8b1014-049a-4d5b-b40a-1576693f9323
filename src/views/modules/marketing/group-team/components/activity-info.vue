<template>
  <el-dialog
    v-model="dialogTableVisible"
    :title="$t('groups.GroupActivityInfo')"
    width="40%"
    :close-on-click-modal="false"
  >
    <div class="mod-groupActivity-info">
      <el-form
        ref="dataFormRef"
        :model="dataForm"
        label-width="auto"
        class="form-box"
        @submit.prevent
      >
        <el-form-item :label="$t('groups.eventName')">
          <span class="table-cell-text line-clamp-one">{{ dataForm.activityName }}</span>
        </el-form-item>
        <el-form-item :label="$t('group.actStartTime')">
          <span class="table-cell-text line-clamp-one">{{ dataForm.startTime }}</span>
        </el-form-item>
        <el-form-item :label="$t('groups.eventEndTime')">
          <span class="table-cell-text line-clamp-one">{{ dataForm.endTime }}</span>
        </el-form-item>
        <el-form-item :label="$t('group.groupNum')">
          <div class="table-cell-text line-clamp-one">
            {{ dataForm.groupNumber }}
            <span>{{ $t("groups.people") }}</span>
          </div>
        </el-form-item>
        <el-form-item :label="$t('groups.groupValidityPeriod')">
          <div class="table-cell-text line-clamp-one">
            {{ dataForm.groupValidTime }}
            <span>{{ $t("groups.minute") }}</span>
          </div>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('groups.tip4')"
            placement="right"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('groups.limitPurchase')">
          <span
            v-if="dataForm.hasMaxNum === 0"
            class="table-cell-text line-clamp-one"
          >{{ $t('station.close') }}</span>
          <span
            v-if="dataForm.hasMaxNum === 1"
            class="table-cell-text line-clamp-one"
          >{{ $t('groups.turnOn') }}</span>
        </el-form-item>
        <el-form-item
          v-if="dataForm.hasMaxNum"
          :label="$t('product.maxNum')"
        >
          <div class="table-cell-text line-clamp-one">
            {{ dataForm.maxNum }}
            <span>{{ $t("groups.memberPeople") }}</span>
          </div>
        </el-form-item>
        <el-form-item :label="$t('groups.simulation')">
          <div>
            <span
              v-if="dataForm.hasRobot === 0"
              class="table-cell-text line-clamp-one"
            >{{ $t('station.close') }}</span>
            <span
              v-if="dataForm.hasRobot === 1"
              class="table-cell-text line-clamp-one"
            >{{ $t('groups.turnOn') }}</span>
          </div>
        </el-form-item>
        <el-form-item :label="$t('groups.groupMode')">
          <span
            v-if="dataForm.hasGroupTip === 0"
            class="table-cell-text line-clamp-one"
          >{{ $t('station.close') }}</span>
          <span
            v-if="dataForm.hasGroupTip === 1"
            class="table-cell-text line-clamp-one"
          >{{ $t('groups.turnOn') }}</span>
        </el-form-item>
        <el-form-item :label="$t('groups.activityWarmUp')">
          <span
            v-if="dataForm.isPreheat === 0"
            class="table-cell-text line-clamp-one"
          >{{ $t('station.close') }}</span>
          <span
            v-if="dataForm.isPreheat === 1"
            class="table-cell-text line-clamp-one"
          >{{ $t('groups.turnOn') }}</span>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script setup>
const dataForm = ref({
  groupActivityId: null,
  shopId: null,
  activityName: null,
  status: null,
  startTime: null,
  endTime: null,
  groupValidTime: null,
  groupNumber: null,
  hasMaxNum: 0,
  maxNum: null,
  hasRobot: 0,
  hasLeaderPrice: 0,
  isPreheat: 0,
  hasGroupTip: 0,
  groupOrderCount: null,
  groupNumberCount: null,
  createTime: null,
  updateTime: null,
  groupProds: [],
  validEndTime: []
})
const dialogTableVisible = ref(false)

const init = (groupActivityId) => {
  dialogTableVisible.value = true
  nextTick(() => {
    if (groupActivityId) {
      http({
        url: http.adornUrl('/group/activity/info/' + groupActivityId),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        dataForm.value = data
      })
    }
  })
}
defineExpose({
  init
})
</script>
