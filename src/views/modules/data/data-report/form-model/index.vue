<template>
  <div class="mod-order-order">
    <div class="main-container">
      <div v-if="formList && formList.length">
        <div
          v-for="form in formList"
          :key="form.id"
          class="card-item"
        >
          <el-card
            shadow="never"
            class="card-item-card"
          >
            <template #header>
              <div class="clearfix title">
                <el-tooltip
                  v-if="form.formName.length > 12"
                  effect="light"
                  placement="top"
                >
                  <template #content>
                    <span>
                      {{ form.formName.substring(0,25) }}
                      <br v-if="form.formName.length>25">
                      {{ form.formName.substring(25,50) }}
                      <br v-if="form.formName.length>50">
                      {{ form.formName.substring(50,75) }}
                      <br v-if="form.formName.length>75">
                      {{ form.formName.substring(75,1000) }}
                    </span>
                  </template>
                  <div class="hid">
                    {{ form.formName }}
                  </div>
                </el-tooltip>
                <span v-else>{{ form.formName }}</span>
              </div>
            </template>
            <div class="title-text">
              <el-tooltip
                effect="light"
                placement="top"
              >
                <template #content>
                  <div>
                    {{ form.content.substring(0,25) }}
                    <br v-if="form.content.length>25">
                    {{ form.content.substring(25,50) }}
                    <br v-if="form.content.length>50">
                    {{ form.content.substring(50,75) }}
                    <br v-if="form.content.length>75">
                    {{ form.content.substring(75,1000) }}
                  </div>
                </template>
                <span>{{ form.content }}</span>
              </el-tooltip>
            </div>
            <div class="content-container">
              <div class="content">
                <span>{{ $t("formData.timePeriod") }}：</span>
                <span class="content-item">{{ $t("formData.natural") }}{{ form.timeType === 1?$t("formData.date"):form.timeType === 2?$t("formData.week"):$t('formData.month') }}</span>
              </div>
              <div class="content">
                <span>{{ $t("formData.timeLimit") }}：</span>
                <span
                  class="content-item"
                >{{ $t('formData.near') }}{{ form.timeRamge }}{{ form.timeType === 1?$t('formData.day'):form.timeType === 2?$t('formData.week'):$t('formData.month') }}</span>
              </div>
              <div class="content">
                <span>{{ $t('formData.numberOfIndicators') }}：</span>
                <span class="content-item">{{ form.formItemList.length }}个</span>
              </div>
              <div class="content-last">
                <span class="content-title">{{ $t('formData.reportIndicators') }}：</span>
                <span class="content-last-item">
                  <el-tooltip
                    effect="light"
                    placement="top"
                  >
                    <template #content><div>
                      {{ form.formItems.substring(0,25) }}
                      <br v-if="form.formItems.length>25">
                      {{ form.formItems.substring(25,50) }}
                      <br v-if="form.formItems.length>50">
                      {{ form.formItems.substring(50,75) }}
                      <br v-if="form.formItems.length>75">
                      {{ form.formItems.substring(75,1000) }}
                    </div></template>
                    <span>{{ form.formItems }}</span>
                  </el-tooltip>
                </span>
              </div>
            </div>
            <div style="text-align:center;">
              <div
                class="default-btn primary-btn"
                @click="onAddHandle(form.formId)"
              >
                {{ $t("formData.addMyReport") }}
              </div>
            </div>
          </el-card>
        </div>
      </div>
      <el-empty
        v-else
        class="empty-form"
        :description="$t('shop.noData')"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="onGetDataList"
    />
  </div>
</template>

<script setup>
import AddOrUpdate from '../components/add-or-update.vue'

onMounted(() => {
  // 携带参数查询
  onGetDataList()
})
/**
 * 获取数据列表
 */
const formList = ref(null)
const onGetDataList = () => {
  http({
    url: http.adornUrl('/admin/form/getRecommendFormList'),
    method: 'get'
  }).then(({ data }) => {
    formList.value = data || []
    formList.value.forEach(form => {
      let items = ''
      form.formItemList.forEach(item => {
        items = items + item.value + '、'
      })
      items = items.substring(0, items.length - 1)
      form.formItems = items
    })
  })
}
// 新增 / 修改
const addOrUpdateRef = ref(null)
const addVisible = ref(false)
const onAddHandle = (id) => {
  addVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id, true)
  })
}

</script>

<style lang="scss" scoped>
.mod-order-order {
  .main-container {
    .card-item {
      float: left;
      width: 298px;
      background: #FFFFFF;
      margin: 5px 10px 15px 10px;
      .card-item-card {
        border-radius: 0;
      }
      &:hover {
        .card-item-card {
          border: 1px solid #155BD4;
        }
      }
      :deep(.el-card__body) {
        padding: 15px;
      }
    }
    .title-text {
      width: 100%;
      height: 65px;
      color: #989898;
      font-size: 14px;
      line-height: 22px;
      word-break: break-word;
      word-wrap:break-word;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
    }
    .content-container {
      border: 1px solid #eee;
      border-left: none;
      border-right: none;
      padding: 15px 0;
      margin: 15px 0;
    }
  }

}
  div :deep(.el-card__header) {
    border-bottom: none;
    padding-bottom: 0;
  }
  div :deep(.el-card__body) {
    padding-top: 18px;
  }
  .title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }

  .title .hid {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5em;
  }

  .content {
    color: #989898;
    font-size: 14px;
    line-height: 30px;
  }

  .content-title {
    float: left;
    width: 72px;
  }

  .content-item {
    color: #303030;
  }

  .content-last {
    height: 65px;
    color: #989898;
    font-size: 14px;
    line-height: 30px;
  }

  .content-last-item {
    float: left;
    line-height: 20px;
    width: 184px;
    color: #303030;
    padding-top: 5px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }
  .empty-form {
    margin-top: 150px;
  }
</style>
