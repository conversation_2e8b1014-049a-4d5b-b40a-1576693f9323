<template>
  <div class="mod-index-img">
    <el-dialog
      v-model="visible"
      :title="!dataForm.formId ? $t('crud.addTitle') : $t('temp.modify')"
      :close-on-click-modal="false"
      :width="dialogWidth"
      @close="onCloseDialogHandle"
    >
      <el-form
        ref="dataFormRef"
        :model="dataForm"
        :rules="dataRule"
        label-width="110px"
        @submit.prevent
      >
        <el-form-item
          :label="$t('formData.reportName')"
          prop="formName"
        >
          <el-input
            v-model="dataForm.formName"
            :placeholder="$t('formData.pleaseEnterTheReportName')"
            maxlength="50"
            show-word-limit

            clearable
          />
        </el-form-item>
        <el-form-item
          :label="$t('formData.typeOfData')"
          prop="fromType"
        >
          <el-radio-group v-model="dataForm.fromType">
            <el-radio :label="1">
              {{ $t("formData.shop") }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="$t('formData.timePeriod')"
          prop="timeType"
        >
          <el-radio-group
            v-model="dataForm.timeType"
            change="onChange"
          >
            <el-radio :label="1">
              {{ $t("formData.natureDay") }}
            </el-radio>
            <el-radio :label="2">
              {{ $t("formData.natureWeek") }}
            </el-radio>
            <el-radio :label="3">
              {{ $t("formData.naturalMoon") }}
            </el-radio>
          </el-radio-group>
          <el-tooltip
            class="item"
            effect="light"
            :content="$t('formData.accordingToTheSelectedType')"
            placement="top"
          >
            <span>
              <el-icon><QuestionFilled /></el-icon>
            </span>
          </el-tooltip>
        </el-form-item>
        <el-form-item
          :label="$t('formData.timeFormat')"
          prop="timeFormat"
        >
          <el-radio-group
            v-model="dataForm.timeFormat"
          >
            <el-radio :label="1">
              {{ $t("formData.customTime") }}
            </el-radio>
            <el-radio :label="2">
              {{ $t("formData.specifyTimeRange") }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="dataForm.timeFormat === 1"
          :label="$t('formData.customTime')"
          prop="time"
          class="customTime"
        >
          <div class="date-picker">
            <el-date-picker
              v-if="dataForm.timeType === 1"
              v-model="dataForm.time"
              type="daterange"
              width="350px"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
              :picker-options="pickerOptions"
              @input="onDaterangeChange"
            />
            <el-date-picker
              v-else-if="dataForm.timeType === 3"
              v-model="dataForm.time"
              type="monthrange"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
              :picker-options="pickerOptions"
              @input="onDaterangeChange"
            />
            <el-date-picker
              v-else-if="dataForm.timeType === 2"
              v-model="dateWeek"
              :picker-options="{'firstDayOfWeek': 1}"
              type="week"
              :format="'YYYY' + ' [' + $t('user.year') + '] ' + 'ww' + ' [' + $t('user.week') + ']'"
              :placeholder="$t('time.selectWeek')"
              @change="onWeek"
            />
          </div>
        </el-form-item>
        <el-form-item
          v-if="dataForm.timeFormat === 2"
          :label="$t('formData.timeLimit')"
          prop="timeRamge"
          style="width:500px;"
        >
          <el-input
            v-model="dataForm.timeRamge"
            maxlength="5"
            :placeholder="$t('formData.pleaseEnterTheTime')"
            oninput="value=value.replace(/[^1-9.]/g,'')"
          >
            <template #prepend>
              {{ $t("formData.near") }}
            </template>
            <template #append>
              {{ dataForm.timeType === 1?$t('formData.day'):dataForm.timeType === 2?$t('formData.week'):$t('formData.month') }}
              <el-tooltip
                class="item"
                effect="light"
                :content="$t('formData.timeTip')"
                placement="top"
              >
                <span>
                  <el-icon><QuestionFilled /></el-icon>
                </span>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          :label="$t('number')"
          prop="seq"
        >
          <el-input-number
            v-model="dataForm.seq"
            controls-position="right"
            :min="1"
            :max="9999"
            @change="onSeqChange"
          />
        </el-form-item>
        <el-form-item
          :label="$t('formData.selectIndicator')"
          prop="formItem"
          class="customTime"
        >
          <div class="box-card">
            <div style="width:100%">
              <el-checkbox
                v-for="item in formItemList"
                :key="item.id"
                v-model="item.select"
                @click.prevent="onSelectItem(item.id)"
              >
                {{ item.value }}
              </el-checkbox>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="default-btn primary-btn"
            @click="onSubmit()"
          >
            {{ $t("crud.filter.submitBtn") }}
          </div>
          <div
            class="default-btn"
            @click="onClose()"
          >
            {{ $t("crud.filter.cancelBtn") }}
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import moment from 'moment'
import { ElMessage } from 'element-plus'
import { setDialogWidth, widthChange } from '@/utils/setDialogWidth'

const emit = defineEmits(['refreshDataList'])
const pickerOptions = reactive({
  shortcuts: [{
    text: $t('formData.lastWeek'),
    onClick (picker) {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      picker.$emit('pick', [start, end])
    }
  }, {
    text: $t('formData.lastMonth'),
    onClick (picker) {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      picker.$emit('pick', [start, end])
    }
  }, {
    text: $t('formData.lastThreeMonths'),
    onClick (picker) {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      picker.$emit('pick', [start, end])
    }
  }]
})
const validateNumber = (rule, value, callback) => {
  const reg = /[^\d]/
  if (reg.test(value)) {
    callback(new Error($t('formData.pleaseThan0')))
  } else if (dataForm.value.timeFormat === 2 && value <= 0) {
    callback(new Error($t('formData.noTimeZero')))
  } else {
    callback()
  }
}
const validateFormName = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('formData.theReportNameCannotBeEmpty')))
  } else {
    callback()
  }
}
const dataRule = reactive({
  formName: [
    { required: true, message: $t('formData.theReportNameCannotBeEmpty'), trigger: 'blur' },
    { validator: validateFormName, trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: $t('formData.startTimeCannotBeEmpty'), trigger: 'blur' }
  ],
  endTime: [
    { required: true, message: $t('formData.endTimeCannotBeEmpty'), trigger: 'blur' }
  ],
  timeRamge: [
    { required: true, message: $t('formData.timeRangeCannotBeEmpty'), trigger: 'blur' },
    { validator: validateNumber, trigger: 'blur' }
  ]
})

const _them = reactive({
  dialogWidth: ''
})
const dialogWidth = toRef(_them, 'dialogWidth')
const defWidth = 950
onMounted(() => {
  dialogWidth.value = setDialogWidth(defWidth)
  widthChange(_them, defWidth)
})

// 获取分类数据 formType true:推荐报表
const dataFormRef = ref(null)
const dataForm = ref({
  formId: null,
  formName: '',
  fromType: 1,
  timeType: 1,
  timeFormat: 1,
  formItemIds: '',
  startTime: '',
  endTime: '',
  timeRamge: 1,
  time: '',
  weekTime: '',
  seq: 1
})
const dateWeek = ref('')
let formItemListSelect = []
const visible = ref(false)
const init = (id, fromType) => {
  visible.value = true
  dataForm.value.formId = id || null
  nextTick(() => {
    dataForm.value.formName = ''
    dataForm.value.content = ''
    dataForm.value.fromType = 1
    dataForm.value.timeType = 1
    dataForm.value.timeFormat = 1
    dataForm.value.formItemIds = ''
    dataForm.value.timeRamge = 1
    dataForm.value.seq = 1
    dataForm.value.time = ''
    dataForm.value.weekTime = ''
    dataForm.value.startTime = ''
    dataForm.value.endTime = ''
    formItemListSelect = []
    dataFormRef.value?.resetFields()
  })
  if (dataForm.value.formId) {
    // 获取产品数据
    http({
      url: http.adornUrl(`/admin/form/info/${dataForm.value.formId}`),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      dataForm.value = data
      if (fromType) {
        dataForm.value.recommendForm = fromType
        dataForm.value.formId = null
      }
      if (dataForm.value.timeType === 2) {
        dateWeek.value = dataForm.value.startTime
      } else {
        dataForm.value.time = dataForm.value.startTime && dataForm.value.endTime ? [moment(dataForm.value.startTime, 'YYYY-MM-DD'), moment(dataForm.value.endTime, 'YYYY-MM-DD')] : []
      }
      onGetFormItem()
    })
  } else {
    nextTick(() => {
      dataFormRef.value?.resetFields()
      dataForm.value.recommendForm = fromType
      dataForm.value.imgUrl = ''
    })
    onGetFormItem()
  }
}
const onWeek = () => {
  const date = new Date(dateWeek.value)
  dataForm.value.startTime = moment(date).add(-1, 'd').format('YYYY-MM-DD hh:mm:ss')
  dataForm.value.endTime = moment(date).add(5, 'd').format('YYYY-MM-DD hh:mm:ss')
}
const onSeqChange = () => {
  if (!dataForm.value.seq) {
    dataForm.value.seq = 1
  }
}
/**
 * 获取报表项数据
 */
const formItemList = ref([])
const onGetFormItem = () => {
  http({
    url: http.adornUrl('/admin/form/getFormItem'),
    method: 'get',
    params: http.adornParams({
      type: 1
    })
  }).then(({ data }) => {
    formItemList.value = data
    if (dataForm.value.formItemIds) {
      const ids = dataForm.value.formItemIds.split(',')
      formItemList.value.forEach(element => {
        if (ids.indexOf(element.id.toString()) > -1) {
          element.select = true
          formItemListSelect.push(element)
        } else {
          element.select = false
        }
      })
    }
  })
}
/**
 * 选择开始时间/结束时间触发事件
 */
const { proxy } = getCurrentInstance()
const onDaterangeChange = () => {
  proxy.$forceUpdate()
}
/**
 * 选择报表项
 */
const onSelectItem = (id) => {
  formItemListSelect = []
  formItemList.value.forEach(element => {
    if (element.id === id) {
      element.select = !element.select
    }
    if (element.select) {
      formItemListSelect.push(element)
    }
  })
  proxy.$forceUpdate()
}
// 表单提交
const page = {
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
}
let isSubmit = true
const onSubmit = () => {
  dataForm.value.recommendForm = false
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      if (dataForm.value.timeFormat === 1 && !(dataForm.value.time || dateWeek.value)) {
        ElMessage.error($t('formData.timeCannotBeEmpty'))
        return
      }
      if (formItemListSelect.length < 1) {
        ElMessage.error($t('formData.pleaseSelectAnIndicator'))
        return
      }
      if (!isSubmit) {
        return true
      }
      if (dataForm.value.timeFormat === 1) {
        if (dataForm.value.timeType === 1 || dataForm.value.timeType === 3) {
          dataForm.value.startTime = moment(dataForm.value.time[0]).format('YYYY-MM-DD hh:mm:ss')
          dataForm.value.endTime = moment(dataForm.value.time[1]).format('YYYY-MM-DD hh:mm:ss')
        }
      }
      isSubmit = false
      dataForm.value.formItemIds = onGetFormItemIds()
      const param = dataForm.value
      http({
        url: http.adornUrl('/admin/form'),
        method: param.formId ? 'put' : 'post',
        data: http.adornData(param)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            isSubmit = true
            visible.value = false
            emit('refreshDataList', page)
          }
        })
      }).catch(() => {
        isSubmit = true
      })
    }
  })
}
/**
 * 获取已选择的报表项id
 */
const onGetFormItemIds = () => {
  let ids = ''
  formItemListSelect.forEach(item => {
    ids = ids + item.id + ','
  })
  ids = ids.substring(0, ids.length - 1)
  return ids
}
// 关闭对话框回调
const onCloseDialogHandle = () => {
  dataFormRef.value?.resetFields()
}
/**
* 关闭弹窗
*/
const onClose = () => {
  visible.value = false
}
defineExpose({
  init
})
</script>
<style lang="scss" scoped>
.box-card {
  background: #F7F8FA;
  padding: 18px;
}
.date-picker {
  width: 350px;
}
.mod-index-img {
  .customTime :deep(.el-form-item__label:before){
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
}
.input-row {
    display: block;
    // 选择器(下拉框)  &  输入框 宽度
    .el-form-item .el-form-item__content .el-select,
    .el-form-item .el-form-item__content .el-input {
      width: 200px;
      white-space: nowrap;
      display: -webkit-inline-flex;
    }
  }
</style>
