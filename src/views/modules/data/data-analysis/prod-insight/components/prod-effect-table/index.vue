<template>
  <div>
    <el-table
      :data="tableData"
      style="width: 100%"
      header-cell-class-name="table-header"
      row-class-name="table-row"
      :default-sort="{prop: 'date', order: 'descending'}"
    >
      <el-table-column
        prop="prodInfo"
        :label="$t('temp.prodInfo')"
        fixed="left"
        min-width="350"
      >
        <template #default="scope">
          <div class="prod-image">
            <prod-pic
              height="60"
              width="60"
              :pic="scope.row.prodUrl"
            />
            <span class="name">
              <span class="prod-name">{{ scope.row.prodName }}</span>
              <span class="prod-price">{{ '￥ ' + scope.row.price }}</span>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isVisible1"
        prop="expose"
        :label="$t('dataAnaly.impressions')"
        sortable
      />
      <el-table-column
        v-if="isVisible2"
        prop="exposePersonNum"
        :label="$t('dataAnaly.exposure')"
        sortable
      />
      <el-table-column
        v-if="isVisible3"
        prop="addCartPerson"
        :label="$t('dataAnaly.numberOfAdditionalPurchases')"
      />
      <el-table-column
        v-if="isVisible4"
        prop="addCart"
        :label="$t('dataAnaly.numberOfCases')"
      />
      <el-table-column
        v-if="isVisible5"
        prop="placeOrderPerson"
        :label="$t('dataAnaly.placeOrderPerson')"
      />
      <el-table-column
        v-if="isVisible6"
        prop="payPerson"
        :label="$t('dataAnaly.payers')"
      />
      <el-table-column
        v-if="isVisible7"
        prop="placeOrderNum"
        :label="$t('dataAnaly.placeOrderNum')"
      />
      <el-table-column
        v-if="isVisible8"
        prop="payNum"
        :label="$t('dataAnaly.numberOfGoodsPaid')"
      />
      <el-table-column
        v-if="isVisible9"
        prop="placeOrderAmount"
        :label="$t('dataAnaly.placeOrderAmount')"
      />
      <el-table-column
        v-if="isVisible10"
        prop="payAmount"
        :label="$t('dataAnaly.commodityPaymentAmount')"
      />
      <el-table-column
        v-if="isVisible11"
        prop="singleProdRate"
        :label="$t('dataAnaly.singleProductConversionRate')"
      >
        <template #default="scope">
          <span>{{ scope.row.singleProdRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isVisible12"
        prop="refundNum"
        :label="$t('dataAnaly.numberOfOrdersRequestedForRefund')"
        sortable
        min-width="160"
      >
        <template #default="scope">
          <span>{{ scope.row.refundNum || '0' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isVisible13"
        prop="refundPerson"
        :label="$t('dataAnaly.numberOfPeopleApplyingForRefund')"
      >
        <template #default="scope">
          <span>{{ scope.row.refundPerson || '0' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isVisible14"
        prop="refundSuccessNum"
        :label="$t('dataAnaly.numberOfSuccessfullyRefundedOrders')"
      >
        <template #default="scope">
          <span>{{ scope.row.refundSuccessNum || '0' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isVisible15"
        prop="refundSuccessPerson"
        :label="$t('dataAnaly.numberOfSuccessfulRefunds')"
      />
      <el-table-column
        v-if="isVisible16"
        prop="refundSuccessAmount"
        :label="$t('dataAnaly.successfulRefundAmount')"
      />
      <el-table-column
        v-if="isVisible17"
        prop="refundSuccessRate"
        :label="$t('dataAnaly.refundRate')"
      >
        <template #default="scope">
          <span>{{ scope.row.refundSuccessRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        min-width="120"
        :label="$t('text.menu')"
        align="center"
      >
        <template #default="scope">
          <div
            class="default-btn text-btn"
            @click="onHandleClick(scope.row)"
          >
            {{ $t("dataAnaly.trendAnalysis") }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="trendVisible"
      :title="$t('dataAnaly.naturalDailyTrendChart')"
      width="40%"
    >
      <el-main v-loading="showLoading">
        <!-- 图表 -->
        <div
          id="prod-survey"
          class="realtime-chart-box"
          style="width:100%;height:300px"
        />
      </el-main>
    </el-dialog>
  </div>
</template>

<script setup>
import * as $echarts from 'echarts'
const props = defineProps({
  prodData: {
    type: Object,
    default: null
  }
})

const tableData = ref(props.prodData.data)
const sumData = ref(props.prodData.sumData)
watch(props.prodData, (newValue) => {
  tableData.value = newValue.data
  sumData.value = newValue.sumData
  onChangVisibles()
}, {
  deep: true
})

const trendVisible = ref(false) // 单个商品趋势弹窗
const showLoading = ref(false)
const onHandleClick = (row) => {
  trendVisible.value = true
  showLoading.value = true
  onSingleProdTrendData(row.prodId)
}
const onJudgeStrArrIncludeOtherString = (str) => {
  return sumData.value.includes(str)
}
/**
 * 控制表格列显隐
 */
const isVisible1 = ref(false)
const isVisible2 = ref(false)
const isVisible3 = ref(false)
const isVisible4 = ref(false)
const isVisible5 = ref(false)
const isVisible6 = ref(false)
const isVisible7 = ref(false)
const isVisible8 = ref(false)
const isVisible9 = ref(false)
const isVisible10 = ref(false)
const isVisible11 = ref(false)
const isVisible12 = ref(false)
const isVisible13 = ref(false)
const isVisible14 = ref(false)
const isVisible15 = ref(false)
const isVisible16 = ref(false)
const isVisible17 = ref(false)
const onChangVisibles = () => {
  isVisible1.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.impressions'))
  isVisible2.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.exposure'))
  isVisible3.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfAdditionalPurchases'))
  isVisible4.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfCases'))
  isVisible5.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.placeOrderPerson'))
  isVisible6.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.payers'))
  isVisible7.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.placeOrderNum'))
  isVisible8.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfGoodsPaid'))
  isVisible9.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.placeOrderAmount'))
  isVisible10.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.commodityPaymentAmount'))
  isVisible11.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.singleProductConversionRate'))
  isVisible12.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfOrdersRequestedForRefund'))
  isVisible13.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfPeopleApplyingForRefund'))
  isVisible14.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfSuccessfullyRefundedOrders'))
  isVisible15.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.numberOfSuccessfulRefunds'))
  isVisible16.value = onJudgeStrArrIncludeOtherString($t('dataAnaly.successfulRefundAmount'))
  isVisible17.value = onJudgeStrArrIncludeOtherString($t('home.refundRate'))
}

/**
 * 单个商品的趋势图
 */
let dateArr = [564, 324, 234, 123] // 趋势图时间数组
let seriesDataArr = [] // 趋势图数据
const onGetRealTimechartData = () => {
  const myChart = $echarts.init(document.getElementById('prod-survey'), 'walden')
  myChart.clear()
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: [$t('dataAnaly.pageviews'), $t('dataAnaly.numberOfVisitors'), $t('dataAnaly.numberOfPayments'), $t('dataAnaly.numberOfPaymentProducts')]
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateArr,
      axisLine: {
        lineStyle: {
          color: '#999'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false }
    },
    series: seriesDataArr
  }
  myChart.setOption(option, true)
  window.addEventListener('resize', function () {
    myChart.resize()
  })
  nextTick(() => {
    showLoading.value = false
  })
}
/**
 * 获取单个商品的趋势图数据
 */
const onSingleProdTrendData = (prodId) => {
  showLoading.value = true
  if (props.prodData.dateValue === 1 || props.prodData.dateValue === 4 || !props.prodData.dateValue) {
    const curProd = props.prodData.data.find(el => el.prodId === prodId)
    const date = new Date()
    dateArr = ['' + date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()]
    seriesDataArr = []
    const obj1 = onInitObj($t('dataAnaly.pageviews'))
    const obj2 = onInitObj($t('dataAnaly.numberOfVisitors'))
    const obj3 = onInitObj($t('dataAnaly.numberOfPayments'))
    const obj4 = onInitObj($t('dataAnaly.numberOfPaymentProducts'))
    obj1.data.push(curProd.expose)
    obj2.data.push(curProd.exposePersonNum)
    obj3.data.push(curProd.payPerson)
    obj4.data.push(curProd.payNum)
    seriesDataArr.push(obj1)
    seriesDataArr.push(obj2)
    seriesDataArr.push(obj3)
    seriesDataArr.push(obj4)
    nextTick(() => {
      onGetRealTimechartData()
    })
  } else {
    http({
      url: http.adornUrl('/multishop/prodAnalysis/getSingleProdTrend'),
      method: 'get',
      params: http.adornParams(
        {
          prodId,
          shopId: null,
          dateType: props.prodData.params.dateValue,
          group: props.prodData.params.group,
          status: props.prodData.params.status,
          prodName: props.prodData.params.prodName,
          startTime: props.prodData.params.startTime,
          endTime: props.prodData.params.endTime
        }
      )
    }).then(({ data }) => {
      if (data) {
        dateArr = []
        seriesDataArr = []
        const obj1 = onInitObj($t('dataAnaly.pageviews'))
        const obj2 = onInitObj($t('dataAnaly.numberOfVisitors'))
        const obj3 = onInitObj($t('dataAnaly.numberOfPayments'))
        const obj4 = onInitObj($t('dataAnaly.numberOfPaymentProducts'))
        data.forEach(i => {
          dateArr.push(i.currentDay)
          obj1.data.push(i.browse)
          obj2.data.push(i.visitor)
          obj3.data.push(i.payPerson)
          obj4.data.push(i.payNum)
        })
        seriesDataArr.push(obj1)
        seriesDataArr.push(obj2)
        seriesDataArr.push(obj3)
        seriesDataArr.push(obj4)
        onGetRealTimechartData()
      }
    })
  }
}
/**
 * 初始化一个obj
 */
const onInitObj = (name) => {
  const obj = {}
  obj.name = name
  obj.type = 'line'
  obj.smooth = true
  obj.data = []
  return obj
}

</script>
<style scoped>
.prod-image {
  width: 320px;
  display: flex;
  align-items: center;
}
.prod-image .name {
  width: 260px;
  margin-left: 10px;
  line-height: 20px;
  color: #606266;
}
.prod-image .name .prod-name{
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 20px;
}
.prod-price {
  color: red;
}
</style>
