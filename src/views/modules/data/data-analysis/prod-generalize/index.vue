<template>
  <div class="page-prod-generalize">
    <!-- 数据 -->

    <!-- 实时概况板块 -->
    <div class="realtime-situation">
      <!-- 栏目标题行 -->
      <div class="new-page-title">
        <!-- 左边 -->
        <div class="title-left">
          <p class="line" />
          <span class="text">{{ $t("dataAnaly.ovelProduvervi") }}</span>
        </div>
        <!-- 右边 -->
        <div class="new-page-title-right">
          <div class="title-right">
            <el-select
              v-model="dateValue"
              style="width: 100px"
              @change="onSetDateRange(dateValue)"
            >
              <el-option
                v-for="item in dateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span
              v-if="dateValue === 2 || dateValue === 3"
              class="title-time"
            >{{ dateRange[0] }}{{ $t("time.tip")
            }}{{ dateRange[1] }}</span>
          </div>
          <div
            v-if="dateValue === 4"
            style="margin-bottom: 8px"
            class="title-picker"
          >
            <el-date-picker
              v-model="dateRange[0]"
              style="width: 150px; margin-top: 7px"
              type="date"
              clearable
              :placeholder="$t('admin.seleData')"
              :picker-options="PickerOptions"
              @change="onHandleRangeFour()"
            />
          </div>
          <div
            v-if="dateValue === 5"
            class="title-picker"
            style="margin-bottom: 8px"
          >
            <el-date-picker
              v-model="preMonth"
              style="width: 150px; margin-top: 7px"
              type="month"
              clearable
              :placeholder="$t('dataAnaly.selectMonth')"
              :picker-options="monthPickerOptions"
              @change="onHandleMonth()"
            />
          </div>
        </div>
      </div>
      <!-- /栏目标题行 -->
      <!-- 商品概况列表 -->
      <div
        v-loading="dataListLoading"
        class="item-list"
      >
        <prod-survey-table :prod-data="prodData" />
      </div>
      <!-- /商品概况列表 -->
    </div>
    <!-- /实时概况板块 -->
    <!-- 说明文字板块 -->
    <div>
      <div class="title-text-info">
        <span>{{ $t("dataAnaly.orderTip1") }}{{ $t("dataAnaly.orderTip2") }}</span>
      </div>
    </div>
    <!-- /说明文字板块 -->
    <!-- 整体看板 -->
    <div class="whole-plate">
      <!-- 栏目标题行 -->
      <div class="new-page-title">
        <div class="title-left">
          <p class="line" />
          <span class="text">{{
            $t("dataAnaly.commodityTrendAnalysis")
          }}</span>
        </div>
        <div class="title-right">
          <el-select
            v-model="dateValue3"
            style="width: 100px"
            @change="onSetDateRange3(dateValue3)"
          >
            <el-option
              v-for="item in dateOptions2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span
            v-if="dateValue3 === 2 || dateValue3 === 3"
            class="title-time"
          >{{ dateRange3[0] }} - {{ dateRange3[1] }}</span>
          <div
            v-if="dateValue3 === 5"
            class="title-picker"
          >
            <el-date-picker
              v-model="preMonth3"
              style="width: 150px"
              clearable
              type="month"
              :placeholder="$t('time.tip')"
              :picker-options="monthPickerOptions"
              @change="onHandleMonth3()"
            />
          </div>
          <div style="margin-left: 10px">
            <el-popover
              placement="right"
              width="500"
              trigger="click"
            >
              <el-form
                :model="customIndexForm"
                class="form-inline"
                @submit.prevent
              >
                <el-form-item
                  :label="$t('dataAnaly.commodityOverview') + ':'"
                >
                  <el-checkbox-group v-model="customIndexForm.prodSurvey">
                    <el-checkbox
                      :label="$t('dataAnaly.numberOfNewProducts')"
                      name="prodSurvey"
                    />
                    <el-checkbox
                      :label="$t('dataAnaly.numberOfProductsVisited')"
                      name="prodSurvey"
                    />
                    <el-checkbox
                      :label="$t('dataAnaly.numberOfProductsForSale')"
                      name="prodSurvey"
                    />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  :label="$t('dataAnaly.commodityFlow') + ':'"
                >
                  <el-checkbox-group v-model="customIndexForm.prodFlow">
                    <el-checkbox
                      :label="$t('dataAnaly.shareVisits')"
                      name="prodSurvey"
                    />
                    <el-checkbox
                      :label="$t('dataAnaly.productViews')"
                      name="prodSurvey"
                    />
                    <el-checkbox
                      :label="$t('dataAnaly.commodityVisitors')"
                      name="prodSurvey"
                    />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  :label="$t('dataAnaly.shareVisits') + ':'"
                >
                  <el-checkbox-group v-model="customIndexForm.prodTransForm">
                    <el-checkbox
                      :label="$t('dataAnaly.numberOfCases')"
                      name="prodSurvey"
                    />
                    <el-checkbox
                      :label="$t('dataAnaly.orderNumber')"
                      name="prodSurvey"
                    />
                    <el-checkbox
                      :label="$t('dataAnaly.numberOfPayment')"
                      name="prodSurvey"
                    />
                  </el-checkbox-group>
                </el-form-item>
                <span id="maxSelectSpan">{{ $t("dataAnaly.mostChoices") }}{{ maxSelectNum
                }}{{ $t("dataAnaly.item") }}</span>
                <span>{{ $t("dataAnaly.chosen") }} {{ selectNum }}
                  {{ $t("dataAnaly.item") }}</span>
              </el-form>
              <template #reference>
                <div

                  class="default-btn"
                >
                  {{
                    $t("dataAnaly.selectTheIndicatorToDisplay")
                  }}
                </div>
              </template>
            </el-popover>
          </div>
        </div>
      </div>
      <!-- /栏目标题行 -->
      <!-- 图表 -->
      <div class="charts-box">
        <div
          id="time-survey"
          class="realtime-chart-box"
          style="width: 100%; height: 300px"
        />
      </div>

      <!-- /图表 -->
    </div>
    <!-- /整体看板 -->

    <!-- 商品排行整体看板 -->
    <div class="refund-plate">
      <!-- 栏目标题行 -->
      <div class="new-page-title">
        <div class="title-left">
          <p class="line" />
          <span class="text">{{ $t("dataAnaly.commodityRanking") }}</span>
        </div>
        <div class="title-right">
          <el-select
            v-model="dateValue2"
            style="width: 100px"
            @change="onSetDateRange2(dateValue2)"
          >
            <el-option
              v-for="item in dateOptions2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span
            v-if="dateValue2 === 2 || dateValue2 === 3"
            class="title-time"
          >{{ dateRange2[0] }}{{ $t("time.tip") }}{{ dateRange2[1] }}</span>
          <div
            v-if="dateValue2 === 5"
            class="title-picker"
          >
            <el-date-picker
              v-model="preMonth2"
              clearable
              style="width: 150px"
              type="month"
              :placeholder="$t('dataAnaly.selectMonth')"
              :picker-options="monthPickerOptions"
              @change="onHandleMonth2()"
            />
          </div>
        </div>
      </div>
      <!-- /栏目标题行 -->

      <!-- 下 -->
      <div class="ranking-box">
        <div class="ranking-left">
          <div class="ranking-title">
            {{ $t("dataAnaly.paymentAmountTOP") }}
          </div>
          <el-table
            :data="payAmountTopData"
            style="width: 100%"
            :header-cell-style="analysisTableHeaderClass"
            :cell-style="analysisTableCellClass"
          >
            <el-table-column
              :label="$t('home.rank')"
              type="index"
              width="100"
            />
            <el-table-column
              :label="$t('product.prodInfo')"
              type="index"
              width="500"
            >
              <template #default="scope">
                <div
                  class="table-cell-con"
                  @click="onToProdDetail(scope.row)"
                >
                  <div class="table-cell-image">
                    <prod-pic
                      :pic="scope.row.pic"
                    />
                  </div>
                  <span class="table-cell-text">{{ scope.row.prodName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('home.payAmount')"
              align="center"
              prop="payAmount"
              :formatter="(row, column, cellValue, index)=>parseFloat(cellValue).toFixed(2)"
            />
          </el-table>
        </div>
        <div class="ranking-right">
          <div class="ranking-title">
            {{ $t("dataAnaly.numberOfVisitorsTOP") }}
          </div>
          <el-table
            :header-cell-style="analysisTableHeaderClass"
            :cell-style="analysisTableCellClass"
            :data="visitorTopData"
            style="width: 100%"
          >
            <el-table-column
              :label="$t('home.rank')"
              type="index"
              width="100"
            />
            <el-table-column
              :label="$t('product.prodInfo')"
              width="430"
            >
              <template #default="scope">
                <div
                  class="table-cell-con"
                  @click="onToProdDetail(scope.row)"
                >
                  <div class="table-cell-image">
                    <prod-pic
                      :pic="scope.row.pic"
                    />
                  </div>
                  <span class="table-cell-text">{{ scope.row.prodName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('dataAnaly.numberOfVisitors')"
              prop="visitorNum"
              align="center"
            />
            <el-table-column
              :label="$t('dataAnaly.visitToPayConversionRate')"
              align="right"
              prop="visitorToPayRate"
            >
              <template #default="scope">
                <span>{{ parseFloat(scope.row.visitorToPayRate * 100).toFixed(2) + '%' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- /商品排行整体看板 -->

    <!-- 商品详情弹窗 -->
    <prod-detail
      v-if="prodDetailVisiable"
      ref="prodDetailRef"
      @handle-hide-pop="prodDetailVisiable=false"
    />
  </div>
</template>

<script setup>
import * as $echarts from 'echarts'
import ProdSurveyTable from './components/prod-survey-table/index.vue'
import ProdDetail from './components/prod-detail/index.vue'
import moment from 'moment'

const PickerOptions = reactive({
  disabledDate (time) {
    const date = moment().startOf('days')
    return (
      time.getTime() > date.valueOf()
    )
  }
})
const monthPickerOptions = reactive({
  disabledDate (time) {
    const month1 = moment().startOf('month')
    return (
      time.getTime() > month1.valueOf()
    )
  }
})
const dateOptions = [
  {
    label: $t('dataAnaly.nearly7Days'),
    value: 2
  },
  {
    label: $t('dataAnaly.nearly30Days'),
    value: 3
  },
  {
    label: $t('dataAnaly.natureDay'),
    value: 4
  },
  {
    label: $t('dataAnaly.naturalMoon'),
    value: 5
  }
]
const dateOptions2 = [
  {
    label: $t('dataAnaly.nearly7Days'),
    value: 2
  },
  {
    label: $t('dataAnaly.nearly30Days'),
    value: 3
  },
  {
    label: $t('dataAnaly.naturalMoon'),
    value: 5
  }
]

const prodData = reactive({
  dateValue: 2, // 商品整体概况,时间选择状态
  data: {
    newProd: 0, // 新增商品数
    visitedProd: 0, // 被访问商品数
    dynamicSale: 0, // 动销商品数
    expose: 0, // 商品曝光数
    browse: 0, // 商品浏览量
    visitor: 0, // 商品访客数
    addCart: 0, // 加购件数
    orderNum: 0, // 下单件数
    payNum: 0 // 支付件数
  }, // 商品整体概况数据
  lastData: {},
  rate: {}
}) // 商品 概况列表

const maxSelectNum = 3 // 最多选择几项
const customIndexForm = reactive({
  prodSurvey: [], // '新增商品数', '被访问商品数', '动销商品数'
  prodFlow: [],
  prodTransForm: []
})
const selectNum = ref(0)
const sumData = ref([])
const textShare = (div, color) => {
  /**
   * 文字效果
   */
  div.style.color = color
}
watch([() => customIndexForm.prodSurvey, () => customIndexForm.prodFlow, () => customIndexForm.prodTransForm], () => {
  selectNum.value = customIndexForm.prodSurvey.length + customIndexForm.prodFlow.length + customIndexForm.prodTransForm.length
  sumData.value = []
  sumData.value = sumData.value.concat(customIndexForm.prodSurvey).concat(customIndexForm.prodFlow).concat(customIndexForm.prodTransForm)
  nextTick(() => {
    const checks = document.getElementsByName('prodSurvey')
    const span = document.getElementById('maxSelectSpan')
    checks.forEach(i => {
      if (selectNum.value >= maxSelectNum) {
        textShare(span, 'red')
        if (!i.checked) {
          i.setAttribute('disabled', 'true')
        }
      }
    })
    if (selectNum.value < maxSelectNum) {
      textShare(span, 'black')
      checks.forEach(i => {
        i.removeAttribute('disabled')
      })
    }
  })
}, {
  deep: true
})
watch(() => sumData.value, () => {
  onControlTrendDataShow()
})

let myChartTimeSurvey = null
const dateValue = ref(2)
onMounted(() => {
  dateValue.value = 2
  onSetDateRange(2)
  onSetDateRange2(2)
  onSetDateRange3(2)
  nextTick(() => {
    setTimeout(function () {
      customIndexForm.prodFlow = [$t('dataAnaly.shareVisits'), $t('dataAnaly.productViews'), $t('dataAnaly.commodityVisitors')]
      myChartTimeSurvey.resize()
    }, 500)
  })
})

const prodDetailRef = ref(null)
const prodDetailVisiable = ref(false)
/**
 * 到商品详情
 */
const onToProdDetail = (row) => {
  prodDetailVisiable.value = true
  nextTick(() => {
    prodDetailRef.value?.init(row.prodId)
  })
}

/**
 * 1:今天 2: 近七天 3:近30天 4:昨天 5:自然月(前一个月如当前月为7月，自然月为6月)
 */
const nowDate = null
const dateRange = ref([])
const preMonth = ref('') // 前一个月
let dateRange1 = [] // 商品整体概况的时间选择范围
const onSetDateRange = (val) => {
  let startDay = null
  let endDay = null
  prodData.dateValue = val
  if (val === 1) {
    startDay = 0
    endDay = 0
    dateRange1 = [moment().format('L') + ' 00:00:00', nowDate]
    onGetProdSurvey()
    return
  } else if (val === 2) {
    startDay = -7
    endDay = -1
  } else if (val === 3) {
    startDay = -30
    endDay = -1
  } else if (val === 4) {
    startDay = 0
    endDay = 0
  } else if (val === 5) {
    preMonth.value = onGetPreMonth()
    onGetProdSurvey()
    return
  } else {
    return
  }
  // 开始时间
  const startTime = moment().add(startDay, 'days').startOf('days')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days')
  dateRange.value = [startTime.format('L'), endTime.format('L')]
  dateRange1 = [startTime.format('LL'), endTime.format('LL')]
  onGetProdSurvey()
}
/**
 * 2: 近七天 3:近30天 5:自然月(前一个月如当前月为7月，自然月为6月)
 */
const dateRange2 = ref([]) // 展示的时间参数
let dateRange2Param = [] // 要传递的时间参数
const preMonth2 = ref('') // 前一个月
const onSetDateRange2 = (val) => {
  let startDay = null
  let endDay = null
  if (val === 2) {
    startDay = -7
    endDay = -1
  } else if (val === 3) {
    startDay = -30
    endDay = -1
  } else if (val === 5) {
    preMonth2.value = onGetPreMonth()
    onGetPayAmountTop()
    return
  } else {
    return
  }
  // 开始时间
  const startTime = moment().add(startDay, 'days').startOf('days')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days')
  dateRange2.value = [startTime.format('L'), endTime.format('L')]
  dateRange2Param = [startTime.format('LL'), endTime.format('LL')]
  onGetPayAmountTop()
}
/**
 * 2: 近七天 3:近30天 5:自然月(前一个月如当前月为7月，自然月为6月)
 */
let dateRange3Param = []
const dateRange3 = ref([]) // 展示的时间参数
const preMonth3 = ref('') // 前一个月
const onSetDateRange3 = (val) => {
  let startDay = null
  let endDay = null
  if (val === 2) {
    startDay = -7
    endDay = -1
  } else if (val === 3) {
    startDay = -30
    endDay = -1
  } else if (val === 5) {
    preMonth3.value = onGetPreMonth()
    onGetProdTrendAnalysis()
    return
  } else {
    return
  }
  // 开始时间
  const startTime = moment().add(startDay, 'days').startOf('days')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days')
  dateRange3.value = [startTime.format('L'), endTime.format('L')]
  dateRange3Param = [startTime.format('LL'), endTime.format('LL')]
  onGetProdTrendAnalysis()
}
/**
 /**
 * 获取前一个月的时间
 */
const onGetPreMonth = () => {
  const date = moment().add(-1, 'month').startOf('month')
  // 上一个月的开始时间
  const timeStar = moment(date).startOf('month').format('LL')
  // 上一个月的结束时间
  const timeEnd = moment(date).endOf('month').format('LL')
  dateRange1 = [timeStar, timeEnd]
  dateRange2Param = [timeStar, timeEnd]
  dateRange3Param = [timeStar, timeEnd]
  return moment(date).format('L')
}
const onGetMonthToStr = (date) => {
  const start = moment(date).startOf('month').format('LL')
  const end = moment(date).endOf('month').format('LL')
  return [start, end]
}
/**
 * 当dateRange == 4 监听dateRange[0]的变化
 */
const onHandleRangeFour = () => {
  const date = dateRange.value[0]
  if (date) {
    const start = moment(date).startOf('days').format('LL')
    const end = moment(date).endOf('days').format('LL')
    dateRange1 = [start, end]
  } else {
    dateRange1 = ['1971-01-01 00:00:00', '1971-01-01 00:00:00']
  }
  onGetProdSurvey()
}
/**
 * 当dateRange == 5 监听dateRange[0]的变化
 */
const onHandleMonth = () => {
  if (preMonth.value) {
    dateRange1 = onGetMonthToStr(preMonth.value)
  } else {
    dateRange1 = ['1971-01-01 00:00:00', '1971-01-01 00:00:00']
  }
  onGetProdSurvey()
}
const onHandleMonth2 = () => {
  if (preMonth2.value) {
    dateRange2Param = onGetMonthToStr(preMonth2.value)
  } else {
    dateRange2Param = ['1971-01-01 00:00:00', '1971-01-01 00:00:00']
  }
  onGetPayAmountTop()
}
const onHandleMonth3 = () => {
  if (preMonth3.value) {
    dateRange3Param = onGetMonthToStr(preMonth3.value)
  } else {
    dateRange3Param = ['1971-01-01 00:00:00', '1971-01-01 00:00:00']
  }
  onGetProdTrendAnalysis()
}
/**
 * 获取商品概况数据
 */
const dataListLoading = ref(false)
const onGetProdSurvey = () => {
  dataListLoading.value = true
  http({
    url: http.adornUrl('/multishop/prodAnalysis/getProdSurvey'),
    method: 'get',
    params: http.adornParams(
      {
        shopId: null,
        dateType: dateValue.value,
        startTime: dateRange1[0],
        endTime: dateRange1[1]
      }
    )
  }).then(({ data }) => {
    if (data) {
      prodData.data = data.data
      prodData.lastData = data.lastData
      prodData.rate = data.rate
      dataListLoading.value = false
    }
  })
}
/**
 * 商品趋势分析数据
 */
const dateValue3 = ref(2)
let dateArr = [] // 趋势图时间数组
let seriesDataArr = [] // 所有数据
const onGetProdTrendAnalysis = () => {
  http({
    url: http.adornUrl('/multishop/prodAnalysis/getProdTrendAnalysis'),
    method: 'get',
    params: http.adornParams(
      {
        dateType: dateValue3.value,
        startTime: dateRange3Param[0],
        endTime: dateRange3Param[1]
      }
    )
  }).then(({ data }) => {
    if (data) {
      dateArr = []
      seriesDataArr = []
      const obj1 = onInitObj($t('dataAnaly.numberOfNewProducts'))
      const obj2 = onInitObj($t('dataAnaly.numberOfProductsVisited'))
      const obj3 = onInitObj($t('dataAnaly.numberOfProductsForSale'))
      const obj4 = onInitObj($t('dataAnaly.shareVisits'))
      const obj5 = onInitObj($t('dataAnaly.productViews'))
      const obj6 = onInitObj($t('dataAnaly.commodityVisitors'))
      const obj7 = onInitObj($t('dataAnaly.numberOfCases'))
      const obj8 = onInitObj($t('dataAnaly.orderNumber'))
      const obj9 = onInitObj($t('dataAnaly.numberOfPayment'))
      data.forEach(i => {
        dateArr.push(i.currentDay)
        obj1.data.push(i.newProd)
        obj2.data.push(i.visitedProd)
        obj3.data.push(i.dynamicSale)
        obj4.data.push(i.shareVisit)
        obj5.data.push(i.browse)
        obj6.data.push(i.visitor)
        obj7.data.push(i.addCart)
        obj8.data.push(i.orderNum)
        obj9.data.push(i.payNum)
      })
      seriesDataArr.push(obj1)
      seriesDataArr.push(obj2)
      seriesDataArr.push(obj3)
      seriesDataArr.push(obj4)
      seriesDataArr.push(obj5)
      seriesDataArr.push(obj6)
      seriesDataArr.push(obj7)
      seriesDataArr.push(obj8)
      seriesDataArr.push(obj9)
    }
    onControlTrendDataShow()
    onGetRealTimechartData()
  })
}
/**
 * 控制趋势图的数据显示
 */
let seriesControlDataArr = [] // 控制显示的数据
const lineStyle = [
  {
    normal: {
      color: '#29CB97',
      lineStyle: {
        color: '#29CB97'
      }
    }
  },
  {
    normal: {
      color: '#0058FF',
      lineStyle: {
        color: '#0058FF'
      }
    }
  },
  {
    normal: {
      color: '#F56C6C',
      lineStyle: {
        color: '#F56C6C'
      }
    }
  }
]
const areaStyle = [
  {
    color: new $echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: 'rgba(41, 203, 151, 0.41)'
    }, {
      offset: 1,
      color: '#ffffff'
    }])
  },
  {
    color: new $echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: 'rgba(24,144,255,0.6)'
    }, {
      offset: 1,
      color: '#ffffff'
    }])
  },
  {
    color: new $echarts.graphic.LinearGradient(0, 0, 0, 1, [{
      offset: 0,
      color: '#F56C6C'
    }, {
      offset: 1,
      color: '#ffffff'
    }])
  }
]
const onControlTrendDataShow = () => {
  seriesControlDataArr = []
  let length = 0
  sumData.value.forEach(i => {
    length = length + 1
    seriesDataArr.forEach(element => {
      if (i === element.name) {
        element.smooth = true
        element.itemStyle = lineStyle[length - 1]
        element.areaStyle = areaStyle[length - 1]
        seriesControlDataArr.push(element)
      }
    })
  })
  if (sumData.value.length < 1) {
    seriesControlDataArr = []
  }
  if (length === sumData.value.length) {
    onGetRealTimechartData()
  }
}
/**
 * 初始化一个obj
 */
const onInitObj = (name) => {
  const obj = {}
  obj.name = name
  obj.type = 'line'
  obj.data = []
  return obj
}
/**
 * 商品趋势分析表
 */
const onGetRealTimechartData = () => {
  myChartTimeSurvey = $echarts.init(document.getElementById('time-survey'), 'walden')
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: { // 图例配置
      data: sumData.value,
      selectedMode: false, // 关闭图例选择
      itemHeight: 8, // 图例标记的图形宽度。
      itemWidth: 18, // 图例标记的图形高度。
      icon: 'roundRect', // 图例项的 icon。
      itemGap: 40, // 图例每项之间的间隔
      textStyle: { // 图例的公用文本样式。
        color: '#999999',
        size: 12,
        padding: [0, 0, 0, 10]
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    // x轴的颜色和宽度

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dateArr,
      axisLine: {
        lineStyle: {
          color: '#999'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false }
    },
    series: seriesControlDataArr
  }
  myChartTimeSurvey.setOption(option, true)
  window.addEventListener('resize', function () {
    myChartTimeSurvey.resize()
  })
}
/**
 * 获取支付金额TOP
 */
const dateValue2 = ref(2)
const payAmountTopData = ref([]) // 支付金额TOP
const visitorTopData = ref([]) // 访问top
const onGetPayAmountTop = () => {
  payAmountTopData.value = []
  visitorTopData.value = []
  http({
    url: http.adornUrl('/multishop/prodAnalysis/getPayAmountTop'),
    method: 'get',
    params: http.adornParams(
      {
        current: 1,
        size: 6,
        dateType: dateValue2.value,
        startTime: dateRange2Param[0],
        endTime: dateRange2Param[1]
      }
    )
  }).then(({ data }) => {
    if (data) {
      payAmountTopData.value = data.payAmounts
      visitorTopData.value = data.visitors
    }
  })
}

/**
 * 数据表格头部样式
 */
const analysisTableHeaderClass = () => {
  return {
    height: '42px', background: '#FFF', color: '#666666', 'font-weight': '500', border: 'none'
  }
}
/**
 * 数据表格单元格样式
 */
const analysisTableCellClass = () => {
  return {
    height: '64px', padding: '8px 0', color: '#000'
  }
}

</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
