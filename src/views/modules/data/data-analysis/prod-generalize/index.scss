
.page-prod-generalize {
  .title-text-info {
    padding: 20px;
    height: 60px;
    margin-top: 20px;
    margin-bottom: 35px;
    display: flex;
    align-items: center;
    border: 1px solid #94B4EB;
    background: #edf4ff;
    box-shadow: 0 2px 6px rgba(51, 51, 51, 0.04);
  }
  .title-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .new-page-title  {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .new-page-title-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .title-right {
    display: flex;
    align-items: center;
    .title-time {
      padding-right: 0;
    }
  }
  .title-picker {
    margin-left: 10px;
    margin-right: 10px;
  }
  .title-time {
    color: #999999;
    vertical-align: middle;
    display: inline-block;
    padding: 0 10px;
  }

  .charts-title .charts-title-item .line {
    width: 18px;
    height: 8px;
    opacity: 1;
    border-radius: 8px;
    margin-right: 10px;
  }
  /**
    整体看板
     */
  .whole-plate {
    margin: 20px 0 30px;
  }

  /**
    退款看板
     */
  .refund-plate {
    // 下
    .ranking-box {
      margin-top: 10px;
      display: flex;
      width: 100%;
      .ranking-left,
      .ranking-right {
        display: flex;
        flex-direction: column;
        width: calc(50% - 9px);
        height: 100%;
        box-sizing: border-box;
        border: 1px solid #EAF0F4;
        border-bottom: none;
        :deep(.el-table td) {
          border: none;
        }
      }

      .ranking-left {
        margin-right: 20px;
      }
      .ranking-left :deep(.el-table__header-wrapper),
      .ranking-right :deep(.el-table__header-wrapper) {
        padding: 10px 0 !important;
      }
      .ranking-title {
        font-size: 16px;
        font-weight: 400;
        line-height: 50px;
        height: 50px;
        color: #463333;
        padding-left: 20px;
        margin-bottom: 10px;
        background: #F7F8FA;
      }
    }
  }
  // 图片 + 文本
  .table-cell-con {
    display: flex;
    align-items: center;
    cursor: pointer;
    .table-cell-image {
      width: 42px;
      height: 42px;
      margin-right: 10px;
      :deep(img) {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .table-cell-text {
      flex: 1;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
      word-break: break-word;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 20px;
    }
  }
}
.charts-box {
  border: 1px solid #EAF0F4;
  padding: 28px 10px;
  width: 100%;
}
