<template>
  <!-- 商品概况表格 -->
  <div class="item-list">
    <!-- 商品概况表格 -->
    <div class="ranking-right">
      <table
        class="table"
        style="width: 100%;"
      >
        <!-- 商品概况行 -->
        <tr>
          <td>
            <span style="font-size: 14px; font-weight:bold;color: black; margin-left:20px;">{{ $t("dataAnaly.commodityOverview") }}</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.numberOfNewProducts') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.addProdDescribe')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.newProd ? prodData.data.newProd : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.newProdRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.newProdRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.newProdRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.newProdRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.newProdRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.newProdRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.numberOfProductsVisited') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.prodTip2')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span
              style="font-size: 14px; font-weight:bold;color: black;"
            >{{ prodData.data.visitedProd ? prodData.data.visitedProd : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.visitedProdRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.visitedProdRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.visitedProdRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.visitedProdRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.visitedProdRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.visitedProdRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.numberOfProductsForSale') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip0')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span
              style="font-size: 14px; font-weight:bold;color: black;"
            >{{ prodData.data.dynamicSale ? prodData.data.dynamicSale : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.dynamicSaleRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.dynamicSaleRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.dynamicSaleRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.dynamicSaleRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.dynamicSaleRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.dynamicSaleRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
        </tr>
        <!-- /商品概况行 -->
        <!-- 商品流量行 -->
        <tr>
          <td>
            <span style="font-size: 14px; font-weight:bold;color: black; margin-left:20px;">{{ $t("dataAnaly.commodityFlow") }}</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.shareVisits') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip1')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.shareVisit ? prodData.data.shareVisit : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.shareVisitRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.shareVisitRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.shareVisitRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.shareVisitRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.shareVisitRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.shareVisitRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.productViews') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip2')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.browse ? prodData.data.browse : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.browseRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.browseRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.browseRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.browseRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.browseRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.browseRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.commodityVisitors') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip3')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.visitor ? prodData.data.visitor : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.visitorRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.visitorRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.visitorRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.visitorRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.visitorRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.visitorRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
        </tr>
        <!-- /商品流量行 -->
        <!-- 商品转换行 -->
        <tr>
          <td>
            <span style="font-size: 14px; font-weight:bold;color: black; margin-left:20px;">{{ $t("dataAnaly.commodityConversion") }}</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.numberOfCases') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip4')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.addCart ? prodData.data.addCart : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.addCartRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.addCartRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.addCartRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.addCartRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.addCartRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.addCartRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.orderNumber') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip5')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.orderNum ? prodData.data.orderNum : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.orderNumRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.orderNumRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.orderNumRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.orderNumRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.orderNumRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.orderNumRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
          <td>
            <span class="ranking-text">{{ $t('dataAnaly.numberOfPayment') }}</span>
            <el-popover
              placement="top"
              width="200"
              trigger="hover"
              :content="$t('dataAnaly.timeTip6')"
            >
              <template #reference>
                <el-icon class="ranking-text">
                  <QuestionFilled />
                </el-icon>
              </template>
            </el-popover>
            <br>
            <span style="font-size: 14px; font-weight:bold;color: black;">{{ prodData.data.payNum ? prodData.data.payNum : 0 }}</span>
            <br>
            <span
              :class="['font-set',prodData.dateValue === 1 ? 'hidden':'']"
            >{{ [' -',' -',$t('dataAnaly.days7Before'),$t('dataAnaly.days30Before'),$t('dataAnaly.fromThePreviousDay'),$t('dataAnaly.monthBefore')][prodData.dateValue] }}</span>
            <img
              v-if="prodData.rate.payNumRate < 0 && prodData.dateValue !== 1"
              src="~@/assets/img/downArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <img
              v-if="prodData.rate.payNumRate > 0 && prodData.dateValue !== 1"
              src="~@/assets/img/upArrow.png"
              style="margin-bottom: 2px"
              width="8"
              height="10"
              alt=""
            >
            <span
              v-if="prodData.rate.payNumRate !== 0 && prodData.dateValue !== 1"
              class="font-set"
              :class="[prodData.rate.payNumRate > 0 ? 'font-green' : 'font-red']"
            >{{ ratio((prodData.rate.payNumRate * 100).toFixed(2)) }}</span>
            <span
              v-if="prodData.rate.payNumRate === 0 && prodData.dateValue !== 1"
              class="font-set font-gray"
            >- 0%</span>
          </td>
        </tr>
        <!-- /商品转换行 -->
      </table>
    </div>
    <!-- /商品概况表格 -->
  </div>
</template>

<script setup>
const ratio = computed(() => {
  return (value) => {
    value = isNaN(value) ? 0 : value
    if (value < 0) {
      return value * -1 + '%'
    }
    return value + '%'
  }
})
defineProps({
  prodData: {
    type: Object,
    default: null
  }
})

</script>
<style lang="scss" scoped>
.font-set {
  color: darkgrey;
  font-size: 12px;
  margin-right: 10px;
}
.hidden {
  visibility: hidden; // 不显示内容，但是需要占位
}
.table {
  border-collapse: collapse;
}
.table tr {
  margin: 5px 0;
  border-bottom: 1px whitesmoke solid; // 每一行表格的结束的分割线
}
.table tr td {
  padding-top: 5px;
  padding-bottom: 5px;
  text-align: left;
  line-height: 2em;
}
.table tr td span {
  margin-top: 0.5em;
}
.ranking-text {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
}
.font-green {
  color: #3CC480;
}
.font-gray {
  color: darkgrey;
}
.font-red {
  color: #FF4141;
}
</style>
