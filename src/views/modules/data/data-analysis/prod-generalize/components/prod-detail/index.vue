<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('shopFeature.titText.prodDet')"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1198px"
    top="10vh"
  >
    <div class="prod-info name">
      <div class="tit">
        {{ $t('product.prodName') }}:
      </div>
      <div class="content">
        {{ prodInfo.prodName }}
      </div>
    </div>
    <div class="prod-info brief">
      <div class="tit">
        {{ $t('product.productSellingPoints') }}:
      </div>
      <div class="content">
        {{ prodInfo.brief===''?$t('chat.none'):prodInfo.brief }}
      </div>
    </div>
    <div class="prod-info imgs">
      <div class="tit">
        {{ $t('product.pic') }}:
      </div>
      <div class="content">
        <el-image
          v-for="(item, index) in imgsList"
          :key="index"
          class="prod-imgs"
          :src="checkFileUrl(item)"
          :preview-src-list="checkFileUrl(imgsList)"
          fit="contain"
        />
      </div>
    </div>
    <div class="prod-info skus">
      <div class="tit">
        {{ $t('product.productSpecifi') }}:
      </div>
    </div>
    <div class="prod-info sku-table">
      <el-table
        ref="skuListTableRef"
        :data="prodInfo.skuList"
        header-cell-class-name="table-header"
        row-class-name="table-row"
        height="340"
      >
        <el-table-column
          prop="pic"
          :label="$t('product.pic')"
        >
          <template #default="scope">
            <ImgShow
              :src="scope.row.pic"
              :class-list="['sku-img']"
            />
          </template>
        </el-table-column>

        <el-table-column
          v-for="(item, index) in skuPropertiesTitleList"
          :key="index"
          :label="item"
        >
          <template #default="scope">
            <span>{{ scope.row.skuName.split(' ')[index] }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="prodInfo.prodType === 3"
          prop="scorePrice"
          :label="$t('data.integralPrice')"
        >
          <template #default="scope">
            <span>{{ scope.row.skuScore }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="price"
          :label="$t('product.sellingPrice')"
        >
          <template #default="scope">
            <span>￥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="oriPrice"
          :label="$t('product.marketPrice')"
        >
          <template #default="scope">
            <span>￥{{ scope.row.oriPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="stocks"
          :label="$t('marketing.inventory')"
        >
          <template #default="scope">
            <span>{{ scope.row.stocks }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="volume"
          :label="$t('product.commodityVolume')"
        >
          <template #default="scope">
            <span>{{ scope.row.volume }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="weight"
          :label="$t('product.commodityWeight')"
        >
          <template #default="scope">
            <span>{{ scope.row.weight }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn"
          @click="dialogVisible = false"
        >
          {{ $t('order.cancel') }}
        </div>
        <div
          class="default-btn primary-btn"
          @click="dialogVisible = false"
        >
          {{ $t('order.confirm') }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const emit = defineEmits(['handleHidePop'])

const dialogVisible = ref(false)
const prodInfo = ref({})
const imgsList = ref([])
const skuPropertiesTitleList = ref([])
const init = (prodId) => {
  dialogVisible.value = true
  nextTick(() => {
    http({
      url: http.adornUrl(`/prod/prod/info/${prodId}`),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      data.pic = checkFileUrl(data.pic)
      if (data.skuList) {
        data.skuList.forEach((el, index) => {
          el.pic = el.pic ? checkFileUrl(el.pic) : data.pic
          if (index === 0 && el.properties) {
            skuPropertiesTitleList.value = el.properties.split(';').map(item => item.slice(0, item.indexOf(':')))
          }
        })
      }
      imgsList.value = data.imgs.split(',').map(el => checkFileUrl(el))
      prodInfo.value = data
    })
  })
}
const handleClose = (done) => {
  emit('handleHidePop')
  done()
}

defineExpose({
  init
})

</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 10px;
}
.prod-info {
  display: flex;
  align-content: center;
  margin-bottom: 30px;
  &.skus {
    margin-bottom: 20px;
  }
  &.sku-table {
    margin-bottom: 0;
    // eslint-disable-next-line vue-scoped-css/no-unused-selector
    .sku-img {
      width: 40px;
      height:40px;
    }
  }
  .tit {
    margin-right: 12px;
  }
  .content {
    flex: 1;
    .prod-imgs {
      width: 80px;
      height: 80px;
      margin-right:12px;
    }
  }
}
</style>
