<template>
  <div class="billing-box">
    <div class="manage-check">
      <div class="title">
        <span>{{ $t('settlementDetail.tip1') }}</span>
        <span>

          <el-tooltip
            class="item"
            effect="dark"
            placement="right"
          >

            <template #content><div
              class="title-con-pit"
            >

              <p>{{ $t('settlementDetail.tip2_2') }}</p>
              <p>&nbsp;</p>
              <p>{{ $t('settlementDetail.tip2_3') }}</p>
              <p>{{ $t('settlementDetail.tip2_4') }}</p>
              <p>{{ $t('settlementDetail.tip2_5') }}</p>
              <p>{{ $t('settlementDetail.tip2_6') }}</p>
              <p>{{ $t('settlementDetail.tip2_7') }}</p>
              <p>{{ $t('settlementDetail.tip2_8') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_9') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_10') }}</p>
              <p>{{ $t('settlementDetail.tip2_11') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_12') }}</p>
              <p>{{ $t('settlementDetail.tip2_13') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_14') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_15') }}</p>
              <p>{{ $t('settlementDetail.tip2_16') }}</p>
              <p>{{ $t('settlementDetail.tip2_17') }}</p>
              <p>{{ $t('settlementDetail.tip2_18') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_19') }}</p>
              <p>{{ $t('settlementDetail.tip2_20') }}</p>
              <p>{{ $t('settlementDetail.tip2_21') }}</p>
              <p>{{ $t('settlementDetail.tip2_22') }}</p>
              <p>{{ $t('settlementDetail.tip2_23') }}</p>
              <p>{{ $t('settlementDetail.tip2_24') }}</p>
              <p>{{ $t('settlementDetail.tip2_25') }}</p>
              <p>{{ $t('settlementDetail.tip2_26') }}</p>
              <p>{{ $t('settlementDetail.tip2_27') }}</p>
              <p>{{ $t('settlementDetail.tip2_28') }}</p>
              <p>{{ $t('settlementDetail.tip2_29') }}</p>
              <p>{{ $t('settlementDetail.tip2_30') }}</p>
              <p>{{ $t('settlementDetail.tip2_31') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_32') }}</p>
              <p>&nbsp;&nbsp;&nbsp;{{ $t('settlementDetail.tip2_33') }}</p>
            </div></template>
            <span style="font-size:14px;color:#155BD4;cursor:pointer;">{{ $t('settlementDetail.tip27') }} <el-icon><QuestionFilled /></el-icon></span>
          </el-tooltip>
        </span>
      </div>
      <div class="table">
        <el-table
          :data="data.tableData"
          style="width: 100%;margin-bottom: 20px;"
          row-key="id"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          default-expand-all
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        >
          <el-table-column
            prop="shopName"
            :label="$t('shopProcess.shopName')"
            width="130"
            align="center"
          />
          <el-table-column
            prop="prodName"
            :label="$t('product.prodName')"
            width="130"
            align="center"
          />
          <el-table-column
            prop="categoryName"
            :label="$t('publics.category')"
            width="130"
            align="center"
          />

          <el-table-column
            prop="prodTotalAmount"
            :label="$t('settlementDetail.prodTotalAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.activityType===5">-</span>
              <span v-else>￥ {{ scope.row.productTotalAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="actualTotal"
            :label="$t('order.userPayunt')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.actualTotal }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="multishopReduce"
            :label="$t('settlementDetail.multishopReduce')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.multishopReduce }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformShareReduce"
            :label="$t('settlementDetail.platformShareReduce')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.platformShareReduce }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="distributionAmount"
            :label="$t('settlementDetail.distributionAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.distributionAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="useScore"
            :label="$t('settlementDetail.useScore')"
            width="130"
            align="center"
          />
          <el-table-column
            prop="rate"
            :label="$t('settlementDetail.rate')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>{{ scope.row.rate }}%</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformCommission"
            :label="$t('shopWallet.platformCommission')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.platformCommission }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="scoreAmount"
            :label="$t('settlementDetail.scoreAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.scoreAmount }}</span>
            </template>
          </el-table-column>
          <!-- 平台会员折扣 -->
          <el-table-column
            prop="memberAmount"
            :label="$t('settlementDetail.platMemberAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.memberAmount }}</span>
            </template>
          </el-table-column>
          <!-- 店铺会员折扣 -->
          <el-table-column
            prop="memberAmount"
            :label="$t('settlementDetail.shopMemberAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.shopMemberDiscountAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformCouponAmount"
            :label="$t('settlementDetail.platformCouponAmount')"
            width="160"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.platformCouponAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="shopCouponAmount"
            :label="$t('settlementDetail.shopCouponAmount')"
            width="160"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.shopCouponAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="discountAmount"
            :label="$t('settlementDetail.discountAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.discountAmount }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="comboAmount"
            :label="$t('settlementDetail.comboAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.comboAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop=" seckillAmount"
            :label="$t('settlementDetail.seckillAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.seckillAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="groupAmount"
            :label="$t('settlementDetail.groupAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.groupAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="shopChangeFreeAmount"
            :label="$t('settlementDetail.shopChangeFreeAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.shopChangeFreeAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformShopChangeAmount"
            :label="$t('settlementDetail.platformShopChangeAmount')"
            width="140"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.platformShopChangeAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundAmount"
            :label="$t('settlementDetail.refundAmount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.refundAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundCount"
            :label="$t('settlementDetail.refundCount')"
            width="130"
            align="center"
          >
            <template #default="scope">
              <span>{{ scope.row.refundCount || 0 }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div
        v-if="data.freightAmount !== 0"
        class="message"
      >
        <span>{{ $t('order.shippingAmunt') }}：{{ data.freightAmount }} {{ $t('transport.yuan') }}</span>
      </div>
      <div
        v-if="data.freeFreightAmount !== 0"
        class="message"
      >
        <span>{{ $t('settlementDetail.freeFreightAmount') }}{{ data.freeFreightAmount > 0 ? '+':'' }}{{ data.freeFreightAmount }}  {{ $t('transport.yuan') }}
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('settlementDetail.freeFreightAmountTips')"
            placement="top"
          >
            <span style="font-size:14px;color:#46a6ff;cursor:pointer;"><el-icon><QuestionFilled /></el-icon></span>
          </el-tooltip>
        </span>
      </div>
      <div
        v-if="data.platformFreeFreightAmount !== 0"
        class="message"
      >
        <span>{{ $t('settlementDetail.platformFreeFreightAmount') }}：{{ data.platformFreeFreightAmount }} {{ $t('transport.yuan') }}
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('settlementDetail.platformFreeFreightAmountTips')"
            placement="top"
          >
            <span style="font-size:14px;color:#46a6ff;cursor:pointer;"><el-icon><QuestionFilled /></el-icon></span>
          </el-tooltip>
        </span>
      </div>
      <div class="back">
        <div
          class="default-btn"
          plain
          @click="back"
        >
          {{ $t('homes.goBack') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
const Router = useRouter()
const Route = useRoute()
const orderNumber = Route.query.orderNumber
const refundSn = Route.query.refundSn || ''
const reason = Route.query.reason
const data = reactive({
  freightAmount: 0,
  tableData: []
})

onMounted(() => {
  getData(orderNumber, refundSn, reason)
})

// 获取订单详情
const getData = (orderNumber, refundSn, reason) => {
  http({
    url: http.adornUrl('/multishop/order_item/get_order_detail'),
    method: 'get',
    params: { orderNumber, refundSn, reason }
  }).then(({ data: _data }) => {
    data.freightAmount = _data.freightAmount
    data.freeFreightAmount = _data.freeFreightAmount ? _data.freeFreightAmount : 0
    data.platformFreeFreightAmount = _data.platformFreeFreightAmount ? _data.platformFreeFreightAmount : 0
    data.tableData = _data.orderItemDetailList
  })
}
const back = () => {
  Router.push('/shop/shop-wallet-log/index')
}

</script>

<style lang="scss" scoped>
 .manage-check {
   background: #FFFFFF;
   width: 100%;
   padding: 24px;
   .title {
     font-size: 18px;
     margin-bottom: 16px;
     display: flex;
     justify-content: space-between;
   }
 }

  .message {

    margin-bottom: 20px;
    span {
      font-size: 14px;
      margin-right: 60px;
    }
  }

 .table {
   margin-top: 20px;
 }
 .back {
   margin-top: 10px;
 }
 .title-con-pit{
  max-height:80vh;
  max-width: 80vw;
  overflow-y: auto;
}
</style>
