<template>
  <!-- 会员列表-编辑-交易明细 -->
  <div>
    <el-table
      :data="dataList"
      header-cell-class-name="table-header"
      row-class-name="table-row-low"
      class="user-edit-table"
      style="width: 100%;margin-bottom: 20px;"
    >
      <el-table-column
        prop="payTime"
        :label="$t('user.payTime')"
        width="160"
      />
      <el-table-column
        prop="orderNumber"
        :label="$t('order.number')"
        width="180"
      />
      <el-table-column
        prop="total"
        :label="$t('user.prodTotalPrice')"
      />
      <el-table-column
        prop="freightAmount"
        :label="$t('user.orderFreight')"
      />
      <el-table-column
        prop="actualTotal"
        :label="$t('order.actualAmount')"
      />
      <el-table-column
        prop="reduceAmount"
        :label="$t('user.preferentialAmount')"
      />
      <el-table-column
        prop="payScore"
        :label="$t('user.payScore')"
      />
      <el-table-column
        prop="orderType"
        :label="$t('order.orderType')"
      >
        <template #default="scope">
          <span>{{
            [
              $t("order.normalOrder"),
              $t("order.groupPurchaseOrder"),
              $t("order.spikeOrder"),
              $t("user.scoreOrder"),
            ][scope.row.orderType]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="payType"
        :label="$t('order.paymentMethod')"
        width="180"
      >
        <template #default="scope">
          <span>{{
            [
              $t("order.pointsPayment"),
              $t("order.wecProPay"),
              $t("order.alipayPCPayment"),
              $t("order.wechatScanCodePayment"),
              $t("order.wechatH5Payment"),
              $t("order.weclAccountPay"),
              $t("order.alipayH5Payment"),
              $t("order.alipayAPPPayment"),
              $t("order.wechatAPPPayment"),
              $t("order.balancePayment"),
              $t("order.payPalPayment"),
              $t("order.balanceScanCodePayment"),
              $t("order.offlinePayment")
            ][scope.row.payType]
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      v-if="dataList.length"
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      :total="page.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />
    <!-- /分页 -->
  </div>
</template>

<script setup>

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const dataList = ref([])

let userId = null
const init = (id) => {
  userId = id
  getData(page)
}

// 获取数据
const getData = (pageParam) => {
  if (!userId) {
    return
  }
  http({
    url: http.adornUrl('/order/order/getOrderByUserId'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        { userId }
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getData(page)
}

// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getData(page)
}

defineExpose({
  init
})

</script>
