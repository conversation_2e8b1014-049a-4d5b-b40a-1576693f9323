<template>
  <!-- 编辑会员-打标签 -->
  <div>
    <el-dialog
      v-model="visible"
      :title="$t('user.selectTag')"
      width="36%"
      class="el-upload-list el-upload-list--picture-card"
    >
      <el-form
        :inline="true"
        :model="searchForm"
        class="demo-form-inline form"
      >
        <el-form-item>
          <el-input
            v-model="tagName"

            :placeholder="$t('user.tagTip1')"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <div
            class="default-btn primary-btn"
            @click="onSearch"
          >
            {{ $t("pictureManager.query") }}
          </div>
        </el-form-item>
      </el-form>
      <el-main>
        <div class="main">
          <div
            v-for="item in tagList"
            :key="item.tagId"
          >
            <span
              :class="active.includes(item.userTagId)?'active':'Classification'"
              @click="oncheck(item)"
            >{{ item.tagName }}</span>
          </div>
        </div>
        <div
          v-if="tagList.length < 1"
          style="text-align:center"
        >
          {{ $t('order.noData') }}
        </div>
      </el-main>

      <!-- 分页 -->
      <el-pagination
        v-if="tagList.length"
        :current-page="page.pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
      <!-- /分页 -->
      <template #footer>
        <div class="dialog-footer">
          <div
            class="default-btn"
            @click="visible = false"
          >
            {{ $t('user.cancel') }}
          </div>
          <div
            class="default-btn primary-btn"
            @click="confirm"
          >
            {{ $t('user.confirm') }}
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  value: {
    default: '',
    type: String
  },
  // 最大上传数量
  limit: {
    default: 10,
    type: Number
  },
  tagCategory: {
    default: 0,
    type: Number
  },
  // 判断是会员还是客户 0:客户  1：会员
  type: {
    default: 0,
    type: Number
  }
})

const emit = defineEmits(['refreshDataList'])

const visible = ref(false)
const dataForm = reactive({
  tagList: [], // 标签
  userIds: []
})
const tagList = ref([]) // 标签
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const active = ref([]) // 选种的标签
const searchForm = reactive({
  tagType: 0, // 标签类型0手动1条件2系统
  tagName: null
})
let tagName = null

const init = (ids) => {
  visible.value = true
  dataForm.userIds = ids
  active.value = []
  tagName = ''
  searchForm.tagName = ''
  getTagList()
}
// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getTagList(page)
}
// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getTagList(page)
}
// 分页获取手动标签
const getTagList = (pageParam) => {
  http({
    url: http.adornUrl('/user/userTag/byTagType'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize,
          tagCategory: props.tagCategory
        },
        searchForm
      )
    )
  }).then(({ data }) => {
    tagList.value = data.records
    page.total = data.total
  })
}
// 搜索
const onSearch = () => {
  searchForm.tagName = tagName
  getTagList(page)
}
// 多选
const oncheck = (item) => {
  if (active.value.includes(item.userTagId)) {
    const index = active.value.indexOf(item.userTagId)
    active.value.splice(index, 1)
  } else {
    active.value.push(item.userTagId)
  }
  if (active.value.length > props.limit) {
    const index = active.value.indexOf(item.userTagId)
    active.value.splice(index, 1)
    return ElMessage.error($t('user.tipError2').replace('NUM', props.limit))
  }
}

const confirm = () => {
  if (!dataForm.userIds) {
    return
  }
  if (!active.value.length) {
    ElMessage.error($t('user.selectTagError'))
    return
  }
  dataForm.tagList = []
  active.value.forEach(item => {
    dataForm.tagList.push(item)
  })
  const param = dataForm
  param.tagCategory = props.type
  http({
    url: http.adornUrl('/user/userTag/updateBatchTag'),
    method: 'put',
    data: http.adornData(param)
  }).then(() => {
    active.value = []
    ElMessage({
      message: $t('user.success'),
      type: 'success',
      duration: 1500,
      onClose: () => {
        visible.value = false
        emit('refreshDataList', page)
      }
    })
  }).catch(() => {})
}

defineExpose({
  init
})

</script>
<style lang="scss" scoped>
.active {
  float: left;
  margin-left: 10px;
  padding: 10px;
  background: #155bd4;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 14px;
  color:#f7f7f7;
}
.Classification {
  float: left;
  margin-left: 10px;
  padding: 10px;
  background: #f7f7f7;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}
</style>
