<template>
  <div v-if="dialogVisible">
    <el-dialog
      v-model="dialogVisible"
      :title="$t('proactiveRefund.proactiveRefund')"
      width="500"
      align-center
      destroy-on-close
      @closed="closed"
    >
      <el-form
        ref="dataFormRef"
        style="max-width: 600px"
        :model="dataForm"
        status-icon
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
      >
        <el-form-item
          :label="$t('proactiveRefund.currentlyRefundable')"
        >
          <span>{{ canRefundAmount ? canRefundAmount.toFixed(2) : 0.00 }}</span>
        </el-form-item>
        <el-form-item
          :label="$t('proactiveRefund.refundAmount')"
          prop="refundAmount"
          required
        >
          <div class="refund-input">
            <el-input
              v-model="dataForm.refundAmount"
              :placeholder="$t('proactiveRefund.enterRefundAmount')"
              :disabled="!activeRefundStatus"
              @change="onchange"
            />
            <el-button
              type="primary"
              link
              style="margin-left: 10px"
              @click="onAllRefund"
            >
              {{ $t('proactiveRefund.fullRefund') }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="dialogVisible = false"
          >
            {{ $t('crud.filter.cancelBtn') }}
          </el-button>
          <el-button
            type="primary"
            @click="onSubmitForm"
          >
            {{ $t('crud.filter.submitBtn') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const dialogVisible = ref(false)

const props = defineProps({
  orderNumber: {
    type: Number || String,
    default: null
  },
  activeRefundStatus: {
    type: Boolean,
    default: null
  },
  canRefundAmount: {
    type: Number,
    default: null
  }
})

const validateRefundAmount = (rule, value, callback) => {
  const str = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
  if (!str.test(value) || Number(value) > 9999999 || Number(value) === 0) {
    callback(new Error($t('proactiveRefund.enterCorrectAmount')))
  } else {
    callback()
  }
}

const rules = reactive({
  refundAmount: [
    { required: true, message: $t('proactiveRefund.enterRefundAmount') },
    { validator: validateRefundAmount, trigger: 'blur' }
  ]
})

const dataForm = reactive({
  refundAmount: null
})

const init = () => {
  dialogVisible.value = true
}

const onchange = (e) => {
  const val = Number(e)
  const canNum = props.canRefundAmount
  if (val > canNum) {
    dataForm.refundAmount = canNum
  }
  if (!dataForm.refundAmount || dataForm.refundAmount < 0) {
    dataForm.refundAmount = 0
  }
}

const onAllRefund = () => {
  if (props.canRefundAmount) {
    dataForm.refundAmount = props.canRefundAmount.toFixed(2)
  }
}

const dataFormRef = ref(null)
const onSubmitForm = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      http({
        url: http.adornUrl('/order/refund/doActiveRefund'),
        method: 'post',
        data: http.adornParams({
          orderNumber: props.orderNumber,
          refundAmount: dataForm.refundAmount
        })
      }).then(() => {
        ElMessage({
          message: $t('order.refundsuccessfully'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            dialogVisible.value = false
            // 退款成功后刷新页面
            location.reload()
          }
        })
      })
    } else {
      return false
    }
  })
}

const closed = () => {
  dialogVisible.value = false
}

defineExpose({
  init
})

</script>

<style scoped lang="scss">

.refund-input {
  display: flex;
  align-items: center;
}

</style>
