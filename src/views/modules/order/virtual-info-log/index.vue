<template>
  <div class="page-virtual-info-log">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="orderNumber"
            :label="$t('order.number') + ':'"
          >
            <el-input
              v-model="searchForm.orderNumber"
              type="text"
              clearable
              :placeholder="$t('order.number')"
            />
          </el-form-item>
          <el-form-item
            prop="stationId"
            :label="$t('virtualLog.station') + ':'"
          >
            <el-select
              v-model="searchForm.stationId"
              clearable
            >
              <el-option
                v-for="node in stationList"
                :key="node.stationId"
                :label="node.stationName"
                :value="node.stationId"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="writeOffCode"
            :label="$t('virtualLog.code') + ':'"
          >
            <el-input
              v-model="searchForm.writeOffCode"
              type="text"
              clearable
              :placeholder="$t('virtualLog.code')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch('searchForm')"
            >
              {{ $t('shop.resetMap') }}
            </div>
            <div
              v-if="isAuth('virtual:verify:export')"
              class="default-btn"
              @click="onExportOrder()"
            >
              {{ $t('crud.excelBtn') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="table-con prod-table">
        <el-table
          ref="prodCommTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          style="width: 100%"
        >
          <el-table-column
            align="left"
            prop="orderNumber"
            :label="$t('order.number')"
          />
          <el-table-column
            align="left"
            prop="prodName"
            :label="$t('product.prodName')"
          />
          <el-table-column
            align="left"
            prop="stationName"
            :label="$t('virtualLog.station')"
          >
            <template #default="{row}">
              <span class="table-cell-text line-clamp-one">{{ row.stationName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            :label="$t('virtualLog.code')"
          >
            <template #default="scope">
              <span
                class="table-cell-text line-clamp-one"
              >{{ scope.row.writeOffCode || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="createTime"
            :label="$t('virtualLog.time')"
            width="160"
          />
        </el-table>
      </div>
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { isAuth } from '@/utils/index.js'
import { ElLoading, ElMessage } from 'element-plus'

onMounted(() => {
  getAllStation()
  getDataList()
})

const stationList = ref([])
const getAllStation = () => {
  http({
    url: http.adornUrl('/admin/station/listShopAllStation'),
    method: 'get'
  }).then(({ data }) => {
    stationList.value = data
  })
}

let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  orderNumber: '',
  stationId: '',
  writeOffCode: ''
})
const getDataList = (pageParam, newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  tempSearchForm.current = pageParam == null ? page.currentPage : pageParam.currentPage
  tempSearchForm.size = pageParam == null ? page.pageSize : pageParam.pageSize
  http({
    url: http.adornUrl('/shop/orderVirtualVerifyLog/page'),
    method: 'get',
    params: http.adornParams(tempSearchForm)
  }).then(({ data }) => {
    if (data) {
      dataList.value = data.records
      page.total = data.total
    } else {
      dataList.value = []
      page.total = 0
    }
  })
}

const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}

// 重置表单
const searchFormRef = ref(null)
const onResetSearch = () => {
  searchFormRef.value?.resetFields()
  getDataList()
}

let isExportOrderBtn = false
const onExportOrder = () => {
  if (isExportOrderBtn) return
  isExportOrderBtn = true
  const loading = ElLoading.service({
    lock: true,
    target: '.page-virtual-info-log',
    customClass: 'export-load',
    background: 'transparent',
    text: $t('formData.exportIng')
  })
  http({
    url: http.adornUrl('/shop/orderVirtualVerifyLog/export'),
    method: 'get',
    params: http.adornParams(searchForm),
    responseType: 'blob' // 解决文件下载乱码问题
  }).then(({ data }) => {
    loading.close()
    isExportOrderBtn = false
    const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
    const fileName = $t('virtualLog.virtualLogXls')
    const elink = document.createElement('a')
    if ('download' in elink) { // 非IE下载
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
    ElMessage({
      message: $t('stock.exportSuccess'),
      type: 'success',
      duration: 1500
    })
  }).catch(() => {
    loading.close()
    isExportOrderBtn = false
  })
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList()
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList()
}

</script>

<style lang="scss" scoped>
.page-virtual-info-log {
  .table-cell-text {
    margin-right: 70px;
  }
  :deep(.export-load) {
    top: -50% !important;
  }
}
</style>
