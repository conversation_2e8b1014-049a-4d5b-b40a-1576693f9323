.page-order-order {
  .main {
    .order-status-nav {
      position: relative;
      display: block;
      width: 100%;
      margin-bottom: 15px;
      line-height: 40px;
      border-bottom: 1px solid #ddd;

      ul,
      li {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .nav-list {
        display: flex;
        flex-wrap: nowrap;
        flex: 0 0 auto;
      }

      .nav-item {
        min-width: 79px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        float: left;
        height: 40px;
        line-height: 40px;
        background: #f7f8fa;
        border: 1px solid #ddd;
        padding: 0 20px;
        margin: 0 -1px -1px 0;
        cursor: pointer;
      }

      .selected {
        background: #fff;
        border-bottom: 1px solid #fff;
      }
    }

    .tit {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      background: #F7F8FA;
      z-index: 11;
      height: 57px;
      font-weight: bold;

      .column-title {
        text-align: center;
      }

      .item {
        padding: 0 10px;
        width: 10%;
        text-align: center;
      }

      .product {
        width: 25%;
        margin-bottom: 15px;
        text-align: left !important;
      }
    }

    .fixed-top {
      position: fixed;
      width: calc(100% - 310px);
      min-width: calc(1200px - 330px);
      top: 90px;
    }

    .fold-fixed-top {
      width: calc(100% - 180px);
      min-width: calc(1200px - 200px);
    }

    .prod {
      margin-bottom: 15px;

      .prod-tit {
        padding: 10px;
        background: #F7F8FA;
        height: 49px;
        display: flex;
        align-items: center;
        border-left: 1px solid #EBEDF0;
        border-top: 1px solid #EBEDF0;
        border-right: 1px solid #EBEDF0;

        .order-number {
          color: #333333;
          font-size: 14px
        }

        .order-time {
          color: #999999;
          font-size: 14px
        }

        span {
          margin-right: 15px;
        }
      }

      .prod-cont {
        display: flex;
        border: 1px solid #EBEDF0;
        color: #495060;

        .item {
          display: flex;
          display: -webkit-flex;
          align-items: center;
          padding: 10px;
          text-align: center;
          justify-content: center !important;
          height: 100%;
          border-right: 1px solid #eee;

          .totalprice {
            color: #ff4141;
            margin-bottom: 10px;
          }

          .operate {
            color: #2d8cf0;

            .operate-btn {
              margin: 0 !important;
              height: auto;
            }

            .default-btn + .default-btn {
              display: block;
              margin-top: 10px;
              margin-left: 0;
            }
          }

          .buyer-info {
            .buyer-name {
              margin-bottom: 4px;
            }
          }

          span {
            display: inline-block;
          }
        }

        .prod-item {
          padding: 0;
          display: flex;
          flex-direction: column !important;
          height: 100%;
          border-right: 1px solid #eee;

          .items.name {
            width: 100%;
            border-bottom: 1px solid #EBEDF0;
            text-align: left;
            flex-direction: column;

            &:last-child {
              border-bottom: none;
            }

            .order-prod-item-info {
              width: 100%;
              display: flex;
              align-items: center;
              padding: 10px !important;

              .prod-image {
                width: 60px;
                height: 60px;
                margin-right: 20px;
                padding: 0;
                line-height: 60px;
              }

              .prod-name {
                flex: 1;
                margin-left: auto;
                max-width: 78% !important;

                .prod-con {
                  width: 85% !important;
                  display: block;
                  padding: 0 !important;
                  overflow: hidden;

                  .prod-name-txt {
                    padding-right: 10px;
                    box-sizing: border-box;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    word-break: break-word;
                  }

                  .prod-name-sku {
                    color: #999;
                  }

                  .order-status {
                    display: inline-block;
                    margin-top: 15px;
                    margin-right: 10px;
                    padding: 2px 4px;
                    border: 1px solid #e43130;
                    border-radius: 2px;
                    color: #e43130;
                    font-size: 14px;
                  }
                }
              }

              .prod-price {
                width: 28%;
                display: flex;
                justify-content: flex-start;
                flex-direction: column;
                overflow: hidden;
                position: relative;
                right: 0 !important;

                span {
                  display: block;
                  text-align: left;
                  word-break: keep-all;

                  &:first-child {
                    margin-bottom: 10px;
                  }
                }
              }
            }

            // 赠品盒子
            .order-prod-item-give-con {
              width: 100%;
              padding: 0 50px 10px 82px;
            }
          }
        }
      }
    }

    .empty {
      display: block;
      height: 200px;
      line-height: 200px;
      text-align: center;
      color: #aaa;
    }

    .transaction-price {
      text-align: left;
    }
  }

  .search-bar {
    .input-row {
      .is-active {
        color: #155bd4;
      }
    }
  }

  // 修改物流弹窗
  .change-logistics {
    color: #333;
    padding: 0 20px;

    .warning {
      padding: 10px;
      border: 1px solid #f1924e;
      background: #fff5ed;
    }

    .log-list {
      max-height: 600px;
      margin-top: 30px;
      overflow-y: auto;

      .item {
        padding-bottom: 20px;

        .i-tit {
          display: flex;
          align-items: center;

          .big {
            font-weight: 600;
            margin-right: 15px;
          }
        }

        .item-goods {
          position: relative;
          width: 100%;
          height: 110px;
          overflow-x: auto;

          .goods-box {
            position: absolute;
            left: 0;
            display: flex;
            margin-top: 0;
            -webkit-transition: all 0.3s;
            -moz-transition: all 0.3s;
            transition: all 0.3s;

            .item {
              margin-right: 10px;
              font-size: 12px;
              cursor: pointer;

              .img {
                width: 60px;
                height: 60px;
                font-size: 0;
                margin-bottom: 4px;
                margin-top: 4px;
                position: relative;

                .number {
                  position: absolute;
                  bottom: 0;
                  right: 0;
                  background: rgba(0, 0, 0, 0.3);
                  color: #fff;
                  border-radius: 6px 0 6px 0;
                  font-size: 12px;
                  height: 16px;
                  line-height: 16px;
                  padding: 0 5px;
                }
              }

              .name {
                width: 60px;
                height: 16px;
                line-height: 16px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #999;
              }
            }

            .item:last-child {
              margin: 0;
            }
          }
        }
      }
    }

    .log-info-table {
      margin-top: 20px;
      max-height: 600px;
      overflow-y: auto;
    }

    :deep(.el-form-item) {
      display: flex;
      margin-bottom: 20px;
    }

    .info-int {
      margin-left: -10px;
      :deep(.el-select) {
        width: 190px;
      }
    }
  }
}

div :deep(.el-form-item--small .el-form-item__content) {
  display: flex;
  flex-wrap: wrap;
}
