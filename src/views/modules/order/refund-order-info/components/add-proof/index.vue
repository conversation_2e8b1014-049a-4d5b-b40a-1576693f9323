<template>
  <el-dialog
    v-model="visible"
    :title="$t('refund.areplenishProof')"
    :close-on-click-modal="false"
    width="600px"
    class="page-station-add-or-update"
    @closed="onClosed"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="100px"
      label-position="top"
    >
      <el-form-item
        :label="$t('refund.proofDesc')"
        prop="proof"
      >
        <el-input
          v-model.trim="dataForm.proof"
          type="textarea"
          :rows="4"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('refund.uploadProof')"
        prop="imgs"
        style="width:624px;"
      >
        <uploadProof
          ref="uploadProofRef"
          v-model="dataForm.imgs"
          :limit="3"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="visible = false"
        >
          {{ $t('order.cancel') }}
        </el-button>
        <el-button
          type="primary"
          @click="onSubmit()"
        >
          {{ $t('groups.submit') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import uploadProof from '../upload-proof/index.vue'

const props = defineProps({
  refundDetail: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['close', 'refreshData'])
const dataForm = reactive({
  proof: '',
  imgs: ''
})
const dataRule = {
  proof: [
    { required: true, message: $t('refund.proofDescEmptyTips'), trigger: 'blur' }
  ],
  imgs: [
    { required: true, message: $t('refund.uploadProofTips'), trigger: 'blur' }
  ]
}

const onClosed = () => {
  uploadProofRef.value.clearFiles()
  emit('close')
}

const dataFormRef = ref()
const visible = ref(false)
const init = () => {
  visible.value = true
  dataFormRef.value?.resetFields()
  isSubmit = false
}

// 表单提交
const uploadProofRef = ref(null)
let isSubmit = false
const onSubmit = () => {
  dataFormRef.value?.validate(async (valid) => {
    if (valid) {
      if (isSubmit) {
        return false
      }
      isSubmit = true
      const saveFileRes = await uploadProofRef.value.saveAttachFileToPlat()
      if (!saveFileRes) {
        // eslint-disable-next-line require-atomic-updates
        isSubmit = false
        return
      }
      http({
        url: http.adornUrl('/multishop/orderRefundIntervention/saveInterventionVoucher'),
        method: 'post',
        data: http.adornData({
          refundId: props.refundDetail.refundId,
          sysType: 1, // 0.普通用户 1.商家端 2.平台端
          refundSts: props.refundDetail.refundStatus,
          voucherDesc: dataForm.proof,
          imgUrls: dataForm.imgs
        })
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            isSubmit = false
            visible.value = false
            emit('refreshData')
          }
        })
      }).catch(() => {
        isSubmit = false
      })
    }
  })
}

defineExpose({
  init
})
</script>
