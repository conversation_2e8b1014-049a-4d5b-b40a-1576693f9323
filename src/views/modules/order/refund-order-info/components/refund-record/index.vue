<template>
  <div class="components-refund-record">
    <!-- 交易结束 -->
    <div
      v-if="closingSts"
      class="item"
    >
      <div class="title">
        <span>{{ $t('refund.closing') }}</span>
        <span>{{ closingSts }}</span>
      </div>
      <div
        v-if="refundDetail.platformMessage"
        class="form-box"
      >
        <div class="form-item">
          <span class="label">
            {{ $t('refund.platNote') }}：
          </span>
          <span class="content">{{ refundDetail.platformMessage }}</span>
        </div>
      </div>
      <div class="time">
        {{ refundDetail.interventionFinishTime }}
      </div>
    </div>
    <!-- 撤销平台介入申请 -->
    <div
      v-if="refundDetail.applyInterventionTime&&refundDetail.platformInterventionStatus===-1"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.buyer') }}</span>
        <span>{{ $t('refund.repealApplyInter') }}</span>
      </div>
      <div class="time">
        {{ refundDetail.interventionFinishTime }}
      </div>
    </div>
    <!-- 平台介入中 -->
    <div
      v-for="(intervenItem,intervenInx) in refundInterList"
      :key="intervenInx"
      class="item"
    >
      <div class="title">
        <span> {{ [$t('order.buyer'),$t('order.merchant'),$t('refund.plat')][intervenItem.sysType] }}</span>
        <span>{{ $t('refund.addProof') }}</span>
      </div>
      <div class="form-box">
        <div class="form-item">
          <span class="label">
            {{ $t('refund.proofDesc') }}：
          </span>
          <span class="content">
            {{ intervenItem.voucherDesc }}
          </span>
        </div>
        <div class="mar-top form-item">
          <span class="label">
            {{ $t('refund.uploadProof') }}：
          </span>
          <div class="content">
            <imgGeneral :img-list="intervenItem.imgList" />
          </div>
        </div>
      </div>
      <div class="time">
        {{ intervenItem.createTime }}
      </div>
    </div>
    <!-- 买家申请平台介入 -->
    <div
      v-if="refundDetail.applyInterventionTime"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.buyer') }}</span>
        <span>{{ $t('refund.applyPlatInter') }}</span>
      </div>
      <div class="form-box">
        <div class="form-item">
          <span class="label">
            {{ $t('components.reasonForApply') }}：
          </span>
          <span class="content">
            {{ refundDetail.applyInterventionReason }}
          </span>
        </div>
        <div class="mar-top form-item">
          <span class="label">
            {{ $t('refund.uploadProof') }}：
          </span>
          <div class="content">
            <imgGeneral :img-list="applyInterventionImgs" />
          </div>
        </div>
      </div>
      <div class="time">
        {{ refundDetail.applyInterventionTime }}
      </div>
    </div>
    <!-- 退款关闭 -->
    <div
      v-if="(refundDetail.returnMoneySts === -1 || refundDetail.platformInterventionStatus!==-1) && refundCloseDesc"
      class="item"
    >
      <div class="title">
        <span>{{ refundCloseDesc.title }}</span>
        <span>{{ refundCloseDesc.txt }}</span>
      </div>
      <div class="form-box">
        <div
          v-if="refundDetail.rejectMessage"
          class="form-item"
        >
          <span class="label">
            {{ $t('components.denialReason') }}：
          </span>
          <span class="content">{{ refundDetail.rejectMessage || '-' }}</span>
        </div>
        <div
          v-if="!refundDetail.refundDelivery?.refundDeliveryId"
          class="mar-top form-item"
        >
          <span class="label">
            {{ $t('order.merchantNotes') }}：
          </span>
          <span class="content">{{ refundDetail.sellerMsg || $t('productComm.no') }}</span>
        </div>
        <div class="mar-top form-item">
          <span class="label">
            {{ $t('refund.proofDesc') }}：
          </span>
          <div class="content">
            <imgGeneral :img-list="rejectImgs" />
          </div>
        </div>
      </div>
      <div class="time">
        {{ refundCloseDesc.time }}
      </div>
    </div>
    <!-- 退款成功 -->
    <div
      v-if="refundDetail.refundTime && !refundDetail.rejectTime"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.merchant') }}</span>
        <span>{{ $t("order.refundsuccessfully") }}</span>
      </div>
      <div class="time">
        {{ refundDetail.refundTime }}
      </div>
    </div>
    <!-- 同意退款,等待发放退款 -->
    <div
      v-if="refundDetail.decisionTime && !refundDetail.rejectTime"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.merchant') }}</span>
        <span>{{ $t("order.agreeToRefund") }}</span>
      </div>
      <div class="time">
        {{ refundDetail.decisionTime }}
      </div>
    </div>
    <!-- 商家已收货,等待商家同意退款 -->
    <div
      v-if="refundDetail.receiveTime"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.merchant') }}</span>
        <span>{{ $t("order.merchantHasReceivedWaShGr") }}</span>
      </div>
      <div class="time">
        {{ refundDetail.receiveTime }}
      </div>
    </div>
    <!-- 买家发货, 等待商家收货 -->
    <div
      v-if="refundDetail.shipTime"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.buyer') }}</span>
        <span>{{ $t("order.buyerShipmentWaPro") }}</span>
      </div>
      <div class="time">
        {{ refundDetail.shipTime }}
      </div>
    </div>
    <!-- 商家已处理, 等待商家退款 -->
    <div
      v-if="refundDetail.applyType === 1 && refundDetail.handelTime && !refundDetail.rejectTime"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.merchant') }}</span>
        <span>{{ $t("order.merchantHasProcessedWaSh") }}</span>
      </div>
      <div class="form-box">
        <div class="form-item">
          <span class="label">
            {{ $t('order.merchantNotes') }}：
          </span>
          <span class="content">{{ refundDetail.sellerMsg || $t('productComm.no') }}</span>
        </div>
      </div>
      <div class="time">
        {{ refundDetail.handelTime }}
      </div>
    </div>
    <!-- 商家已处理, 等待买家发货 -->
    <div
      v-if="refundDetail.applyType === 2 && refundDetail.handelTime && (!refundDetail.rejectTime || refundDetail.refundDelivery?.refundDeliveryId)"
      class="item"
    >
      <div class="title">
        <span>{{ $t('order.merchant') }}</span>
        <span>{{ $t("order.merchantHasProcessedWaBuy") }}</span>
      </div>
      <div class="form-box">
        <div class="form-item">
          <span class="label">
            {{ $t('order.merchantNotes') }}：
          </span>
          <span class="content">{{ refundDetail.sellerMsg || $t('productComm.no') }}</span>
        </div>
      </div>
      <div class="time">
        {{ refundDetail.handelTime }}
      </div>
    </div>
    <!-- 买家提交退款申请 -->
    <div class="item">
      <div class="title">
        <span>{{ $t('order.buyer') }}</span>
        <span>{{ $t("order.submitARefundRequestWa") }}</span>
      </div>
      <div class="time">
        {{ refundDetail.applyTime }}
      </div>
    </div>
  </div>
</template>

<script setup>
import imgGeneral from '../img-general/index.vue'
const props = defineProps({
  refundDetail: {
    type: Object,
    default: () => ({})
  }
})

// 退款关闭说明
const refundCloseDesc = computed(() => {
  const info = props.refundDetail
  if (info.cancelTime) {
    return {
      title: $t('order.buyer'),
      txt: $t('order.refundClosed') + $t('refund.buyerWithdrawn'),
      time: info.cancelTime
    }
  }
  if (info.rejectTime) {
    return {
      title: $t('order.merchant'),
      txt: $t('order.refundFailed') + $t('order.merchantRejected'),
      time: info.rejectTime
    }
  }
  if (info.handelTime && !info.rejectTime) {
    return {
      title: $t('order.buyer'),
      txt: $t('order.refundClosed') + $t('order.buyerHasWithdrawn'),
      time: info.handelTime
    }
  }
  return null
})

// 平台介入状态 -1.没有介入 1.用户申请介入 2.平台同意介入 3.平台拒绝介入 5.平台同意退款成功
// 交易结束状态
const closingSts = computed(() => {
  switch (props.refundDetail.platformInterventionStatus) {
    case 3:
      return $t('refund.platRejectRefund')
    case 5:
      return $t('refund.platAgreeRefundSucceed')
    default:
      return ''
  }
})

// 平台介入中补充凭证列表
const refundInterList = computed(() => {
  const list = props.refundDetail.orderRefundInterventionList || []
  return list.reverse().map(item => {
    item.imgList = onHandleImgs(item.imgUrls)
    return item
  })
})

// 申请平台介入凭证
const applyInterventionImgs = computed(() => {
  return onHandleImgs(props.refundDetail.applyInterventionImgUrls)
})

// 拒绝凭证
const rejectImgs = computed(() => {
  return props.refundDetail.shopImgUrls?.split(',').map(item => {
    return checkFileUrl(item)
  })
})

// 图片处理
const onHandleImgs = (imgStr) => {
  if (imgStr) {
    return imgStr.split(',').map(url => {
      return checkFileUrl(url)
    })
  }
  return []
}

</script>

<style lang="scss" scoped>
@use './index.scss';
</style>
