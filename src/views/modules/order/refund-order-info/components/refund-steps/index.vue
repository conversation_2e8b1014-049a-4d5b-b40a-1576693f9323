<template>
  <div class="component-refund-steps">
    <!-- 仅退款 -->
    <template v-if="refundDetail.applyType === 1 && platInterveneSts ===-1">
      <el-steps
        :active="onlyRefundStepsStatus"
        align-center
        :process-status="onlyRefundProcessStatus"
      >
        <el-step :title="$t('order.buyer') + ' ' + $t('order.requestARefund')" />
        <el-step :title="$t('admin.merAgreeRefund')" />
        <el-step :title="$t('order.refundsuccessfully')" />
      </el-steps>
    </template>
    <template v-if="refundDetail.applyType === 2 && platInterveneSts ===-1">
      <el-steps
        :active="stepsStatus"
        align-center
        :process-status="refundDetail.returnMoneySts === -1 ? 'error' : 'wait'"
      >
        <el-step :title="$t('order.buyer') + ' ' + $t('order.requestARefundT')" />
        <el-step :title="$t('admin.merAgreeRefund')" />
        <el-step :title="$t('order.shippedByBuyer')" />
        <el-step :title="$t('order.merchant') + ' ' + $t('order.successfulProcessing')" />
        <el-step :title="$t('order.refundsuccessfully')" />
      </el-steps>
    </template>
    <!-- 平台介入 -->
    <template v-if="platInterveneSts !==-1">
      <el-steps
        :active="platInterStepsActive"
        align-center
      >
        <el-step :title="$t('order.buyer') + ' ' + $t('order.requestARefund')" />
        <el-step :title=" $t('admin.merAgreeRefund')" />
        <el-step
          :title="$t('refund.platInter')"
          :status="platInterveneStepStatus"
        />
        <el-step
          :title="$t('order.refundsuccessfully')"
          :status="platInterveneSts===5?'':'wait'"
        />
      </el-steps>
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  refundDetail: {
    type: Object,
    default: () => ({})
  }
})

const onlyRefundStepsStatus = computed(() => {
  let index = 0
  if (props.refundDetail.handelTime) {
    index++
  }
  if (props.refundDetail.refundTime) {
    index += 2
  }
  return index
})

const onlyRefundProcessStatus = computed(() => {
  switch (props.refundDetail.returnMoneySts) {
    case 1:
    case 2:
      return 'finish'
    case 5:
      return 'success'
    default:
      return 'error'
  }
})

const stepsStatus = computed(() => {
  if (props.refundDetail.cancelTime) return 0
  let index = 1
  if (props.refundDetail.handelTime) {
    index++
  }
  if (props.refundDetail.shipTime) {
    index++
  }
  if (props.refundDetail.receiveTime) {
    index++
  }
  if (!props.refundDetail.receiveTime && props.refundDetail.returnMoneySts === 5) {
    index++
  }
  if (props.refundDetail.rejectTime) {
    index--
  }
  // 退款成功的情况下进度条也要加2
  if (props.refundDetail.refundTime) {
    index++
  }
  return index
})

const platInterveneSts = computed(() => props.refundDetail.platformInterventionStatus || -1) // 平台介入状态
const platInterStepsActive = computed(() => {
  let index = 0
  if (props.refundDetail.handelTime) {
    index++
  }
  if (platInterveneSts.value !== -1) {
    index++
  }
  if (platInterveneSts.value === 3) {
    index++
  }
  if (platInterveneSts.value === 5) {
    index += 2
  }
  return index
})

// 进度条平台介入 平台介入状态 -1.没有介入 1.用户申请介入 2.平台同意介入 3.平台拒绝介入 5.平台同意退款成功
const platInterveneStepStatus = computed(() => {
  switch (platInterveneSts.value) {
    case 1:
      return 'wait'
    case 3:
      return 'error'
    default:
      return 'finish'
  }
})
</script>

<style scoped>
.component-refund-steps{
  width: 100%;
}
</style>
