.page-refund-order-info{
  // 平台介入tips
  .plat-intervene {
    font-size: 14px;
    color: #e6a23c;
    width: 100%;
    display: flex;
    align-items: center;
    background-color: #FDF6EC;
    padding: 20px;
    margin-bottom: 19px;
    &:deep(.el-icon) {
      font-size: 14px;
      margin-right: 2px;
    }
  }
}

.mod-order-refundOrderInfo {
  height: 100%;
  width: 100%;
  font: 14px Arial, "PingFang SC", "Hiragino Sans GB", STHeiti,
  "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  color: #495060;

  .order-number .text {
    font-size: 14px;
    color: #8a8a8a;
  }

  .order-state .state-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .title-left span {
    font-weight: bold;
  }

  .title-left .text {
    color: red;
  }

  .title-mid .text {
    color: #8a8a8a;
  }

  .content .order-state {
    position: relative;
    margin-top: 15px;
  }

  .order-state .order-info {
    width: 100%;
    border: 1px solid #E8E9EC;
    margin: 15px 0;
    display: flex;
  }

  .order-info .detail-title {
    line-height: 19px;
    display: flex;
    align-items: center;
    font-weight: bold;
    color: #333333;
  }
  .order-info .logistics-box {
    height: 220px;
    overflow-y: auto;
    position: relative;
    margin-top: 15px;
  }

  .order-info .logistics-box::before {
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 10px;
    height: 10px;
    content: " ";
    font-size: 0;
    background: #fff;
    z-index: 1;
  }

  .order-info .logistics-box::after {
    position: absolute;
    left: 6px;
    top: 0;
    display: block;
    height: 100%;
    content: " ";
    font-size: 0;
    background: #eee;
    z-index: 0;
  }

  .order-info .logistics-box .item {
    padding: 0 0 40px 25px;
    position: relative;
    margin-left: 6px;
    border-left: 1px solid #eee;
  }

  .order-info .logistics-box .left-line {
    border-left: none;
  }

  .order-info .logistics-box .item::before {
    position: absolute;
    left: -10px;
    top: 0;
    display: block;
    width: 19px;
    height: 19px;
    border-radius: 50%;
    content: " ";
    font-size: 0;
    background: #ccc;
    border: 5px solid #fff;
    z-index: 2;
  }

  .order-info .logistics-box .item:first-child::before {
    background: #c00;
  }

  .order-info .logistics-box .item .time {
    color: #999;
  }

  .order-info .logistics-box .item .text {
    margin-top: 5px;
    width: 600px;
  }

  .order-info .order-details {
    width: 620px;
    padding-left: 45px;
    padding-top: 20px;
    padding-bottom: 20px;
    border-right: 1px solid #e9eaec;
    &.refund-record-left{
      min-height: 400px;
    }
  }

  .order-info .detail-cont {
    position: relative;
  }

  .sellerRemark {
    margin-top: 20px;
    display: flex;
  }

  .sellerRemark .content {
    height: 120px;
    line-height: 120px;
    float: right;
    margin-bottom: 30px;
    width: 95%;
  }

  .detail01  {
    display: flex;
    align-items: center;
  }
  .text-width {
    margin-bottom: 10px;
  }
  .detail02 .text-width {
    width: 100%;
  }
  .text-width .text-width-item {
    line-height: 27px;
    display: flex;
    .title{
      white-space: nowrap;
    }
    .img-box{
      width: 40px;
      height: 40px;
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
  .text-width .text-width-item .text {
    display: inline-block;
    max-width: 250px;
    line-height: 27px;
    word-break: break-word;
  }

  .order-info .detail-cont {
    margin: 15px 20px 0 0;
  }

  .order-info .buyers {
    width: 1030px;
    padding-left: 45px;
    padding-top: 20px;
    position: relative;
    .refund-record-title{
      justify-content: space-between;
      padding-right: 45px;
      padding-bottom: 15px;
      .proof-btn{
        color: #155BD4;
        display: flex;
        align-items: center;
        cursor: pointer;
        .txt{
          margin-left: 3px;
        }
      }
    }
    .refund-record{
      width: 100%;
      height: calc(100% - 54px);
      overflow-y: auto;
      position: absolute;
      left: 0;
      padding-left: 45px;
    }
  }

  .buyers .buyers-info {
    margin-top: 15px;
    position: relative;
  }

  .buyers .buyers-info .img-up {
    margin-top: 15px;
  }

  .buyers .detail02 {
    display: flex;
    height: 100%;
    line-height: 25px;
    margin-top: 15px;
  }

  .item-info span {
    margin-bottom: 15px;
    line-height: 30px;
  }

  .item-info .text {
    position: absolute;
    right: 0;
  }

  .order-log .log-title {
    margin-top: 20px;
    width: 100%;
    line-height: 20px;
    font-family: Microsoft YaHei;
    font-weight: bold;
  }

  .order-log .log-cont {
    margin-top: 15px;
    color: #666666;
    > div {
      margin-bottom: 10px;
    }
    > div:last-child {
      margin-bottom: 0;
    }
    .reject-info {
      margin-left: 134px;
      display: flex;
      .content {
        flex: 1;
        word-break: break-word;
        display: flex;
        align-items: center;
      }
    }
  }
  .refundId {
    margin-bottom: 15px;
    margin-left: 20px;
  }
  .num-cont {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    .state-title {
      width: 500px;
      box-sizing: border-box;
      padding: 20px 45px;
      border: 1px solid #E8E9EC;
      border-right: none;
      .item {
        display: flex;
        margin-bottom: 15px;
        &:last-child {
          margin-bottom: 0;
        }
        .text {
          color: #155BD4;
        }
      }
    }
  }
  .refund-progress {
    width: 100%;
    border: 1px solid #E8E9EC;
    display: flex;
    justify-content: center;
    align-items: center;
    .item {
      width: 100%;
    }
  }
  .btn-bar {
    margin-top: 15px;
  }
  .remark-title {
    min-width: 70px;
    display: flex;
    align-items: center;
  }
  .denial::before {
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
  }
  .remark-content{
    display: inline-block;
    word-break: break-word;
  }

  .input-bar {
    display: inline-block;
    width: 100%;
    &.denial-reason{
      display: flex;
      div{
        flex: 1;
      }
      .right{
        margin-left: 100px;
      }
    }
    .remark-title {
      margin-bottom: 10px;
    }
  }
}
div :deep(.el-step.is-center .el-step__head) {
  display: flex;
  justify-content: center;
  align-items: center;
}
div :deep(.el-textarea__inner) {
  border-radius: 0 !important;
  padding: 12px 10px;
}

.remark-content :deep(.el-input__inner){
  padding-right: 20px !important;
}

// 商品信息
.item-list {
  .prod-info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .con {
      flex: 1;
      margin-left: 3%;
    }
    .gift-icon {
      display: inline-block;
      padding: 4px;
      border-radius: 4px;
      background: #e43130;
      color: #fff;
      font-size: 13px;
      margin-right: 5px;
      line-height: 1em;
    }
    .con .sku {
      margin-left: 3%;
      color: #999;
    }
  }
  // 赠品
  .gift-prod {
    width: 100%;
    padding: 10px 50px 0 68px;
  }
}
