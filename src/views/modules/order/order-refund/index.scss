.clearfix:after {
  /*伪元素是行内元素 正常浏览器清除浮动方法*/
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.screening-conditions {
  display: block;
  padding: 25px 25px 0 25px !important;
  background: #f7f8fa;
  margin-bottom: 20px;
}

.mod-refund-order.order-refund {
  .screening-conditions {
    padding-bottom: 0;
  }
}
:deep(.el-range-editor.el-input__wrapper) {
  margin-right: 10px;
}
.mod-refund-order {
  .main {
    .order-status-nav {
      position: relative;
      display: block;
      width: 100%;
      margin-bottom: 15px;
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid #ddd;
      ul,
      li {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      .nav-item {
        float: left;
        height: 40px;
        line-height: 40px;
        background: #f7f8fa;
        border: 1px solid #ddd;
        padding: 0 20px;
        margin: 0 -1px -1px 0;
        cursor: pointer;
      }
      .selected {
        background: #fff;
        border-bottom: 1px solid #fff;
      }
    }
    .tit {
      margin-bottom: 15px;
      background: #f7f8fa;
      z-index: 11;
      height: 57px;
      font-weight: bold;
    }
    .fixed-top {
      position: fixed;
      width: calc(100% - 310px);
      min-width: calc(1200px - 330px);
      top: 90px;
    }

    .fold-fixed-top {
      width: calc(100% - 180px);
      min-width: calc(1200px - 200px);
    }
    .column-title {
      text-align: center;
    }
    .transaction-price {
      text-align: center;
    }
  }
  .tit {
    display: flex;
    height: 45px;
    align-items: center;
  }
  .tit .item {
    padding: 0 10px;
    width: 10%;
    text-align: center;
  }
  .tit .product {
    width: 25%;
  }
  .prod-tit {
    padding: 10px;
    background: #F7F8FA;
    height: 49px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-left: 1px solid #EBEDF0;
    border-top: 1px solid #EBEDF0;
    border-right: 1px solid #EBEDF0;
    box-sizing: border-box;
  }
  .prod-tit span {
    margin-right: 15px;
  }
  .prod-cont {
    display: flex;
    border: 1px solid #EBEDF0;
    color: #495060;
    box-sizing: border-box;
  }
  .prod-cont .item {
    display: flex;
    display: -webkit-flex;
    align-items: center;
    padding: 10px;
    text-align: left;
    height: 100%;
    border-right: 1px solid #eee;
  }
  .prod-con .prod-name-txt {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .prod-cont .item span {
    display: block;
  }
  .prod-cont .prod-item {
    display: flex;
    align-items: inherit;
    flex-direction: column;
    height: 100%;
    border-right: 1px solid #eee;
  }
  .prod-name {
    width: 55%;
    text-align: left;
  }
  .prod-con {
    display: block;
    padding: 0 !important;
  }
  .prod-price {
    width: 28%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    right: 0 !important;
  }
  .prod-price span {
    display: block;
  }
  .prod-price span:first-child {
    margin-bottom: 10px;
  }
  .order-status {
    display: inline-block;
    margin-top: 15px;
    padding: 2px 4px;
    border: 1px solid #e43130;
    border-radius: 2px;
    color: #e43130;
  }
  .prod-cont .items .info{
    display: flex;
  }
  .prod-cont .items.name {
    height: 100%;
    border-bottom: 1px solid #dddee1;
  }
  .order-prod-item-info {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    .prod-image {
      width: 60px;
      height: 60px;
      margin-right: 20px;
    }
    .prod-name {
      flex: 1;
      margin-left: auto;
      width: 72% !important;
      .prod-con {
        text-align: left;
        width: 85% !important;
        display: block;
        padding: 0 !important;
        .prod-name-txt {
          padding-right: 10px;
          box-sizing: border-box;
          display: -webkit-box;
          word-break: break-word;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .prod-name-sku {
          color: #999;
        }
        .order-status {
          display: inline-block;
          margin-top: 15px;
          margin-right: 10px;
          padding: 2px 4px;
          border: 1px solid #e43130;
          border-radius: 2px;
          color: #e43130;
        }
      }
    }
  }
  // 赠品盒子
  .order-prod-item-give-con {
    width: 100%;
    padding: 0 50px 10px 82px;
  }
  .prod-cont .items.name:last-child {
    border-bottom: none;
  }
  .prod-image {
    margin-right: 20px;
    width: 80px;
    height: 80px;
  }
  .item span {
    display: block;
  }
  .item .operate {
    color: #2d8cf0;
  }
  .item .operate .default-btn + .default-btn {
    display: block;
    margin-top: 10px;
    margin-left: 0;
  }
  .item .totalprice {
    color: #ff4141;
    margin-bottom: 4px;
  }
  .empty {
    display: block;
    height: 200px;
    line-height: 200px;
    text-align: center;
    color: #aaa;
  }
  .buyer-info {
    display: block;
    width: 100%;
  }
  .text-btn {
    margin-left: 0;
  }
}
div :deep(.el-tabs__active-bar) {
  width: 0 !important;
}
div :deep(.el-tabs__item) {
  padding: 0 20px !important;
  min-width: 68px;
  width: auto;
  text-align: center;
}
.operate-btn {
  margin: 0 !important;
}
div :deep(.el-tabs__nav-wrap::after){
  height: 1px;
}
.search-bar {
  .input-row {
    .is-active {
      color: #155bd4;
    }
  }
}
