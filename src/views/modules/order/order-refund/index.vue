<template>
  <div class="mod-refund-order order-refund">
    <div class="screening-conditions search-bar">
      <el-form
        :inline="true"
        :model="dataForm"
        label-width="auto"
        @submit.prevent
        @keyup.enter="getDataList(page)"
      >
        <div class="input-row">
          <el-form-item :label="$t('order.refundId') + ':'">
            <el-input
              v-model="searchForm.refundSn"
              :placeholder="$t('order.refundId')"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('order.typeOfRefund') + ':'">
            <el-select
              v-model="searchForm.refundType"
              clearable
              :placeholder="$t('order.typeOfRefund')"
            >
              <el-option
                v-for="item in refundType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('order.afterSalesStatus') + ':'">
            <el-select
              v-model="returnMoneySts"
              clearable
              :placeholder="$t('order.pleSelAfterSalesSta')"
              @change="handleReturnMoneyStsChange"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('order.number') + ':'">
            <el-input
              v-model="searchForm.orderNumber"
              :placeholder="$t('order.number')"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('order.orderType') + ':'">
            <el-select
              v-model="searchForm.orderType"
              clearable
              :placeholder="$t('order.pleaseSelectOrderType')"
            >
              <el-option
                v-for="item in orderType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('order.applicationType') + ':'">
            <el-select
              v-model="searchForm.applyType"
              clearable
              :placeholder="$t('order.pleaseChooseHowToApply')"
            >
              <el-option
                v-for="item in applyType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('order.createTime') + ':'">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :start-placeholder="$t('time.start')"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              :end-placeholder="$t('time.end')"
            />
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 1 }"
              @click="setDateRange(1)"
            >
              {{ $t("time.t") }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 2 }"
              @click="setDateRange(2)"
            >
              {{ $t("time.y") }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 3 }"
              @click="setDateRange(3)"
            >
              {{ $t("time.n") }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': timeActive === 4 }"
              @click="setDateRange(4)"
            >
              {{ $t("temp.m") }}
            </div>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t("order.query") }}
            </div>
            <div
              class="default-btn"
              @click="clear()"
            >
              {{ $t("shop.resetMap") }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main">
      <div class="content">
        <!-- 导航 -->
        <div class="order-status-nav clearfix">
          <ul class="nav-list clearfix">
            <li
              :class="['nav-item', activeName === 0 ? 'selected' : '']"
              data-sts="0"
              @click="selectNav($event)"
            >
              {{ $t('time.a') }}
            </li>
            <li
              :class="['nav-item', activeName === 1 ? 'selected' : '']"
              data-sts="1"
              @click="selectNav($event)"
            >
              {{ $t('order.buyerApplication') }}
            </li>
            <li
              :class="['nav-item', activeName === 2 ? 'selected' : '']"
              data-sts="2"
              @click="selectNav($event)"
            >
              {{ $t('order.sellerAccepts') }}
            </li>
            <li
              :class="['nav-item', activeName === 3 ? 'selected' : '']"
              data-sts="3"
              @click="selectNav($event)"
            >
              {{ $t("order.buyShipment") }}
            </li>
            <li
              :class="['nav-item', activeName === 4 ? 'selected' : '']"
              data-sts="4"
              @click="selectNav($event)"
            >
              {{ $t("order.sellerReceipt") }}
            </li>
            <li
              :class="['nav-item', activeName === 5 ? 'selected' : '']"
              data-sts="5"
              @click="selectNav($event)"
            >
              {{ $t("order.refundsuccessfully") }}
            </li>
            <li
              :class="['nav-item', activeName === -1 ? 'selected' : '']"
              data-sts="-1"
              @click="selectNav($event)"
            >
              {{ $t("order.refundClosed") }}
            </li>
            <li
              :class="['nav-item', activeName == 6 ? 'selected' : '']"
              data-sts="6"
              @click="selectNav($event)"
            >
              {{ $t('refund.applyInter') }}
            </li>
            <li
              :class="['nav-item', activeName == 7 ? 'selected' : '']"
              data-sts="7"
              @click="selectNav($event)"
            >
              {{ $t('refund.interHandle') }}
            </li>
          </ul>
          <ul class="nav-right" />
        </div>

        <!-- 列标题 -->
        <div :class="['tit']">
          <el-row style="width: 100%">
            <el-col
              id="prod-info-title"
              :span="6"
            >
              <span class="item product">{{ $t("home.product") }}</span>
            </el-col>
            <el-col
              id="price-title"
              :span="3"
              class="transaction-price"
            >
              <span class="item">{{
                $t("order.commodityUnitPriceYuanQuantity")
              }}</span>
            </el-col>
            <el-col
              :span="3"
              class="column-title"
            >
              <span class="item">{{ $t("order.returnAmount") }}({{ $t("admin.dollar") }})</span>
            </el-col>
            <el-col
              :span="3"
              class="column-title"
            >
              <span class="item">{{ $t("order.commodityCondition") }}</span>
            </el-col>
            <el-col
              :span="$t('time.tip') === 'to' ? 2: 3"
              class="column-title"
            >
              <span class="item">{{ $t("order.typeOfRefund") }}</span>
            </el-col>
            <el-col
              :span="2"
              class="column-title"
            >
              <span class="item">{{ $t("group.orderStatus") }}</span>
            </el-col>
            <el-col
              :span="2"
              class="column-title"
            >
              <span class="item">{{ $t("order.afterSalesStatus") }}</span>
            </el-col>
            <el-col
              :span="2"
              class="column-title"
            >
              <span class="item">{{ $t("publics.operating") }}</span>
            </el-col>
          </el-row>
        </div>
        <div
          v-show="showHeadScroll"
          :class="['tit', 'fixed-top',sidebarFold?'fold-fixed-top':'']"
        >
          <el-row style="width: 100%">
            <el-col
              id="prod-info-title"
              :span="6"
            >
              <span class="item product">{{ $t("home.product") }}</span>
            </el-col>
            <el-col
              id="price-title"
              :span="3"
              class="transaction-price"
            >
              <span class="item">{{
                $t("order.commodityUnitPriceYuanQuantity")
              }}</span>
            </el-col>
            <el-col
              :span="3"
              class="column-title"
            >
              <span class="item">{{ $t("order.returnAmount") }}({{ $t("admin.dollar") }})</span>
            </el-col>
            <el-col
              :span="3"
              class="column-title"
            >
              <span class="item">{{ $t("order.commodityCondition") }}</span>
            </el-col>
            <el-col
              :span="$t('time.tip') === 'to' ? 2: 3"
              class="column-title"
            >
              <span class="item">{{ $t("order.typeOfRefund") }}</span>
            </el-col>
            <el-col
              :span="2"
              class="column-title"
            >
              <span class="item">{{ $t("group.orderStatus") }}</span>
            </el-col>
            <el-col
              :span="2"
              class="column-title"
            >
              <span class="item">{{ $t("order.afterSalesStatus") }}</span>
            </el-col>
            <el-col
              :span="2"
              class="column-title"
            >
              <span class="item">{{ $t("publics.operating") }}</span>
            </el-col>
          </el-row>
        </div>

        <div
          v-for="orderRefund in dataList"
          :key="orderRefund.refundId"
          class="prod"
          style="margin-bottom: 15px"
        >
          <div class="prod-tit">
            <div>
              <span>{{ $t("order.refundId") }}：{{ orderRefund.refundSn }}</span>
              <span>{{ $t("order.number") }}:{{ orderRefund.orderNumber }}</span>
              <span>{{ $t("order.applicationTime") }}：{{ orderRefund.applyTime }}</span>
            </div>
            <div />
          </div>
          <div class="prod-cont">
            <el-row style="width: 100%">
              <el-col
                :span="$t('language') === 'English' ? 9 : 10"
                style="height: 100%"
              >
                <div class="prod-item">
                  <div
                    v-for="orderItem in orderRefund.orderItems"
                    :key="orderItem.orderItemId"
                    class="items name"
                  >
                    <div class="order-prod-item-info">
                      <div class="prod-image">
                        <prod-pic
                          :pic="orderItem.pic"
                          height="60px"
                          width="60px"
                        />
                      </div>
                      <div class="prod-name">
                        <div class="prod-con">
                          <div class="prod-name-txt">
                            {{ orderItem.prodName }}
                          </div>
                          <div
                            v-if="orderItem.skuName"
                            class="prod-name-sku"
                          >
                            {{ orderItem.skuName }}
                          </div>
                          <div
                            v-if="
                              orderRefund.orderType === 1 ||
                                orderRefund.orderType === 2
                            "
                            class="order-status"
                          >
                            {{
                              orderRefund.orderType === 1
                                ? $t("order.groupPurchaseOrder")
                                : orderRefund.orderType === 2
                                  ? $t("order.spikeOrder"):""
                            }}
                          </div>
                          <div
                            v-if="orderRefund.orderMold === 1"
                            class="order-status"
                          >
                            {{ $t("order.virtualOrder") }}
                          </div>
                          <div
                            v-if="orderRefund.orderMold === 3"
                            class="order-status"
                          >
                            {{ $t("order.electronicCardOrder") }}
                          </div>
                          <div
                            v-if="orderItem.activityType===5"
                            class="order-status"
                          >
                            {{ $t("order.giveawayPord") }}
                          </div>
                        </div>
                      </div>
                      <div class="prod-price">
                        <span>{{ orderItem.price.toFixed(2) }}</span>
                        <span>{{ orderItem.prodCount
                        }}{{ $t("marketing.item") }}</span>
                      </div>
                    </div>
                    <!-- 组合信息 -->
                    <div
                      v-if="orderItem.comboList"
                      class="order-prod-item-give-con"
                    >
                      <combo-list
                        :combo-list="orderItem.comboList"
                        :tag-name="'【' + $t('order.combo') + '】'"
                      />
                    </div>

                    <!-- 赠品信息 -->
                    <div
                      v-if="orderItem.giveawayList"
                      class="order-prod-item-give-con"
                    >
                      <combo-list
                        :combo-list="orderItem.giveawayList"
                        :tag-name="'【' + $t('order.giveawayPord') + '】'"
                      />
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col
                :span="3"
                style="height: 100%"
              >
                <div class="item">
                  <div>
                    <span class="totalprice">{{
                      orderRefund.refundAmount.toFixed(2) +
                        " + " +
                        orderRefund.refundScore +
                        $t("order.integral")
                    }}</span>
                    <div>
                      <span>{{ orderRefund.goodsNum
                      }}{{ $t("marketing.item") }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col
                :span="3"
                style="height: 100%"
              >
                <div class="item">
                  <div class="buyer-info">
                    <div style="margin-bottom: 4px">
                      {{
                        orderRefund.applyType === 1
                          ? $t("order.onlyRefund")
                          : $t("order.refunds")
                      }}
                    </div>
                    <div
                      class="totalprice"
                      style="color: #333333;margin-bottom: 4px"
                    >
                      {{
                        orderRefund.isReceiver
                          ? $t("order.goodsReceived")
                          : $t("order.goodsNotReceived")
                      }}
                    </div>
                    <div
                      class="totalprice"
                      style="color: #333333;margin-bottom: 4px"
                    >
                      {{
                        orderRefund.buyerReason
                      }}
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col
                :span="2"
                style="height: 100%"
              >
                <div class="item">
                  <div>
                    <span v-if="orderRefund.refundType === 1">{{
                      $t("order.wholeGoodsRefund")
                    }}</span>
                    <span v-if="orderRefund.refundType === 2">{{
                      $t("order.singleItemRefund")
                    }}</span>
                  </div>
                </div>
              </el-col>
              <el-col
                :span="2"
                style="height: 100%"
              >
                <div class="item">
                  <span
                    v-if="orderRefund.status === 1"
                    type="danger"
                  >{{ $t("order.pendingPayment") }}</span>
                  <span
                    v-else-if="orderRefund.status === 2"
                    type="danger"
                  >{{ $t("order.toBeShipped") }}</span>
                  <span
                    v-else-if="orderRefund.status === 3"
                    type="danger"
                  >{{ $t("order.pendingReceipt") }}</span>
                  <span
                    v-else-if="orderRefund.status === 7"
                    type="danger"
                  >{{ $t("group.waitGroup") }}</span>
                  <span
                    v-else-if="orderRefund.status === 5"
                    type="danger"
                  >{{ $t("order.successfulTransaction") }}</span>
                  <span
                    v-else-if="orderRefund.status === 6"
                  >{{
                    $t("order.transactionFailed")
                  }}</span>
                </div>
              </el-col>
              <el-col
                :span="2"
                style="height: 100%"
              >
                <div
                  v-if="orderRefund.platformInterventionStatus && orderRefund.platformInterventionStatus!==-1"
                  class="item"
                >
                  <!-- 平台介入状态 -1.没有介入 1.用户申请介入 2.平台同意介入 3.平台拒绝介入 5.平台同意退款成功  7.平台介入处理中 -->
                  <span>{{
                    [
                      "",
                      $t('refund.applyInter'),
                      '',
                      $t('order.refundClosed'),
                      '',
                      $t('order.refundsuccessfully'),
                      '',
                      $t('refund.interHandle')
                    ][orderRefund.platformInterventionStatus]
                  }}</span>
                </div>
                <div
                  v-else
                  class="item"
                >
                  <span
                    v-if="orderRefund.returnMoneySts === 1"
                    type="danger"
                  >{{ $t("order.buyerApplication") }}</span>
                  <span
                    v-else-if="orderRefund.returnMoneySts === 2"
                    type="danger"
                  >{{ $t("order.sellerAccepts") }}</span>
                  <span
                    v-else-if="orderRefund.returnMoneySts === 3"
                    type="danger"
                  >{{ $t("order.buyShipment") }}</span>
                  <span
                    v-else-if="orderRefund.returnMoneySts === 4"
                    type="danger"
                  >{{ $t("order.sellerReceipt") }}</span>
                  <span
                    v-else-if="orderRefund.returnMoneySts === 5"
                    type="danger"
                  >{{ $t("order.refundsuccessfully") }}</span>
                  <span
                    v-else-if="orderRefund.returnMoneySts === 6"
                    type="danger"
                  >{{ $t("order.withdrawsApplication") }}</span>
                  <span
                    v-else
                    type="danger"
                  >{{ $t("order.refundClosed") }}</span>
                </div>
              </el-col>
              <el-col
                :span="$t('language') === 'English' ? 3 : 2"
                style="height: 100%"
              >
                <div class="item">
                  <div
                    class="operate"
                    style="width: 100%; text-align: center;"
                  >
                    <div
                      class="default-btn text-btn operate-btn"
                      @click="viewOrderHandle(orderRefund.orderNumber)"
                    >
                      {{ $t("order.viewOrder") }}
                    </div>
                    <div
                      class="default-btn text-btn operate-btn"
                      @click="toImbox(orderRefund)"
                    >
                      {{ $t("order.contactBuyer") }}
                    </div>
                    <div
                      v-if="isAuth('admin:orderRefund:update') && ![-1,2,5].includes(orderRefund.returnMoneySts)"
                      :class="['default-btn','text-btn','operate-btn', $t('language') === 'English' ?'btn-height':'']"
                      @click="
                        refundHandle(orderRefund.refundId, orderRefund.shopId)
                      "
                    >
                      {{ $t("order.processingRefunds") }}
                    </div>
                    <div
                      v-if="[-1,2,5].includes(orderRefund.returnMoneySts)"
                      class="default-btn text-btn operate-btn"
                      @click="
                        refundHandle(orderRefund.refundId, orderRefund.shopId)
                      "
                    >
                      {{ $t("order.lookRefunds") }}
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div
          v-if="!dataList.length"
          class="empty"
        >
          {{ $t("order.noData") }}
        </div>
      </div>
    </div>
    <el-pagination
      v-if="dataList.length"
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      :total="page.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'
import moment from 'moment'
import { useCommonStore } from '@/stores/common.js'
import { ElMessage } from 'element-plus'
const commonStore = useCommonStore()
const options = [
  {
    value: 1,
    label: $t('order.buyerApplication')
  },
  {
    value: 2,
    label: $t('order.sellerAccepts')
  },
  {
    value: 3,
    label: $t('order.buyShipment')
  },
  {
    value: 4,
    label: $t('order.sellerReceipt')
  },
  {
    value: 5,
    label: $t('order.refundsuccessfully')
  },
  {
    value: -1,
    label: $t('order.refundClosed')
  },
  {
    value: 6,
    label: $t('refund.applyInter')
  },
  {
    value: 7,
    label: $t('refund.interHandle')
  }
]

const orderType = [
  {
    value: 0,
    label: $t('order.normalOrder')
  },
  {
    value: 1,
    label: $t('order.groupPurchaseOrder')
  },
  {
    value: 2,
    label: $t('order.spikeOrder')
  }
]

const refundType = [
  {
    value: 1,
    label: $t('order.wholeOrderRefund')
  },
  {
    value: 2,
    label: $t('order.singleItemRefund')
  }
]

const applyType = [
  {
    value: 1,
    label: $t('order.onlyRefund')
  },
  {
    value: 2,
    label: $t('order.refunds')
  }
]

const showHeadScroll = ref(false)
const sts = ref(0)
const dataForm = ref({})
const dateRange = ref([])
const searchForm = ref([])
const returnMoneySts = ref(null)
const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const dataListLoading = ref(false)
const activeName = ref(0)
const timeActive = ref(null)
const priceWidth = ref('109px')
const infoWidth = ref('400px')

const sidebarFold = computed({
  // 二级菜单折叠状态
  get () { return commonStore.sidebarFold },
  set (val) { commonStore.updateSidebarFold(val) }
})

const router = useRouter()
const { query } = useRoute()
onMounted(() => {
  // 携带参数查询
  searchForm.value = query
  if (query.returnMoneySts) {
    returnMoneySts.value = Number(query.returnMoneySts)
  }
  getDataList(page)
  // 监听页面滚动
  window.addEventListener('scroll', scrollToTop)
  setTimeout(() => {
    getEleWidth()
  })
  window.onresize = () => {
    return (() => {
      setTimeout(() => {
        getEleWidth()
      })
    })()
  }
})
onUnmounted(() => {
  // 页面销毁时移除监听
  window.removeEventListener('scroll', scrollToTop)
})

/**
 * 订单状态修改
 */
const handleReturnMoneyStsChange = (val) => {
  activeName.value = Number(val)
  searchForm.value.returnMoneySts = Number(val)
  getDataList(page)
}
const getEleWidth = () => {
  const div = document.getElementById('price-title') // prod-info-title
  const div1 = document.getElementById('prod-info-title')
  if (!div && !div1) {
    return
  }
  const w = div.offsetWidth // 返回元素的总宽度
  const w1 = div1.offsetWidth
  priceWidth.value = w + 'px'
  infoWidth.value = w1 + 'px'
}
/**
 * 页面滚动事件
 */
const scrollToTop = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
  showHeadScroll.value = scrollTop > 400
}

let tempSearchForm = null
/**
 * 获取数据列表
 */
// eslint-disable-next-line no-unused-vars
const getDataList = (pageParam, newData = false) => {
  const params = searchForm.value
  dataListLoading.value = true
  let paramReturnMoneySts = returnMoneySts.value || null
  let handleInterventionStatus = null
  if (returnMoneySts.value === 6 || returnMoneySts.value === 7) {
    paramReturnMoneySts = null
    handleInterventionStatus = returnMoneySts.value === 6 ? 1 : 2
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = {
      current: page.currentPage,
      size: page.pageSize,
      orderNumber: params ? (params.orderNumber ? params.orderNumber : null) : null,
      returnMoneySts: paramReturnMoneySts,
      handleInterventionStatus,
      refundSn: params ? (params.refundSn ? params.refundSn : null) : null,
      orderType: params ? ((params.orderType || params.orderType === 0) ? params.orderType : null) : null,
      applyType: params ? (params.applyType ? params.applyType : null) : null,
      refundType: params ? (params.refundType ? params.refundType : null) : null,
      startTime: dateRange.value ? (dateRange.value[0] ? dateRange.value[0] : null) : null, // 开始时间
      endTime: dateRange.value ? (dateRange.value[1] ? dateRange.value[1] : null) : null // 结束时间
    }
  } else {
    tempSearchForm.current = page.currentPage
    tempSearchForm.size = page.pageSize
    tempSearchForm.returnMoneySts = paramReturnMoneySts
    tempSearchForm.handleInterventionStatus = handleInterventionStatus
  }
  http({
    url: http.adornUrl('/order/refund/page'),
    method: 'get',
    params: http.adornParams(Object.assign(tempSearchForm))
  })
    .then(({ data }) => {
      dataList.value = data.records
      page.total = data.total
      dataListLoading.value = false
      sts.value = returnMoneySts.value ? returnMoneySts.value : 0
      activeName.value = Number(sts.value)
    })
}
/**
 * 每页数
 * @param val
 */
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getDataList(page)
}
/**
 * 当前页
 * @param val
 */
const currentChangeHandle = (val) => {
  page.currentPage = val
  getDataList(page)
}
/**
 * 导航选择状态
 */
const selectNav = (e) => {
  const stsParam = e.currentTarget.dataset.sts
  sts.value = parseInt(stsParam)
  activeName.value = parseInt(stsParam)
  returnMoneySts.value = sts.value === 0 ? null : parseInt(stsParam)
  page.currentPage = 1
  getDataList(page)
}
/**
 * 查看订单
 */
const viewOrderHandle = (id) => {
  router.push({
    path: '/order/order-info/index',
    query: {
      orderNumber: id
    }
  })
}
/**
 * 前往消息盒子
 * @param order
 */
const toImbox = (order) => {
  if (order.userWriteOff) {
    ElMessage({
      message: $t('order.userlogoutTips'),
      type: 'error'
    })
    return
  }
  const path = router.resolve({
    path: '/message-box',
    query: {
      userId: order.userId,
      orderNumber: order.orderNumber
    }
  })
  const win = window.open(path.href, 'view_window', 'noopener,noreferrer')
  if (win) win.opener = null
}
/**
 * 处理退款
 */
const refundHandle = (id) => {
  router.push({
    path: '/order/refund-order-info/index',
    query: {
      refundId: id
    }
  })
}
/**
 * 根据选项设置时间
 * 1:今天 2:昨天 3: 近七天 4:近30天 5:近60天
 */
const setDateRange = (val) => {
  timeActive.value = val
  let startDay = null
  let endDay = null
  if (val === 1) {
    startDay = 0
    endDay = 0
  } else if (val === 2) {
    startDay = -1
    endDay = -1
  } else if (val === 3) {
    startDay = -7
    endDay = -1
  } else if (val === 4) {
    startDay = -30
    endDay = -1
  } else {
    return
  }
  // 开始时间
  const startTime = moment().add(startDay, 'days').startOf('days').format('LL')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days').format('LL')
  dateRange.value = [startTime, endTime]
}
/**
 * 清空按钮
 */
const clear = () => {
  searchForm.value = {}
  dateRange.value = []
  returnMoneySts.value = null
  timeActive.value = null
}
/**
 * 搜索查询
 * @param newData
 */
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}
</script>

<style lang="scss" scoped>
  @use './index.scss';
</style>
