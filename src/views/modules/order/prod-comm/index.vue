<template>
  <div class="page-prod-prodComm">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="prodName"
            :label="$t('product.prodName') + ':'"
          >
            <el-input
              v-model="searchForm.prodName"
              type="text"
              clearable
              :placeholder="$t('product.prodName')"
            />
          </el-form-item>
          <el-form-item
            prop="nickName"
            :label="$t('users.name') + ':'"
          >
            <el-input
              v-model="searchForm.nickName"
              type="text"
              clearable
              :placeholder="$t('users.name')"
            />
          </el-form-item>
          <el-form-item
            prop="stationName"
            :label="$t('station.stationNames') + ':'"
          >
            <el-input
              v-model="searchForm.stationName"
              type="text"
              clearable
              :placeholder="$t('station.stationNames')"
            />
          </el-form-item>
          <el-form-item :label="$t('productComm.recTime') + ':'">
            <el-date-picker
              v-model="recDateRange"
              type="datetimerange"
              :range-separator="$t('time.tip')"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              value-format="YYYY-MM-DD HH:mm:ss"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
            />
          </el-form-item>
          <el-form-item :label="$t('productComm.replyTime') + ':'">
            <el-date-picker
              v-model="replyDateRange"
              type="datetimerange"
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch('searchForm')"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="table-con prod-table">
        <el-table
          ref="prodCommTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          style="width: 100%"
        >
          <el-table-column
            align="left"
            prop="prodName"
            :label="$t('product.prodName')"
            min-width="360"
          >
            <template #default="scope">
              <div class="table-cell-con">
                <div>
                  <prod-pic
                    width="60px"
                    height="60px"
                    :pic="scope.row.prodPic"
                  />
                </div>
                <span class="table-cell-text">{{ scope.row.prodName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="stationName"
            :label="$t('station.stationNames')"
            width="200"
          >
            <template #default="{row}">
              <span class="table-cell-text line-clamp-one">{{ row.stationName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="nickName"
            :label="$t('users.name')"
            width="150"
          >
            <template #default="scope">
              <span
                v-if="scope.row.isAnonymous===1"
                class="table-cell-text line-clamp-one"
              >{{ $t("productComm.anonymousuUser") }}</span>
              <span
                v-else
                class="table-cell-text line-clamp-one"
              >{{ scope.row.nickName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="score"
            :label="$t('productComm.score')"
            width="130"
          />
          <el-table-column
            align="left"
            prop="isAnonymous"
            :label="$t('productComm.isAnonymous')"
            width="130"
          >
            <template #default="scope">
              <div class="tag-text">
                {{ [$t('publics.no'), $t('publics.yes')][scope.row.isAnonymous] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="recTime"
            :label="$t('productComm.recTime')"
            width="250"
          >
            <template #default="scope">
              <div>{{ scope.row.recTime || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="replyTime"
            :label="$t('productComm.replyTime')"
            width="250"
          >
            <template #default="scope">
              <div>{{ scope.row.replyTime || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            :label="$t('publics.operating')"
            width="200"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('prod:prodComm:update')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.prodCommId, true)"
                >
                  {{ $t('groups.edit') }}
                </div>
                <div
                  v-if="isAuth('prod:prodComm:page')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.prodCommId, false)"
                >
                  {{ $t('live.view') }}
                </div>
                <div
                  v-if="isAuth('prod:prodComm:delete')"
                  class="default-btn text-btn"
                  @click="onDelete(scope.row.prodCommId)"
                >
                  {{ $t("text.delBtn") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddOrUpdate from './add-or-update.vue'

onMounted(() => {
  getDataList()
})

let tempSearchForm = null // 保存上次点击查询的请求条件
let theParams = null // 保存上次点击查询的请求条件
const dataListLoading = ref(false)
const recDateRange = ref([])
const replyDateRange = ref([])
const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  prodName: '',
  nickName: ''
})
const getDataList = (pageParam, newData = false) => {
  dataListLoading.value = true
  if (newData || !tempSearchForm) {
    theParams = JSON.parse(JSON.stringify(searchForm))

    tempSearchForm = {
      current: pageParam == null ? page.currentPage : pageParam.currentPage,
      size: pageParam == null ? page.pageSize : pageParam.pageSize,
      recStartTime: recDateRange.value === null ? null : recDateRange.value[0],
      recEndTime: recDateRange.value === null ? null : recDateRange.value[1],
      replyTimeStart: replyDateRange.value === null ? null : replyDateRange.value[0],
      replyTimeEnd: replyDateRange.value === null ? null : replyDateRange.value[1]
    }
  } else {
    tempSearchForm.current = pageParam == null ? page.currentPage : pageParam.currentPage
    tempSearchForm.size = pageParam == null ? page.pageSize : pageParam.pageSize
  }
  http({
    url: http.adornUrl('/prod/prodComm/page'),
    method: 'get',
    params: http.adornParams(Object.assign(tempSearchForm, theParams))
  }).then(({ data }) => {
    dataList.value = data.records
    dataList.value?.forEach(element => {
      element.nickName = !element.user ? '' : element.user.nickName ? element.user.nickName : ''
      element.pic = element.pics ? element.pics.split(',')[0] : '@/assets/img/def.png'
    })
    page.total = data.total
    dataListLoading.value = false
  })
}

const addOrUpdateVisible = ref(false)
const addOrUpdateRef = ref(null)
// 新增 / 修改
const onAddOrUpdate = (id, isEdit) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id, isEdit)
  })
}
const onDelete = (prodCommId) => {
  ElMessageBox.confirm($t('admin.isDeleOper') + '?', $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/prod/prodComm/' + prodCommId),
      method: 'delete',
      data: http.adornData({})
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          refreshChange()
        }
      })
    })
  })
}
// 刷新回调用
const refreshChange = () => {
  getDataList(page)
}
const onSearch = (newData = false) => {
  getDataList(page, newData)
}

const searchFormRef = ref(null)
/**
 * 重置表单
 */
const onResetSearch = () => {
  searchFormRef.value?.resetFields()
  replyDateRange.value = []
  recDateRange.value = []
  getDataList()
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList()
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList()
}

</script>

<style lang="scss" scoped>
.page-prod-prodComm {
  .table-cell-text {
    margin-right: 70px;
  }
}
</style>
