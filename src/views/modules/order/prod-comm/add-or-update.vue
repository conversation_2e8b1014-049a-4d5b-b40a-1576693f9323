<template>
  <el-dialog
    v-model="visible"
    :title="!isEdit ? $t('live.view') : $t('groups.edit')"
    :close-on-click-modal="false"
    width="700px"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      :label-width="$t('language')==='English'?'100px':'80px'"
      :class="{'form-en':$t('language')==='English'}"
      @submit.prevent
      @keyup.enter="onSubmit()"
    >
      <div v-if="!isEdit">
        <el-row>
          <el-col :span="12">
            <!-- 记录时间 -->
            <el-form-item
              :label="$t('productComm.recTime')"
              prop="userName"
            >
              <el-input
                v-model="dataForm.recTime"
                :readonly="true"
                :disabled="!isEdit"
              />
            </el-form-item>
            <!-- IP来源 -->
            <el-form-item
              :label="$t('productComm.postip')"
              prop="userName"
            >
              <el-input
                v-model="dataForm.postip"
                :readonly="true"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 回复时间 -->
            <el-form-item
              :label="$t('productComm.replyTime')"
              prop="userName"
              :readonly="true"
            >
              <el-input
                v-model="dataForm.replyTime"
                :readonly="true"
                :disabled="!isEdit"
              />
            </el-form-item>
            <!-- 评价得分 -->
            <el-form-item
              :label="$t('productComm.score')"
              prop="score"
            >
              <el-input
                v-model="dataForm.score"
                :readonly="true"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 评论内容 -->
        <el-form-item
          :label="$t('productComm.content')"
          prop="userName"
        >
          <el-input
            v-model="dataForm.content"
            type="textarea"
            :rows="4"
            :readonly="true"
            :disabled="!isEdit"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <!-- 掌柜回复 -->
        <el-form-item
          :label="$t('productComm.replyContent')"
          type="textarea"
          prop="userName"
        >
          <el-input
            v-model="dataForm.replyContent"
            type="textarea"
            :rows="4"
            :readonly="!isEdit"
            :disabled="!isEdit"
            maxlength="480"
            show-word-limit
          />
        </el-form-item>
        <!-- 评论图片 -->
        <el-form-item
          :label="$t('productComm.pics')"
          prop="userName"
        >
          <div v-if="!pics">
            {{ $t("productComm.no") }}
          </div>
          <imgs-upload
            v-model="pics"
            :disabled="true"
            :modal="false"
            :prompt="false"
          />
        </el-form-item>
        <!-- 是否匿名 -->
        <el-form-item
          :label="$t('productComm.isAnonymous')"
          prop="isAnonymous"
        >
          <el-radio-group
            v-model="dataForm.isAnonymous"
            :disabled="true"
          >
            <el-radio :label="1">
              {{ $t("publics.yes") }}
            </el-radio>
            <el-radio :label="0">
              {{ $t("publics.no") }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div v-if="isEdit">
        <el-form-item
          :label="$t('productComm.replyContent')"
          type="textarea"
          prop="replyContent"
          class="textarea-right"
        >
          <el-input
            v-model="dataForm.replyContent"
            type="textarea"
            :rows="5"
            :readonly="!isEdit"
            :disabled="!isEdit"
            maxlength="480"
            show-word-limit
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn"
          @click="visible = false"
        >
          {{ $t("crud.filter.cancelBtn") }}
        </div>
        <div
          v-if="isEdit"
          class="default-btn primary-btn"
          @click="onSubmit()"
        >
          {{ $t("crud.filter.submitBtn") }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { validNoEmptySpace } from '@/utils/validate'
const emit = defineEmits(['refreshDataList'])

const validateReplyContent = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}
const dataRule = ref({
  replyContent: [{ validator: validateReplyContent }]
})

const dataFormRef = ref(null)
const pics = ref('')
const isEdit = ref(false)
const visible = ref(false)
const dataForm = ref({
  prodCommId: null,
  prodId: null,
  orderItemId: null,
  userId: null,
  content: null,
  replyContent: null,
  recTime: null,
  replyTime: null,
  replySts: null,
  postip: null,
  score: null,
  usefulCounts: null,
  photoJson: null,
  isAnonymous: null,
  status: 1
})
const init = (prodCommId, isEditParam) => {
  isEdit.value = isEditParam
  dataForm.value.prodCommId = prodCommId || 0
  visible.value = true
  pics.value = ''
  nextTick(() => {
    dataFormRef.value?.resetFields()
    if (dataForm.value.prodCommId) {
      http({
        url: http.adornUrl('/prod/prodComm/info/' + dataForm.value.prodCommId),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        dataForm.value = data
        let imgs = ''
        dataForm.value.picsArray?.forEach(element => {
          imgs = imgs + element + ','
        })
        pics.value = imgs.substr(0, imgs.length - 1)
      })
    }
  })
}
defineExpose({ init })

// 表单提交
const onSubmit = () => {
  if (!dataForm.value.replyContent) {
    ElMessage({
      message: $t('product.content'),
      type: 'error',
      duration: 1500
    })
    return
  }
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      http({
        url: http.adornUrl('/prod/prodComm'),
        method: dataForm.value.prodCommId ? 'put' : 'post',
        data: http.adornData(dataForm.value)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }
  })
}

</script>
<style scoped>
.textarea-right :deep(.el-textarea__inner){
  padding-right: 50px;
}
.textarea-right :deep(.el-textarea .el-input__count) {
  right: 21px;
}
.form-en :deep(.el-form-item__label) {
  word-break:break-word;
}
</style>
