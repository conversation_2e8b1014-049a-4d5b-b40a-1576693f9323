<template>
  <el-dialog
    v-model="visible"
    :title=" $t('text.editBtn')"
    :close-on-click-modal="false"
    :before-close="onCancel"
  >
    <el-form
      ref="dataFormRef"
      :rules="rules"
      :model="dataForm"
      label-position="left"
      label-width="auto"
      @submit.prevent
    >
      <!-- 订单id -->
      <el-form-item
        :label="$t('order.number')+'：'"
        prop="orderNumber"
      >
        <div>{{ dataForm.orderNumber || '' }}</div>
      </el-form-item>
      <!-- 发票类型 1.电子普通发票 -->
      <el-form-item
        :label="$t('order.invoiceType')+'：'"
        prop="invoiceType"
      >
        <div v-if="dataForm.invoiceType === 1">
          {{ $t('order.normalInvoiceType') }}
        </div>
      </el-form-item>
      <!-- 抬头类型 1.单位 2.个人 -->
      <el-form-item
        :label="$t('order.headerType')+'：'"
        prop="headerType"
      >
        <div>{{ ['',$t('order.unit'),$t('order.personal')][dataForm.headerType] }}</div>
      </el-form-item>
      <!-- 抬头名称 -->
      <el-form-item
        v-if="dataForm.headerName"
        :label="$t('order.headerName')+'：'"
        prop="headerName"
      >
        <div>{{ dataForm.headerName || '' }}</div>
      </el-form-item>
      <!-- 发票税号 -->
      <el-form-item
        v-if="dataForm.invoiceTaxNumber && dataForm.headerType === 1"
        :label="$t('order.invoiceTaxNumber')+'：'"
        prop="invoiceTaxNumber"
      >
        <div>{{ dataForm.invoiceTaxNumber || '' }}</div>
      </el-form-item>
      <!-- 订单状态 -->
      <el-form-item
        :label="$t('group.orderStatus')+'：'"
        prop="orderStatus"
      >
        <span v-if="dataForm.orderStatus === 1">{{ $t('order.pendingPayment') }}</span>
        <span v-if="dataForm.orderStatus === 2">{{ $t('order.toBeShipped') }}</span>
        <span v-if="dataForm.orderStatus === 3">{{ $t('order.pendingReceipt') }}</span>
        <span v-if="dataForm.orderStatus === 4">{{ $t('order.toBeEvaluated') }}</span>
        <span v-if="dataForm.orderStatus === 5">{{ $t('order.success') }}</span>
        <span v-if="dataForm.orderStatus === 6">{{ $t('order.fail') }}</span>
        <span v-if="dataForm.orderStatus === 7">{{ $t('group.waitGroup') }}</span>
      </el-form-item>
      <!-- 发票状态 1.申请中 2.已开票 -->
      <el-form-item
        :label="$t('order.invoiceStatus')+'：'"
        prop="invoiceState"
      >
        <div>{{ ['',$t('order.applicationInProgress'),$t('order.invoiceIssued'),$t('order.fail')][dataForm.invoiceState] }}</div>
      </el-form-item>
      <!-- 发票上传： -->
      <el-form-item
        v-if="isAuth('order:orderInvoice:upload')"
        :label="$t('order.invoiceUpload')+'：'"
      >
        <div>
          <el-link
            v-if="fileInfo.filePath"
            target="_blank"
            rel="noopener noreferrer"
            :underline="false"
            :href="checkFileUrl(fileInfo.filePath)"
          >
            {{ fileInfo.fileName }}
          </el-link>
          <el-upload
            ref="uploadRef"
            v-model:file-list="fileList"
            accept=".pdf"
            action=""
            :http-request="handleFileChange"
            :headers="{Authorization: cookie.get('bbcAuthorization_vs'),locale:lang}"
            :multiple="false"
            :auto-upload="false"
            :limit="1"
            :before-upload="beforeUpload"
          >
            <template #trigger>
              <el-button type="primary">
                {{ $t('order.SelectFile') }}
              </el-button>
            </template>
            <el-button
              style="margin-left: 10px;"
              type="success"
              @click="onUploadConfirm"
            >
              {{ $t('pictureManager.confirmUpload') }}
              <el-tooltip
                :content="$t('pictureManager.uploadPDF')"
                placement="top"
                effect="light"
              >
                <el-icon
                  class="el-icon-question el-icon--right"
                >
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-button>
          </el-upload>
        </div>
      </el-form-item>
      <!-- 申请时间 -->
      <el-form-item
        :label="$t('order.applicationTime')+'：'"
        prop="applicationTime"
      >
        <div>{{ dataForm.applicationTime }}</div>
      </el-form-item>
      <!-- 上传时间 -->
      <el-form-item
        v-if="dataForm.uploadTime"
        :label="$t('order.uploadTime')+'：'"
        prop="uploadTime"
      >
        <div>{{ dataForm.uploadTime }}</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn"
          @click="onCancel"
        >
          {{ $t("crud.filter.cancelBtn") }}
        </div>
        <div
          v-if="isAuth('order:orderInvoice:commit')"
          class="default-btn primary-btn"
          @click="beforeSubmit()"
        >
          {{ $t("crud.filter.submitBtn") }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import cookie from 'vue-cookies'
import http from '@/utils/http.js'
import { isAuth } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
const emit = defineEmits(['refreshDataList'])
const lang = reactive(localStorage.getItem('bbcLang') || 'zh_CN')
const rules = ref(null)

const fileInfo = ref({})
const visible = ref(false)
const dataForm = ref({
  orderInvoiceId: 0,
  orderNumber: null,
  shopId: null,
  invoiceType: null,
  headerType: null,
  headerName: null,
  invoiceTaxNumber: null,
  invoiceState: null,
  fileId: null,
  applicationTime: null,
  uploadTime: null,
  ossList: null,
  attachFile: null
})
const init = (orderInvoiceId) => {
  visible.value = true
  nextTick(() => {
    resetDataFrom()
    if (!orderInvoiceId) {
      return
    }
    dataForm.value.orderInvoiceId = orderInvoiceId || 0
    http({
      url: http.adornUrl(`/m/orderInvoice/info/${orderInvoiceId}`),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      dataForm.value = data
      // 请求文件
      if (dataForm.value.fileId) {
        http({
          url: http.adornUrl('/admin/file/get_file_by_id'),
          method: 'get',
          params: http.adornParams({
            fileId: dataForm.value.fileId
          })
        }).then(({ data }) => {
          fileInfo.value = data
        })
      }
    })
  })
}
defineExpose({ init })

/**
 * 清空数据
 */
const resetDataFrom = () => {
  dataForm.value = {
    orderInvoiceId: 0,
    orderNumber: null,
    shopId: null,
    invoiceType: null,
    headerType: null,
    headerName: null,
    invoiceTaxNumber: null,
    invoiceState: null,
    fileId: null,
    applicationTime: null,
    uploadTime: null,
    ossList: null,
    attachFile: null
  }
  fileInfo.value = {}
}

/**
 * 查看是否已经更改过申请信息
 */
const beforeSubmit = () => {
  http({
    url: http.adornUrl('/m/orderInvoice/is_change'),
    method: 'get',
    params: http.adornParams({
      orderInvoiceId: dataForm.value.orderInvoiceId,
      applicationTime: dataForm.value.applicationTime
    })
  }).then(({ data }) => {
    if (data === true) {
      ElMessageBox.confirm($t('order.uploadInvoiceTip2'), $t('text.tips'), {
        confirmButtonText: $t('crud.filter.submitBtn'),
        cancelButtonText: $t('crud.filter.cancelBtn'),
        type: 'warning'
      }).then(() => {
        onSubmit()
      })
    } else {
      onSubmit()
    }
  })
}

const dataFormRef = ref(null)
/**
 * 表单提交
 */
const onSubmit = () => {
  dataFormRef.value?.validate(valid => {
    if (!valid) {
      return
    }
    if (!fileInfo.value.fileId) {
      ElMessage({
        message: $t('order.uploadInvoiceTip4'),
        type: 'info',
        duration: 1000
      })
      return
    }
    http({
      url: http.adornUrl('/m/orderInvoice'),
      method: 'put',
      data: dataForm.value
    }).then(() => {
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1000,
        onClose: () => {
          visible.value = false
          emit('refreshDataList')
        }
      })
    })
  })
}

const uploadRef = ref(null)
/**
 * 上传图片
 */
const onUploadConfirm = () => {
  if (fileList.value?.length < 1) {
    return ElMessage.error($t('order.uploadInvoiceTip'))
  }
  if (dataForm.value.orderStatus < 4) {
    ElMessageBox.confirm($t('order.uploadInvoiceTip1'), $t('chat.tips'), {
      confirmButtonText: $t('chat.confirm'),
      cancelButtonText: $t('chat.cancel'),
      type: 'warning'
    }).then(() => {
      uploadRef.value?.submit()
    })
  } else {
    uploadRef.value?.submit()
  }
}

const fileList = ref([])

/**
 * 上传前检查合法性
 * @param file
 * @returns {boolean}
 */
const beforeUpload = (file) => {
  const isImage = file.type === 'application/pdf'
  if (!isImage) {
    ElMessage.error($t('pictureManager.pdfTypeErrorTips'))
    return false
  }
  return true
}
const handleFileChange = (e) => {
  const file = e.file
  http({
    url: http.adornUrl('/admin/file/getPreSignUrl'),
    method: 'get',
    params: http.adornParams({
      fileName: file.name,
      isImFile: false
    })
  }).then(({ data }) => {
    uploadFile(data.preSignUrl, file).then(() => {
      http({
        url: http.adornUrl('/admin/file/uploadSuccess'),
        method: 'put',
        data: [{
          fileId: data.fileId,
          attachFileGroupId: 0,
          fileSize: file.size
        }]
      }).then(() => {
        if (!data) {
          return ElMessage.error($t('pictureManager.pdfUploadErrorTips'))
        }
        fileInfo.value = {
          filePath: data.filePath,
          fileName: file.name,
          fileId: data.fileId
        }
        dataForm.value.fileId = data.fileId
        uploadRef.value.uploadFiles = []
        fileList.value = []
      }).catch((err) => {
        ElMessage($t('biz.imgbox.requestError'), true)
        throw err
      })
    })
  })
}
/**
 * 取消事件
 */
const onCancel = () => {
  visible.value = false
  fileList.value = []
}
</script>

<style scoped lang="scss">
  :deep(.el-form-item__content) {
    word-break: break-all;
  }
  :deep(.el-upload-list__item-file-name) {
    white-space: break-spaces;
    text-align: left;
    overflow-wrap: break-word;
  }
</style>
