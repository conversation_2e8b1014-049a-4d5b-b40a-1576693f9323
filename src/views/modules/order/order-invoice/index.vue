<template>
  <!-- 发票管理 -->
  <div class="page-order-invoice">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="orderNumber"
            :label="$t('order.number')+':'"
          >
            <el-input
              v-model="searchForm.orderNumber"
              type="text"
              clearable
              :placeholder="$t('order.number')"
            />
          </el-form-item>
          <el-form-item
            prop="invoiceState"
            :label="$t('order.invoiceStatus')+':'"
          >
            <el-select
              v-model="searchForm.invoiceState"
              clearable
              :placeholder="$t('order.invoiceStatus')"
            >
              <el-option
                v-for="item in invoiceStates"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="resetForm()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <div class="main-container">
      <!-- 表格 -->
      <div class="table-con invoice-table">
        <el-table
          ref="InvoiceTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          style="width: 100%"
        >
          <!-- 订单id -->
          <el-table-column
            :label="$t('order.number')"
            prop="orderNumber"
          >
            <template #default="{row}">
              <span>{{ row.orderNumber }}</span>
            </template>
          </el-table-column>
          <!-- 抬头类型 1.单位 2.个人 -->
          <el-table-column
            :label="$t('order.headerType')"
            prop="headerType"
          >
            <template #default="{row}">
              <span>{{ ['',$t('order.unit'),$t('order.personal')][row.headerType] }}</span>
            </template>
          </el-table-column>
          <!-- 发票状态 1.申请中 2.已开票 -->
          <el-table-column
            :label="$t('order.invoiceStatus')"
            prop="invoiceState"
          >
            <template #default="{row}">
              <span>{{ ['',$t('order.applicationInProgress'),$t('order.invoiceIssued'),$t('order.fail')][row.invoiceState] }}</span>
            </template>
          </el-table-column>
          <!-- 订单状态 -->
          <el-table-column
            :label="$t('order.status')"
            prop="orderStatus"
          >
            <template #default="scope">
              <span v-if="scope.row.orderStatus === 1">{{ $t('order.pendingPayment') }}</span>
              <span v-if="scope.row.orderStatus === 2">{{ $t('order.toBeShipped') }}</span>
              <span v-if="scope.row.orderStatus === 3">{{ $t('order.pendingReceipt') }}</span>
              <span v-if="scope.row.orderStatus === 4">{{ $t('order.toBeEvaluated') }}</span>
              <span v-if="scope.row.orderStatus === 5">{{ $t('order.success') }}</span>
              <span v-if="scope.row.orderStatus === 6">{{ $t('order.fail') }}</span>
              <span v-if="scope.row.orderStatus === 7">{{ $t('group.waitGroup') }}</span>
            </template>
          </el-table-column>
          <!-- 申请时间 -->
          <el-table-column
            :label="$t('order.applicationTime')"
            prop="applicationTime"
          >
            <template #default="{row}">
              <span>{{ row.applicationTime || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 上传时间 -->
          <el-table-column
            :label="$t('order.uploadTime')"
            prop="uploadTime"
          >
            <template #default="{row}">
              <span>{{ row.uploadTime || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('crud.menu')"
            fixed="right"
            align="center"
            width="200"
          >
            <template #default="{row}">
              <el-button
                type="text"
                @click="viewOrderHandle(row.orderNumber)"
              >
                {{ $t('order.viewOrder') }}
              </el-button>
              <el-button
                v-if="isAuth('order:orderInvoice:update')"
                type="text"
                @click="onAddOrUpdate(row.orderInvoiceId)"
              >
                {{ $t('text.editBtn') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>

    <!-- 弹窗, 新增 / 修改 -->
    <AddOrUpdate
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'
import AddOrUpdate from './add-or-update.vue'

const invoiceStates = [
  {
    label: $t('order.applicationInProgress'),
    value: 1
  },
  {
    label: $t('order.invoiceIssued'),
    value: 2
  },
  {
    label: $t('order.fail'),
    value: 3
  }
]

onMounted(() => {
  getDataList()
})

let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = ref({})
// 获取数据列表
const getDataList = (pageParam, newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm.value))
  }
  http({
    url: http.adornUrl('/m/orderInvoice/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam ? pageParam.currentPage : page.currentPage,
          size: pageParam ? pageParam.pageSize : page.pageSize
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 条件查询
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}

const router = useRouter()
/**
 * 查看订单
 */
const viewOrderHandle = (id) => {
  router.push({
    path: '/order/order-info/index',
    query: {
      orderNumber: id
    }
  })
}

const addOrUpdateVisible = ref(false)// 编辑弹窗显隐
const addOrUpdateRef = ref(null)
// 新增 / 修改
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id)
  })
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}

// 重置
const resetForm = () => {
  searchForm.value = {}
}
// 刷新回调用
const refreshChange = () => {
  page.current = 1
  getDataList(page)
}

</script>
