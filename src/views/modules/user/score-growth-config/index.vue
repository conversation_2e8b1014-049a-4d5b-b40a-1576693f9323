<template>
  <!-- 成长值设置 -->
  <div class="mod-marketing-distribution page-score-growth-config">
    <!-- 成长值设置 -->
    <div
      v-if="!isLoad"
      class="distribution-recruit-set gray-box top-redius border-bottom-gray"
    >
      <div class="title">
        {{ $t('user.growthSetting') }}
      </div>
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="growthGainFormRef"
        label-width="180px"
        class="set-form"
        label-position="left"
        :rules="growthGainRule"
        :model="growthGain"
        @submit.prevent
      >
        <el-form-item
          :label="$t('user.growthSwitch')"
          style="width:440px"
          prop="shopGrowthSwitch"
        >
          <el-switch v-model="growthGain.shopGrowthSwitch" />
        </el-form-item>
        <el-form-item
          v-if="growthGain.shopGrowthSwitch"
          :label="$t('user.perPurchase')"
          style="width:440px"
          prop="buyPrice"
        >
          <el-input
            v-model.Number="growthGain.buyPrice"
            @change="setShopGetScore"
          >
            <template #append>
              {{ $t('coupon.yuan') }}{{ $t('user.getOneGrowth') }}
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="growthGain.shopGrowthSwitch"
          :label="$t('user.completedOrderGet')"
          style="width:440px"
          prop="buyOrder"
        >
          <el-input
            v-model.Number="growthGain.buyOrder"
            @change="setShopComplete"
          >
            <template #append>
              {{ $t('user.growth') }}
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <div
        v-if="isAuth('brand:brandGrowthConfig:update')"
        class="default-btn"
        @click="onSubmit(1)"
      >
        {{ $t('user.save') }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { isAuth } from '@/utils/index.js'

const emit = defineEmits(['refreshDataList'])

const growthGain = ref({
  buyOrder: '',
  buyPrice: '',
  shopGrowthSwitch: ''
})
let id = 0
const growthGainRule = reactive({
  buyPrice: [
    { required: true, message: $t('user.consumptionGetGrowthCannotEmpty'), trigger: 'blur' }
  ],
  buyOrder: [
    { required: true, message: $t('user.orderAcquisitionCannotEmpty'), trigger: 'blur' }
  ]
})

const isLoad = ref(true)
onMounted(() => {
  getData()
})

// 获取数据
const getData = (type = 0) => {
  nextTick(() => {
    http({
      url: http.adornUrl('/brandGrowthConfig'),
      method: 'get',
      params: http.adornParams({ type })
    }).then(({ data }) => {
      if (data) {
        growthGain.value = {
          buyOrder: data.growthGainDTO.buyOrder || 1,
          buyPrice: data.growthGainDTO.buyPrice || 1,
          shopGrowthSwitch: data.growthGainDTO.shopGrowthSwitch
        }
        id = data.id
        isLoad.value = false
      }
    })
  })
}

const growthGainFormRef = ref(null)
let isSubmit = false
const onSubmit = (value) => {
  let data = ''
  switch (value) {
    case 1:
      data = {
        growthGainDTO: {
          buyOrder: Number(growthGain.value.buyOrder),
          buyPrice: Number(growthGain.value.buyPrice),
          shopGrowthSwitch: growthGain.value.shopGrowthSwitch
        }
      }
      break
  }
  growthGainFormRef.value.validate((valid) => {
    if (valid) {
      if (isSubmit) {
        return false
      }
      isSubmit = true
      http({
        url: http.adornUrl('/brandGrowthConfig/saveOrUpdate'),
        method: 'put',
        data: http.adornData({
          ...data,
          id
        })
      }).then(() => {
        ElMessage({
          message: $t('user.succeeded'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            emit('refreshDataList')
            isSubmit = false
          }
        })
      }).catch(() => {
        isSubmit = false
      })
    }
  })
}

const setShopGetScore = () => {
  const num = Math.round(growthGain.value.buyPrice) || 1
  growthGain.value.buyPrice = num < 1 ? 1 : num
  if (num >= 100000000) {
    growthGain.value.buyPrice = 100000000
  }
}

const setShopComplete = () => {
  const num = Math.round(growthGain.value.buyOrder) || 1
  growthGain.value.buyOrder = num < 1 ? 1 : num
  if (num >= 100000000) {
    growthGain.value.buyOrder = 100000000
  }
}

</script>
<style lang="scss" scoped>
@use "index";
</style>
