.page-user-label {
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .btn-bar {
    display: flex;
    align-items: end;

    .font-line {
      margin-left: 10px;
      padding-bottom: 5px;
    }
  }

  .tabs {
    .label-container {
      background-color:#f2f2f6 ;
      padding: 10px;
      margin-top:10px ;
      border-radius: 10px;
      .label-tips {
        margin: 15px 0 10px 0;
        .label-type{
          font-size: 16px;
          font-weight: bold;
        }
        .label-tips {
          font-size: 14px;
          color: #999;
          margin-left: 15px;
        }
      }
      .label-content {
        display: flex;
        flex-wrap: wrap;
        .label-item {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 215px;
          height: 120px;
          padding: 12px;
          font-size: 12px;
          color: #666;
          background: white;
          border: 1px solid #E8E9EC;
          border-radius: 4px;
          margin: 0 12px 12px 0;
          .label-name {
            font-size: 14px;
            color: #000;
          }
          .op-btn-wrapper {
            text-align: right;
            & span {
              color: #155bd4;
              cursor: pointer;
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
}
.no-data{
  line-height: 24px;
  color: #999;
  text-align: center;
  margin:auto
}
