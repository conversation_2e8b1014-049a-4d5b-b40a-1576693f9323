.component-user-label-edit {
  .edit-label-content {
    font-size: 12px;
    overflow: auto;
    /* 行内通用 */
    .currency-line {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .currency-line .title {
      min-width: 130px;
      font-size: 12px;
      font-weight: normal;
      text-align: right;
      padding: 0 15px 0 0;
      margin: 0;
    }

    .currency-line .must-term {
      color: #F56C6C;
      margin-right: 4px;
    }

    .currency-line .el-checkbox {
      width: 180px;
      margin-right: 10px;
    }

    .currency-line .el-radio__label, .currency-line .el-checkbox__label, .currency-line .el-input, .currency-line .el-range-input {
      font-size: 12px;
    }

    .currency-line .module-content {
      padding: 0 15px;
    }

    .currency-line .module-content .option-line {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }

    .currency-line .module-content .option-line .unit-wrapper {
      display: flex;
      align-items: center;
    }

    .currency-line .module-content .option-line .first-iterm {
      margin-left: 25px;
    }

    .currency-line .module-content .option-line .interval-line {
      padding: 0 10px;
    }

    .currency-line .module-content .option-line .unit {
      display: inline-block;
      width: 50px;
      height: 40px;
      line-height: 40px;
      background: #efefef;
      border: 1px solid #dcdfe6;
      border-left: none;
      text-align: center;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }

    .currency-line .module-content .el-select {
      width: 150px;
      height: 38px;
      margin-right: 15px;
    }

    .currency-line .module-content .cur-inp {
      width: 100%;
    }

    .currency-line .module-content .cur-inp .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    /* /行内通用 */
    .label-condition-title-line {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 700;
      margin-left: 10px;
      margin-bottom: 10px;
    }

    .label-condition-title-line .tips {
      font-size: 12px;
      font-weight: normal;
      color: #595961;
      padding-left: 20px;
    }

    .label-name-line input {
      width: 300px;
      height: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 3px;
    }
  }
}

@media (max-width: 1880px) {
  .component-user-label-edit .el-dialog {
    width: 60%;
  }
}

@media (max-width: 1650px) {
  .component-user-label-edit .el-dialog {
    width: 70%;
  }
}

@media (max-width: 1380px) {
  .component-user-label-edit .el-dialog {
    width: 80%;
  }
}
