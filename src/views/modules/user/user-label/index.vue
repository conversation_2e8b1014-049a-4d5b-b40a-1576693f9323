<template>
  <!-- 会员标签 -->
  <div class="mod-member-label page-user-label">
    <div class="title">
      {{ $t('user.allLabels') }}
    </div>
    <div class="tabs">
      <el-tabs>
        <!-- 条件标签 -->
        <el-tab-pane :label="$t('user.conditionLabel')">
          <div class="btn-bar">
            <div
              v-if="isAuth('user:userTag:save')"
              class="default-btn primary-btn"
              @click="showEditFrame('',1)"
            >
              {{ $t('user.addLabel') }}
            </div>
            <div
              v-if="isAuth('user:userTag:edit')"
              class="default-btn"
              @click="refreshLabel()"
            >
              {{ $t('user.updateLabelData') }}
            </div>
            <div class="font-line">
              {{ $t('user.updateTime') }}：{{ (customLabelList[0]?.statisticUpdateTime) ? customLabelList[0].statisticUpdateTime : $t('user.noUpdate') }}
            </div>
          </div>
          <!-- 不同标签内容 -->
          <div class="label-container">
            <!-- 自定义标签 -->
            <div class="label-tips">
              <span class="label-type">{{ $t('user.customConditionLabel') }}</span>
              <span class="label-tips">{{ $t('user.customConditionLabelTips') }}</span>
            </div>
            <div
              v-if="customLabelList.length > 0"
              class="label-content"
            >
              <div
                v-for="(item, index) in customLabelList"
                :key="index"
                class="label-item"
              >
                <div class="label-name">
                  {{ item.tagName }}
                </div>
                <div class="font-line">
                  {{ $t('user.peopleNum') }}：{{ item.userNum }}
                </div>
                <div class="font-line">
                  {{ $t('user.creationTime') }}：{{ item.createTime }}
                </div>
                <div class="op-btn-wrapper">
                  <span
                    v-if="isAuth('user:userTag:update')"
                    @click="showEditFrame(item.userTagId,1)"
                  >{{ $t('user.editBtn') }}</span>
                  <span
                    v-if="isAuth('user:userTag:delete')"
                    @click="deleteLabel(item.userTagId)"
                  >{{ $t('user.deleteBtn') }}</span>
                </div>
              </div>
            </div>
            <el-empty
              v-else
              class="no-data"
              :description="$t('shop.noData')"
            />
          </div>
        </el-tab-pane>
        <!-- 手动标签 -->
        <el-tab-pane :label="$t('user.manualLabel')">
          <div
            v-if="isAuth('user:userTag:save')"
            class="default-btn primary-btn"
            @click="showEditFrame('',0)"
          >
            {{ $t('user.addLabel') }}
          </div>
          <!-- 标签内容 -->
          <div class="label-container">
            <!-- 手动标签 -->
            <div class="label-tips">
              <span class="label-type">{{ $t('user.manualLabel') }}</span>
            </div>
            <div
              v-if="manualLabelList.length > 0"
              class="label-content"
            >
              <div
                v-for="(item, index) in manualLabelList"
                :key="index"
                class="label-item"
              >
                <div class="label-name">
                  {{ item.tagName }}
                </div>
                <div class="font-line">
                  {{ $t('user.peopleNum') }}：{{ item.userNum }}
                </div>
                <div class="font-line">
                  {{ $t('user.creationTime') }}：{{ item.createTime }}
                </div>
                <div class="op-btn-wrapper">
                  <span
                    v-if="isAuth('user:userTag:update')"
                    @click="showEditFrame(item.userTagId,0)"
                  >{{ $t('user.editBtn') }}</span>
                  <span
                    v-if="isAuth('user:userTag:delete')"
                    @click="deleteLabel(item.userTagId)"
                  >{{ $t('user.deleteBtn') }}</span>
                </div>
              </div>
            </div>
            <el-empty
              v-else
              class="no-data"
              :description="$t('shop.noData')"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 新增/编辑标签 -->
    <user-label-edit
      v-if="isShowEditFrame"
      ref="addOrEditRef"
      :tag-category="1"
      @refresh-label-list="addOrEditSucc"
    />
  </div>
</template>

<script setup>
import userLabelEdit from './components/user-label-edit/index.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils/index.js'

onMounted(() => {
  getCustomLabelList()
})

const isShowEditFrame = ref(false)
const addOrEditRef = ref(null)
// 新增弹窗
const showEditFrame = (userTagId, tagType) => {
  isShowEditFrame.value = true
  nextTick(() => {
    addOrEditRef.value?.init(userTagId, tagType)
  })
}

const customLabelList = ref([])
const manualLabelList = ref([])
// 获取自定义标签列表
const getCustomLabelList = () => {
  http({
    url: http.adornUrl('/user/userTag/getTagList'),
    method: 'get',
    params: http.adornParams({
      tagCategory: 1
    })
  }).then(({ data }) => {
    const customLabelListPar = []
    const manualLabelListPar = []
    for (let i = 0; i < data.length; i++) {
      if (data[i].tagType === 1) {
        customLabelListPar.push(data[i])
      } else if (data[i].tagType === 0) {
        manualLabelListPar.push(data[i])
      }
    }
    customLabelList.value = customLabelListPar
    manualLabelList.value = manualLabelListPar
  })
}

const refreshLabel = () => {
  if (!customLabelList.value.length) {
    ElMessage.warning($t('user.noDataAvailablePleaseAddTag'))
    return
  }
  http({
    url: http.adornUrl('/user/userTag/batchRefresh'),
    method: 'GET',
    data: http.adornData({})
  }).then(() => {
    getCustomLabelList()
    ElMessage.success($t('user.updateSucceeded'))
  })
}

const deleteLabel = (userTagId) => {
  ElMessageBox.confirm($t('user.isDelete'), $t('resource.tips'), {
    confirmButtonText: $t('resource.confirm'),
    cancelButtonText: $t('resource.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/user/userTag/' + userTagId),
      method: 'DELETE',
      data: http.adornData()
    }).then(() => {
      getCustomLabelList()
      ElMessage.success($t('user.deletionSucceeded'))
    })
  })
}

const addOrEditSucc = (data) => {
  isShowEditFrame.value = false
  if (data) {
    getCustomLabelList()
  }
}

</script>

<style lang="scss" scoped>
@use "index";
</style>
