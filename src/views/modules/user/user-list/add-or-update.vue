<template>
  <!-- 会员编辑弹窗 -->
  <div>
    <el-dialog
      v-model="visible"
      :title="
        !dataForm.userId
          ? $t('user.add')
          : $t('user.details')
      "
      :close-on-click-modal="false"
      class="user-update-dialog"
      width="70%"
      @closed="handleDialogClose"
    >
      <!-- 用户信息 -->
      <div class="user-info-item">
        <el-divider
          class="info-title"
          content-position="left"
        >
          <h3>{{ $t('user.userInfo') }}</h3>
        </el-divider>
        <div class="info-content">
          <div class="base-info" />
          <div class="user-status" />
        </div>
      </div>
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="dataFormRef"
        :model="dataForm"
        :rules="dataRule"
        :label-width="
          $t('language') === 'English' ? '130px' : '80px'"
        @submit.prevent
        @keyup.enter="onSubmit()"
      >
        <el-container style="margin-top:20px">
          <el-aside width="210px">
            <el-form-item
              :label="$t('publics.profilePicture')"
              prop="pic"
            >
              <img
                v-if="!dataForm.pic"
                src="@/assets/img/userImg.jpg"
                style="width: 130px; height: 130px"
                alt
              >
              <img
                v-else
                :src="checkFileUrl(dataForm.pic)"
                class="image"
                style="width: 130px; height: 130px"
                alt
              >
            </el-form-item>
          </el-aside>
          <el-container>
            <el-main>
              <el-row>
                <el-col
                  :span="7"
                  justify="start"
                >
                  <el-form-item
                    :label="$t('users.name') + ':'"
                    prop="nickName"
                    size="small"
                  >
                    <span v-if="nameVisible">{{ dataForm.nickName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    :label="$t('publics.status') + ':'"
                    size="small"
                    prop="status"
                  >
                    <span v-if="dataForm.status == 0">{{
                      $t("publics.disable")
                    }}</span>
                    <span v-if="dataForm.status == 1">{{
                      $t("publics.normal")
                    }}</span>
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="type===1"
                  :span="9"
                >
                  <el-form-item
                    :label="$t('user.openingTime') + ':'"
                    prop="userRegtime"
                    size="small"
                  >
                    <span>{{ dataForm.registTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-main>
            <el-footer>
              <el-row
                :gutter="10"
              >
                <el-col
                  v-if="isAuth('brand:coupon:send')"
                  :span="$t('language') == '简体中文' ? 4 : 5"
                >
                  <div
                    class="default-btn primary-btn"
                    @click="updateCoupon()"
                  >
                    {{ $t("user.sendCoupons") }}
                  </div>
                </el-col>
                <el-col
                  v-if="isAuth('user:userList:addLabel')"
                  :span="$t('language') == '简体中文' ? 5 : 7"
                >
                  <div
                    class="default-btn primary-btn"
                    @click="updateTags()"
                  >
                    {{ $t("user.tagging") }}
                  </div>
                </el-col>
              </el-row>
            </el-footer>
          </el-container>
        </el-container>
        <el-form-item
          v-if="type===0"
          :label="$t('user.customerLabel')"
          prop="userTagParam"
        >
          <div v-if="customerTags&&customerTags.length > 0">
            <el-tag
              v-for="tag in customerTags"
              :key="tag.tagId"
              :closable="tag.tagType === 0 && isAuth('user:userList:addLabel')"
              :disable-transitions="false"
              @close="handleClose(tag)"
            >
              {{ tag.tagName }}
            </el-tag>
          </div>
          <span v-else>{{ $t('order.no') }}</span>
        </el-form-item>
        <el-form-item
          v-if="type===1"
          :label="$t('user.memberTags')"
          prop="memberTags"
        >
          <div
            v-if="memberTags&&memberTags.length > 0"
          >
            <el-tag
              v-for="tag in memberTags"
              :key="tag.tagId"
              :closable="tag.tagType === 0 && isAuth('user:userList:addLabel')"
              :disable-transitions="false"
              @close="handleClose(tag)"
            >
              {{ tag.tagName }}
            </el-tag>
          </div>
          <span v-else>{{ $t('order.no') }}</span>
        </el-form-item>
        <!-- 账户资产 -->
        <div v-if="type===1">
          <el-divider content-position="left">
            <h3>{{ $t("user.accountAssets") }}</h3>
          </el-divider>
          <el-container>
            <el-main>
              <div>
                <h4>
                  {{ $t("user.coupons") }}
                  <el-popover
                    placement="top"
                    width="200"
                    trigger="hover"
                    :content="$t('user.couponTip3')"
                  >
                    <template #reference>
                      <el-icon><QuestionFilled /></el-icon>
                    </template>
                  </el-popover>
                </h4>
                <br>
                <div>
                  <span>{{ $t("user.notUsed") }}：{{
                    dataForm.couponUserParam.couponUsableNums || 0
                  }}&nbsp;&nbsp;{{ $t("marketing.piece") }} </span>
                  <span style="margin-left: 60px">{{ $t("user.used") }}：{{
                    dataForm.couponUserParam.couponUsedNums || 0
                  }}&nbsp;&nbsp;{{ $t("marketing.piece") }} </span>
                  <span style="margin-left: 60px">{{ $t("user.invalid") }}：{{
                    dataForm.couponUserParam.couponExpiredNums || 0
                  }}&nbsp;&nbsp;{{ $t("marketing.piece") }} </span>
                </div>
                <br>
              </div>
            </el-main>
          </el-container>
        </div>
        <el-divider
          v-if="type===1"
          content-position="left"
        >
          <h3>{{ $t("user.otherInfo") }}</h3>
        </el-divider>
      </el-form>
      <div>
        <el-tabs>
          <el-tab-pane :label="$t('user.tradeDetails')">
            <trade-detail ref="tradeDetailRef" />
          </el-tab-pane>
          <el-tab-pane
            v-if="type===1"
            :label="$t('user.couponDetails')"
          >
            <coupon-detail ref="couponDetailRef" />
          </el-tab-pane>
          <el-tab-pane
            v-if="type===1"
            :label="$t('user.growthLog')"
          >
            <growth-detail ref="growthDetailRef" />
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div
            class="default-btn primary-btn"
            @click="onSubmit()"
          >
            {{ $t("user.confirm") }}
          </div>
        </div>
      </template>
    </el-dialog>
    <!-- 送优惠券弹窗 -->
    <update-user-coupon
      v-if="updateCouponVisible"
      ref="updateCouponRef"
      :get-way="1"
      @refresh-data-list="refreshChange"
    />
    <!-- 打标签弹窗 -->
    <update-user-tags
      v-if="updateTagsVisible"
      ref="updateTagsRef"
      :tag-category="type"
      :type="type"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import UpdateUserTags from './components/update-user-tags/index.vue'
import UpdateUserCoupon from './components/update-user-coupon/index.vue'
import TradeDetail from './components/trade-detail/index.vue'
import CouponDetail from './components/coupon-detail/index.vue'
import GrowthDetail from './components/growth-detail/index.vue'
import { isAuth, checkFileUrl } from '@/utils/index.js'

const emit = defineEmits(['refreshDataList'])

const nameVisible = ref(true)

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const dataRule = {}

const visible = ref(false)
let isSubmit = false
const dataFormRef = ref(null)
const type = ref(0) // 判断是会员信息还是客户信息 0:客户  1：会员
const dataForm = ref({
  userId: 0,
  nickName: '',
  pic: '',
  status: 1,
  couponUserParam: {
    couponUsableNums: 0,
    couponUsedNums: 0,
    couponExpiredNums: 0
  }
})
const customerTags = ref([])
const memberTags = ref([])
const init = (id, typePar) => {
  dataForm.value.userId = id || 0
  type.value = typePar || 0
  visible.value = true
  isSubmit = false
  nextTick(() => {
    dataFormRef.value?.resetFields()
  })
  if (dataForm.value.userId) {
    if (isSubmit) {
      return false
    }
    isSubmit = true
    http({
      url: http.adornUrl(`/user/info/${dataForm.value.userId}`),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      dataForm.value = data
      customerTags.value = dataForm.value.customerTags
      memberTags.value = dataForm.value.memberTags
      initTradeDetail(dataForm.value.userId)
      initCouponDetail(dataForm.value.userId)
      initGrowthDetail(dataForm.value.userId)
    }).catch(() => {
      isSubmit = false
    })
  }
}

// 修改后刷新
const refreshChange = () => {
  init(dataForm.value.userId, type.value)
}

// 移除标签
const handleClose = (tag) => {
  http({
    url: http.adornUrl('/user/userTag/deleteUserTag'),
    method: 'delete',
    data: http.adornData({
      userId: dataForm.value.userId,
      userTagId: tag.tagId
    })
  }).then(() => {
    refreshChange()
  })
}

// 表单提交
const onSubmit = () => {
  visible.value = false
}

const updateTagsVisible = ref(false)
const updateTagsRef = ref(null)
// 打标签
const updateTags = (id) => {
  const ids = id ? [id] : [dataForm.value.userId]
  updateTagsVisible.value = true
  nextTick(() => {
    updateTagsRef.value?.init(ids)
  })
}

const updateCouponVisible = ref(false)
const updateCouponRef = ref(null)
// 送优惠券
const updateCoupon = (id) => {
  const ids = id ? [id] : [dataForm.value.userId]
  updateCouponVisible.value = true
  nextTick(() => {
    updateCouponRef.value?.init(ids)
  })
}

const tradeDetailRef = ref(null)
// 调用交易信息组件方法
const initTradeDetail = (id) => {
  tradeDetailRef.value?.init(id)
}

const couponDetailRef = ref(null)
// 优惠券明细
const initCouponDetail = (id) => {
  couponDetailRef.value?.init(id)
}

const growthDetailRef = ref(null)
// 调用成长值记录组件方法
const initGrowthDetail = (id) => {
  growthDetailRef.value?.init(id)
}

// 修改名称或者状态完成后刷新详情
const refreshInfo = () => {
  init(dataForm.value.userId, type.value)
  emit('refreshDataList', page)
}
/**
 * 关闭回调
 */
const handleDialogClose = () => {
  customerTags.value = []
  memberTags.value = []
}

defineExpose({
  refreshInfo,
  init
})

</script>

<style lang="scss" scoped>
:deep(.user-update-dialog) {
  .el-tabs__content {
    overflow: auto;
  }
  .user-edit-table {
    margin-bottom: 20px;
  }
  .el-tag.is-closable {
    margin-right: 5px;
  }
}
</style>
