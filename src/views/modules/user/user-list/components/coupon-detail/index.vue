<template>
  <!-- 编辑会员-优惠券明细 -->
  <div class="component-coupon-detail">
    <div class="user-coupon-detail-select-con">
      <el-select
        v-model="status"
        clearable
        style="margin-bottom:10px; width: 200px;"
        @change="statusChange()"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <el-table
      :data="dataList"
      header-cell-class-name="table-header"
      row-class-name="table-row-low"
      class="user-edit-table"
      style="width: 100%"
    >
      <el-table-column
        prop="receiveTime"
        :label="$t('user.getCouponTime')"
        width="250"
        align="left"
      />
      <el-table-column
        :label="$t('marketing.couponName')"
        width="120"
        align="center"
      >
        <template #default="scope">
          <div>
            <span>{{ scope.row.coupon.couponName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('coupon.couponType')"
        width="120"
        align="center"
      >
        <template #default="scope">
          <span>{{
            [
              $t("coupon.voucher"),
              $t("coupon.discountVoucher"),
              $t("user.coinCertificate"),
            ][scope.row.coupon.couponType - 1]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="score"
        :label="$t('user.effectiveTime')"
        width="300"
        align="center"
      >
        <template #default="scope">
          <div>
            <span>{{ scope.row.userStartTime }}{{ $t("text.to")
            }}{{ scope.row.userEndTime }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        :label="$t('user.couponStatus')"
        width="100"
        align="center"
      >
        <template #default="scope">
          <span>{{
            [$t("user.invalid"), $t("user.notUsed"), $t("user.used")][
              scope.row.status
            ]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('user.userRules')"
        align="center"
      >
        <template #default="scope">
          <div v-if="scope.row.coupon.couponType == 1">
            <span>{{ $t("marketing.full") }}{{ '￥' + scope.row.coupon.cashCondition
            }}{{ $t("marketing.reducea")
            }}{{ '￥' + scope.row.coupon.reduceAmount }}</span>
          </div>
          <div v-if="scope.row.coupon.couponType == 2">
            <span>{{
              $t("user.discountMsg")
                .replace("PRICE", scope.row.coupon.cashCondition)
                .replace("FOLD", scope.row.coupon.couponDiscount)
            }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      v-if="dataList.length"
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      :total="page.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />
    <!-- /分页 -->
  </div>
</template>

<script setup>

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const options = [{
  value: 0,
  label: $t('user.invalid')
}, {
  value: 1,
  label: $t('user.notUsed')
}, {
  value: 2,
  label: $t('user.used')
}]

let status = null
onMounted(() => {
  getData()
})

let userId = null
const init = (id) => {
  status = null
  userId = id
  getData(page)
}

const dataList = ref([])
// 获取数据
const getData = (pageParam) => {
  if (!userId) {
    return
  }
  http({
    url: http.adornUrl('/admin/coupon/pageByUserId'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        { userId, status }
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getData(page)
}

// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getData(page)
}

const statusChange = () => {
  page.currentPage = 1
  getData(page)
}

defineExpose({
  init
})

</script>

<style lang="scss" scoped>
@use "index";
</style>
