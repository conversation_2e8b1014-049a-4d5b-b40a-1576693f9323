<template>
  <!-- 成长值记录 -->
  <div class="component-growth-detail">
    <el-table
      :data="dataList"
      header-cell-class-name="table-header"
      row-class-name="table-row-low"
      class="user-edit-table"
      style="width: 100%"
    >
      <el-table-column
        prop="createTime"
        :label="$t('user.changeTime')"
        width="180"
      />
      <el-table-column
        prop="bizId"
        :label="$t('order.number')"
      />
      <el-table-column
        prop="changeGrowth"
        :label="$t('user.changeGrowthValue')"
      />
      <el-table-column
        prop="remarks"
        :label="$t('publics.remark')"
      >
        <template #default="scope">
          <span>
            <span v-if="scope.row.source === 0">
              {{ $t("user.systemModificationOfUserGrowthValue") }}
            </span>
            <span v-if="scope.row.source === 1">
              {{ $t("user.growthValueObtainedFromOrderConfirmationOfReceipt") }}
            </span>
            <span v-if="scope.row.source === 2">
              {{ $t("user.RefundOfGrowthValueForOrderRefund") }}
            </span>
            <span v-if="scope.row.source === 3">
              {{ $t("user.growthValueObtainedFromUserTopUpBalance") }}
            </span>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      v-if="dataList.length"
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      :total="page.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    />
    <!-- /分页 -->
  </div>
</template>

<script setup>

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
onMounted(() => {
  getData()
})

let userId = null
const init = (id) => {
  userId = id
  getData(page)
}

const dataList = ref([])
// 获取数据
const getData = (pageParam) => {
  if (!userId) {
    return
  }
  http({
    url: http.adornUrl('/user/userGrowthLog/pageByUserId'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        { userId }
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getData(page)
}

// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getData(page)
}

defineExpose({
  init
})

</script>

<style lang="scss" scoped>
@use "index";
</style>
