<template>
  <!-- 会员权益 -->
  <el-dialog
    v-model="visible"
    :title="!dataForm.rightsId ? $t('user.add') : $t('user.revise')"
    :close-on-click-modal="false"
    width="550px"
    class="user-right-container"
  >
    <!-- native modifier has been removed, please confirm whether the function has been affected  -->
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="auto"

      @submit.prevent
    >
      <el-form-item
        :label="$t('userRights.rightsNameCn')+':'"
        prop="rightsNameCn"
      >
        <el-input
          v-model="dataForm.rightsNameCn"
          :placeholder="$t('user.enterContentCn')"
          maxlength="5"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('userRights.rightsNameEn')+':'"
        prop="rightsNameEn"
      >
        <el-input
          v-model="dataForm.rightsNameEn"
          :placeholder="$t('user.enterContentEn')"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('userRights.rightsIntroduceCn')+':'"
        prop="descriptionCn"
      >
        <el-input
          v-model="dataForm.descriptionCn"
          type="textarea"
          :autosize="{ minRows: 2}"
          maxlength="250"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('userRights.rightsIntroduceEn')+':'"
        prop="descriptionEn"
      >
        <el-input
          v-model="dataForm.descriptionEn"
          type="textarea"
          :autosize="{ minRows: 2}"
          maxlength="250"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('userRights.rightsIcon')+':'"
        prop="icon"
      >
        <img-upload v-model="dataForm.icon" />
      </el-form-item>
      <el-form-item
        :label="$t('userRights.sequence')+':'"
        prop="seq"
      >
        <el-input
          v-model.number="dataForm.seq"
          :placeholder="$t('userRights.errorIntegerTip1')"
          precision="0"
          @change="setShopGetScore"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn"
          @click="visible = false"
        >
          {{ $t('user.cancel') }}
        </div>
        <div
          class="default-btn primary-btn"
          @click="onSubmit()"
        >
          {{ $t('user.confirm') }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const emit = defineEmits(['refreshDataList'])

const dataForm = ref({})

const validateName = (rule, value, callback) => {
  if (!value || !value.trim()) {
    callback(new Error($t('userRights.nameCanNotEmpty')))
  } else {
    callback()
  }
}
const validateDescription = (rule, value, callback) => {
  if (!value || !value.trim()) {
    callback(new Error($t('userRights.intrCanNotEmpty')))
  } else {
    callback()
  }
}
const dataRule = {
  rightsNameCn: [
    { required: true, message: $t('userRights.nameCanNotEmptyCn'), trigger: 'blur' },
    { validator: validateName, trigger: 'blur' }
  ],
  icon: [
    { required: true, message: $t('userRights.iconCanNotEmpty'), trigger: 'blur' }
  ],
  descriptionCn: [
    { required: true, message: $t('userRights.intrCanNotEmptyCn'), trigger: 'blur' },
    { validator: validateDescription, trigger: 'blur' }
  ]
}

const visible = ref(false)
const dataFormRef = ref(null)
let isSubmit = false
const init = (rightsId) => {
  dataForm.value = {
    rightsId: null,
    rightsNameCn: null,
    rightsNameEn: null,
    icon: null,
    descriptionCn: null,
    descriptionEn: null,
    seq: 1
  }
  dataForm.value.rightsId = rightsId || 0
  visible.value = true
  isSubmit = false
  nextTick(() => {
    dataFormRef.value?.resetFields()
    if (dataForm.value.rightsId) {
      http({
        url: http.adornUrl('/user/userRights/info/' + dataForm.value.rightsId),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        if (data?.userRightsLangList) {
          data.userRightsLangList.forEach(val => {
            const rightsName = ['rightsNameCn', 'rightsNameEn'][val.lang]
            const description = ['descriptionCn', 'descriptionEn'][val.lang]
            data[rightsName] = val.rightsName
            data[description] = val.description
          })
        }
        dataForm.value = data
      })
    } else {
      // 获取最大的序号
      http({
        url: http.adornUrl('/user/userRights/getMaxSeq'),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        dataForm.value.seq = data
      })
    }
  })
}

// 顺序数值判断
const setShopGetScore = () => {
  const num = Math.round(dataForm.value.seq)
  dataForm.value.seq = num < 1 ? 1 : num
  if (num >= 100000000) {
    dataForm.value.seq = 100000000
  }
}
/**
 * 表单提交
 */
const onSubmit = () => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      if (isSubmit) {
        return false
      }
      isSubmit = true
      getVipInternationalization()
      http({
        url: http.adornUrl('/user/userRights'),
        method: dataForm.value.rightsId ? 'put' : 'post',
        data: http.adornData(dataForm.value)
      }).then(() => {
        ElMessage({
          message: $t('user.succeeded'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      }).catch(() => {
        isSubmit = false
      })
    }
  })
}

// vip 国际化
const getVipInternationalization = () => {
  if (dataForm.value?.userRightsLangList) {
    dataForm.value.userRightsLangList.forEach((val) => {
      if (val.lang === 0) {
        val.rightsName = dataForm.value.rightsNameCn
        val.description = dataForm.value.descriptionCn
      }
      if (val.lang === 1) {
        val.rightsName = dataForm.value.rightsNameEn
        val.description = dataForm.value.descriptionEn
      }
    })
  } else {
    dataForm.value.userRightsLangList = [
      {
        lang: 0,
        rightsName: dataForm.value.rightsNameCn,
        description: dataForm.value.descriptionCn

      },
      {
        lang: 1,
        rightsName: dataForm.value.rightsNameEn,
        description: dataForm.value.descriptionEn
      }
    ]
  }
}

defineExpose({
  init
})

</script>

<style lang="scss" scoped>
.user-right-container{
  :deep(.el-textarea__inner){
    padding-right:15%
  }
}
</style>
