<template>
  <div class="mod-supplier-supplier">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"

        @submit.prevent
      >
        <div class="input-row margin-bottom-none">
          <el-form-item
            prop="reason"
            :label="$t('stock.stockBillReason')+':'"
          >
            <el-input
              v-model="searchForm.reason"
              :style="$t('language')==='简体中文'?'':'min-width: 390px;'"
              type="text"
              clearable
              :placeholder="$t('stock.stockBillReasonInputTips')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('stock.stockType')+':'"
            prop="type"
          >
            <el-select
              v-model="searchForm.type"
              clearable
            >
              <el-option
                :label="$t('stock.sendStock')"
                :value="1"
              />
              <el-option
                :label="$t('stock.receiveStock')"
                :value="2"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('stock.sysSet')+':'"
            prop="sysSet"
          >
            <el-select
              v-model="searchForm.sysSet"
              clearable
            >
              <el-option
                :label="$t('publics.yes')"
                :value="1"
              />
              <el-option
                :label="$t('publics.no')"
                :value="0"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('coupon.expStatus')+':'"
            prop="status"
          >
            <el-select
              v-model="searchForm.status"
              clearable
            >
              <el-option
                :label="$t('shop.ena')"
                :value="1"
              />
              <el-option
                :label="$t('publics.disable')"
                :value="0"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="text-btn-con">
              <div
                class="default-btn primary-btn"
                @click="onSearch(true)"
              >
                {{ $t('crud.searchBtn') }}
              </div>
              <div
                class="default-btn"
                @click="resetForm('searchForm')"
              >
                {{ $t('shop.resetMap') }}
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('multishop:stockReason:add')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <div class="table-con">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('stock.stockBillReason')"
            prop="reason"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.reason || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.stockType')"
            prop="type"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.type === 1 ? $t('stock.sendStock') : $t('stock.receiveStock') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.sysSet')"
            prop="sysSet"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.sysSet ? $t('publics.yes') : $t('publics.no') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('coupon.expStatus')"
            prop="status"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.status === 1 ? $t('shop.ena') : $t('publics.disable') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.remark')"
            prop="remark"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.remark || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            fixed="right"
            width="300"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('multishop:stockReason:edit') && !scope.row.sysSet"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.stockChangeReasonId)"
                >
                  {{ $t('crud.editTitle') }}
                </div>
                <div
                  v-if="isAuth('multishop:stockReason:enable') && scope.row.status === 0"
                  class="default-btn text-btn"
                  @click="changeStatus(scope.row.stockChangeReasonId, 1)"
                >
                  {{ $t('shop.ena') }}
                </div>
                <div
                  v-if="isAuth('multishop:stockReason:disable') && scope.row.status === 1"
                  class="default-btn text-btn"
                  @click="changeStatus(scope.row.stockChangeReasonId, 0)"
                >
                  {{ $t('publics.disable') }}
                </div>
                <div
                  v-if="isAuth('multishop:stockReason:delete') && !scope.row.sysSet"
                  class="default-btn text-btn"
                  @click="onDelete(scope.row.stockChangeReasonId, -1)"
                >
                  {{ $t('text.delBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'
import AddOrUpdate from './components/customizereason-add-or-update.vue'

let tempSearchForm = null // 保存上次点击查询的请求条件

const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  reason: '', // 原因
  type: '', // 出入库类型
  sysSet: '', // 是否系统内置
  status: '' // 状态
}) // 搜索
const addOrUpdateVisible = ref(false)
onMounted(() => {
  getDataList(page)
})

const getDataList = (pageParam, newData = false) => {
  if (page) {
    const size = Math.ceil(page.total / page.pageSize)
    page.currentPage = (page.currentPage > size ? size : page.currentPage) || 1
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/shop/stockChangeReason/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}
// 新增 / 修改
const addOrUpdateRef = ref(null)
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id, null)
  })
}
const changeStatus = (id, status) => {
  http({
    url: http.adornUrl('/shop/stockChangeReason/changeStatus'),
    method: 'put',
    params: http.adornParams({
      stockChangeReasonId: id,
      status
    })
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1500,
      onClose: () => {
        refreshChange()
      }
    })
  })
}
const onDelete = (id) => {
  const ids = id ? [id] : []
  ElMessageBox.confirm($t('admin.isDeleOper') + '?', $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    changeStatus(id, -1)
    page.total = page.total - ids.length
  }).catch(() => {
  })
}
/**
 * 刷新回调
 */
const refreshChange = () => {
  resetForm('searchForm')
  getDataList(page)
}
const onSearch = (newData = false) => {
  getDataList(page, newData)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}
const searchFormRef = ref(null)
const resetForm = () => {
  searchFormRef.value.resetFields()
}

</script>
