<template>
  <div class="content main-container">
    <!-- 表格 -->
    <div class="table-con">
      <el-table
        ref="dataListRef"
        :data="dataList"
        header-cell-class-name="table-header"
        row-class-name="table-row-low"
        style="width: 100%"
        :row-key="(row, index) => { return row.stockBillNo }"
        @selection-change="onSelectSome"
      >
        <el-table-column
          type="selection"
          fixed="left"
          width="55"
          :reserve-selection="true"
        />
        <el-table-column
          :label="type === 1 ? $t('stock.outStockOrderNo') : $t('stock.inStockOrderNo')"
          prop="stockBillNo"
          fixed="left"
          width="240"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.stockBillNo }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.stockPointName')"
          prop="stockPointName"
          width="200"
        >
          <template #default="scope">
            {{ scope.row.stockPointName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.stockPointType')"
          prop="stockPointType"
          width="120"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ ['', $t('stock.warehouse'), $t('stock.station')][scope.row.stockPointType] || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.stockBillType')"
          prop="stockBillType"
          width="140"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ typeFilter(scope.row.stockBillType) }}{{ scope.row.reason ? '-' + scope.row.reason : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.billStatus')"
          prop="status"
          width="200"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ type === 1 ? statusStrOutArr[scope.row.status] : statusStrInArr[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="type === 1 ? $t('stock.actualOutCount') : $t('stock.actualInCount')"
          prop="totalCount"
          width="100px"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.totalCount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="totalAmount"
          width="180"
        >
          <template #header>
            <div class="total-in-amount">
              <span>{{ type === 1 ? $t('stock.totalOutAmount') : $t('stock.totalInAmount') }}</span>
              <el-tooltip
                v-if="type !== 1"
                effect="dark"
                :content="$t('stock.totalInAmountTips')"
                placement="right"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.totalAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('takeStock.maker')"
          prop="employeeMobile"
          width="180"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.makerName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.createOrderTime')"
          prop="createTime"
          width="200"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('crud.menu')"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <div class="text-btn-con">
              <div
                v-if="((isAuth('multishop:receiveStock:edit') && type === 2) || (isAuth('multishop:sendStock:edit') && type === 1)) && scope.row.status === 2"
                class="default-btn text-btn"
                @click="updateHandle(scope.row.stockBillLogId)"
              >
                {{ $t('text.editBtn') }}
              </div>
              <el-popconfirm
                v-if="((isAuth('multishop:receiveStock:voided') && type === 2) || (isAuth('multishop:sendStock:voided') && type === 1)) && scope.row.status === 2"
                :confirm-button-text="$t('takeStock.voidInventory')"
                :cancel-button-text="$t('order.cancel')"
                :hide-icon="true"
                :title="$t('stock.voidInventoryTips')"
                placement="top"
                @confirm="voidedHandle(scope.row.stockBillLogId)"
              >
                <template #reference>
                  <div
                    v-if="((isAuth('multishop:receiveStock:voided') && type === 2) || (isAuth('multishop:sendStock:voided') && type === 1)) && scope.row.status === 2"
                    class="default-btn text-btn"
                  >
                    {{ $t('takeStock.voidInventory') }}
                  </div>
                </template>
              </el-popconfirm>
              <div
                class="default-btn text-btn"
                @click="detailHandle(scope.row.stockBillLogId)"
              >
                {{ $t('shop.withdrawalDetail') }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { isAuth } from '@/utils'

const emit = defineEmits(['refreshList'])
const Router = useRouter()
const typeFilter = (val) => {
  switch (val) {
    case 1:return $t('stock.purchaseInStock')
    case 2:return $t('stock.returnToStorage')
    case 3:return $t('stock.otherEntries')
    case 4:return $t('stock.sellOut')
    case 5:return $t('stock.editOutBound')
    case 6:return $t('stock.otherOutbound')
    case 7:return $t('stock.inventoryInitialization')
    case 8:return $t('stock.orderCancelled')
    case 9:return $t('stock.editStorage')
    case 10:return $t('stock.profitStorage')
    case 11:return $t('stock.lossOutBound')
    case 12: return $t('stock.transferWarehouse')
    case 13: return $t('stock.transferOutWarehouse')
    case 14: return $t('stock.inventoryModeSwitchesOutInventory')
    case 15: return $t('stock.inventoryModeSwitchesInInventory')
    case 16: return $t('stock.secondKillReplenishmentStock')
    case 17: return $t('stock.voucherExpire')
  }
}
const props = defineProps({
  dataList: {
    // eslint-disable-next-line vue/require-valid-default-prop
    default: [],
    type: Array // 数据列表
  },
  type: {
    default: 0,
    type: Number // 1 出库 2 入库
  }
})

let isSubmit = false
const dataListSelections = ref([])
const statusStrInArr = [
  $t('stock.voided'),
  $t('stock.inStorage'),
  $t('stock.waitSubmit')
]
const statusStrOutArr = [
  $t('stock.voided'),
  $t('stock.inOutbound'),
  $t('stock.waitSubmit')
]

// 多选变化
const onSelectSome = (val) => {
  dataListSelections.value = val
}
const getSelectList = () => {
  return dataListSelections.value
}
defineExpose({ getSelectList })
const updateHandle = (stockBillLogId) => {
  const path = props.type === 1 ? '/stock/warehouse/send/add-or-update' : '/stock/warehouse/receive/add-or-update'
  Router.push({
    path,
    query: {
      type: props.type,
      stockBillLogId
    }
  })
}
const voidedHandle = (stockBillLogId) => {
  if (isSubmit) {
    return
  }
  isSubmit = true
  http({
    url: http.adornUrl('/shop/stockBillLog/voided'),
    method: 'put',
    params: http.adornParams(
      { stockBillLogId }
    )
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1000
    })
    isSubmit = false
    emit('refreshList')
  }).catch(() => {
    isSubmit = false
  })
}
const detailHandle = (stockBillLogId) => {
  const path = props.type === 1 ? '/stock/warehouse/send/detail' : '/stock/warehouse/receive/detail'
  Router.push({
    path,
    query: {
      type: props.type,
      stockBillLogId
    }
  })
}

</script>

<style lang="scss" scoped>
.total-in-amount{
  display: flex;
  align-items: center;
  // eslint-disable-next-line vue-scoped-css/no-unused-selector
  .el-icon{
    margin-left: -5px;
  }
}
</style>
