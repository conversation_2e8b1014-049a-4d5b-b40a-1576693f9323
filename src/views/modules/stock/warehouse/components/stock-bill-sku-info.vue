<template>
  <!-- 商品信息 -->
  <div class="prodItem-container">
    <span
      v-if="required"
      class="required-icon"
    >*</span>
    <span class="title">{{ $t('group.prodInfo') }}</span>
    <el-divider />
    <div
      class="default-btn primary-btn"
      @click="selectProdHandle()"
    >
      {{ $t('shop.addProd') }}
    </div>
    <el-tooltip
      class="item"
      :disabled="stockBillLogItems.length === 0"
      effect="dark"
      :content="$t('stock.uploadNotEmptyTips')"
      placement="top"
    >
      <div
        class="default-btn"
        :class="{'disabled-btn': stockBillLogItems.length > 0}"
        @click="uploadSku"
      >
        {{ $t('stock.batchImport') }}
      </div>
    </el-tooltip>
    <div
      v-if="failureCount > 0"
      class="default-btn"
      @click="deleteFailureProd"
    >
      {{ $t('stock.deleteFailureProd') }}
    </div>
    <div class="count-desc-text">
      {{ $t('order.total') }}{{ stockBillLogItems.length }}{{ type === 1 ? $t('stock.prodCountOutTips') : $t('stock.prodCountTips') }}:{{ totalCount }}
    </div>
    <div
      v-if="failureCount > 0"
      class="count-desc-text"
    >
      {{ $t('stock.include') }}{{ failureCount }}{{ $t('stock.failureCountTips') }}
    </div>
    <div
      v-if="!verifyFlag"
      class="error-tips"
    >
      {{ $t('stock.prodNotEmpty') }}
    </div>
    <div class="prodItem-table">
      <el-table
        :data="stockBillLogItems.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
        header-cell-class-name="table-header"
        row-class-name="table-row"
      >
        <el-table-column
          :label="$t('group.prodInfo')"
          prop="reason"
          fixed="left"
          align="center"
          width="320px"
        >
          <template #default="scope">
            <div class="prod-info-container">
              <div
                v-if="scope.row.type === 1"
                class="prod-image"
              >
                <ImgShow :src="scope.row.pic" />
              </div>
              <div
                v-else
                class="prod-image"
              >
                <ImgShow :src="scope.row.pic" />
              </div>
              <div class="prod-name">
                <el-tooltip
                  :content="scope.row.prodName"
                  placement="top"
                  effect="light"
                >
                  <div class="prod-name-txt">
                    {{ scope.row.prodName }}
                  </div>
                </el-tooltip>
                <el-tooltip
                  :content="scope.row.skuName"
                  placement="top"
                  effect="light"
                >
                  <div class="spec">
                    {{ scope.row.skuName }}
                  </div>
                </el-tooltip>
                <div class="prod-no">
                  {{ scope.row.partyCode }}
                </div>
              </div>
              <!-- <div v-if="scope.row.type === 1" class="prod-name">
                <el-tooltip :content="scope.row.prodName" placement="top" effect="light">
                  <div class="prod-name-txt">
                    {{scope.row.prodName}}
                  </div>
                </el-tooltip>
                <el-tooltip :content="scope.row.skuName" placement="top" effect="light">
                  <div class="spec">
                    {{scope.row.skuName}}
                  </div>
                </el-tooltip>
                <div class="prod-no">
                  {{scope.row.partyCode}}
                </div>
              </div>
              <div v-else class="prod-name">
                <el-tooltip :content="scope.row.name" placement="top" effect="light">
                  <div class="prod-name-txt">
                    {{scope.row.name}}
                  </div>
                </el-tooltip>
                <el-tooltip :content="scope.row.spec" placement="top" effect="light">
                  <div class="spec">
                    {{scope.row.spec}}
                  </div>
                </el-tooltip>
                <div class="prod-no">
                  {{scope.row.barCode}}
                </div>
              </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.unit')"
          prop="type"
          align="center"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.unit || $t('stock.pieces') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="type === 1"
          prop="stocks"
          :label="$t('stock.availableStock')"
          align="center"
        />
        <el-table-column
          prop="stock_count"
          :label="type === 1 ? $t('stock.deliveryCount') : $t('stock.warehousingCount')"
          align="center"
        >
          <template #default="scope">
            <el-tooltip
              :disabled="scope.row.status !== -1"
              :content="$t('stock.productAvailable')"
              placement="top"
              effect="light"
            >
              <div class="custom-rate">
                <el-input
                  v-model="scope.row.stockCount"
                  :disabled="scope.row.status === -1"
                  type="number"
                  :min="0"
                  :max="9999999"
                  :precision="0"
                  style="width: 80%;"
                  @blur="inputStockCount(scope.$index, scope.row.stockCount)"
                />
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('crud.menu')"
          fixed="right"
        >
          <template #default="scope">
            <div class="text-btn-con">
              <div
                class="default-btn text-btn"
                @click="onDelete(scope.$index)"
              >
                {{ $t('text.delBtn') }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.currentPage"
        :page-size="page.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="stockBillLogItems.length"
        @current-change="onPageChange"
      />
    </div>
    <skuSelect
      v-if="addProdVisible"
      ref="ProdsSelectRef"
      :chosen-check-items="productItems"
      :is-memory-old-data="true"
      :is-compose="0"
      :is-filter-bind-voucher="1"
      :type="type===1 ? 1 : 0"
      data-url="/m/stockPointSku/page"
      :http-params="httpParams"
      @refresh-select-prods="selectProdItem"
    />
    <stockSkuUpload
      v-if="batchImportVisible"
      ref="skuUploadRef"
      :type="type"
      :stock-point-id="stockPointId"
      @refresh-data-list="refreshDataList"
    />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import skuSelect from './sku-select-components.vue'
import stockSkuUpload from '../../components/stock-sku-upload.vue'

const props = defineProps({
  type: {
    default: 0,
    type: Number // 1 出库 2 入库
  },
  stockBillLogId: {
    default: 0,
    type: Number
  },
  required: { // 是否显示必填星标
    default: false,
    type: Boolean
  }
})

const stockBillLogItems = ref([]) // 合并商品与单品结果列表
const productItems = ref([]) // 已选择的商品项
const addProdVisible = ref(false) // 添加商品弹窗是否可见
const batchImportVisible = ref(false) // 批量导入商品弹窗是否可见
const verifyFlag = ref(true) // 数据校验标记，true：正确，false：错误
const failureCount = ref(0) // 失效的商品数量
const page = reactive({
  currentPage: 1, // 初始页
  pageSize: 10 // 每页数据大小
})

// 总出入库量
const totalCount = computed(() => {
  let count = 0
  const list = stockBillLogItems.value.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)
  if (list && list.length > 0) {
    list.forEach(item => {
      count += Number(item.stockCount || 0)
    })
  } else {
    count = 0
  }
  return count || 0
})

onMounted(() => {
  init()
})

const init = () => {
  if (props.stockBillLogId) {
    getSkuList()
  }
  // 出库需要请求仓库信息获取默认仓库id
  if (props.type === 2) {
    getDefaultWarehouseId()
  }
}

let defaultWarehouseId = null
const getDefaultWarehouseId = () => {
  http({
    url: http.adornUrl('/m/stockPointSku/stockPoint'),
    method: 'get'
  }).then(({ data }) => {
    defaultWarehouseId = data.defaultWarehouseId
  })
}

const getSkuList = () => {
  http({
    url: http.adornUrl('/shop/stockBillLogItem/list'),
    method: 'get',
    params: http.adornParams(
      {
        stockBillLogId: props.stockBillLogId
      }
    )
  }).then(({ data }) => {
    stockBillLogItems.value = data
    productItems.value = []
    data.forEach(item => {
      if (item.status === -1) {
        ++failureCount.value
      }
      productItems.value.push(Object.assign({}, item))
    })
  })
}
const ProdsSelectRef = ref(null)
const httpParams = ref({})
const selectProdHandle = () => {
  if (!stockPointInfo.stockPointId) {
    ElMessage({
      message: $t('stock.pleaseSelectStockPointFirst'),
      type: 'error',
      duration: 1500
    })
    return
  }
  addProdVisible.value = true
  if (props.type === 1) {
    httpParams.value = {
      stockPointId: stockPointInfo.stockPointId,
      stockPointType: stockPointInfo.stockPointType, // 库存点类型 1仓库 2门店
      type: stockPointInfo.warehouseType, // 仓库类型(0默认仓库，1区域仓库)
      stockMode: stockPointInfo.stockMode // 库存模式
    }
  } else {
    httpParams.value = {
      type: stockPointInfo.warehouseType, // 仓库类型(0默认仓库，1区域仓库)
      stockMode: stockPointInfo.stockMode, // 库存模式
      stockPointType: stockPointInfo.stockPointType, // 库存点类型 1仓库 2门店
      stockPointId: defaultWarehouseId,
      inStockPointId: stockPointInfo.stockPointId
    }
  }
  nextTick(() => {
    ProdsSelectRef.value?.init(httpParams.value)
  })
}
const deleteFailureProd = () => {
  const stockBillLogArr = []
  const prodArr = []
  productItems.value.forEach(item => {
    if (item.status !== -1) {
      prodArr.push(item)
      stockBillLogArr.push(item)
    }
  })
  productItems.value = prodArr
  stockBillLogItems.value = stockBillLogArr
  page.currentPage = 1
  failureCount.value = 0
}
const selectProdItem = (prodItem) => {
  productItems.value = prodItem
  mergeProdAndRetail()
}
const mergeProdAndRetail = () => {
  const res = []
  productItems.value.forEach(item => {
    res.push({ ...item, ...stockPointInfo, type: 1 })
  })
  verifyFlag.value = res.length !== 0
  stockBillLogItems.value = res
}
const skuUploadRef = ref(null)
const uploadSku = () => {
  if (stockBillLogItems.value.length > 0) {
    return
  }
  if (!stockPointInfo.stockPointId) {
    ElMessage({
      message: $t('stock.pleaseSelectStockPointFirst'),
      type: 'error',
      duration: 1500
    })
    return
  }
  batchImportVisible.value = true
  nextTick(() => {
    skuUploadRef.value?.init()
  })
}
const refreshDataList = (e) => {
  page.currentPage = 1
  stockBillLogItems.value = e.stockBillLogItemList || []
  productItems.value = []
  stockBillLogItems.value.forEach(item => {
    if (props.type === 1 && item.stockPointSkuList?.length) {
      const pointListIndex = item.stockPointSkuList.findIndex((pointItem) => {
        return pointItem.stockPointId === stockPointInfo.stockPointId
      })
      item.stocks = item.stockPointSkuList[pointListIndex].stock
    }
    productItems.value.push(item)
  })
}
/**
 * 校验数据，校验成功返回数据项，不成功返回null
 */
const verifyDataForm = () => {
  if (stockBillLogItems.value.length === 0) {
    verifyFlag.value = false
    return null
  } else {
    for (let i = 0; i < stockBillLogItems.value.length; i++) {
      if (!stockBillLogItems.value[i].stockCount || stockBillLogItems.value[i].stockCount === '') {
        ElMessage({
          message: $t('stock.countNotEmptyOr0'),
          type: 'error',
          duration: 1500,
          onClose: () => {
          }
        })
        return null
      }
    }
    verifyFlag.value = true
    return stockBillLogItems.value
  }
}
const inputStockCount = (index, val) => {
  let stockCount = parseInt(val)
  stockCount = isNaN(stockCount) ? 0 : stockCount
  if (stockCount > 9999999) {
    stockCount = 9999999
  }
  // 当前位置在商品数组
  if (props.type === 1) {
    if (stockCount > productItems.value[((page.currentPage - 1) * page.pageSize) + index].stocks) {
      stockCount = productItems.value[((page.currentPage - 1) * page.pageSize) + index].stocks
    }
  }
  productItems.value[((page.currentPage - 1) * page.pageSize) + index].stockCount = stockCount
  stockBillLogItems.value[((page.currentPage - 1) * page.pageSize) + index].stockCount = stockCount
}
/**
 * 删除已选择的商品项
 * @param index 当前商品项在当前页的位置
 */
const onDelete = (index) => {
  // 页码-1 * 页面大小 + index = 当前删除项在合并数组中的位置
  stockBillLogItems.value.splice(((page.currentPage - 1) * page.pageSize) + index, 1)
  // 在商品数组中删除对应的数据
  productItems.value.splice(((page.currentPage - 1) * page.pageSize) + index, 1)
  // 判断当前页码是否超过删除后的页码大小
  if (page.currentPage > ((stockBillLogItems.value.length - 1) / page.pageSize + 1)) {
    page.currentPage = --page.currentPage || 1
  }
}
const onPageChange = (val) => {
  page.currentPage = val
}

let stockPointInfo = {}
const stockPointId = ref(null)
// retainProductItem true：保留商品信息 false：清空商品信息
const onResetData = (data, retainProductItem = false) => {
  console.log('stockPointId', data)
  stockPointId.value = data.stockPointId
  if (retainProductItem === false) {
    productItems.value = []
    stockBillLogItems.value = []
    totalCount.value = 0
  }
  stockPointInfo = data
}

defineExpose({
  init,
  verifyDataForm,
  onResetData
})
</script>

<style lang="scss" scoped>

.prodItem-container {
  .required-icon {
    color: #f56c6c;
    margin-right: 4px;
  }
  .title {
    color: #333333;
    font-size: 16px;
    font-weight: bold;
  }
  .count-desc-text {
    margin-top: 8px;
    margin-bottom: 8px;
    color: #333333;
    font-size: 14px;
  }
  .error-tips {
    color: #e43130;
    font-size: 14px;
    padding-bottom: 6px;
  }
  .disabled-btn {
    color: #C0C4CC;
    &:hover {
      cursor: not-allowed;
      color: #C0C4CC;
    }
  }
  .prodItem-table {
    .prod-info-container {
      height: 100%;
      display: flex;
      justify-content: space-between;
      .prod-image {
        margin-right: 20px;
        width: 80px;
        height: 80px;
        &:deep(img) {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .prod-name {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .prod-name-txt {
          font-size: 14px;
          color: #333333;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          word-break: break-word;
        }
        .spec {
          font-size: 14px;
          color: #333333;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
        }
        .prod-no {
          font-size: 14px;
          color: #333333;
        }
      }
    }
  }
}
@media (max-width: 1440px) {
  div :deep(.el-card__body) {
    padding-bottom: 40px;
  }
}
</style>
