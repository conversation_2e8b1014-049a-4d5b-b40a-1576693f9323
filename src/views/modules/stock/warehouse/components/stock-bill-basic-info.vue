<template>
  <!-- 基本信息 -->
  <div class="component-stock-bill-basic-info basicInfo-container">
    <!-- native modifier has been removed, please confirm whether the function has been affected  -->
    <el-form
      ref="dataFormRef"
      label-width="auto"
      :model="dataForm"
      :rules="rules"
      @submit.prevent
    >
      <span class="title">{{ $t('product.basicInformation') }}</span>
      <el-divider />
      <!-- 基本信息 -->

      <!-- 库存点类型 -->
      <el-form-item
        :label="$t('stock.stockPointType')"
      >
        <el-radio-group
          v-model="dataForm.stockPointType"
          @change="onPointTypeChange"
        >
          <el-radio :label="1">
            {{ $t('stock.warehouse') }}
          </el-radio>
          <el-radio :label="2">
            {{ $t('stock.station') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 库存点名称（新建） -->
      <el-form-item
        :label="$t('stock.stockPointName')"
        prop="stockPointId"
      >
        <el-select
          v-model="dataForm.stockPointId"
          :placeholder="$t('stock.pleaseSelect')"
          @change="onPointIdChange"
        >
          <el-option
            v-for="item in pointOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="type === 1 ? $t('stock.deliveryDate') : $t('stock.warehousingDate')"
        prop="businessTime"
      >
        <el-date-picker
          v-model="dataForm.businessTime"
          type="date"
          :placeholder="$t('admin.seleData')"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disabledDate"
          class="el-date-picker"
        />
      </el-form-item>
      <el-form-item
        ref="stockChangeReasonIdRef"
        :label="type === 1 ? $t('stock.deliveryReason') : $t('stock.warehousingReason')"
        prop="stockChangeReasonId"
      >
        <el-select
          v-model="dataForm.stockChangeReasonId"
          :placeholder="$t('tip.select')"
          @change="selectReason"
        >
          <el-option
            v-for="node in reasonList"
            :key="node.stockChangeReasonId"
            :label="node.reason"
            :value="node.stockChangeReasonId"
          />
        </el-select>
        <!--新建/刷新-->
        <div class="create-refresh-btn">
          <div
            class="default-btn text-btn"
            @click="getReasonList"
          >
            {{ $t('admin.refresh') }}
          </div>
          <el-divider direction="vertical" />
          <div
            class="default-btn text-btn"
            @click.stop="createReason()"
          >
            {{ $t('admin.newConstruction') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('publics.remark')"
        prop="remark"
      >
        <el-input
          v-model="dataForm.remark"
          class="remark"
          maxlength="50"
          type="textarea"
          show-word-limit
          :row="5"
        />
      </el-form-item>
      <!-- 附件 -->
      <span class="title">{{ $t('stock.appendix') }}</span>
      <el-divider />
      <el-form-item :label="type === 1 ? $t('stock.deliveryCertificate') : $t('stock.warehousingCertificate')">
        <div>
          <div class="business-qual">
            <imgs-upload
              v-model="dataForm.qualifications"
              :limit="4"
              :prompt="false"
            />
          </div>
          <div class="upload-tips">
            {{ type === 1 ? $t('stock.businessOutQualTips') : $t('stock.businessInQualTips') }}
          </div>
        </div>
      </el-form-item>
    </el-form>
    <reason-add-or-update
      v-if="reasonAddOrUpdateVisible"
      ref="reasonAddOrUpdateRef"
      @refresh-data-list="getReasonList"
    />
  </div>
</template>

<script setup>
import reasonAddOrUpdate from './customizereason-add-or-update.vue'

const props = defineProps({
  type: {
    default: 0,
    type: Number // 1 出库 2 入库
  },
  stockBillLogId: {
    default: 0,
    type: Number
  }
})

const emit = defineEmits(['changeStockPointId'])

const dataForm = ref({
  stockBillLogId: null,
  businessTime: '', // 出入库时间
  stockChangeReasonId: '', // 出入库原因id
  remark: '', // 出入库备注信息
  qualifications: '', // 出入库凭证
  status: '',
  stockPointType: 1,
  stockPointId: ''
})
const reasonAddOrUpdateVisible = ref(false)
const reasonList = ref([]) // 出入库原因
// 出入库日期选择限制
const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

const validateWarehouseId = (rule, value, callback) => {
  if (value && dataForm.value.stockPointType === 1 && warehouseList[value]?.type === 0 && !(warehouseList[value].provinceId && warehouseList[value].cityId && warehouseList[value].areaId)) {
    callback(new Error($t('stock.defaultWarehouseAddress')))
  } else {
    callback()
  }
}

const rules = {
  businessTime: [
    { required: true, message: props.type === 1 ? $t('stock.deliveryDateNotEmpty') : $t('stock.warehousingDateNotEmpty'), trigger: 'blur' }
  ],
  stockChangeReasonId: [
    { required: true, message: props.type === 1 ? $t('stock.deliveryReasonNotEmpty') : $t('stock.warehousingReasonNotEmpty'), trigger: 'blur' }
  ],
  stockPointId: [
    { required: true, message: $t('stock.pleaseSelectStockPoint'), trigger: 'blur' },
    { validator: validateWarehouseId, trigger: 'change' }
  ]
}
onMounted(() => {
  init()
})

const pointOptions = ref([])
const init = async () => {
  if (props.stockBillLogId) {
    getStockBillInfo()
  } else {
    // 初始化出入库原因
    getReasonList()
    // 非编辑直接获取（仓库）库存点信息
    pointOptions.value = await onGetPointList(1, true)
  }
}
const getStockBillInfo = () => {
  http({
    url: http.adornUrl('/shop/stockBillLog/info/' + props.stockBillLogId),
    method: 'get',
    params: http.adornParams(
      {
        stockBillLogId: props.stockBillLogId
      }
    )
  }).then(async ({ data }) => {
    // 获取完出入库详情之后，获取对应库存点信息
    pointOptions.value = await onGetPointList(data.stockPointType, false)
    dataForm.value = data
    // 存在stockBillLogId的，则在请求完成之后，将dataForm的值传给sku-info组件，并且保留商品信息
    emit('changeStockPointId', {
      warehouseType: dataForm.value.warehouseType,
      stockPointId: dataForm.value.stockPointId,
      stockMode: dataForm.value.stockMode,
      stockPointType: dataForm.value.stockPointType
    }, true)
    getReasonList()
  })
}
const stockChangeReasonIdRef = ref(null)
const selectReason = () => {
  stockChangeReasonIdRef.value?.clearValidate()
}
const reasonAddOrUpdateRef = ref(null)
const createReason = () => {
  reasonAddOrUpdateVisible.value = true
  nextTick(() => {
    reasonAddOrUpdateRef.value.init(null, props.type)
  })
}
/**
 * 获取出入库原因
 */
const getReasonList = () => {
  http({
    url: http.adornUrl('/shop/stockChangeReason/list'),
    method: 'get',
    params: http.adornParams(
      {
        status: 1,
        type: props.type
      }
    )
  }).then(({ data }) => {
    if (data && props.stockBillLogId && dataForm.value) {
      let isExist = false
      for (let i = 0; i < data.length; i++) {
        if (data[i].stockChangeReasonId === dataForm.value.stockChangeReasonId) {
          isExist = true
        }
      }
      if (!isExist) {
        dataForm.value.stockChangeReasonId = null
      }
    }
    reasonList.value = data
  })
}
/**
 * 校验表单数据，校验成功返回表单数据，校验失败返回null (父组件调用）
 */
const dataFormRef = ref(null)
const verifyDataForm = async () => {
  let flag = false
  await dataFormRef.value?.validate(valid => {
    if (valid) {
      flag = true
    }
  })
  if (flag) {
    dataForm.value.type = props.type
    return dataForm.value
  } else {
    return null
  }
}

const onPointTypeChange = async (val) => {
  dataForm.value.warehouseType = ''
  dataForm.value.stockMode = ''
  dataForm.value.stockPointId = ''
  dataFormRef.value.clearValidate('stockPointId')
  pointOptions.value = await onGetPointList(val, true)
}

const onPointIdChange = async (val) => {
  if (dataForm.value.stockPointType === 1) {
    dataForm.value.warehouseType = warehouseList[val].type
  } else {
    dataForm.value.stockMode = stationList[val].stockMode
  }
  // 选择的库存点改变，则清空选择的商品，并将库存点信息传递到商品选择组件
  emit('changeStockPointId', {
    warehouseType: dataForm.value.warehouseType,
    stockPointId: dataForm.value.stockPointId,
    stockMode: dataForm.value.stockMode,
    stockPointType: dataForm.value.stockPointType
  })
}

const warehouseList = {}
const stationList = {}
/**
 * 获取库存点列表
 * @param {*} val 库存点类型 typeOptions
 * @param {*} flag 是否需要清空sku-info组件的信息
 */
const onGetPointList = (val, flag = false) => {
  return new Promise((resolve) => {
    if (val === 2) {
      http({
        url: http.adornUrl('/admin/station/list_station'),
        method: 'get'
      }).then(({ data }) => {
        const list = data.map((item) => {
          stationList[item.stationId] = item
          return {
            value: item.stationId,
            label: item.stationName
          }
        })
        if (flag) {
          emit('changeStockPointId', {
            warehouseType: '',
            stockPointId: '',
            stockMode: '',
            stockPointType: dataForm.value.stockPointType
          })
        }
        resolve(list)
      })
    } else {
      http({
        url: http.adornUrl('/m/warehouse/list_warehouse'),
        method: 'get'
      }).then(({ data }) => {
        const list = []
        data.forEach((item) => {
          warehouseList[item.warehouseId] = item
          if (item.type === 0 && !val) {
            // 仓库默认选择默认仓库
            dataForm.value.warehouseType = item.type
            dataForm.value.stockPointId = item.warehouseId
            list.unshift({
              value: item.warehouseId,
              label: item.warehouseName
            })
            return
          }
          list.push({
            value: item.warehouseId,
            label: item.warehouseName
          })
        })
        if (flag) {
          emit('changeStockPointId', {
            warehouseType: dataForm.value.warehouseType,
            stockPointId: dataForm.value.stockPointId,
            stockMode: dataForm.value.stockMode,
            stockPointType: dataForm.value.stockPointType
          })
        }
        resolve(list)
      })
    }
  })
}

defineExpose({ verifyDataForm })
</script>

<style lang="scss" scoped>

.component-stock-bill-basic-info.basicInfo-container {
  .title {
    color: #333333;
    font-size: 16px;
    font-weight: bold;
  }
  .el-date-picker {
    width: 200px;
  }
  :deep(.el-select) {
    width: 200px;
  }
  .remark {
    width: 30%;
  }
  .upload-tips {
    font-size: 12px;
    color: #999;
    height: 16px;
    line-height: 20px;
    margin-top: 13px;
  }
  .create-refresh-btn {
    display: inline-block;
    margin-left: 10px;
    & :deep(.el-divider--vertical) {
      margin: 0 2px;
    }
  }
}
// 刷新 | 新建

</style>
