<template>
  <div>
    <div class="search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="_searchForm"
        label-width="auto"

        @submit.prevent
      >
        <!-- 表单项 -->
        <div class="input-row">
          <el-form-item :label="$t('takeStock.productFilter')">
            <el-select
              v-model="_searchForm.prodKeyType"
              @change="prodKeyTypeChange"
            >
              <el-option
                v-for="node in prodKeyArr"
                :key="node.key"
                :label="node.label"
                :value="node.key"
              />
            </el-select>
            <el-input
              v-model="_searchForm.prodKey"
              type="text"
              clearable
              :placeholder="prodKeyArr[_searchForm.prodKeyType - 1].inputTips"
            />
          </el-form-item>
        </div>
        <div class="input-row">
          <el-form-item :label="$t('stock.createOrderTime')">
            <el-date-picker
              v-model="createDateRange"

              type="datetimerange"
              clearable
              :range-separator="$t('time.tip')"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              value-format="YYYY-MM-DD HH:mm:ss"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
              @change="createTimeChange"
            />&nbsp;
            <div
              class="default-btn"
              style="margin-left: 20px;"
              :class="{ 'is-active': createTimeActive === 1 }"
              @click="setDateRange(1, 'createTime')"
            >
              {{
                $t("time.t")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': createTimeActive === 2 }"
              @click="setDateRange(2, 'createTime')"
            >
              {{
                $t("time.y")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': createTimeActive === 3 }"
              @click="setDateRange(3, 'createTime')"
            >
              {{
                $t("time.n")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': createTimeActive === 4 }"
              @click="setDateRange(4, 'createTime')"
            >
              {{
                $t("temp.m")
              }}
            </div>
          </el-form-item>
        </div>
        <div class="input-row">
          <el-form-item :label="type === 1 ? $t('stock.outStockTime') : $t('stock.inStockTime')">
            <el-date-picker
              v-model="businessDateRange"
              type="datetimerange"
              clearable
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              :start-placeholder="$t('time.start')"
              :end-placeholder="$t('time.end')"
              @change="businessTimeChange"
            />&nbsp;
            <div
              class="default-btn"
              style="margin-left: 20px;"
              :class="{ 'is-active': businessTimeActive === 1 }"
              @click="setDateRange(1, 'businessTime')"
            >
              {{
                $t("time.t")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': businessTimeActive === 2 }"
              @click="setDateRange(2, 'businessTime')"
            >
              {{
                $t("time.y")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': businessTimeActive === 3 }"
              @click="setDateRange(3, 'businessTime')"
            >
              {{
                $t("time.n")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': businessTimeActive === 4 }"
              @click="setDateRange(4, 'businessTime')"
            >
              {{
                $t("temp.m")
              }}
            </div>
          </el-form-item>
        </div>
        <div class="input-row">
          <el-form-item :label="type === 1 ? $t('stock.outStockOrderNo') : $t('stock.inStockOrderNo')">
            <el-input
              v-model="_searchForm.stockBillNo"
              type="text"
              clearable
              :placeholder="type === 1 ? $t('stock.outStockOrderNo') : $t('stock.inStockOrderNo')"
            />
          </el-form-item>
          <el-form-item :label="$t('stock.sourceOrderNo')">
            <el-input
              v-model="_searchForm.sourceOrderNo"
              type="text"
              clearable
              :placeholder="$t('stock.sourceOrderNo')"
            />
          </el-form-item>
        </div>
        <div class="input-row">
          <el-form-item :label="$t('stock.stockBillType')">
            <el-select
              v-model="_searchForm.stockBillType"
              filterable
              clearable
            >
              <el-option
                v-for="node in businessDetailType"
                :key="node.stockBillType"
                :label="node.label"
                :value="node.stockBillType"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('takeStock.maker')">
            <el-select
              v-model="_searchForm.employeeId"
              filterable
              clearable
            >
              <el-option
                v-for="node in employeeList"
                :key="node.employeeId"
                :label="node.nickname"
                :value="node.employeeId"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="input-row">
          <el-form-item>
            <div class="text-btn-con">
              <div
                class="default-btn primary-btn"
                @click="onSearch"
              >
                {{ $t('crud.searchBtn') }}
              </div>
              <div
                v-if="(isAuth('multishop:receiveStock:export') && type === 2) || (isAuth('multishop:sendStock:export') && type === 1)"
                class="default-btn primary-btn"
                @click="exportStockLog"
              >
                {{ $t('order.ExportingFiles') }}
              </div>
              <div
                class="default-btn"
                @click="resetForm()"
              >
                {{ $t('shop.resetMap') }}
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import moment from 'moment'
import { isAuth } from '@/utils'

const emit = defineEmits(['searchChange', 'exportStockLog', 'update:searchForm'])
const props = defineProps({
  searchForm: {
    default: () => ({
      prodKeyType: 1, // 1：商品名称 2：商品编码
      prodKey: '', // 搜索商品关键词
      stockBillNo: '', // 单据编号
      sourceOrderNo: '', // 关联单号
      stockBillType: '', // 单据类型
      employeeId: '', // 制单员工id
      createStartTime: '', // 制单起始时间
      createEndTime: '', // 制单结束时间
      businessStartTime: '', // 出入库起始时间
      businessEndTime: '', // 出入库结束时间
      type: 0 // 1 出库 2 入库
    }),
    type: Object
  },
  type: {
    default: 0,
    type: Number
  }
})
const _searchForm = computed({
  get () {
    return props.searchForm
  },
  set (val) {
    emit('update:searchForm', val)
  }
})
const createTimeActive = ref(0)
const businessTimeActive = ref(0)
const stockBillTypeInStrArr = [
  {
    stockBillType: 1, label: $t('stock.purchaseInStock')
  },
  {
    stockBillType: 2, label: $t('stock.returnToStorage')
  },
  {
    stockBillType: 3, label: $t('stock.otherEntries')
  },
  {
    stockBillType: 7, label: $t('stock.inventoryInitialization')
  },
  {
    stockBillType: 8, label: $t('stock.orderCancelled')
  },
  {
    stockBillType: 9, label: $t('stock.editStorage')
  },
  {
    stockBillType: 10, label: $t('stock.profitStorage')
  },
  {
    stockBillType: 12, label: $t('stock.transferWarehouse')
  },
  {
    stockBillType: 15, label: $t('stock.inventoryModeSwitchesInInventory')
  },
  {
    stockBillType: 16, label: $t('stock.secondKillReplenishmentStock')
  }
]
const stockBillTypeOutStrArr = [
  {
    stockBillType: 4, label: $t('stock.sellOut')
  },
  {
    stockBillType: 5, label: $t('stock.editOutBound')
  },
  {
    stockBillType: 6, label: $t('stock.otherOutbound')
  },
  {
    stockBillType: 11, label: $t('stock.lossOutBound')
  },
  {
    stockBillType: 13, label: $t('stock.transferOutWarehouse')
  },
  {
    stockBillType: 14, label: $t('stock.inventoryModeSwitchesOutInventory')
  },
  {
    stockBillType: 17, label: $t('stock.voucherExpire')
  }
]
const employeeList = ref([]) // 员工列表
const prodKeyArr = [
  { key: 1, label: $t('product.prodName'), inputTips: $t('takeStock.inputName') },
  { key: 2, label: $t('product.commodityCode'), inputTips: $t('takeStock.inputPartyCode') }
] // 商品筛选类型
const businessDetailType = ref([]) // 单据类型
const createDateRange = ref([]) // 制单时间范围
const businessDateRange = ref([]) // 入库时间范围

onMounted(() => {
  init()
})

const init = () => {
  _searchForm.value.type = props.type
  businessDetailType.value = props.type === 1 ? stockBillTypeOutStrArr : stockBillTypeInStrArr
  // 获取员工列表
  getEmployeeList()
}
const getEmployeeList = () => {
  http({
    url: http.adornUrl('/sys/shopEmployee/list'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    employeeList.value = data
  })
}
const onSearch = () => {
  // 发送搜索事件
  emit('searchChange')
}
const exportStockLog = () => {
  // 发送导出事件
  emit('exportStockLog')
}
const resetForm = () => {
  _searchForm.value.prodKeyType = 1
  _searchForm.value.prodKey = ''
  _searchForm.value.stockBillNo = null
  _searchForm.value.sourceOrderNo = ''
  _searchForm.value.stockBillType = ''
  _searchForm.value.employeeId = ''
  _searchForm.value.createStartTime = ''
  _searchForm.value.createEndTime = ''
  _searchForm.value.businessStartTime = ''
  _searchForm.value.businessEndTime = ''
  createTimeActive.value = 0
  businessTimeActive.value = 0

  createDateRange.value = []
  businessDateRange.value = []
}
const prodKeyTypeChange = () => {
  _searchForm.value.prodKey = ''
}
const createTimeChange = () => {
  if (!createDateRange.value || createDateRange.value.length === 0) {
    _searchForm.value.createStartTime = null
    _searchForm.value.createEndTime = null
  } else {
    _searchForm.value.createStartTime = createDateRange.value[0]
    _searchForm.value.createEndTime = createDateRange.value[1]
  }
}
const businessTimeChange = () => {
  if (!businessDateRange.value || businessDateRange.value.length === 0) {
    _searchForm.value.businessStartTime = null
    _searchForm.value.businessEndTime = null
  } else {
    _searchForm.value.businessStartTime = businessDateRange.value[0]
    _searchForm.value.businessEndTime = businessDateRange.value[1]
  }
}
/**
 * 根据选项设置时间
 * 1:今天 2:昨天 3: 近七天 4:近30天 5:近60天
 */
const setDateRange = (val, type) => {
  let startDay = null
  let endDay = null
  if (val === 1) {
    startDay = 0
    endDay = 0
  } else if (val === 2) {
    startDay = -1
    endDay = -1
  } else if (val === 3) {
    startDay = -7
    endDay = -1
  } else if (val === 4) {
    startDay = -30
    endDay = -1
  } else {
    return
  }
  // 开始时间
  const startTime = moment().add(startDay, 'days').startOf('days').format('LL')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days').format('LL')
  if (type === 'createTime') {
    createDateRange.value = [startTime, endTime]
    _searchForm.value.createStartTime = startTime
    _searchForm.value.createEndTime = endTime
    createTimeActive.value = val
  } else if (type === 'businessTime') {
    businessDateRange.value = [startTime, endTime]
    _searchForm.value.businessStartTime = startTime
    _searchForm.value.businessEndTime = endTime
    businessTimeActive.value = val
  }
}

</script>

<style lang="scss" scoped>
:deep(.select-time-btn) {
  margin: 0 12px;
  display: inline-block;
  color: #AAAAAA;
  font-size: 14px;
  cursor:pointer;
}
:deep(.el-form-item) {
  .is-active {
    color: #155BD4;
  }
}
</style>
