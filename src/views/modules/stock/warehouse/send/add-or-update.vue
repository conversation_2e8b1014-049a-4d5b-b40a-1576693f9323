<template>
  <div
    :ref="(el)=>{containerRef=el}"
    class="container"
  >
    <div class="content-container">
      <div class="new-page-title">
        <div class="line" />
        <div class="text">
          {{ $t('stock.newOutboundDetails') }}
        </div>
      </div>
      <basicInfo
        ref="basicInfoRef"
        :type="dataForm.type"
        :stock-bill-log-id="stockBillLogId"
        @change-stock-point-id="onChangeStockPointId"
      />
      <skuListInfo
        ref="skuListInfoRef"
        :required="true"
        :type="dataForm.type"
        :stock-bill-log-id="stockBillLogId"
        :stock-bill-log-info="stockBillLogInfo"
      />
    </div>
    <div
      class="foot-btn"
      :style="{left,width}"
    >
      <div
        class="default-btn primary-btn"
        @click="confirmSave(1)"
      >
        {{ $t('stock.confirmDelivery') }}
      </div>
      <div
        class="default-btn"
        @click="confirmSave(2)"
      >
        {{ $t('stock.saveDraft') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import basicInfo from '../components/stock-bill-basic-info.vue'
import skuListInfo from '../components/stock-bill-sku-info.vue'
const Route = useRoute()
const Router = useRouter()
let isSubmit = false
const stockBillLogId = Route.query.stockBillLogId // 出入明细id
const dataForm = reactive({
  businessTime: '', // 出库时间
  stockChangeReasonId: '', // 出库原因
  remark: '', // 备注
  qualifications: '', // 出库凭证
  stockBillLogItems: [], // 商品项
  type: 1, // 1 出库 2 入库
  stockBillType: 6
})
const type = ref(1) // 1：出库 2：入库
const stockBillLogInfo = ref({
  stockPointType: 1,
  stockPointId: ''
})

const basicInfoRef = ref(null)
const skuListInfoRef = ref(null)
const confirmSave = async (status) => {
  const dataForm = await basicInfoRef.value?.verifyDataForm()
  const stockBillLogItems = skuListInfoRef.value?.verifyDataForm()
  if (!dataForm || !stockBillLogItems) {
    // eslint-disable-next-line require-atomic-updates
    isSubmit = false
    return
  }
  if (isSubmit) {
    return
  }
  isSubmit = true
  dataForm.stockBillLogItems = stockBillLogItems
  dataForm.status = status
  dataForm.type = type
  dataForm.stockBillLogId = stockBillLogId
  http({
    url: http.adornUrl('/shop/stockBillLog'),
    method: dataForm.stockBillLogId ? 'put' : 'post',
    data: http.adornData(dataForm)
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1500,
      onClose: () => {
        isSubmit = false
        Router.push({
          path: '/stock/warehouse/send/list'
        })
      }
    })
  }).catch(() => {
    isSubmit = false
  })
}
let containerRef
const width = ref('')
const left = ref('')
const resizeHander = debounce(() => {
  try {
    left.value = (containerRef?.getBoundingClientRect().left - 20) + 'px'
    width.value = (containerRef?.clientWidth + 40) + 'px'
  } catch (e) {
    left.value = 'auto'
    width.value = '100%'
  }
}, 50)
onMounted(() => {
  resizeHander()
  window.addEventListener('resize', resizeHander)
  window.addEventListener('scroll', resizeHander)
})
onUnmounted(() => {
  window.removeEventListener('resize', resizeHander)
  window.removeEventListener('scroll', resizeHander)
})

// 防抖函数
function debounce (fn, delay = 50) {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(fn, delay)
  }
}

// 更换库存点后，删除选择的商品 data: 库存点信息；retainProductItem true：保留商品信息 false：清空商品信息
const onChangeStockPointId = (data, retainProductItem) => {
  skuListInfoRef.value?.onResetData(data, retainProductItem)
}

</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  .content-container {
    padding-bottom: 60px;
  }
  /* 脚部按钮 */
  .foot-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 15px 0;
    display: flex;
    justify-content: center;
    background: #fff;
    // border-top: 1px solid #ddd;
    box-shadow: 0 -2px 3px rgba(139, 139, 139, 0.1);
    z-index: 10;
  }
}

</style>
