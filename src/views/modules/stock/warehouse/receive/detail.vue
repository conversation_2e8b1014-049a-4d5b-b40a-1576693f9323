<template>
  <div class="page-stock-warehouse-receive-detail container">
    <span class="title">{{ $t('product.basicInformation') }}</span>
    <el-divider />
    <div class="basic-container">
      <div class="basic-row">
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.inStockOrderNo') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.stockBillNo || '-' }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.billStatus') }}:
          </p>
          <p class="txt">
            {{ statusFilter( stockBillLogInfo.status) }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.stockPointType') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.stockPointType === 1? $t('stock.warehouse'): $t('stock.station') }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.createOrderRemark') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.remark || '-' }}
          </p>
        </div>
      </div>
      <div class="basic-row">
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('takeStock.maker') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.makerName || '-' }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.createOrderTime') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.createTime || '-' }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.stockPointName') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.stockPointName || '-' }}
          </p>
        </div>
      </div>
      <div class="basic-row">
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.sourceOrderNo') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.sourceOrderNo || '-' }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.inStockTime') }}:
          </p>
          <p class="txt">
            {{ stockBillLogInfo.businessTime || '-' }}
          </p>
        </div>
        <div class="basic-row-item">
          <p class="sub-tit">
            {{ $t('stock.stockBillType') }}:
          </p>
          <p class="txt">
            {{ typeFilter(stockBillLogInfo.stockBillType) }}{{ stockBillLogInfo.reason ? '-' + stockBillLogInfo.reason : '' }}
          </p>
        </div>
      </div>
    </div>
    <div style="height: 20px" />
    <span class="title">{{ $t('stock.appendix') }}</span>
    <el-divider />
    <div class="qualifications-container">
      <span class="sub-tit">{{ $t('stock.warehousingCertificate') }}:</span>
      <div v-if="imgs.length">
        <el-image
          v-for="(img,index) in imgs"
          :key="index"
          class="tab-img"
          :src="checkFileUrl(img)"
          :preview-src-list="checkFileUrl(imgs)"
        />
      </div>
      <span v-if="!imgs.length">--</span>
    </div>
    <div style="height: 20px" />
    <span class="title">{{ $t('group.prodInfo') }}</span>
    <el-divider />
    <div class="prod-container">
      <div class="sub-tit">
        {{ $t('order.total') }}{{ stockBillLogItems.length }}{{ $t('stock.prodCountTips') }}:{{ totalCount }},{{ $t('stock.totalInAmount2') }}:{{ totalAmount }}
      </div>
      <div class="prodItem-table">
        <el-table
          :data="stockBillLogItems.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
          header-cell-class-name="table-header"
          row-class-name="table-row"
        >
          <el-table-column
            :label="$t('group.prodInfo')"
            prop="reason"
            fixed="left"
            align="center"
            width="320px"
          >
            <template #default="scope">
              <div class="prod-info-container">
                <div class="prod-image">
                  <ImgShow :src="scope.row.pic" />
                </div>
                <div class="prod-name">
                  <el-tooltip
                    :content="scope.row.prodName"
                    placement="top"
                    effect="light"
                  >
                    <div class="prod-name-txt">
                      {{ scope.row.prodName }}
                    </div>
                  </el-tooltip>
                  <el-tooltip
                    :content="scope.row.skuName"
                    placement="top"
                    effect="light"
                  >
                    <div class="spec">
                      {{ scope.row.skuName }}
                    </div>
                  </el-tooltip>
                  <div class="prod-no">
                    {{ scope.row.partyCode }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.unit')"
            prop="type"
            align="center"
          >
            <template #default>
              <span class="table-cell-text line-clamp-one">{{ $t('stock.pieces') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.warehousingCount')"
            prop="type"
            align="center"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.stockCount || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('product.sellingPrice')"
            prop="type"
            align="center"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.price || scope.row.unitPrice || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.costAmount')"
            prop="type"
            align="center"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ stockBillLogInfo.stockBillType ===1? scope.row.unitPrice: '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="type"
            align="center"
          >
            <template #header>
              <div class="total-in-amount">
                <span>{{ $t('stock.totalInAmount') }}</span>
                <el-tooltip
                  effect="dark"
                  :content="$t('stock.totalInAmountTips')"
                  placement="right"
                >
                  <el-icon><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.totalAmount || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="stockBillLogItems.length"
          @current-change="onPageChange"
        />
      </div>
    </div>
    <div
      v-if="stockBillLogInfo.status === 2"
      class="foot-btn"
    >
      <el-button
        type="primary"
        @click="editHandle"
      >
        {{ $t('text.editBtn') }}
      </el-button>
      <el-button @click="voidedHandle">
        {{ $t('takeStock.voidInventory') }}
      </el-button>
    </div>
    <!-- 作废提示对话框 -->
    <el-dialog
      v-model="voidedVisibleDialog"
      :title="$t('text.tips')"
    >
      <div>
        {{ $t('stock.voidInventoryTips') }}
      </div>
      <template #footer>
        <span

          class="dialog-footer"
        >
          <el-button @click="voidedVisibleDialog = false">{{ $t('crud.filter.cancelBtn') }}</el-button>
          <el-button
            type="primary"
            @click="confirmVoided"
          >{{ $t('crud.filter.submitBtn') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import Big from 'big.js'
import { ElMessage } from 'element-plus'
const Route = useRoute()
const Router = useRouter()

const statusFilter = (val) => {
  switch (val) {
    case 0:
      return $t('stock.voided')
    case 1:
      return $t('stock.inStorage')
    case 2:
      return $t('stock.waitSubmit')
  }
}
const typeFilter = (val) => {
  switch (val) {
    case 1:return $t('stock.purchaseInStock')
    case 2:return $t('stock.returnToStorage')
    case 3:return $t('stock.otherEntries')
    case 7:return $t('stock.inventoryInitialization')
    case 8:return $t('stock.orderCancelled')
    case 9:return $t('stock.editStorage')
    case 10:return $t('stock.profitStorage')
    case 12:return $t('stock.transferWarehouse')
    case 15:return $t('stock.inventoryModeSwitchesInInventory')
    case 16:return $t('stock.secondKillReplenishmentStock')
  }
}

let isSubmit = false
let stockBillLogId = null
const stockBillLogInfo = ref({})
const stockBillLogItems = ref([])
const totalCount = ref('')
const totalAmount = ref('')
const imgs = ref([])
const page = reactive({
  currentPage: 1, // 初始页
  pageSize: 10 // 每页数据大小
})
const voidedVisibleDialog = ref(false)

onMounted(() => {
  stockBillLogId = Route.query.stockBillLogId
  init()
})

const init = () => {
  getStockBillInfo()
  getSkuList()
}
const getStockBillInfo = () => {
  http({
    url: http.adornUrl('/shop/stockBillLog/info/' + stockBillLogId),
    method: 'get',
    params: http.adornParams(
      {
        stockBillLogId
      }
    )
  }).then(({ data }) => {
    imgs.value = data.qualifications ? data.qualifications.split(',') : []
    imgs.value = imgs.value.map(item => {
      return checkFileUrl(item)
    })
    stockBillLogInfo.value = data
    totalAmount.value = data.totalAmount
  })
}
const getSkuList = () => {
  http({
    url: http.adornUrl('/shop/stockBillLogItem/list'),
    method: 'get',
    params: http.adornParams(
      {
        stockBillLogId
      }
    )
  }).then(({ data }) => {
    stockBillLogItems.value = data
    let _totalCount = 0
    // let totalAmount = 0
    stockBillLogItems.value.forEach(item => {
      _totalCount += parseInt(item.stockCount)
      item.totalAmount = bigProductTotalAmount(item.unitPrice, item.stockCount)
      // totalAmount += parseInt(item.totalAmount)
    })
    totalCount.value = _totalCount
    // totalAmount = totalAmount
  })
}
const editHandle = () => {
  Router.push({
    path: '/stock/warehouse/receive/add-or-update',
    query: {
      type: 2,
      stockBillLogId
    }
  })
}
const confirmVoided = () => {
  if (isSubmit) {
    return
  }
  isSubmit = true
  http({
    url: http.adornUrl('/shop/stockBillLog/voided'),
    method: 'put',
    params: http.adornParams(
      { stockBillLogId }
    )
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1000
    })
    voidedVisibleDialog.value = false
    isSubmit = false
    getStockBillInfo()
  }).catch(() => {
    voidedVisibleDialog.value = false
    isSubmit = false
  })
}
const voidedHandle = () => {
  voidedVisibleDialog.value = true
}
const onPageChange = (val) => {
  page.currentPage = val
}
// 精度运算-乘法
const bigProductTotalAmount = (a, b) => {
  return new Big(a).times(b)
}

</script>

<style lang="scss" scoped>
.page-stock-warehouse-receive-detail.container {
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  width: 100%;
  padding-bottom: 80px;
  .title {
    color: #333333;
    font-size: 14px;
    font-weight: bold;
  }
  .basic-container {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    .basic-row {
      display: flex;
      width:33%;
      flex-direction: column;
      .basic-row-item {
        height: 50px;
        display: flex;
        flex-direction: row;
        .sub-tit {
          min-width: 80px;
          font-size: 14px;
          color: #333333;
        }
        .txt {
          font-size: 14px;
          color: #333333;
          word-break: break-word;
        }
      }
    }
  }
  .qualifications-container {
    display: flex;
    flex-direction: row;
    .sub-tit {
      font-size: 14px;
      color: #333333;
      margin-right: 10px;
    }
    .tab-img {
      width: 60px;
      height: 60px;
      margin-right: 5px;
      cursor: pointer;
    }
  }
  .prod-container {
    .sub-tit {
      font-size: 14px;
      color: #333333;
      margin-bottom: 16px;
    }
    .prodItem-table {
      .prod-info-container {
        height: 100%;
        display: flex;
        justify-content: space-between;
        .prod-image {
          margin-right: 20px;
          width: 80px;
          height: 80px;
          :deep(img) {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        .prod-name {
          flex: 1;
          height: 80px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          .prod-name-txt {
            font-size: 14px;
            color: #333333;
            word-break: break-all;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
          }
          .spec {
            font-size: 14px;
            color: #333333;
            word-break: break-all;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
          }
          .prod-no {
            font-size: 14px;
            color: #333333;
          }
        }
      }
      .total-in-amount{
        display: flex;
        align-items: center;
        justify-content: center;
        // eslint-disable-next-line vue-scoped-css/no-unused-selector
        .el-icon{
          margin-left: -5px;
        }
      }
    }
  }
  /* 脚部按钮 */
  .foot-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 15px 0;
    display: flex;
    justify-content: center;
    background: #fff;
    // border-top: 1px solid #ddd;
    box-shadow: 0 -2px 3px rgba(139, 139, 139, 0.1);
    z-index: 1999;
  }
}
</style>
