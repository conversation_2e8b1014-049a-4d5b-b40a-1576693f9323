<template>
  <div class="page-stock-purchases-order-new mod-groupActivity">
    <div class="new-page-title">
      <div class="line" />
      <div class="text">
        {{
          view ? $t('purchase.order.viewPurchaseOrder') :
          !dataForm.purchaseOrderId
            ? $t('purchase.order.newPurchaseOrder')
            : $t('purchase.order.purchaseOrderInbound')
        }}
      </div>
    </div>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="auto"
      class="form-box"
      @submit.prevent
    >
      <!-- 库存点类型（新建） -->
      <el-form-item
        v-if="!view && !dataForm.purchaseOrderId"
        :label="$t('stock.stockPointType')"
      >
        <el-radio-group
          v-model="pointType"
          @change="onPointTypeChange"
        >
          <el-radio :label="0">
            {{ $t('stock.warehouse') }}
          </el-radio>
          <el-radio :label="1">
            {{ $t('stock.station') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 库存点名称（新建） -->
      <el-form-item
        v-if="!view && !dataForm.purchaseOrderId"
        :label="$t('stock.stockPointName')"
        prop="warehouseId"
      >
        <el-select
          v-model="dataForm.warehouseId"
          :placeholder="$t('stock.pleaseSelect')"
          class="groupActivity-input"
          @change="onChangeWarehouse"
        >
          <el-option
            v-for="item in pointOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 库存点类型（展示） -->
      <el-form-item
        v-if="dataForm.purchaseOrderId"
        :label="$t('stock.stockPointType')"
      >
        {{ dataForm.stockPointType===1 ? $t('stock.warehouse') : $t('stock.station') }}
      </el-form-item>
      <!-- 库存点名称（展示） -->
      <el-form-item
        v-if="dataForm.purchaseOrderId"
        :label="$t('stock.stockPointName')"
      >
        {{ dataForm.warehouseName || '-' }}
      </el-form-item>
      <!-- 供应商 -->
      <el-form-item
        :label="$t('purchase.order.supplier')"
        prop="supplierName"
      >
        <div
          v-if="!dataForm.supplierName"
          class="default-btn"
          @click="selectSupplier"
        >
          {{
            $t("product.chooseSupplier")
          }}
        </div>
        <el-tag
          v-if="dataForm.supplierName"
          :closable="!dataForm.purchaseOrderId"
          @close="handleClose()"
        >
          {{
            dataForm.supplierName
          }}
        </el-tag>
      </el-form-item>
      <!-- 送达时间 -->
      <el-form-item
        :label="$t('purchase.order.deliverTime')"
        prop="deliverTime"
      >
        <el-date-picker
          v-model="dataForm.deliverTime"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="$t('admin.seleData')"
          :disabled-date="dataForm.purchaseOrderId ? inboundDisabledDate : purchaseDisabledDate"
          :disabled="view"
        />
      </el-form-item>
      <!-- 备注 -->
      <el-form-item
        :label="$t('purchase.order.remark')"
        prop="startTime"
      >
        <el-input
          v-model="dataForm.remark"
          :placeholder="$t('purchase.order.remark')"
          maxlength="50"
          show-word-limit
          type="textarea"
          class="groupActivity-input"
          :disabled="view"
        />
      </el-form-item>
      <!-- 配送类型 -->
      <el-form-item
        :label="$t('product.delType')"
        prop="startTime"
      >
        <el-radio
          v-model="dataForm.dvyType"
          :label="1"
          :disabled="!!dataForm.purchaseOrderId"
        >
          {{ $t('order.expressDelivery') }}
        </el-radio>
        <el-radio
          v-model="dataForm.dvyType"
          :label="3"
          :disabled="!!dataForm.purchaseOrderId"
        >
          {{ $t('order.noNeedRequired') }}
        </el-radio>
      </el-form-item>
      <el-form-item
        v-if="dataForm.dvyType === 1"
        :label="$t('order.logisticsCompany')"
        prop="dvyId"
      >
        <select-lazy
          v-model="dataForm.dvyId"
          class="groupActivity-input"
          :place-tips="$t('order.seleCouCom')"
          :disabled="!!dataForm.purchaseOrderId"
        />
      </el-form-item>
      <!-- 物流编号 -->
      <el-form-item
        v-if="dataForm.dvyType === 1"
        :label="$t('order.logisticsNumber')"
        prop="dvyFlowId"
      >
        <el-input
          v-model="dataForm.dvyFlowId"
          :placeholder="$t('order.entCouNum')"
          maxlength="30"
          show-word-limit
          :disabled="!!dataForm.purchaseOrderId"
          class="groupActivity-input"
        />
      </el-form-item>
      <!-- 选择商品 -->
      <el-form-item
        v-if="!view && !dataForm.purchaseOrderId"
        required
        :label="$t('home.product')"
      >
        <el-button
          plain
          :disabled="!dataForm.supplierId"
          @click="selectSupplierProd"
        >
          {{ $t("product.select") }}
        </el-button>
        <el-button
          plain
          :disabled="!dataForm.supplierId"
          @click="getUpload"
        >
          {{ $t("product.importGoods") }}
        </el-button>
      </el-form-item>
      <!-- 选择供应商商品列表（新增时使用） -->
      <el-form-item v-if="!view">
        <span v-if="prods.length > 0 && !dataForm.purchaseOrderId">
          {{ $t('order.amountOfGoods') + '：' + dataForm.totalStock + ', ' +
            $t('order.totalPrice') + '：' + dataForm.totalAmount
          }}
        </span>
        <el-table
          v-if="prods.length > 0 && !dataForm.purchaseOrderId"
          :data="prods.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
          style="width: 100%"
        >
          <el-table-column
            label=""
            width="300"
          >
            <template #default="scope">
              <div class="mod-order-order">
                <div class="item">
                  <div class="prod-image">
                    <ImgShow
                      :src="scope.row.pic"
                      :img-style="{width:'60px',height:'60px'}"
                    />
                  </div>
                  <div class="prod-name">
                    <div class="item">
                      {{ scope.row.prodName }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 商品编码 -->
          <el-table-column
            :label="$t('product.commodityCode')"
            min-width="180"
          >
            <template #default="scope">
              <span>{{ scope.row.partyCode }}</span>
            </template>
          </el-table-column>
          <!-- 规格 -->
          <el-table-column
            :label="$t('groups.sku')"
            width="100"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.skuName || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 库存 -->
          <el-table-column
            :label="$t('coupon.stock')"
            width="100"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.stocks || '0' }}</span>
            </template>
          </el-table-column>
          <!-- 库存 -->
          <el-table-column
            :label="$t('stock.currentWarehouseInventory')"
            width="100"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.inStock || '0' }}</span>
            </template>
          </el-table-column>
          <!-- 采购数量 -->
          <el-table-column
            :label="$t('purchase.order.purchaseNum')"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.purchaseStock"

                @change="changStock(scope.row)"
              >
                <template #suffix>
                  <el-tooltip

                    class="item"
                    effect="dark"
                    :content="$t('purchase.order.purchaseNumThanMinimum')"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </template>
          </el-table-column>
          <!-- 采购价 -->
          <el-table-column
            :label="$t('purchase.order.purchasePrice')"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.purchasePrice"

                @change="changPrice(scope.row)"
              >
                <template #suffix>
                  <el-tooltip

                    class="item"
                    effect="dark"
                    :content="$t('purchase.order.purchasePriceMustThen0')"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </template>
          </el-table-column>
          <!-- 采购金额 -->
          <el-table-column
            :label="$t('purchase.order.purchaseAmount')"
          >
            <template #default="scope">
              <span>{{ scope.row.purchaseAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('crud.menu')">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.$index)"
              >
                {{ $t('text.delBtn') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <!-- 入库商品列表（入库时使用） -->
      <el-form-item
        v-if="!view && dataForm.purchaseOrderId"
        :label="$t('home.product')"
        prop="startTime"
      >
        <el-button
          plain
          @click="getInboundUpload"
        >
          {{ $t("purchase.order.importInboundProd") }}
        </el-button>
      </el-form-item>
      <el-form-item
        v-if="!view && dataForm.purchaseOrderId"
        prop="startTime"
      >
        <el-table
          v-if="prods.length > 0"
          :data="prods.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
          style="width: 100%"
        >
          <el-table-column
            label=""
            width="300"
          >
            <template #default="scope">
              <div class="mod-order-order">
                <div class="item">
                  <div class="prod-image">
                    <ImgShow
                      :src="scope.row.pic"
                      :img-style="{width:'60px',height:'60px'}"
                    />
                  </div>
                  <div class="prod-name">
                    <div class="item">
                      {{ scope.row.prodName }}
                    </div>
                    <div class="item">
                      {{ scope.row.skuName }}
                    </div>
                    <div class="item">
                      {{ scope.row.partyCode }}
                    </div>
                    <div
                      v-if="scope.row.isDelete"
                      class="item order-status"
                    >
                      {{ $t("purchase.order.deleted") }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 采购金额 -->
          <el-table-column
            :label="$t('purchase.order.purchaseAmount')"
            width="180"
          >
            <template #default="scope">
              <span>{{ scope.row.purchaseAmount }}</span>
            </template>
          </el-table-column>
          <!-- 预计入库量 -->
          <el-table-column
            :label="$t('purchase.order.estimatedIncomingQuantity')"
            width="150"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.purchaseStock }}</span>
            </template>
          </el-table-column>
          <!-- 已入库量 -->
          <el-table-column
            :label="$t('purchase.order.numberInStock')"
            width="150"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.alreadyStock }}</span>
            </template>
          </el-table-column>
          <!-- 剩余入库量 -->
          <el-table-column
            :label="$t('purchase.order.remainingIncomingQuantity')"
            width="150"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.remainingStock }}</span>
            </template>
          </el-table-column>
          <!-- 实际入库量 -->
          <el-table-column
            :label="$t('purchase.order.actualIncomingQuantity')"
            width="180"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.actualStock"
                :disabled="!!scope.row.isDelete"
                @change="changActualStock(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <!-- 详情列表（查看详情时使用） -->
      <el-form-item
        v-if="view && dataForm.purchaseOrderId"
        :label="$t('home.product')"
        prop="startTime"
      >
        <span v-if="prods.length > 0 ">
          {{ $t('order.amountOfGoods') + '：' + dataForm.totalStock + ', ' +
            $t('order.totalPrice') + '：' + dataForm.totalAmount
          }}
        </span>
        <el-table
          v-if="prods.length > 0"
          :data="prods.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
          style="width: 100%"
        >
          <el-table-column
            label=""
            width="300"
          >
            <template #default="scope">
              <div class="mod-order-order">
                <div class="item">
                  <div class="prod-image">
                    <ImgShow
                      :src="scope.row.pic"
                      :img-style="{width:'60px',height:'60px'}"
                    />
                  </div>
                  <div class="prod-name">
                    <div class="item">
                      {{ scope.row.prodName }}
                    </div>
                    <!-- <div class="item">
                        {{scope.row.skuName}}
                      </div>
                      <div class="item">
                        {{scope.row.partyCode}}
                      </div> -->
                    <div
                      v-if="scope.row.isDelete"
                      class="item order-status"
                    >
                      {{ $t("purchase.order.deleted") }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 商品规格 -->
          <el-table-column
            :label="$t('product.productSpecifi')"
          >
            <template #default="scope">
              <span> {{ scope.row.skuName || '-' }} </span>
            </template>
          </el-table-column>
          <!-- 商品编码 -->
          <el-table-column
            width="220"
            :label="$t('product.commodityCode')"
          >
            <template #default="scope">
              <span> {{ scope.row.partyCode }} </span>
            </template>
          </el-table-column>
          <!-- 采购数量 -->
          <el-table-column
            :label="$t('purchase.order.purchaseNum')"
          >
            <template #default="scope">
              <span> {{ scope.row.purchaseStock }} </span>
            </template>
          </el-table-column>
          <!-- 采购价 -->
          <el-table-column
            :label="$t('purchase.order.purchasePrice')"
          >
            <template #default="scope">
              <span> {{ scope.row.purchasePrice }} </span>
            </template>
          </el-table-column>
          <!-- 采购金额 -->
          <el-table-column
            :label="$t('purchase.order.purchaseAmount')"
          >
            <template #default="scope">
              <span> {{ scope.row.purchaseAmount }} </span>
            </template>
          </el-table-column>
          <!-- 预计入库量 -->
          <el-table-column
            :label="$t('purchase.order.estimatedIncomingQuantity')"
            width="150"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.purchaseStock }}</span>
            </template>
          </el-table-column>
          <!-- 实际入库量 -->
          <el-table-column
            :label="$t('purchase.order.actualIncomingQuantity')"
          >
            <template #default="scope">
              <span> {{ scope.row.actualStock }} </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item class="pagination-box">
        <el-pagination
          v-if="prods.length"
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="prods.length"
          @current-change="onPageChange"
        />
      </el-form-item>
      <!-- 操作 -->
      <el-form-item>
        <div
          class="default-btn"
          @click="back()"
        >
          {{
            $t("shopFeature.edit.back")
          }}
        </div>
        <div
          v-if="!view"
          class="default-btn primary-btn"
          @click="onSubmit()"
        >
          {{
            dataForm.purchaseOrderId ? $t('purchase.order.inbound') : $t("groups.submit")
          }}
        </div>
      </el-form-item>
    </el-form>

    <!-- 供应商选择弹窗-->
    <supplier-select
      v-if="supplierSelectVisible"
      ref="supplierSelectRef"
      :is-single="true"
      @refresh-select-supplier="supplierSelectHandle"
    />

    <!-- 供应商商品选择弹窗-->
    <supplier-prod-select
      v-if="supplierProdSelectVisible"
      ref="supplierProdSelectRef"
      :type="1"
      :mold-list="[0]"
      :is-show-is-stock="true"
      :search-form="{warehouseId:dataForm.warehouseId}"
      data-url="/supplier/supplierProd/page"
      @refresh-select-supplier="supplierProdSelectHandle"
    />
    <!-- excel -->
    <excel-upload
      v-if="uploadVisible"
      ref="excelUploadRef"
      :model-url="modelUrl"
      :upload-url="uploadUrl"
      :template-name="templateName"
      @refresh-data-list="refreshDataList"
    />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import moment from 'moment'
import Big from 'big.js'
import ExcelUpload from '../components/purchases-prod-upload.vue'
import { Debounce } from '@/utils/debounce'

const uploadVisible = ref(false)
const supplierSelectVisible = ref(false)
const supplierProdSelectVisible = ref(false)
const modelUrl = ref('')
const uploadUrl = ref('')
const templateName = ref('')
const prods = ref([])
const supplierProds = []
const view = ref(false)
// dialogWidth:'895px'
const dataForm = ref({
  purchaseOrderId: '',
  purchaseNumber: '',
  deliverTime: '',
  dvyType: 1,
  dvyId: '',
  dvyFlowId: '',
  supplierId: '',
  purchaseStock: 0,
  purchasePrice: 0,
  totalAmount: 0,
  totalStock: 0,
  actualTotalStock: 0,
  supplierName: null,
  remark: '',
  deliveryExpresses: [],
  warehouseId: '' // 库存点id
})
let isSubmit = false

const page = reactive({
  currentPage: 1, // 初始页
  pageSize: 10 // 每页数据大小
})

const pointType = ref(0)
const validateWarehouseId = (rule, value, callback) => {
  if (value && pointType.value === 0 && defaultWarehouse.warehouseId === value && !(defaultWarehouse.provinceId && defaultWarehouse.cityId && defaultWarehouse.areaId)) {
    callback(new Error($t('stock.defaultWarehouseAddress')))
  } else {
    callback()
  }
}

const purchaseDisabledDate = (time) => {
  const month1 = moment().startOf('day')
  return (
    time.getTime() < month1.valueOf()
  )
}

const inboundDisabledDate = (time) => {
  const month1 = moment().startOf('day')
  return (
    time.getTime() > month1.valueOf()
  )
}

const validateDvyFlowId = (rule, value, callback) => {
  const reg = /^[A-Za-z0-9]+$/
  if (!reg.test(value)) {
    callback(new Error($t('order.checkDvyIdMsg')))
  } else {
    callback()
  }
}
const dataRule = {
  supplierName: [
    { required: true, message: $t('purchase.order.selectSupplier'), trigger: 'blur' }
  ],
  deliverTime: [
    { required: true, message: $t('purchase.order.deliverTimeNotEmpty'), trigger: 'blur' }
  ],
  dvyId: [
    { required: true, message: $t('order.seleCouCom'), trigger: ['blur', 'change'] }
  ],
  dvyFlowId: [
    { required: true, message: $t('order.logEmpty'), trigger: 'blur' },
    { validator: validateDvyFlowId, trigger: 'blur' }
  ],
  warehouseId: [
    { required: true, message: $t('stock.pleaseSelectStockPoint'), trigger: 'blur' },
    { validator: validateWarehouseId, trigger: 'change' }
  ]
}

onMounted(() => {
  init()
})
const commonStore = useCommonStore()
const dataFormRef = ref()
const Route = useRoute()
const Router = useRouter()
const pointOptions = ref([])
const init = async () => {
  dataForm.value.purchaseOrderId = Route.query.purchaseOrderId ? parseInt(Route.query.purchaseOrderId) : null
  view.value = !!Route.query.view
  // 更新菜单路径
  const navTitles = JSON.parse(JSON.stringify(commonStore.selectMenu))
  const title = view.value ? $t('purchase.order.viewPurchaseOrder') : !dataForm.value.purchaseOrderId ? $t('purchase.order.newPurchaseOrder') : $t('purchase.order.purchaseOrderInbound')
  navTitles.splice(navTitles.length - 1, 1, title)
  commonStore.updateSelectMenu(navTitles)
  // 初始化数据
  isSubmit = false
  prods.value = []
  dataFormRef.value?.resetFields()
  if (dataForm.value.purchaseOrderId) {
    http({
      url: http.adornUrl('/purchase/order/info/' + dataForm.value.purchaseOrderId),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      dataForm.value = data
      // dataForm.value.supplierName = data.supplierName
      if (!view.value) {
        data.purchaseProds.forEach(item => {
          item.alreadyStock = item.actualStock
          const remainingStock = item.purchaseStock - item.actualStock
          item.remainingStock = remainingStock < 0 ? 0 : remainingStock
          item.actualStock = item.remainingStock
        })
      }
      prods.value = data.purchaseProds
    })
  } else {
    pointOptions.value = await onGetPointList('')
  }
}
const handleDelete = (index) => {
  const realIndex = (page.currentPage - 1) * page.pageSize + index
  prods.value.splice(realIndex, 1)
  setTotalAmountAndCount()
}
const changActualStock = (row) => {
  const numReg = /^([0-9]|[1-9]\d+)(\.\d*)?$/
  const numRe = new RegExp(numReg)
  if (!numRe.test(row.actualStock) || !row.actualStock) {
    row.actualStock = 0
  }
  if (row.actualStock > 9999999) {
    row.actualStock = 9999999
  }
  row.actualStock = Math.round(row.actualStock)
}
const changStock = (row) => {
  const numReg = /^([0-9]|[1-9]\d+)(\.\d*)?$/
  const numRe = new RegExp(numReg)
  if (!numRe.test(row.purchaseStock) || !row.purchaseStock || row.purchaseStock < row.minOrderQuantity) {
    row.purchaseStock = row.minOrderQuantity
  } else if (row.purchaseStock > 9999999) {
    row.purchaseStock = 9999999
  }
  row.purchaseStock = Math.round(row.purchaseStock)
  row.purchaseAmount = new Big(row.purchaseStock).times(row.purchasePrice)
  setTotalAmountAndCount()
}
const changPrice = (row) => {
  const numReg = /^([0-9]|[1-9]\d+)(\.\d*)?$/
  let purchasePrice = row.purchasePrice
  const numRe = new RegExp(numReg)
  if (!row.purchasePrice || !numRe.test(purchasePrice) || !purchasePrice || purchasePrice < row.price) {
    purchasePrice = row.price
  } else if (purchasePrice > 9999999.99) {
    purchasePrice = 9999999.99
  }
  row.purchasePrice = parseFloat(purchasePrice).toFixed(2)
  row.purchaseAmount = new Big(row.purchaseStock).times(row.purchasePrice)
  setTotalAmountAndCount()
}
const setTotalAmountAndCount = () => {
  let totalAmount = 0
  let totalStock = 0
  prods.value.forEach(item => {
    totalAmount = new Big(totalAmount).plus(item.purchaseAmount)
    totalStock = new Big(totalStock).plus(item.purchaseStock)
  })
  dataForm.value.totalAmount = totalAmount
  dataForm.value.totalStock = totalStock
}
// 表单提交
const onSubmit = Debounce(() => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      // if (!dataForm.value.deliverTime) {
      //   ElMessage.error($t('purchase.order.deliverTimeNotEmpty'))
      //   return
      // }
      // if (dataForm.value.dvyType === 1) {
      //   if (!dataForm.value.dvyFlowId && dataForm.value.dvyFlowId !== ' ') {
      //     ElMessage.error($t('order.logEmpty'))
      //     return
      //   }
      // }
      if (!prods.value || prods.value.length < 1) {
        ElMessage.error($t('purchase.order.selectSupplierProduct'))
        return
      }
      dataForm.value.updateTime = null
      dataForm.value.createTime = null
      if (dataForm.value.dvyType !== 1) {
        dataForm.value.dvyId = null
        dataForm.value.dvyFlowId = ''
      }
      dataFormRef.value?.validate((valid) => {
        if (valid) {
          if (isSubmit) {
            return false
          }
          dataForm.value.purchaseProds = prods.value
          isSubmit = true
          http({
            url: http.adornUrl(dataForm.value.purchaseOrderId ? '/purchase/order/inbound' : '/purchase/order'),
            method: dataForm.value.purchaseOrderId ? 'put' : 'post',
            data: http.adornData(dataForm.value)
          }).then(() => {
            ElMessage({
              message: $t('publics.operation'),
              type: 'success',
              duration: 1500,
              onClose: () => {
                back()
                isSubmit = false
              }
            })
          }).catch((e) => {
            isSubmit = false
          })
        }
      })
    }
  })
})
// 选择供应商
const supplierSelectRef = ref(null)
const selectSupplier = () => {
  const suppliers = []
  suppliers.push({ supplierId: dataForm.value.supplierId })
  supplierSelectVisible.value = true
  nextTick(() => {
    supplierSelectRef.value?.init(suppliers)
  })
}
// 供应商选择回调
const supplierSelectHandle = (data) => {
  if (!data) {
    return
  }
  dataForm.value.supplierId = data[0].supplierId
  dataForm.value.supplierName = data[0].supplierName
  nextTick(() => {
    dataFormRef.value?.validateField('supplierName')
  })
  // 获取供应商商品
}
// 选择供应商商品
const supplierProdSelectRef = ref(null)
const selectSupplierProd = () => {
  if (!dataForm.value.warehouseId) {
    ElMessage({
      message: $t('stock.pleaseSelectStockPointFirst'),
      type: 'warning',
      duration: 1500
    })
    return
  }
  if (!dataForm.value.supplierId) {
    return
  }
  const supplierProdIds = []
  supplierProds.forEach(item => {
    supplierProdIds.push(item.supplierProdId)
  })
  const skuIds = []
  const retailProdIds = []
  prods.value.forEach(prod => {
    skuIds.push(prod.skuId)
  })
  const data = {
    supplierId: dataForm.value.supplierId,
    retailProdIds,
    stockPointId: dataForm.value.warehouseId,
    skuIds
  }
  supplierProdSelectVisible.value = true
  nextTick(() => {
    supplierProdSelectRef.value?.init(data)
  })
}
// 供应商商品选择回调
const supplierProdSelectHandle = (data) => {
  data.forEach(prod => {
    if (containsId(prod)) {
      prod.purchaseStock = prod.minOrderQuantity
      prod.price = prod.purchasePrice
      prod.purchaseAmount = new Big(prod.purchasePrice).times(prod.minOrderQuantity)
      prod.remark = ''
      prods.value.push(prod)
    }
  })
  setTotalAmountAndCount()
}
const containsId = (prod) => {
  const skuIds = []
  prods.value.forEach(prod => {
    skuIds.push(prod.skuId)
  })
  return skuIds.indexOf(prod.skuId) === -1
}
/**
* 删除供应商
*/
const handleClose = () => {
  // dataForm.value.supplierId = null
  dataForm.value.supplierName = ''
  dataForm.value.supplierId = null
  prods.value = []
  nextTick(() => {
    dataFormRef.value?.validateField('supplierName')
  })
}

const back = () => {
  Router.push('/stock/purchases/order/list')
}
// excel上传回调
const refreshDataList = (data) => {
  if (data.errorMsg) {
    alert(data.errorMsg)
  }
  if (!dataForm.value.purchaseOrderId) {
    supplierRefreshDataList(data)
  } else {
    inboundRefreshDataList(data)
  }
}
// ==============================新增采购订单excel============================
// 弹出导入供应商商品窗口
const excelUploadRef = ref(null)
const getUpload = () => {
  if (!dataForm.value.warehouseId) {
    ElMessage({
      message: $t('stock.pleaseSelectStockPointFirst'),
      type: 'warning',
      duration: 1500
    })
    return
  }
  uploadVisible.value = true
  modelUrl.value = '/purchase/order/downloadModel'
  uploadUrl.value = '/purchase/order/exportExcel/' + dataForm.value.supplierId + '?warehouseId=' + dataForm.value.warehouseId
  templateName.value = $t('purchase.order.purchaseProdTemplate')
  nextTick(() => {
    excelUploadRef.value?.init()
  })
}
// 导入供应商商品回调
const supplierRefreshDataList = (data) => {
  if (data.data) {
    const ids = []
    const list = JSON.parse(data.data)
    prods.value.forEach(item => {
      ids.push(item.skuId)
    })
    list.forEach(prod => {
      prod.purchaseAmount = new Big(prod.purchasePrice).times(prod.purchaseStock || prod.minOrderQuantity)
      prod.pic = checkFileUrl(prod.pic)
      if (containsId(prod)) {
        prods.value.push(prod)
      }
    })
  }
  setTotalAmountAndCount()
}

const getInboundUpload = () => {
  uploadVisible.value = true
  modelUrl.value = '/purchase/order/inbound/export?purchaseOrderId=' + dataForm.value.purchaseOrderId + '&t=' + new Date().getTime()
  uploadUrl.value = '/purchase/order/inbound/exportExcel/' + dataForm.value.purchaseOrderId
  templateName.value = $t('purchase.order.purchaseOrderTemplate')
  nextTick(() => {
    excelUploadRef.value?.init()
  })
}
// 导入采购入库商品回调
const inboundRefreshDataList = (data) => {
  if (data.data) {
    const ids = []
    const list = JSON.parse(data.data)
    list.forEach(item => {
      item.alreadyStock = item.actualStock
      item.alreadyStock = item.purchaseStock - item.remainingStock
      item.purchaseAmount = new Big(item.purchasePrice).times(item.actualStock ? item.actualStock : 0)
      ids.push(item.purchaseProdId)
    })
    for (let i = 0; i < prods.value.length; i++) {
      const item = prods.value[i]
      const index = ids.indexOf(item.purchaseProdId)
      if (index !== -1) {
        prods.value[i] = list[index]
      }
    }
  }
}

const onPointTypeChange = async (val) => {
  if (prods.value && prods.value.length) {
    prods.value = []
    page.currentPage = 1
  }
  dataForm.value.warehouseId = ''
  dataFormRef.value.clearValidate('warehouseId')
  pointOptions.value = await onGetPointList(val)
}

// 修改库存点清空商品
const onChangeWarehouse = () => {
  if (prods.value && prods.value.length) {
    prods.value = []
    page.currentPage = 1
  }
}

/**
 * 获取库存点列表
 * @param {*} val 库存点类型 typeOptions
 */
let defaultWarehouse = {}
const onGetPointList = (val) => {
  return new Promise((resolve) => {
    if (val === 1) {
      http({
        url: http.adornUrl('/admin/station/list_station'),
        method: 'get'
      }).then(({ data }) => {
        const list = data.map((item) => {
          return {
            value: item.stationId,
            label: item.stationName
          }
        })
        resolve(list)
      })
    } else {
      http({
        url: '/m/warehouse/list_warehouse',
        method: 'get'
      }).then(({ data }) => {
        const list = []
        data.forEach((item) => {
          if (item.type === 0 && val === '') {
            list.unshift({
              value: item.warehouseId,
              label: item.warehouseName
            })
            defaultWarehouse = item
            dataForm.value.warehouseId = item.warehouseId
          } else {
            list.push({
              value: item.warehouseId,
              label: item.warehouseName
            })
          }
        })
        resolve(list)
      })
    }
  })
}

const onPageChange = (val) => {
  page.currentPage = val
}
</script>
<style lang="scss" scoped>
.page-stock-purchases-order-new.mod-groupActivity {
  :deep(.date-picker) {
    width: 60%;
  }

  :deep(.card-prod-bottom) {
    position: relative;
    text-align: left;

    .card-prod-name {
      margin: auto;
      padding: 0 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 118px;
      display: inline-block;
    }

    .card-prod-name-button {
      position: absolute;
      top: 24px;
      right: 10px;
    }

    .card-edit-sku-button {
      position: absolute;
      top: 24px;
      left: 10px;
    }
  }

  .groupActivity-input {
    width: 60%;
  }

  :deep(.auxiliary-font) {
    font-size: 12px;
    color: #cbc0cb;
    line-height: 20px;
  }

  :deep(.font-color-red) {
    color: crimson;
  }
  .groupActivity-input {
    width: 524px;
  }
  .form-box {
    margin-left: 30px;
    .pagination-box :deep(.el-form-item__content) {
      justify-content: right;
    }
  }
  .mod-order-order {
    .prod-image {
      margin-right: 10px;
      width: 80px;
      height: 80px;
      float: left;
    }
    .prod-image :deep(img) {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .prod-name {
      width: 100%;
      text-align: left;
      float: right;
      .item {
        box-sizing: border-box;
        display: -webkit-box;
        word-break: break-word;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      .order-status {
        // width: 47px;
        display: inline-block;
        margin-top: 15px;
        padding: 2px 4px;
        border: 1px solid #e43130;
        border-radius: 2px;
        color: #e43130;
      }
    }
  }
}

</style>
