<template>
  <div class="page-stock-purchases-inbound-new mod-groupActivity">
    <div class="new-page-title">
      <div class="line" />
      <div class="text">
        {{
          !dataForm.stockBillLogId
            ? $t('purchase.order.newPurchaseInbound')
            : $t('purchase.order.viewPurchaseInbound')
        }}
      </div>
    </div>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="auto"
      class="form-box"
      @submit.prevent
    >
      <!-- 库存点类型（新建） -->
      <el-form-item
        v-if="!dataForm.stockBillLogId && !dataForm.stockBillLogId"
        :label="$t('stock.stockPointType')"
      >
        <el-radio-group
          v-model="pointType"
          @change="onPointTypeChange"
        >
          <el-radio :label="0">
            {{ $t('stock.warehouse') }}
          </el-radio>
          <el-radio :label="1">
            {{ $t('stock.station') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 库存点名称（新建） -->
      <el-form-item
        v-if="!dataForm.stockBillLogId && !dataForm.stockBillLogId"
        :label="$t('stock.stockPointName')"
        prop="warehouseId"
      >
        <el-select
          v-model="dataForm.warehouseId"
          :placeholder="$t('stock.pleaseSelect')"
          class="groupActivity-input"
          @change="onChangeWarehouse"
        >
          <el-option
            v-for="item in pointOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- 库存点类型（展示） -->
      <el-form-item
        v-if="dataForm.stockBillLogId"
        :label="$t('stock.stockPointType')"
      >
        {{ dataForm.stockPointType===1 ? $t('stock.warehouse') : $t('stock.station') }}
      </el-form-item>
      <!-- 库存点名称（展示） -->
      <el-form-item
        v-if="dataForm.stockBillLogId"
        :label="$t('stock.stockPointName')"
      >
        {{ dataForm.stockPointName || '-' }}
      </el-form-item>
      <!-- 供应商 -->
      <el-form-item
        :label="$t('purchase.order.supplier')"
        prop="supplierName"
      >
        <div
          v-if="!dataForm.supplierName"
          class="default-btn"
          @click="selectSupplier"
        >
          {{
            $t("product.chooseSupplier")
          }}
        </div>
        <el-tag
          v-if="dataForm.supplierName"
          :closable="!dataForm.stockBillLogId"
          @close="handleClose()"
        >
          {{
            dataForm.supplierName
          }}
        </el-tag>
      </el-form-item>
      <!-- 送达时间 -->
      <el-form-item
        :label="$t('purchase.order.deliverTime')"
        prop="deliverTime"
      >
        <el-date-picker
          v-model="dataForm.deliverTime"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="$t('admin.seleData')"
          :picker-options="inboundPickerOptions"
          :disabled="!!dataForm.stockBillLogId"
        />
      </el-form-item>
      <!-- 备注 -->
      <el-form-item
        :label="$t('purchase.order.remark')"
        prop="remark"
      >
        <el-input
          v-model="dataForm.remark"
          :placeholder="$t('purchase.order.remark')"
          maxlength="50"
          show-word-limit
          type="textarea"
          class="groupActivity-input"
          :disabled="!!dataForm.stockBillLogId"
        />
      </el-form-item>
      <!-- 选择商品 -->
      <el-form-item
        v-if="!dataForm.stockBillLogId"
        ref="productRef"
        :label="$t('home.product')"
        prop="prods"
        :rules="prodsRules"
      >
        <el-button
          plain
          :disabled="!dataForm.supplierId"
          @click="selectSupplierProd"
        >
          {{ $t("product.select") }}
        </el-button>
        <el-button
          plain
          :disabled="!dataForm.supplierId"
          @click.stop="getUpload"
        >
          {{ $t("product.importGoods") }}
        </el-button>
      </el-form-item>
      <!-- 选择供应商商品列表（新增时使用） -->
      <el-form-item v-if="!dataForm.stockBillLogId">
        <span v-if="prods.length > 0 && !dataForm.stockBillLogId">
          {{ $t('order.amountOfGoods') + '：' + dataForm.totalCount + ', ' +
            $t('order.totalPrice') + '：' + dataForm.totalAmount
          }}
        </span>
        <el-table
          v-if="prods.length > 0 && !dataForm.stockBillLogId"
          :data="prods.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
          style="width: 100%"
        >
          <el-table-column
            label=""
            width="300"
          >
            <template #default="scope">
              <div class="mod-order-order">
                <div class="item">
                  <div class="prod-image">
                    <ImgShow
                      :src="scope.row.pic"
                      :img-style="{width:'60px',height:'60px'}"
                    />
                  </div>
                  <div class="prod-name-a">
                    <div class="item">
                      {{ scope.row.prodName }}
                    </div>
                    <!-- <div class="item">
                        {{scope.row.skuName}}
                      </div>
                      <div class="item">
                        {{scope.row.partyCode}}
                      </div> -->
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 规格 -->
          <el-table-column
            :label="$t('groups.sku')"
          >
            <template #default="scope">
              <span>{{ scope.row.skuName || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 商品编码 -->
          <el-table-column
            :label="$t('product.commodityCode')"
            min-width="200"
          >
            <template #default="scope">
              <span>{{ scope.row.partyCode }}</span>
            </template>
          </el-table-column>
          <!-- 库存 -->
          <el-table-column
            :label="$t('coupon.stock')"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.stocks || '0' }}</span>
            </template>
          </el-table-column>
          <!-- 当前仓库存 -->
          <el-table-column
            :label="$t('stock.currentWarehouseInventory')"
            width="100"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.inStock || '0' }}</span>
            </template>
          </el-table-column>
          <!-- 采购数量 -->
          <el-table-column
            :label="$t('purchase.order.purchaseNum')"
            width="180"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.purchaseStock"

                @change="changStock(scope.row)"
              >
                <template #suffix>
                  <el-tooltip

                    class="item"
                    effect="dark"
                    :content="$t('purchase.order.purchaseNumThanMinimum')"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </template>
          </el-table-column>
          <!-- 采购价 -->
          <el-table-column
            :label="$t('purchase.order.purchasePrice')"
            width="180"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.unitPrice"

                @change="changPrice(scope.row)"
              >
                <template #suffix>
                  <el-tooltip

                    class="item"
                    effect="dark"
                    :content="$t('purchase.order.purchasePriceMustThen0')"
                    placement="top"
                  >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </template>
          </el-table-column>
          <!-- 采购金额 -->
          <el-table-column
            :label="$t('purchase.order.purchaseAmount')"
            width="180"
          >
            <template #default="scope">
              <el-input
                v-model="scope.row.purchaseAmount"

                disabled
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('crud.menu')">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
              >
                {{ $t('text.delBtn') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <!-- 详情列表（查看详情时使用） -->
      <el-form-item
        v-if="dataForm.stockBillLogId"
        :label="$t('home.product')"
        prop="startTime"
      >
        <br>
        <span v-if="prods.length > 0">
          {{ $t('order.amountOfGoods') + '：' + dataForm.totalCount + ', ' +
            $t('order.totalPrice') + '：' + dataForm.totalAmount
          }}
        </span>
        <el-table
          v-if="prods.length > 0"
          :data="prods.slice((page.currentPage - 1) * page.pageSize, page.currentPage * page.pageSize)"
          style="width: 100%"
        >
          <el-table-column
            label=""
            width="300"
          >
            <template #default="scope">
              <div class="mod-order-order">
                <div class="item">
                  <div class="prod-image">
                    <ImgShow
                      :src="scope.row.pic"
                      :img-style="{width:'60px',height:'60px'}"
                    />
                  </div>
                  <div class="prod-name-a">
                    <div class="item">
                      {{ scope.row.prodName }}
                    </div>
                    <div
                      v-if="!!scope.row.isDelete"
                      class="item order-status"
                    >
                      {{ $t("purchase.order.deleted") }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 规格 -->
          <el-table-column
            :label="$t('groups.sku')"
          >
            <template #default="scope">
              <span>{{ scope.row.skuName || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 商品编码 -->
          <el-table-column
            :label="$t('product.commodityCode')"
            width="250"
          >
            <template #default="scope">
              <span>{{ scope.row.partyCode }}</span>
            </template>
          </el-table-column>
          <!-- 库存 -->
          <el-table-column
            :label="$t('coupon.stock')"
          >
            <template #default="scope">
              <span style="margin-left: 10px">{{ scope.row.stocks }}</span>
            </template>
          </el-table-column>
          <!-- 采购数量 -->
          <el-table-column
            :label="$t('purchase.order.purchaseNum')"
            width="180"
          >
            <template #default="scope">
              <span> {{ scope.row.purchaseStock }} </span>
            </template>
          </el-table-column>
          <!-- 采购价 -->
          <el-table-column
            :label="$t('purchase.order.purchasePrice')"
            width="180"
          >
            <template #default="scope">
              <span> {{ scope.row.unitPrice }} </span>
            </template>
          </el-table-column>
          <!-- 采购金额 -->
          <el-table-column
            :label="$t('purchase.order.purchaseAmount')"
            width="180"
          >
            <template #default="scope">
              <span> {{ scope.row.totalAmount }} </span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item class="pagination-box">
        <el-pagination
          v-if="prods.length"
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="prods.length"
          @current-change="onPageChange"
        />
      </el-form-item>
      <!-- 操作 -->
      <el-form-item>
        <div
          class="default-btn"
          @click="back()"
        >
          {{
            $t("shopFeature.edit.back")
          }}
        </div>
        <div
          v-if="!dataForm.stockBillLogId"
          class="default-btn primary-btn"
          @click="onSubmit()"
        >
          {{
            dataForm.stockBillLogId ? $t('purchase.order.inbound') : $t("groups.submit")
          }}
        </div>
      </el-form-item>
    </el-form>

    <!-- 供应商选择弹窗-->
    <supplier-select
      v-if="supplierSelectVisible"
      ref="supplierSelectRef"
      :is-single="true"
      @refresh-select-supplier="supplierSelectHandle"
    />

    <!-- 供应商商品选择弹窗-->
    <supplier-prod-select
      v-if="supplierProdSelectVisible"
      ref="supplierProdSelectRef"
      :type="1"
      :is-show-is-stock="true"
      :search-form="{warehouseId:dataForm.warehouseId}"
      data-url="/supplier/supplierProd/page"
      @refresh-select-supplier="supplierProdSelectHandle"
    />
    <!-- excel -->
    <excel-upload
      v-if="uploadVisible"
      ref="excelUploadRef"
      :model-url="modelUrl"
      :upload-url="uploadUrl"
      :template-name="templateName"
      @refresh-data-list="refreshDataList"
    />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import moment from 'moment'
import Big from 'big.js'
import ExcelUpload from '../components/purchases-prod-upload.vue'
import { Debounce } from '@/utils/debounce'

const uploadVisible = ref(false)
const supplierSelectVisible = ref(false)
const supplierProdSelectVisible = ref(false)
const roleList = ref([])
const prods = ref([])
const supplierProds = []
// dialogWidth:'895px'
const modelUrl = ref(null)
const uploadUrl = ref(null)
const templateName = ref(null)
const dataForm = reactive({
  remark: '',
  stockBillLogId: '',
  purchaseNumber: '',
  deliverTime: '',
  supplierId: '',
  dvyType: 1,
  stockBillType: 1,
  totalCount: 0,
  totalAmount: 0,
  type: 2,
  supplierName: null,
  deliveryExpresses: [],
  warehouseId: '' // 库存点id
})

const pointType = ref(0)
const validateWarehouseId = (rule, value, callback) => {
  if (value && pointType.value === 0 && defaultWarehouse.warehouseId === value && !(defaultWarehouse.provinceId && defaultWarehouse.cityId && defaultWarehouse.areaId)) {
    callback(new Error($t('stock.defaultWarehouseAddress')))
  } else {
    callback()
  }
}

const page = reactive({
  currentPage: 1, // 初始页
  pageSize: 10 // 每页数据大小
})

let isSubmit = false
const inboundPickerOptions = reactive({
  disabledDate (time) {
    const month1 = moment().startOf('day')
    return (
      time.getTime() > month1.valueOf()
    )
  }
})
const dataRule = {
  supplierName: [
    { required: true, message: $t('purchase.order.selectSupplier'), trigger: 'blur' }
  ],
  deliverTime: [
    { required: true, message: $t('purchase.order.deliverTimeNotEmpty'), trigger: 'blur' }
  ],
  warehouseId: [
    { required: true, message: $t('stock.pleaseSelectStockPoint'), trigger: 'blur' },
    { validator: validateWarehouseId, trigger: 'change' }
  ]
}
// 商品列表校验方法
const prodsValidator = (rule, value, callback) => {
  if (!prods.value.length) {
    callback(new Error($t('marketing.pleaseSelectAProduct')))
  } else {
    callback()
  }
}
// 商品列表校验规则
const prodsRules = { required: true, validator: prodsValidator, trigger: 'blur' }

onMounted(() => {
  init()
})

const pointOptions = ref([])
const commonStore = useCommonStore()
const dataFormRef = ref(null)
const Route = useRoute()
const init = async () => {
  dataForm.stockBillLogId = Route.query.stockBillLogId ? parseInt(Route.query.stockBillLogId) : null
  // 更新菜单路径
  const navTitles = JSON.parse(JSON.stringify(commonStore.selectMenu))
  const title = !dataForm.stockBillLogId ? $t('purchase.order.newPurchaseInbound') : $t('purchase.order.viewPurchaseInbound')
  navTitles.splice(navTitles.length - 1, 1, title)
  commonStore.updateSelectMenu(navTitles)
  // 初始化数据
  isSubmit = false
  prods.value = []
  dataFormRef.value?.resetFields()
  if (dataForm.stockBillLogId) {
    http({
      url: http.adornUrl('/shop/stockBillLog/purchaseInfo/' + dataForm.stockBillLogId),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      try {
        for (const key in dataForm) {
          if (Object.hasOwnProperty.call(dataForm, key)) {
            delete dataForm.key
          }
        }
        data.deliverTime = data.businessTime
        Object.assign(dataForm, data)
      } catch (e) {

      }
      data.stockBillLogItems.forEach(item => {
        item.purchaseStock = item.stockCount
        item.totalAmount = new Big(item.purchaseStock).times(item.unitPrice).valueOf()
      })
      prods.value = data.stockBillLogItems
    })
  } else {
    pointOptions.value = await onGetPointList('')
  }
}
const handleDelete = (index) => {
  const realIndex = (page.currentPage - 1) * page.pageSize + index
  prods.value.splice(realIndex, 1)
  setTotalAmountAndCount()
}
const changStock = (row) => {
  const numReg = /^([0-9]|[1-9]\d+)(\.\d{2})?$/
  const numRe = new RegExp(numReg)
  let stockCount = row.purchaseStock
  if (!numRe.test(stockCount) || !stockCount || stockCount < row.minOrderQuantity) {
    stockCount = row.minOrderQuantity
  } else if (stockCount > 9999999) {
    stockCount = 9999999
  }
  row.purchaseStock = Math.round(stockCount)
  row.purchaseAmount = new Big(stockCount).times(row.unitPrice).valueOf()
  setTotalAmountAndCount()
}
const changPrice = (row) => {
  const numReg = /^([0-9]|[1-9]\d+)(\.\d*)?$/
  let unitPrice = row.unitPrice
  const numRe = new RegExp(numReg)
  if (!unitPrice || !numRe.test(unitPrice) || !unitPrice) {
    unitPrice = row.price
  } else if (!dataForm.isDefault && unitPrice < row.price) {
    unitPrice = row.price
  } else if (unitPrice > 9999999.99) {
    unitPrice = 9999999.99
  }
  row.unitPrice = parseFloat(unitPrice).toFixed(2)
  row.purchaseAmount = new Big(row.purchaseStock).times(unitPrice).valueOf()
  row.purchasePrice = row.unitPrice
  setTotalAmountAndCount()
}
const setTotalAmountAndCount = () => {
  let totalCount = 0
  let totalAmount = 0
  prods.value.forEach(item => {
    totalAmount = new Big(totalAmount).plus(item.purchaseAmount).valueOf()
    totalCount = new Big(totalCount).plus(item.purchaseStock).valueOf()
  })
  dataForm.totalAmount = totalAmount
  dataForm.totalCount = totalCount
}
// 表单提交
const Router = useRouter()
const onSubmit = Debounce(() => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      if (!prods.value || prods.value.length < 1) {
        ElMessage.error($t('purchase.order.selectSupplierProduct'))
        return
      }
      dataFormRef.value?.validate((valid) => {
        if (valid) {
          if (isSubmit) {
            return false
          }
          dataForm.purchaseProds = prods.value
          dataForm.status = 1
          dataForm.stockPointType = pointType.value + 1
          isSubmit = true
          http({
            url: http.adornUrl('/purchase/order/autoPurchase'),
            method: 'post',
            data: http.adornData(dataForm)
          }).then(() => {
            roleList.value = []
            ElMessage({
              message: $t('publics.operation'),
              type: 'success',
              duration: 1500,
              onClose: () => {
                Router.push('/stock/purchases/inbound/list')
                isSubmit = false
              }
            })
          }).catch((e) => {
            isSubmit = false
          })
        }
      })
    }
  })
})
// 选择供应商
const supplierSelectRef = ref(null)
const selectSupplier = () => {
  const suppliers = []
  suppliers.push({ supplierId: dataForm.supplierId })
  supplierSelectVisible.value = true
  nextTick(() => {
    supplierSelectRef.value?.init(suppliers)
  })
}
// 供应商选择回调
const supplierSelectHandle = (data) => {
  if (!data) {
    return
  }
  dataForm.supplierId = data[0].supplierId
  dataForm.supplierName = data[0].supplierName
  dataForm.isDefault = data[0].isDefault
  nextTick(() => {
    dataFormRef.value?.validateField('supplierName')
  })
  // 获取供应商商品
}
// 选择供应商商品
const supplierProdSelectRef = ref(null)
const selectSupplierProd = () => {
  if (!dataForm.warehouseId) {
    ElMessage({
      message: $t('stock.pleaseSelectStockPointFirst'),
      type: 'warning',
      duration: 1500
    })
    return
  }
  if (!dataForm.supplierId) {
    return
  }
  const supplierProdIds = []
  supplierProds.forEach(item => {
    supplierProdIds.push(item.supplierProdId)
  })
  const skuIds = []
  prods.value.forEach(prod => {
    skuIds.push(prod.skuId)
  })
  const data = {
    supplierId: dataForm.supplierId,
    stockPointId: dataForm.warehouseId,
    skuIds
  }
  supplierProdSelectVisible.value = true
  nextTick(() => {
    supplierProdSelectRef.value?.init(data)
  })
}
// 供应商商品选择回调
const productRef = ref()
const supplierProdSelectHandle = (data) => {
  data.forEach(prod => {
    if (containsId(prod)) {
      prod.purchaseStock = prod.minOrderQuantity
      prod.unitPrice = prod.purchasePrice
      prod.price = prod.purchasePrice
      prod.purchaseAmount = new Big(prod.purchasePrice).times(prod.minOrderQuantity).valueOf()
      prods.value.push(prod)
    }
  })
  productRef.value?.validate()
  setTotalAmountAndCount()
}
const containsId = (prod) => {
  const skuIds = []
  prods.value.forEach(prod => {
    skuIds.push(prod.skuId)
  })
  return skuIds.indexOf(prod.skuId) === -1
}
/**
* 删除供应商
*/
const handleClose = () => {
  dataForm.supplierName = ''
  dataForm.supplierId = null
  prods.value = []
  nextTick(() => {
    dataFormRef.value?.validateField('supplierName')
  })
}

const back = () => {
  Router.push('/stock/purchases/inbound/list')
}
// 弹出导入供应商商品窗口
const excelUploadRef = ref()
const getUpload = () => {
  if (!dataForm.warehouseId) {
    ElMessage({
      message: $t('stock.pleaseSelectStockPointFirst'),
      type: 'warning',
      duration: 1500
    })
    return
  }
  uploadVisible.value = true
  modelUrl.value = '/purchase/order/downloadModel'
  uploadUrl.value = '/purchase/order/exportExcel/' + dataForm.supplierId + '?warehouseId=' + dataForm.warehouseId
  templateName.value = $t('purchase.order.purchaseProdTemplate')
  nextTick(() => {
    excelUploadRef.value?.init()
  })
}
// excel上传回调
const refreshDataList = (data) => {
  if (data.errorMsg) {
    alert(data.errorMsg)
  }
  if (data.data) {
    const ids = []
    const list = JSON.parse(data.data)
    prods.value.forEach(item => {
      ids.push(item.skuId)
    })
    list.forEach(prod => {
      prod.unitPrice = prod.purchasePrice
      prod.price = prod.purchasePrice
      prod.purchaseAmount = new Big(prod.purchasePrice).times(prod.purchaseStock || prod.minOrderQuantity).valueOf()
      prod.pic = checkFileUrl(prod.pic)
      if (containsId(prod)) {
        prods.value.push(prod)
      }
    })
  }
  setTotalAmountAndCount()
}

const onPointTypeChange = async (val) => {
  if (prods.value && prods.value.length) {
    prods.value = []
    page.currentPage = 1
  }
  dataForm.warehouseId = ''
  dataFormRef.value.clearValidate('warehouseId')
  pointOptions.value = await onGetPointList(val)
}

// 修改库存点清空商品
const onChangeWarehouse = () => {
  if (prods.value && prods.value.length) {
    prods.value = []
    page.currentPage = 1
  }
}

/**
 * 获取库存点列表
 * @param {*} val 库存点类型 typeOptions
 */
let defaultWarehouse = {}
const onGetPointList = (val) => {
  return new Promise((resolve) => {
    if (val === 1) {
      http({
        url: http.adornUrl('/admin/station/list_station'),
        method: 'get'
      }).then(({ data }) => {
        const list = data.map((item) => {
          return {
            value: item.stationId,
            label: item.stationName
          }
        })
        resolve(list)
      })
    } else {
      http({
        url: '/m/warehouse/list_warehouse',
        method: 'get'
      }).then(({ data }) => {
        const list = []
        data.forEach((item) => {
          if (item.type === 0 && val === '') {
            list.unshift({
              value: item.warehouseId,
              label: item.warehouseName
            })
            defaultWarehouse = item
            dataForm.warehouseId = item.warehouseId
          } else {
            list.push({
              value: item.warehouseId,
              label: item.warehouseName
            })
          }
        })
        resolve(list)
      })
    }
  })
}

const onPageChange = (val) => {
  page.currentPage = val
}
</script>
<style lang="scss" scoped>
.mod-groupActivity {
  :deep(.date-picker) {
    width: 60%;
  }

  :deep(.card-prod-bottom) {
    position: relative;
    text-align: left;

    .card-prod-name {
      margin: auto;
      padding: 0 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 118px;
      display: inline-block;
    }

    .card-prod-name-button {
      position: absolute;
      top: 24px;
      right: 10px;
    }

    .card-edit-sku-button {
      position: absolute;
      top: 24px;
      left: 10px;
    }
  }

  .groupActivity-input {
    width: 60%;
  }

  :deep(.auxiliary-font) {
    font-size: 12px;
    color: #cbc0cb;
    line-height: 20px;
  }

  :deep(.font-color-red) {
    color: crimson;
  }
  .groupActivity-input {
    width: 524px;
  }
  .form-box {
    margin-left: 30px;
    .pagination-box :deep(.el-form-item__content) {
      justify-content: right;
    }
  }
}
.mod-order-order {
  .prod-image {
    margin-right: 20px;
    width: 80px;
    height: 80px;
    float: left;
  }
  .prod-image :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .prod-name-a {
    width: 100%;
    text-align: left;
    float: right;
    // .item {
    //   box-sizing: border-box;
    //   display: -webkit-box;
    //   word-break: break-word;
    //   -webkit-box-orient: vertical;
    //   -webkit-line-clamp: 2;
    //   overflow: hidden;
    // }
  }
}
.order-status {
  display: inline-block;
  margin-top: 15px;
  padding: 2px 4px;
  border: 1px solid #e43130;
  border-radius: 2px;
  color: #e43130;
}
</style>
