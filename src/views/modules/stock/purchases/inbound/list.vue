<template>
  <div class="page-stock-purchases-inbound-list mod-order-order">
    <div class="search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        :inline="true"
        :model="dataForm"
        label-width="auto"

        @submit.prevent
        @keyup.enter="getDataList(page)"
      >
        <div class="input-row">
          <el-form-item :label="$t('purchase.order.stockNumber') + ':'">
            <el-input
              v-model="params.stockBillNo"
              :placeholder="$t('purchase.order.stockNumber')"
              clearable
              :maxlength="50"
            />
          </el-form-item>
          <el-form-item :label="$t('purchase.order.purchaseNumber') + ':'">
            <el-input
              v-model="params.sourceOrderNo"
              :placeholder="$t('purchase.order.purchaseNumber')"
              clearable
              :maxlength="50"
            />
          </el-form-item>
          <el-form-item :label="$t('purchase.order.supplier') + ':'">
            <el-input
              v-model="params.supplierName"
              :maxlength="50"
              :placeholder="$t('purchase.order.supplier')"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t("order.query") }}
            </div>
            <div
              class="default-btn"
              @click="clear()"
            >
              {{ $t("shop.resetMap") }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 列标题 -->
    <div class="main-container">
      <div class="operation-bar">
        <div
          class="default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ $t('crud.addBtn') }}
        </div>
      </div>
      <div class="table-con">
        <el-table
          ref="imgTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <!-- 库存编号 -->
          <el-table-column
            align="center"
            prop="seq"
            :label="$t('purchase.order.stockNumber')"
          >
            <template #default="scope">
              <span>{{ scope.row.stockBillNo }}</span>
            </template>
          </el-table-column>
          <!-- 采购编号 -->
          <el-table-column
            align="center"
            prop="seq"
            :label="$t('purchase.order.purchaseNumber')"
          >
            <template #default="scope">
              <span>{{ scope.row.sourceOrderNo || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 供应商 -->
          <el-table-column
            align="center"
            :label="$t('product.supplier')"
          >
            <template #default="scope">
              <span>{{ scope.row.supplierName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.stockPointName')"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.stockPointName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('stock.stockPointType')"
            width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.stockPointType === 1 ? $t('stock.warehouse') : $t('stock.station') }}</span>
            </template>
          </el-table-column>
          <!-- 采购金额 -->
          <el-table-column
            align="center"
            prop="imgType"
            :label="$t('purchase.order.purchaseAmount')"
          >
            <template #default="scope">
              <span>{{ scope.row.totalAmount }}</span>
            </template>
          </el-table-column>
          <!-- 采购数量 -->
          <el-table-column
            align="center"
            prop="imgType"
            :label="$t('purchase.order.purchaseNum')"
          >
            <template #default="scope">
              <span>{{ scope.row.totalCount }}</span>
            </template>
          </el-table-column>
          <!-- 送达日期 -->
          <el-table-column
            align="center"
            prop="imgType"
            :label="$t('purchase.order.deliverTime')"
          >
            <template #default="scope">
              <span>{{ scope.row.businessTime&&scope.row.businessTime.substring(0,10) }}</span>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="220"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('purchase:purchasesInbound:info')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.stockBillLogId)"
                >
                  {{ $t('shop.withdrawalDetail') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
    </div>
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'

let tempSearchForm = null // 保存上次点击查询的请求条件
const Router = useRouter()
const dataList = ref([])
const dataForm = reactive({})
const params = ref({
  supplierName: '',
  sourceOrderNo: '',
  stockBillNo: ''
})
const page = {
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
}
onActivated(() => {
  // 携带参数查询
  // const query = useRoute().query
  // getDataList(page, query)
})
onMounted(() => {
  // 携带参数查询
  getDataList(page, useRoute().query)
  // 监听页面滚动
  window.addEventListener('scroll', scrollToTop)
})
onUnmounted(() => {
  // 页面销毁时移除监听
  window.removeEventListener('scroll', scrollToTop)
})

/**
 * 页面滚动事件
 */
// eslint-disable-next-line no-unused-vars
let showHeadScroll
const scrollToTop = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
  showHeadScroll = scrollTop > 400
}

/**
 * 获取数据列表
 */
const getDataList = (pageParam, newData = false) => {
  pageParam = (pageParam === undefined ? page : pageParam)
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(params.value))
  }
  http({
    url: http.adornUrl('/shop/stockBillLog/purchasePage'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      ), false
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}
// 新增 / 修改

const onAddOrUpdate = (id = '') => {
  Router.push({
    path: '/stock/purchases/inbound/new',
    query: {
      stockBillLogId: id
    }
  })
}
// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getDataList(page)
}
// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getDataList(page)
}
// 清空按钮
const clear = () => {
  params.value = {
    supplierName: '',
    purchaseNumber: ''
  }
}
// 搜索查询
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}

</script>
