<template>
  <div class="page-stock-inventory-counting-take-stock mod-stock-receive-list">
    <!-- 顶部按钮 -->
    <div
      v-if="isAuth('multishop:takeStock:new')"
      class="default-btn primary-btn"
      @click="onAddOrUpdate()"
    >
      {{ $t('takeStock.newInventory') }}
    </div>
    <div style="height: 10px" />
    <!-- 搜索栏组件 -->
    <stockTakeSearchFrom
      :search-form="searchForm"
      @on-search="onSearch(true)"
      @confirm-export="confirmExport"
    />
    <!-- 表格组件 -->
    <stockTakeTable
      ref="stockTakeTableRef"
      class="stockTakeTable"
      :data-list="dataList"
      @refresh-list="getDataList"
      @refresh-change="refreshChange()"
    />
    <!-- 分页 -->
    <el-pagination
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      :total="page.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="onPageSizeChange"
      @current-change="onPageChange"
    />
  </div>
</template>

<script setup>
import { ElMessageBox, ElLoading } from 'element-plus'
import { isAuth } from '@/utils'
import stockTakeSearchFrom from './components/take-stock-search-form.vue'
import stockTakeTable from './components/take-stock-table.vue'
import { onMounted } from 'vue'
const Router = useRouter()
let tempSearchForm = null // 保存上次点击查询的请求条件

const dataList = ref([]) // 表格数据
// const exportRadio = ref(1) //  1 按搜索条件导出 2 按选择项导出
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  takeStockNo: '', // 盘点单号
  billStatus: '', // 盘点状态 0已作废 1盘点中 2已完成
  maker: '', // 制单人id
  prodKeyType: 1, // 1：商品名称 2：商品编码
  prodKey: '', // 搜索商品关键词(0:商品名称 1：商品编码)
  createStartTime: '', // 盘点起始时间
  createEndTime: '', // 盘点结束时间
  prodName: '',
  partyCode: ''
})
onActivated(() => {
  init()
})
onMounted(() => {
  init()
})
const init = () => {
  getDataList(page)
}
const getDataList = (pageParam, newData = false) => {
  if (searchForm.prodKeyType === 1) {
    searchForm.prodName = searchForm.prodKey
    searchForm.partyCode = null
  } else if (searchForm.prodKeyType === 2) {
    searchForm.partyCode = searchForm.prodKey
    searchForm.prodName = null
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/stock/takeStock/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  })
    .then(({ data }) => {
      dataList.value = data.records
      page.total = data.total
    })
    .catch(() => {
    })
}
/**
 * 导出
 */
const confirmExport = () => {
  if (searchForm.prodKeyType === 1) {
    searchForm.prodName = searchForm.prodKey
    searchForm.partyCode = null
  } else if (searchForm.prodKeyType === 2) {
    searchForm.partyCode = searchForm.prodKey
    searchForm.prodName = null
  }
  ElMessageBox.confirm(`${$t('shop.exportProdTip_physical')}`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      target: '.mod-stock-receive-list',
      customClass: 'export-load',
      background: 'transparent',
      text: $t('formData.exportIng')
    })
    http({
      url: http.adornUrl('/stock/takeStock/exportTakeStock'),
      method: 'get',
      params: http.adornParams(searchForm),
      responseType: 'blob' // 解决文件下载乱码问题
    }).then(({ data }) => {
      loading.close()
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
      const fileName = $t('takeStock.exportInventoryFileName')
      const elink = document.createElement('a')
      if ('download' in elink) { // 非IE下载
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
      } else { // IE10+下载
        navigator.msSaveBlob(blob, fileName)
      }
    }).catch((e) => {
      loading.close()
    })
  })
}
const onAddOrUpdate = () => {
  Router.push({
    path: '/stock/inventory-counting/new-take-stock',
    query: {
    }
  })
}
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}
const refreshChange = () => {
  getDataList(page)
}

</script>

<style lang="scss" scoped>
.mod-stock-receive-list .stockTakeTable:deep(.el-table__fixed-right::before),
.mod-stock-receive-list .stockTakeTable:deep(.el-table__fixed::before){
  height:0 !important
}
</style>
