<template>
  <div>
    <div class="components-take-stock-search-form search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="_searchForm"
        label-width="auto"

        @submit.prevent
      >
        <!-- 表单项 -->
        <div class="input-row">
          <el-form-item :label="$t('takeStock.productFilter')+':'">
            <el-select
              v-model="_searchForm.prodKeyType"
              @change="prodKeyTypeChange"
            >
              <el-option
                v-for="node in prodKeyArr"
                :key="node.key"
                :label="node.label"
                :value="node.key"
              />
            </el-select>
            <el-input
              v-model="_searchForm.prodKey"
              type="text"
              clearable
              :placeholder="prodKeyArr[_searchForm.prodKeyType - 1].inputTips"
            />
          </el-form-item>
          <el-form-item :label="$t('takeStock.InventoryNo')+':'">
            <el-input
              v-model="_searchForm.takeStockNo"
              type="text"
              clearable
              :placeholder="$t('takeStock.InventoryNo')"
            />
          </el-form-item>
          <el-form-item :label="$t('takeStock.InventoryStatus')+':'">
            <el-select
              v-model="_searchForm.billStatus"
              clearable
            >
              <el-option
                :label="$t('takeStock.voided')"
                value="0"
              />
              <el-option
                :label="$t('takeStock.taking')"
                value="1"
              />
              <el-option
                :label="$t('takeStock.complete')"
                value="2"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('takeStock.maker')+':'">
            <el-select
              v-model="_searchForm.maker"
              clearable
            >
              <el-option
                v-for="node in employeeList"
                :key="node.employeeId"
                :label="node.username"
                :value="node.employeeId"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="input-row">
          <el-form-item :label="$t('takeStock.createTime')+':'">
            <el-date-picker
              v-model="createDateRange"
              type="datetimerange"
              :range-separator="$t('time.tip')"
              value-format="YYYY-MM-DD HH:mm:ss"
              :start-placeholder="$t('time.start')"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0),
                              new Date(2000, 2, 1, 23, 59, 59)]"
              :end-placeholder="$t('time.end')"
              clearable
              @change="createTimeChange"
            />&nbsp;
            <div
              class="default-btn"
              style="margin-left: 20px;"
              :class="{ 'is-active': createTimeActive === 1 }"
              @click="setDateRange(1)"
            >
              {{
                $t("time.t")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': createTimeActive === 2 }"
              @click="setDateRange(2)"
            >
              {{
                $t("time.y")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': createTimeActive === 3 }"
              @click="setDateRange(3)"
            >
              {{
                $t("time.n")
              }}
            </div>
            <div
              class="default-btn"
              :class="{ 'is-active': createTimeActive === 4 }"
              @click="setDateRange(4)"
            >
              {{
                $t("temp.m")
              }}
            </div>
          </el-form-item>
          <el-form-item>
            <div class="text-btn-con">
              <div
                class="default-btn primary-btn"
                @click="onSearch"
              >
                {{ $t("crud.searchBtn") }}
              </div>
              <div
                v-if="isAuth('multishop:takeStock:export')"
                class="default-btn primary-btn"
                @click="confirmExport"
              >
                {{ $t("order.ExportingFiles") }}
              </div>
              <div
                class="default-btn"
                @click="resetForm()"
              >
                {{ $t("shop.resetMap") }}
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'
import moment from 'moment'

const emit = defineEmits(['onSearch', 'confirmExport', 'update:searchForm'])
const props = defineProps({
  searchForm: {
    default: () => ({
      takeStockNo: '', // 盘点单号
      billStatus: '', // 盘点状态 0已作废 1盘点中 2已完成
      maker: '', // 制单人id
      prodKeyType: 1, // 1：商品名称 2：商品编码
      prodKey: '', // 搜索商品关键词(0:商品名称 1：商品编码)
      startTime: '', // 制单起始时间
      endTime: '', // 制单结束时间
      prodName: '',
      partyCode: ''
    }),
    type: Object
  }
})

const createTimeActive = ref(0)
const employeeList = ref([]) // 员工列表
const prodKeyArr = [
  { key: 1, label: $t('product.prodName'), inputTips: $t('takeStock.inputName') },
  { key: 2, label: $t('product.commodityCode'), inputTips: $t('takeStock.inputPartyCode') }
] // 商品筛选类型
const createDateRange = ref([]) // 制单时间范围
const _searchForm = computed({
  get () {
    return props.searchForm
  },
  set (val) {
    emit('update:searchForm', val)
  }
})
onMounted(() => {
  init()
})
let type
const init = () => {
  _searchForm.value.type = type
  // 获取员工列表
  getEmployeeList()
}
const getEmployeeList = () => {
  http({
    url: http.adornUrl('/sys/shopEmployee/list'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    employeeList.value = data
  })
}
const onSearch = () => {
  // 发送搜索事件
  emit('onSearch')
}
const confirmExport = () => {
  // 发送导出事件
  emit('confirmExport')
}
const resetForm = () => {
  _searchForm.value.takeStockNo = '' // 盘点单号
  _searchForm.value.billStatus = '' // 盘点状态 0已作废 1盘点中 2已完成
  _searchForm.value.maker = '' // 制单人ID
  _searchForm.value.prodKeyType = 1 // 1：商品名称 2：商品编码
  _searchForm.value.prodKey = '' // 搜索商品关键词(0:商品名称 1：商品编码)
  _searchForm.value.startTime = '' // 盘点起始时间
  _searchForm.value.endTime = '' // 盘点结束时间
  createDateRange.value = [] // 盘点时间
}
const prodKeyTypeChange = () => {
  _searchForm.value.prodKey = ''
}
const createTimeChange = () => {
  _searchForm.value.startTime = createDateRange.value[0]
  _searchForm.value.endTime = createDateRange.value[1]
}
/**
 * 根据选项设置时间
 * 1:今天 2:昨天 3: 近七天 4:近30天 5:近60天
 */
const setDateRange = (val) => {
  let startDay = null
  let endDay = null
  if (val === 1) {
    startDay = 0
    endDay = 0
  } else if (val === 2) {
    startDay = -1
    endDay = -1
  } else if (val === 3) {
    startDay = -7
    endDay = -1
  } else if (val === 4) {
    startDay = -30
    endDay = -1
  } else {
    return
  }
  // 开始时间
  const startTime = moment()
    .add(startDay, 'days')
    .startOf('days')
    .format('LL')
  // 结束时间
  const endTime = moment().add(endDay, 'days').endOf('days').format('LL')
  createDateRange.value = [startTime, endTime]
  _searchForm.value.startTime = startTime
  _searchForm.value.endTime = endTime
  createTimeActive.value = val
}

</script>
<style lang="scss" scoped>
.components-take-stock-search-form{
  :deep(.el-form-item) {
    .is-active {
      color: #155bd4;
    }
  }
}

</style>
