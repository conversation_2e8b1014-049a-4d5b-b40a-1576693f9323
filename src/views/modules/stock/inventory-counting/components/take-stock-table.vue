<template>
  <div class="component-take-stock-table content main-container">
    <!-- 表格 -->
    <div class="table-con">
      <el-table
        ref="dataListRef"
        :data="dataList"
        header-cell-class-name="table-header"
        row-class-name="table-row-low"
        style="width: 100%"
      >
        <el-table-column
          :label="$t('takeStock.InventoryNo')"
          prop="takeStockNo"
          fixed="left"
          width="250"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.takeStockNo }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.stockPointType')"
          prop="stockPointType"
          width="100"
        >
          <template #default="scope">
            <span>{{ ['-', $t('stock.warehouse'), $t('stock.station')][scope.row.stockPointType] || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.stockPointName')"
          prop="warehouseName"
          width="250"
        >
          <template #default="scope">
            {{ scope.row.warehouseName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('takeStock.InventoryStatus')"
          prop="billStatus"
        >
          <template #default="scope">
            <span
              v-if="scope.row.billStatus === 0"
              class="table-cell-text line-clamp-one"
            >{{ $t("takeStock.voided") }}</span>
            <span
              v-if="scope.row.billStatus === 1"
              class="table-cell-text line-clamp-one"
            >{{ $t("takeStock.taking") }}</span>
            <span
              v-if="scope.row.billStatus === 2"
              class="table-cell-text line-clamp-one"
            >{{ $t("takeStock.complete") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('takeStock.skuCount')"
          prop="skuCount"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.skuCount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('takeStock.createTime')"
          prop="createTime"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('takeStock.maker')"
          prop="makerName"
        >
          <template #default="scope">
            <span class="table-cell-text line-clamp-one">{{ scope.row.makerName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('crud.menu')"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <div
              :key="scope.row.takeStockId"
              class="text-btn-con"
            >
              <div
                v-if="isAuth('multishop:takeStockProd:edit') && scope.row.billStatus === 1"
                class="default-btn text-btn"
                @click="updateHandle(scope.row.takeStockId)"
              >
                {{ $t('text.editBtn') }}
              </div>
              <el-popconfirm
                v-if="isAuth('multishop:takeStock:voided') && scope.row.billStatus === 1"
                :confirm-button-text="$t('takeStock.voidInventory')"
                :cancel-button-text="$t('resource.cancel')"
                :hide-icon="true"
                :title="$t('takeStock.voidTip')"
                placement="top"
                @confirm="voidedHandle(scope.row.takeStockId)"
              >
                <template #reference>
                  <div
                    class="default-btn text-btn"
                  >
                    {{ $t('takeStock.voidInventory') }}
                  </div>
                </template>
              </el-popconfirm>
              <div
                v-if="isAuth('multishop:takeStock:detail')"
                class="default-btn text-btn"
                @click="detailHandle(scope.row.takeStockId,scope.row.billStatus)"
              >
                {{ $t('shop.withdrawalDetail') }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>

import { ElMessage } from 'element-plus'
import { isAuth } from '@/utils'
const emit = defineEmits(['refreshChange'])
// eslint-disable-next-line no-unused-vars
const props = defineProps({
  // eslint-disable-next-line vue/require-prop-types
  dataList: {
    default: []
  }
})
const Router = useRouter()
const updateHandle = (takeStockId) => {
  Router.push({
    path: '/stock/inventory-counting/edit-take-stock',
    query: {
      takeStockId
    }
  })
}
const voidedHandle = (takeStockId) => {
  http({
    url: http.adornUrl('/stock/takeStock/voidedInventory/' + takeStockId),
    method: 'put',
    params: http.adornParams()
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1000
    })
    refreshChange()
  })
}
const detailHandle = (takeStockId, billStatus) => {
  Router.push({
    path: '/stock/inventory-counting/detail-take-stock',
    query: {
      takeStockId,
      billStatus
    }
  })
}
const refreshChange = () => {
  emit('refreshChange')
}

</script>
<style scoped>
div :deep(.table-cell-text){
  display: inline !important;
}
</style>
