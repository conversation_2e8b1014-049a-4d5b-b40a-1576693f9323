<template>
  <div class="page-stock-inventory-counting-edit-take-stock new-supplier-mod">
    <div class="new-page-title">
      <div class="line" />
      <div class="text">
        {{ $t('takeStock.editInventory') }}
      </div>
    </div>
    <div class="info-title">
      {{ $t('shopProcess.basicInfo') }}
    </div>
    <el-divider />
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      class="form-box"
      label-width="auto"
      @submit.prevent
      @keyup.enter="onSubmit()"
    >
      <el-form-item
        :label="$t('takeStock.InventoryNo')"
        prop="takeStockNo"
      >
        <span class="table-cell-text line-clamp-one">{{ dataForm.takeStockNo }}</span>
      </el-form-item>
      <el-form-item
        :label="$t('takeStock.createTime')"
        prop="createTime"
      >
        <span class="table-cell-text line-clamp-one">{{ dataForm.createTime }}</span>
      </el-form-item>
      <el-form-item
        :label="$t('stock.stockPointType')"
        prop="stockPointType"
      >
        <span class="table-cell-text line-clamp-one">{{ dataForm.stockPointType === 1 ? $t('stock.warehouse') : $t('stock.station') }}</span>
      </el-form-item>
      <el-form-item
        :label="$t('stock.stockPointName')"
        prop="warehouseName"
      >
        <span class="table-cell-text line-clamp-one">{{ dataForm.warehouseName }}</span>
      </el-form-item>
      <el-form-item
        :label="$t('takeStock.regionName')"
        prop="stockRegionName"
      >
        <el-input
          v-model="dataForm.stockRegionName"
          style="width: 320px"

          type="text"
          show-word-limit
          maxlength="20"
          :placeholder="$t('takeStock.regionName')"
        />
        <el-tooltip
          class="item"
          effect="dark"
          :content="$t('takeStock.regionNameTip')"
          placement="top"
        >
          <el-icon><InfoFilled /></el-icon>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        :label="$t('publics.remark')"
        prop="remark"
      >
        <el-input
          v-model="dataForm.remark"
          style="width: 200px"
          type="textarea"

          show-word-limit
          maxlength="200"
          :autosize="{ minRows: 2, maxRows: 5 }"
          :placeholder="$t('product.content')"
        />
      </el-form-item>
    </el-form>
    <!--sku列表-->
    <skuListInfo
      v-if="dataForm.takeStockId"
      ref="skuListInfoRef"
      :required="true"
      :take-stock-id="dataForm.takeStockId"
      :stock-point-id="dataForm.warehouseId"
      :stock-point-type="dataForm.stockPointType"
    />
    <div
      class="footer"
      style="z-index: 3"
    >
      <div
        class="default-btn"
        @click="back()"
      >
        {{ $t("shopFeature.edit.back") }}
      </div>
      <div
        v-if="isAuth('multishop:takeStockProd:update')"
        type="primary"
        class="default-btn primary-btn"
        @click="updateStock()"
      >
        {{ $t('takeStock.saveDraft') }}
      </div>
      <div
        v-if="isAuth('multishop:takeStockProd:finish')"
        type="primary"
        class="default-btn primary-btn"
        @click="finishStock()"
      >
        {{ $t('takeStock.finishInventory') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { isAuth } from '@/utils'
import skuListInfo from './components/take-stock-sku-info.vue'
import { validNoEmptySpace } from '@/utils/validate'
const Router = useRouter()
const validateStockRegionName = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}
const validateRemark = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}

const dataForm = ref({
  takeStockId: null,
  takeStockNo: null,
  billStatus: null,
  createTime: null,
  stockRegionName: null,
  remark: null,
  shopId: null,
  takeStockProdList: [],
  stockPointId: null,
  warehouseName: null,
  stockPointType: null
})
const dataRule = {
  stockRegionName: [
    { validator: validateStockRegionName, trigger: 'blur' }
  ],
  remark: [
    { validator: validateRemark, trigger: 'blur' }
  ]
}
onMounted(() => {
  if (useRoute().query.takeStockId) {
    dataForm.value.takeStockId = useRoute().query.takeStockId
  }
  getDataList()
})

const getDataList = () => {
  http({
    url: http.adornUrl('/stock/takeStock/info/' + dataForm.value.takeStockId),
    method: 'get',
    params: http.adornParams({
      takeStockId: dataForm.value.takeStockId
    })
  }).then(({ data }) => {
    dataForm.value = data
  })
}
// 表单提交
const onSubmit = () => {
}

const skuListInfoRef = ref(null)
const dataFormRef = ref(null)
const updateStock = () => {
  const flag = checkInfo()
  if (!flag) {
    return
  }
  dataFormRef.value?.validate(valid => {
    if (valid) {
      http({
        url: http.adornUrl('/stock/takeStock'),
        method: 'put',
        data: dataForm.value
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1000
        })
        skuListInfoRef.value?.getSkuList()
        refreshChange()
      })
    }
  })
}
const finishStock = () => {
  const flag = checkInfo()
  if (!flag) {
    return
  }
  dataFormRef.value?.validate(valid => {
    if (valid) {
      http({
        url: http.adornUrl('/stock/takeStock/finishInventory'),
        method: 'put',
        data: dataForm.value
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1000
        })
        Router.push({
          path: '/stock/inventory-counting/take-stock',
          query: {
          }
        })
      })
    }
  })
}
const back = () => {
  Router.push('/stock/inventory-counting/take-stock')
}
const refreshChange = () => {
  getDataList()
}
const checkInfo = () => {
  dataForm.value.takeStockProdList = skuListInfoRef.value?.verifyDataForm()
  if (!dataForm.value.takeStockProdList) {
    ElMessage({
      message: $t('takeStock.prodNotNull'),
      type: 'error',
      duration: 1500
    })
    return false
  }
  if (dataForm.value.takeStockProdList && dataForm.value.takeStockProdList.find(el => !el.totalStock && el.totalStock !== 0)) {
    ElMessage({
      message: $t('takeStock.prodStockNotNull'),
      type: 'error',
      duration: 1500
    })
    return false
  }
  return true
}

</script>

<style scoped>
div :deep(.is-always-shadow) {
  margin-bottom:20px !important;
}
div :deep(.is-success .el-input-number__decrease),
div :deep(.is-success .el-input-number__increase),
div :deep(.is-error .el-input-number__decrease),
div :deep(.is-error .el-input-number__increase) {
  right: 1px !important;
}
div :deep(.el-date-editor .el-range-separator) {
  width: 8%;
}
.form-box {
  margin-left: 10px;
}
.new-supplier-mod .footer {
  height: 50px;
  position: fixed;
  bottom: 0;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 -3px 5px #eee;
  background: #FFF;
}
.info-title {
  color: #333333;
  font-size: 16px;
  font-weight: bold;
}

.prod-info .text {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-left: 10px;
  width: 170px;
}

.new-supplier-mod {
  padding-bottom: 40px;
}

:deep(.el-table__row .el-input__suffix) {
  right: -42px;
}
/* @media (max-width: 1440px) {

} */
</style>
