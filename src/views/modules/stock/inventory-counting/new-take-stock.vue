<template>
  <div class="page-stock-inventory-counting-new-take-stock container">
    <div class="basicInfo-container">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="dataFormRef"
        :model="dataForm"
        :rules="dataRule"
        label-width="auto"
        label-position="right"
        @submit.prevent
      >
        <span class="title">{{ $t("product.basicInformation") }}</span>
        <el-divider />
        <el-form-item
          :label="$t('takeStock.createTime')"
          prop="createTime"
        >
          <el-date-picker
            v-model="dataForm.createTime"
            type="datetime"
            :placeholder="$t('admin.seleData')"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item
          :label="$t('stock.stockPointType')"
          prop="stockPointType"
        >
          <el-radio-group
            v-model="dataForm.stockPointType"
            @change="onChangeStockPointType"
          >
            <el-radio
              :label="1"
            >
              {{ $t('stock.warehouse') }}
            </el-radio>
            <el-radio
              :label="2"
            >
              {{ $t('stock.station') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="$t('stock.stockPointName')"
          prop="warehouseId"
        >
          <el-select
            v-model="dataForm.warehouseId"
            :placeholder="$t('stock.pleaseSelectStockPoint')"
          >
            <el-option
              v-for="(item, index) in stockPointList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="$t('takeStock.regionName')"
          prop="stockRegionName"
        >
          <el-input
            v-model="dataForm.stockRegionName"
            style="width: 30%"

            type="text"
            show-word-limit
            maxlength="20"
            :placeholder="$t('takeStock.regionName')"
          />
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('takeStock.regionNameTip')"
            placement="top"
          >
            <el-icon><InfoFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item
          :label="$t('publics.remark')"
          prop="remark"
        >
          <el-input
            v-model="dataForm.remark"
            style="width: 30%"
            type="textarea"

            show-word-limit
            maxlength="200"
            :autosize="{ minRows: 2, maxRows: 5 }"
            :placeholder="$t('product.content')"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="foot-btn">
      <div
        v-if="isAuth('multishop:takeStock:save')"
        class="default-btn primary-btn"
        @click="confirmSave(1)"
      >
        {{ $t('takeStock.editInventory') }}
      </div>
      <div
        class="default-btn"
        @click="confirmSave(2)"
      >
        {{ $t("resource.cancel") }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { isAuth } from '@/utils'
import moment from 'moment'
import { validNoEmptySpace } from '@/utils/validate'
const Router = useRouter()
const validateStockRegionName = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}
const validateRemark = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}
const validateWarehouseId = (rule, value, callback) => {
  if (value && dataForm.stockPointType === 1 && defaultWarehouse.warehouseId === value && !(defaultWarehouse.provinceId && defaultWarehouse.cityId && defaultWarehouse.areaId)) {
    callback(new Error($t('stock.defaultWarehouseAddress')))
  } else {
    callback()
  }
}

const dataForm = reactive({
  createTime: moment().add('days').format('YYYY-MM-DD HH:mm:ss'), // 盘点开始时间
  stockRegionName: '', // 盘点区域名称
  stockPointType: '', // 库存点类型
  warehouseId: null, // 库存点Id
  remark: '' // 备注
})
const dataRule = {
  createTime: [
    {
      type: 'string',
      required: true,
      message: $t('takeStock.createTimeNotNull'),
      trigger: 'change'
    }
  ],
  stockPointType: [
    {
      required: true,
      message: $t('stock.pleaseSelectStockPointType'),
      trigger: 'change'
    }
  ],
  warehouseId: [
    { required: true, message: $t('stock.pleaseSelectStockPoint'), trigger: 'blur' },
    { validator: validateWarehouseId, trigger: 'change' }
  ],
  stockRegionName: [
    { validator: validateStockRegionName, trigger: 'blur' }
  ],
  remark: [
    { validator: validateRemark, trigger: 'blur' }
  ]
}

onMounted(() => {
  // 初始化数据，默认选择仓库类型
  dataForm.stockPointType = 1
})

let defaultWarehouse = {}
const stockPointList = ref([])
watch(() => dataForm.stockPointType, (newVal) => {
  dataForm.warehouseId = null
  if (newVal === 2) {
    http({
      url: http.adornUrl('/admin/station/list_station'),
      method: 'get'
    }).then(({ data }) => {
      stockPointList.value = data.map(item => {
        return {
          label: item.stationName,
          value: item.stationId
        }
      })
    })
  } else {
    http({
      url: http.adornUrl('/m/warehouse/list_warehouse'),
      method: 'get'
    }).then(({ data }) => {
      stockPointList.value = data.map(item => {
        if (item.type === 0) {
          defaultWarehouse = item
        }
        return {
          label: item.warehouseName,
          value: item.warehouseId
        }
      })
    })
  }
  dataFormRef.value.clearValidate('warehouseId')
})

const dataFormRef = ref(null)
const confirmSave = (status) => {
  if (status === 1) {
    dataFormRef.value?.validate((valid) => {
      if (valid) {
        http({
          url: http.adornUrl('/stock/takeStock'),
          method: 'post',
          data: http.adornData(dataForm)
        }).then(({ data }) => {
          ElMessage({
            message: $t('publics.operation'),
            type: 'success',
            duration: 1500
          })
          Router.push({
            path: '/stock/inventory-counting/edit-take-stock',
            query: {
              takeStockId: data
            }
          })
        })
      }
    })
  } else if (status === 2) {
    Router.push({
      path: '/stock/inventory-counting/take-stock',
      query: {}
    })
  }
}

</script>
<style lang="scss" scoped>
.container {
  .basicInfo-container {
    .title {
      color: #333333;
      font-size: 16px;
      font-weight: bold;
    }
  }
  &:deep(.el-select) {
    width: 220px;
  }
  /* 脚部按钮 */
  .foot-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 15px 0;
    display: flex;
    justify-content: center;
    background: #fff;
    // border-top: 1px solid #ddd;
    box-shadow: 0 -2px 3px rgba(139, 139, 139, 0.1);
    z-index: 1999;
  }
}
</style>
