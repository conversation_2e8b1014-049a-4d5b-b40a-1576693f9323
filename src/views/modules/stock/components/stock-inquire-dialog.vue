<template>
  <el-dialog
    v-model="visible"
    :title="dataList[0] ? dataList[0].prodName : $t('stock.skuStockInfo')"
    class="component-stock-inquire-dialog"
  >
    <el-table
      ref="dataListRef"
      :data="dataList"
      header-cell-class-name="table-header"
      row-class-name="table-row"
      :row-style="{ height:'70px'}"
      style="width: 100%"
    >
      <el-table-column
        prop="partyCode"
        header-align="center"
        align="center"
        :label="$t('product.commodityCode')"
      >
        <template #default="scope">
          <el-tooltip
            :content="scope.row.partyCode"
            placement="top"
            effect="light"
          >
            <div class="sku-name-txt">
              {{ scope.row.partyCode }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column
        prop="skuName"
        header-align="center"
        align="center"
        :label="$t('stock.spec')"
      >
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.skuName"
            :content="scope.row.skuName"
            placement="top"
            effect="light"
          >
            <div class="sku-name-txt">
              {{ scope.row.skuName }}
            </div>
          </el-tooltip>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('stock.unit')"
        prop="createTime"
        align="center"
        width="120"
      >
        <template #default>
          <span class="table-cell-text line-clamp-one">{{ $t('stock.pieces') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('coupon.stock')"
        prop="stocks"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span class="table-cell-text line-clamp-one">{{ scope.row.stocks }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('stock.price')"
        prop="price"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span class="table-cell-text line-clamp-one">{{ scope.row.price }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      :current-page="page.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="page.pageSize"
      :total="page.total"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 12px;"
      @size-change="onPageSizeChange"
      @current-change="onPageChange"
    />
  </el-dialog>
</template>

<script setup>

const visible = ref(false)
const dataList = ref([]) // 数据列表
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = {
  prodId: null
}

const init = (prodId) => {
  visible.value = true
  dataList.value = []
  page.total = 0// 总页数
  page.currentPage = 1 // 当前页数
  page.pageSize = 10 // 每页显示多少条
  searchForm.prodId = prodId
  getDataList()
}
defineExpose({
  init
})
const getDataList = (pageParam) => {
  http({
    url: http.adornUrl('/sku/pageSku'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        searchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  }).catch(() => {
  })
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}

</script>

<!-- eslint-disable-next-line vue-scoped-css/enforce-style-type -->
<style lang="scss">
.component-stock-inquire-dialog .el-dialog__title{
  max-width: 95%;
  display:block;
  word-break: break-word;
}
</style>
