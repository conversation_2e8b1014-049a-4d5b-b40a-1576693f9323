<template>
  <!-- 发货信息，用于导出代发货订单的excel交给快递公司 -->
  <el-dialog
    v-model="visible"
    :modal="false"
    :title="$t('product.uploadTips')"
    :close-on-click-modal="false"
    width="38%"
    class="component-stock-sku-upload"
  >
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      :action="http.adornUrl(`/shop/stockBillLogItem/importExcel`)"
      :headers="{ Authorization: cookie.get('bbcAuthorization_vs'),locale:lang }"
      :data="{
        stockPointId: stockPointId,
        type: type
      }"
      :limit="1"
      name="excelFile"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-error="uploadFalse"
      :on-success="uploadSuccess"
      :file-list="files"
      :auto-upload="false"
      :before-upload="beforeAvatarUpload"
    >
      <!-- :file-list="fileList" -->
      <!-- multiple -->
      <template #tip>
        <div

          class="el-upload__tip"
        />
      </template>
      <template #trigger>
        <div class="default-btn primary-btn">
          {{
            $t("product.selectFile")
          }}
        </div>
      </template>
      <div
        class="default-btn download-btn"
        @click="submitUpload"
      >
        {{ $t("product.import") }}
      </div>
      <div
        class="default-btn download-btn"
        @click="downloadModel"
      >
        {{ $t("product.downloadTemplate") }}
      </div>
    </el-upload>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import http from '@/utils/http.js'
import cookie from 'vue-cookies'

const emit = defineEmits(['refreshDataList', 'refreshDataList'])
const props = defineProps({
  type: {
    default: 0,
    type: Number // 1 出库 2 入库
  },
  stockPointId: {
    default: 0,
    type: String || Number // 库存点id
  }
})

const lang = localStorage.getItem('bbcLang') || 'zh_CN'
let isSubmit = false
const visible = ref(false)
let upload = false
const files = ref([])
const params = reactive({
  type: 0
})

onMounted(() => {
  params.type = props.type
})

const uploadSuccess = (response) => {
  alert(response.data.tips)
  files.value = []
  visible.value = false
  emit('refreshDataList', response.data)
}
const uploadFalse = (response) => {
  alert($t('product.fileUploadFail'))
}
const init = () => {
  visible.value = true
}
defineExpose({ init })
// 上传前对文件的大小的判断
const beforeAvatarUpload = (file) => {
  upload = true
  const extension = file.name.split('.')[1] === 'xls'
  const extension2 = file.name.split('.')[1] === 'xlsx'
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!extension && !extension2) {
    alert($t('product.downloadTemplateTips1'))
  }
  if (!isLt2M) {
    alert($t('product.downloadTemplateTips2'))
  }
  return extension || (extension2 && isLt2M)
}
const uploadRef = ref(null)
const submitUpload = () => {
  upload = false
  uploadRef.value?.submit()
  if (!upload) {
    ElMessage.error($t('shop.fileNullTip'))
  }
}
const handleRemove = () => {
  // console.log(file)
}
const handlePreview = (file) => {
  if (file.response.status) {
    alert($t('product.fileSuccess'))
    emit('refreshDataList')
  } else {
    alert($t('product.fileFail'))
  }
}
// 下载模板
const downloadModel = () => {
  if (isSubmit) {
    return
  }
  isSubmit = true
  http({
    url: http.adornUrl('/shop/stockBillLogItem/downloadModel'),
    method: 'get',
    params: http.adornParams({ type: params.type }),
    responseType: 'blob' // 解决文件下载乱码问题
  }).then(({ data }) => {
    const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
    const fileName = params.type === 1 ? $t('stock.outProdFileName') : $t('stock.inProdFileName')
    const elink = document.createElement('a')
    if ('download' in elink) { // 非IE下载
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
    ElMessage({
      message: $t('stock.downloadSuccessful'),
      type: 'success',
      duration: 1500
    })
    isSubmit = false
  }).catch(() => {
    isSubmit = false
  })
}

</script>
<style scoped>
.download-btn {
  margin-left: 12px;
}
div :deep(.el-dialog__body) {
  padding-top: 10px;
}
</style>
