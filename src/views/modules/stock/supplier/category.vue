<template>
  <div class="mod-supplier-supplier">
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('multishop:supplierCategory:save')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <div class="table-con">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          :row-style="{ height:'70px'}"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('shop.supplierCategoryName')"
            prop="name"
            align="left"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('publics.remark')"
            prop="remark"
            align="left"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.remark || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('product.status')"
            prop="statusName"
            align="left"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.statusName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="220"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('multishop:supplierCategory:update')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.supplierCategoryId)"
                >
                  {{ $t('temp.modify') }}
                </div>
                <div
                  v-if="scope.row.status === 1 && isAuth('multishop:supplierCategory:change')"
                  class="default-btn text-btn"
                  @click="disableStatus(scope.row.supplierCategoryId)"
                >
                  {{ $t("publics.disable") }}
                </div>
                <div
                  v-if="scope.row.status === 0 && isAuth('multishop:supplierCategory:change')"
                  class="default-btn text-btn"
                  @click="changeStatus(scope.row.supplierCategoryId)"
                >
                  {{ $t("shop.ena") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'
import AddOrUpdate from './components/supplierCategory-add-or-update.vue'

const emit = defineEmits(['refreshDataList'])

const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})

const addOrUpdateVisible = ref(false)

onMounted(() => {
  getDataList(page)
})

const getDataList = (pageParam) => {
  http({
    url: http.adornUrl('/supplier/supplierCategory/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        }
      )
    )
  }).then(({ data }) => {
    data.records.forEach(row => {
      row.statusName = row.status === 1 ? $t('shop.ena') : $t('publics.disable')
    })

    dataList.value = data.records
    page.total = data.total
  })
}
// 新增 / 修改
const addOrUpdateRef = ref(null)
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id)
  })
}
const disableStatus = (id) => {
  ElMessageBox.confirm($t('shop.disableCategoryTip'), $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    changeStatus(id)
  }).catch(() => {
  })
}
const changeStatus = (id) => {
  http({
    url: http.adornUrl('/supplier/supplierCategory/changeCategoryStatus'),
    method: 'put',
    params: {
      supplierCategoryId: id
    }
  }).then(() => {
    ElMessage({
      message: $t('publics.operation'),
      type: 'success',
      duration: 1500,
      onClose: () => {
        // visible = false
        emit('refreshDataList')
      }
    })
    getDataList(page)
  })
}
/**
   * 刷新回调
   */
const refreshChange = () => {
  getDataList(page)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}
// const resetForm = (formName) => {
//   $refs[formName].resetFields()
// }

</script>
<style lang="scss" scoped>

</style>
