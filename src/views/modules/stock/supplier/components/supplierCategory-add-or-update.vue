<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.supplierCategoryId ? $t('crud.addTitle') : $t('temp.modify')"
    :append-to-body="visible"
    :width="dialogWidth"
  >
    <!-- native modifier has been removed, please confirm whether the function has been affected  -->
    <!-- native modifier has been removed, please confirm whether the function has been affected  -->
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      :label-width="$t('language') === '简体中文' ? '130px' : '180px'"
      @submit.prevent
      @keyup.enter="onSubmit()"
    >
      <el-form-item
        :label="$t('shop.supplierCategoryName')+':'"
        prop="name"
      >
        <el-input
          v-model="dataForm.name"
          maxlength="10"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('publics.remark')+':'"
        prop="remark"
      >
        <el-input
          v-model="dataForm.remark"
          type="textarea"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span

        class="dialog-footer"
      >
        <div
          class="default-btn"
          @click="visible = false"
        >{{ $t("crud.filter.cancelBtn") }}</div>
        <div
          class="default-btn primary-btn"
          @click="onSubmit()"
        >{{ $t("crud.filter.submitBtn") }}</div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { setDialogWidth, widthChange } from '@/utils/setDialogWidth'
import { validNoEmptySpace } from '@/utils/validate'
import { ElMessage } from 'element-plus'
const emit = defineEmits(['refreshDataList'])

const validEmptyTab = (rule, value, callback) => {
  if (validNoEmptySpace(value)) {
    callback(new Error($t('shopProcess.inputAllSpace')))
  } else {
    callback()
  }
}

const visible = ref(false)
const dataForm = ref({
  supplierCategoryId: null,
  name: null,
  remark: null,
  status: 1
})
const dataRule = reactive({
  name: [
    { required: true, message: $t('shop.supplierCategoryName') + $t('publics.noNull'), trigger: 'blur' },
    { validator: validEmptyTab, trigger: 'blur' }
  ]
})
const _them = reactive({
  dialogWidth: ''
})
const dialogWidth = toRef(_them, 'dialogWidth')
const defWidth = 815
onMounted(() => {
  dialogWidth.value = setDialogWidth(defWidth)

  widthChange(_them, defWidth)
})

const init = (supplierCategoryId) => {
  dataForm.value.supplierCategoryId = supplierCategoryId || 0
  visible.value = true
  nextTick(() => {
    dataFormRef.value?.resetFields()
  })
  if (dataForm.value.supplierCategoryId) {
    http({
      url: http.adornUrl('/supplier/supplierCategory/info/' + dataForm.value.supplierCategoryId),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      dataForm.value = data
    })
  }
}
defineExpose({ init })
// 表单提交
const dataFormRef = ref(null)
const onSubmit = () => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      http({
        url: http.adornUrl('/supplier/supplierCategory'),
        method: dataForm.value.supplierCategoryId ? 'put' : 'post',
        data: http.adornData(dataForm.value)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }
  })
}

</script>
<style scoped>

</style>
