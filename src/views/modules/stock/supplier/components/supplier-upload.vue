<template>
  <!-- 供应商导入 -->
  <el-dialog
    v-model="visible"
    :modal="false"
    :title="$t('product.uploadTips')"
    :close-on-click-modal="false"
    width="38%"
  >
    <div class="tips">
      <p>{{ $t('shop.exportWarn') }}</p>
    </div>
    <div style="display: inline-block">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        :action="http.adornUrl('/supplier/supplier/importExcel')"
        :headers="{ Authorization: cookie.get('bbcAuthorization_vs'),locale:lang }"
        :limit="1"
        name="excelFile"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-error="uploadFalse"
        :on-success="uploadSuccess"
        :file-list="files"
        :auto-upload="false"
        :before-upload="beforeAvatarUpload"
      >
        <template #tip>
          <div

            class="el-upload__tip"
          />
        </template>
        <template #trigger>
          <div class="default-btn primary-btn">
            {{ $t("product.selectFile") }}
          </div>
        </template>
        <div
          v-if="isAuth('supplier:supplier:import')"
          class="default-btn download-btn"
          @click="submitUpload"
        >
          {{ $t("product.import") }}
        </div>
        <div
          v-if="isAuth('supplier:supplier:downLoad')"
          class="default-btn download-btn"
          @click="downloadModel"
        >
          {{ $t("product.downloadTemplate") }}
        </div>
      </el-upload>
    </div>
  </el-dialog>
</template>
<script setup>

import { ElMessage } from 'element-plus'
import http from '@/utils/http.js'
import cookie from 'vue-cookies'
import { isAuth } from '@/utils'
const emit = defineEmits(['refreshDataList', 'refreshDataList'])

const lang = localStorage.getItem('bbcLang') || 'zh_CN'
const visible = ref(false)
let upload = false
const files = ref([])

const uploadSuccess = (response) => {
  alert(response.data)
  files.value = []
  visible.value = false
  emit('refreshDataList')
}
const uploadFalse = (response) => {
  alert($t('product.fileUploadFail'))
}
const init = () => {
  visible.value = true
  nextTick(() => {
    // $refs['dataForm'].resetFields()
  })
}
defineExpose({ init })
// 上传前对文件的大小的判断
const beforeAvatarUpload = (file) => {
  upload = true
  const extension = file.name.split('.')[1] === 'xls'
  const extension2 = file.name.split('.')[1] === 'xlsx'
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!extension && !extension2) {
    alert($t('product.downloadTemplateTips1'))
  }
  if (!isLt2M) {
    alert($t('product.downloadTemplateTips2'))
  }
  return extension || (extension2 && isLt2M)
}
const uploadRef = ref(null)
const submitUpload = () => {
  upload = false
  uploadRef.value?.submit()
  if (!upload) {
    ElMessage.error($t('shop.fileNullTip'))
  }
}
const handleRemove = () => {
}
const handlePreview = (file) => {
  if (file.response.status) {
    alert($t('product.fileSuccess'))
    emit('refreshDataList')
  } else {
    alert($t('product.fileFail'))
  }
}
// 下载模板
const downloadModel = () => {
  http({
    url: http.adornUrl('/supplier/supplier/downLoadModel'),
    method: 'get',
    responseType: 'blob' // 解决文件下载乱码问题
  }).then(({ data }) => {
    const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
    const fileName = $t('shop.fileName')
    const elink = document.createElement('a')
    if ('download' in elink) { // 非IE下载
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  })
}

</script>
<style scoped>
.download-btn {
  margin-left: 12px;
}
div :deep(.el-dialog__body) {
  padding-top: 10px;
}
</style>
