<template>
  <div class="mod-supplier-supplier">
    <div class="search-bar">
      <!-- native modifier has been removed, please confirm whether the function has been affected  -->
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"

        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="supplierName"
            :label="$t('shop.supplierName')+':'"
          >
            <el-input
              v-model="searchForm.supplierName"
              type="text"
              clearable
              :placeholder="$t('shop.supplierName')"
            />
          </el-form-item>
          <el-form-item
            prop="supplierProdCount"
            :label="$t('shop.supplierCategoryName')+':'"
          >
            <el-select
              v-model="searchForm.supplierCategoryId"
              clearable
              :placeholder="$t('tip.select')"
            >
              <el-option
                v-for="category in categoryList"
                :key="category.supplierCategoryId"
                :label="category.name"
                :value="category.supplierCategoryId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="text-btn-con">
              <div
                class="default-btn primary-btn"
                @click="onSearch(true)"
              >
                {{ $t('crud.searchBtn') }}
              </div>
              <div
                class="default-btn"
                @click="resetForm('searchForm')"
              >
                {{ $t('shop.resetMap') }}
              </div>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="table-con">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row"
          :row-style="{ height:'70px'}"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('shop.supplierName')"
            prop="supplierName"
            align="left"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.supplierName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('shop.supplierCategoryName')"
            prop="categoryName"
            align="left"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.categoryName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('shop.supplierProdCount')"
            prop="supplierProdCount"
            align="left"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.supplierProdCount || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="220"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('supplier:prod:edit') && scope.row.isDefault !== 1"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.supplierId,scope.row.supplierName)"
                >
                  {{ $t('text.editBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'

const Router = useRouter()
let tempSearchForm = null // 保存上次点击查询的请求条件

const dataList = ref([])
const categoryList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  supplierName: '',
  supplierCategoryId: null
}) // 搜索
onMounted(() => {
  listCategory().then(({ data }) => {
    categoryList.value = data
  })
  getDataList(page)
})
onActivated(() => {

})

const getDataList = (pageParam, newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/supplier/supplier/prodSupplierPage'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}
// 新增 / 修改
const onAddOrUpdate = (id, name) => {
  Router.push({
    path: '/stock/supplier/new-prod',
    query: {
      supplierId: id,
      supplierName: name
    }
  })
}
const listCategory = () => {
  return http({
    url: http.adornUrl('/supplier/supplierCategory/list'),
    method: 'get',
    params: {
      isAll: 1
    }
  })
}
/**
   * 刷新回调
   */
// eslint-disable-next-line no-unused-vars
const refreshChange = () => {
  getDataList(page)
}
const onSearch = (newData = false) => {
  getDataList(page, newData)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  getDataList(page)
}
const searchFormRef = ref(null)
const resetForm = () => {
  searchFormRef.value?.resetFields()
  searchForm.supplierCategoryId = null
}

</script>
<style lang="scss" scoped>

</style>
