<template>
  <div class="component-stock-upload">
    <el-dialog
      v-model="visible"
      :show-close="false"
      width="650"
    >
      <div class="title">
        <el-tabs
          v-if="spuMold===0 && hasUserPickUp"
          v-model="activeName"
          class="tabs"
          @tab-change="onTabChange"
        >
          <el-tab-pane
            :label="$t('stock.warehouseInventory')"
            name="warehouse"
          />
          <el-tab-pane
            :label="$t('stock.storeInventory')"
            name="station"
          />
        </el-tabs>
        <div
          v-else
          class="title-txt"
        >
          {{ $t('stock.warehouseInventory') }}
        </div>
        <div
          class="close-btn"
          @click="visible = false"
        >
          <el-icon><Close /></el-icon>
        </div>
      </div>
      <!-- 搜索栏 -->
      <div
        v-if="spuMold!==1"
        class="search-bar"
      >
        <el-form
          ref="searchFormRef"
          :inline="true"
          :model="searchForm"
          label-width="auto"
          @submit.prevent
        >
          <div class="input-row">
            <el-form-item
              :label="(activeName === 'warehouse'?$t('stock.warehouseName'):$t('stock.stationNames')) + ':'"
              prop="name"
            >
              <el-input
                v-model="searchForm.name"
                clearable
                :placeholder="(activeName === 'warehouse'?$t('stock.warehouseName'):$t('stock.stationNames'))"
                @change="onChangePointName"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="onSearch(true)"
              >
                {{ $t('stock.search') }}
              </el-button>
              <el-button
                @click="onResetSearch(searchFormRef)"
              >
                {{ $t('stock.reset') }}
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 搜索栏end -->
      <div class="content">
        <el-table
          v-loading="pageLoading"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            :prop="activeName === 'warehouse' ? 'warehouseName' : 'stationName'"
            :label="activeName === 'warehouse'?$t('stock.warehouseName'):$t('stock.stationNames')"
            align="center"
          />
          <el-table-column
            v-if="activeName === 'warehouse'"
            :label="$t('stock.warehouseType')"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.type === 1 ? $t('stock.regionalWarehouse') : $t('stock.defaultWarehouse') }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="activeName === 'station'"
            label="状态"
            prop="status"
          >
            <template #default="scope">
              {{ ['关闭', '营业', '平台下线', '审核中', '审核失败'][scope.row.status] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="stock"
            :label="$t('stock.inventory')"
            align="center"
          />
        </el-table>
        <!-- 分页 -->
        <el-pagination
          v-show="pageData.total > 0"
          style="text-align: right;padding: 16px;"
          :total="pageData.total"
          :current-page="searchForm.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchForm.size"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChangeHandle"
          @current-change="onCurrentChangeHandle"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="visible = false;"
          >
            {{ $t('order.cancel') }}
          </el-button>
          <el-button
            type="primary"
            @click="visible = false;"
          >
            {{ $t('order.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
const visible = ref(false)
const hasUserPickUp = ref(null)
const activeName = ref('warehouse')
const init = (skuIdVal, spuMoldVal, hasUserPickUpVal) => {
  activeName.value = 'warehouse'
  visible.value = true
  searchForm.skuId = skuIdVal
  spuMold.value = spuMoldVal
  hasUserPickUp.value = hasUserPickUpVal
  if (spuMold.value === 1 || spuMold.value === 3) {
    // 虚拟商品||电子卡券只获取默认仓库
    searchForm.type = 0
  } else {
    searchForm.type = undefined
  }
  onGetPageData(true)
}

const spuMold = ref(0)

let tempSearchForm = null // 保存上次点击查询的请求条件
// 头部搜索表单
const searchForm = reactive({
  size: 10,
  current: 1,
  name: '',
  skuId: ''
})
const pageData = ref({
  total: 0,
  pages: 0
})
const dataList = ref([])
const pageLoading = ref(true)
// 获取数据列表
const onGetPageData = (newData = false) => {
  if (newData || !tempSearchForm) {
    if (activeName.value === 'warehouse') {
      searchForm.warehouseName = pointName.value
    } else {
      searchForm.stationName = pointName.value
      searchForm.stockMode = 2
    }
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  } else {
    tempSearchForm.current = searchForm.current
    tempSearchForm.size = searchForm.size
  }
  let url = ''
  if (activeName.value === 'warehouse') {
    url = '/m/warehouse/page'
  } else {
    url = '/admin/station/page'
  }
  http({
    url: http.adornUrl(url),
    method: 'get',
    params: http.adornParams(tempSearchForm)
  }).then(({ data }) => {
    pageData.value.pages = data.pages
    pageData.value.total = data.total
    dataList.value = data.records
    pageLoading.value = false
  }).catch(() => {
    pageLoading.value = false
  })
}

const pointName = ref('')
const onChangePointName = (val) => {
  pointName.value = val
}

const onTabChange = () => {
  pointName.value = ''
  searchForm.name = ''
  searchForm.current = 1
  onGetPageData(true)
}

// 条件查询
const onSearch = (newData = false) => {
  searchForm.current = 1
  onGetPageData(newData)
}

const searchFormRef = ref(null)
const onResetSearch = (formEl) => {
  pointName.value = ''
  formEl.resetFields()
}

// 当前页
const onCurrentChangeHandle = (val) => {
  searchForm.current = val
  onGetPageData()
}

// 每页数
const onSizeChangeHandle = (val) => {
  searchForm.size = val
  searchForm.current = 1
  onGetPageData()
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.component-stock-upload {
  :deep(.el-dialog__header){
    display: none;
  }
  :deep(.el-dialog__body) {
    padding: 0;
  }
  .title {
    position: relative;
    :deep(.el-tabs__nav-scroll) {
      padding: 10px 0 0 20px;
    }
    .close-btn {
      font-size: 18px;
      color: #909399;
      position: absolute;
      top: 10px;
      right: 10px;
      &:hover {
        color: #155bd4;
        cursor: pointer;
      }
    }
    .title-txt {
      font-size: 18px;
      color: #000;
      font-weight: bold;
      padding: 20px;
      border-bottom: #ebeef5 1px solid;
    }
  }
  .search-bar {
    margin: 20px 20px 0;
  }
  .content {
    padding: 20px 20px 0;
  }
  :deep(.el-dialog__footer){
    border-top: #ebeef5 1px solid;
  }
}
</style>
