<template>
  <div class="page-stock-manage-flow mod-marketing-distribution">
    <sku-stock-flow />
  </div>
</template>

<script setup>
import skuStockFlow from './components/sku-stock-flow.vue'

</script>

<style scoped>

</style>

<style lang="scss" scoped>

.page-stock-manage-flow{
  :deep(.el-tabs__item.is-active){
    color:#000;
    font-weight: 800;
  }
  :deep(.el-col-md-6){
      width:30%
  }
  :deep(.select-time-btn) {
  margin-right: 20px;
  display: inline-block;
  color: #AAAAAA;
  font-size: 14px;
  cursor:pointer;
}
  :deep(.select-time-btn:last-child) {
    margin-right: 0;
  }
  :deep(.select-time-btn.is-active) {
    color: #155BD4;
  }
}
</style>
