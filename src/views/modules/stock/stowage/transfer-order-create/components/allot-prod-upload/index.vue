<template>
  <el-dialog
    v-model="visible"
    class="component-purchases-prod-upload"
    :modal="false"
    :title="$t('product.uploadTips')"
    :close-on-click-modal="false"
    width="38%"
  >
    <div>
      <p>{{ $t('stock.uploadTransferOrderProd') }}</p>
    </div>
    <div style="display: inline-block">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        :action="http.adornUrl(uploadUrl)"
        :data="uploadData"
        :headers="{ Authorization: cookie.get('bbcAuthorization_vs'),locale:lang }"
        :limit="1"
        name="excelFile"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-error="uploadFalse"
        :on-success="uploadSuccess"
        :file-list="files"
        :auto-upload="false"
        :before-upload="beforeAvatarUpload"
      >
        <template #tip>
          <div class="el-upload__tip" />
        </template>
        <template #trigger>
          <el-button
            style="margin-right:10px"
            type="primary"
          >
            {{
              $t("product.selectFile")
            }}
          </el-button>
        </template>
        <el-button
          @click="submitUpload"
        >
          {{ $t("product.import") }}
        </el-button>
        <el-button
          @click="downloadModel"
        >
          {{ $t("product.downloadTemplate") }}
        </el-button>
      </el-upload>
    </div>
  </el-dialog>
</template>
<script setup>
import cookie from 'vue-cookies'
import http from '@/utils/http.js'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 下载模板的url
  modelUrl: {
    default: '/allotOrder/downloadModel',
    type: String
  },
  // 上传模板的url
  uploadUrl: {
    default: '/allotOrder/importExcel',
    type: String
  },
  // 下载模板名称
  templateName: {
    default: $t('product.templateName'),
    type: String
  },
  // 上传携带的参数
  uploadData: {
    default: () => {},
    type: Object
  }
})
const emit = defineEmits(['refreshDataList'])

const visible = ref(false)
const upload = ref(false)
const files = ref([])

const uploadSuccess = (response) => {
  alert(response.msg || response.data?.tips || $t('stock.uploadSuccess'))
  const data = response
  visible.value = false
  files.value = []
  emit('refreshDataList', data)
}
const uploadFalse = () => {
  alert($t('product.fileUploadFail'))
}
const init = () => {
  visible.value = true
}
// 上传前对文件的大小的判断
const beforeAvatarUpload = (file) => {
  upload.value = true
  const extension = file.name.split('.')[1] === 'xls'
  const extension2 = file.name.split('.')[1] === 'xlsx'
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!extension && !extension2) {
    alert($t('product.downloadTemplateTips1'))
  }
  if (!isLt2M) {
    alert($t('product.downloadTemplateTips2'))
  }
  return extension || (extension2 && isLt2M)
}
const uploadRef = ref(null)
const submitUpload = () => {
  upload.value = false
  uploadRef.value.submit()
  if (!upload.value) {
    ElMessage.error($t('shop.fileNullTip'))
  }
}
const handleRemove = () => {
}
const handlePreview = (file) => {
  if (file.response.status) {
    alert($t('product.fileSuccess'))
    emit('refreshDataList')
  } else {
    alert($t('product.fileFail'))
  }
}

// 下载模板
const downloadModel = () => {
  http({
    url: http.adornUrl(props.modelUrl),
    method: 'get',
    responseType: 'blob'
  }).then(({ data }) => {
    const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
    const elink = document.createElement('a')
    if ('download' in elink) { // 非IE下载
      elink.download = props.templateName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, props.templateName)
    }
  })
}
defineExpose({ init })
</script>
<style scoped>
</style>
