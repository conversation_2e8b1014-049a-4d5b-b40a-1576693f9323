<template>
  <el-dialog
    v-model="visible"
    :title="$t('product.select')"
    :modal="true"
    top="100px"
    width="70%"
    class="component-transfer-prod-select"
    :close-on-click-modal="false"
  >
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        :model="searchForm"
        @submit.prevent
        @keyup.enter="onGetPageData(true)"
      >
        <div class="search">
          <el-form-item
            :label="$t('product.prodName')"
            prop="spuName"
          >
            <el-input
              v-model="searchForm.spuName"
              :placeholder="$t('product.prodName')"
              clearable
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item
            :label="$t('publics.code')"
            prop="partyCode"
          >
            <el-input
              v-model="searchForm.partyCode"
              :placeholder="$t('publics.code')"
              clearable
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="onSearch()"
            >
              {{ $t('order.query') }}
            </el-button>
            <el-button
              @click="onResetSearch(searchFormRef)"
            >
              {{ $t("shop.resetMap") }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main">
      <el-table
        v-loading="pageLoading"
        :data="dataList"
        row-class-name="table-row"
        header-cell-class-name="table-header"
      >
        <el-table-column
          :label="$t('product.prodName')"
          fixed="left"
          align="center"
          width="380"
        >
          <template #default="scope">
            <div class="prod-item">
              <div class="select-dor to-radio">
                <el-checkbox
                  v-model="scope.row.check"
                  :indeterminate="scope.row.indeterminate"
                  @change="checkProd(scope.$index)"
                />
              </div>
              <div class="prod-info">
                <div class="prod-image">
                  <ImgShow :src="scope.row.pic" />
                </div>
                <div class="prod-name">
                  {{ scope.row.prodName }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('product.productSpecifi')"
          align="center"
          width="160"
        >
          <template #default="scope">
            <div
              v-for="(sku, skuIndex) in scope.row.skuList"
              :key="sku.skuId"
              class="items name"
            >
              <div class="sku-container">
                <div class="select-dor">
                  <el-checkbox
                    v-if="scope.row.skuList.length > 1"
                    v-model="sku.check"
                    class="select-dor-check"
                    @change="checkSku(scope.$index, skuIndex)"
                  />
                </div>
                <div class="name">
                  {{ sku.skuLangVOList[0]?.skuName || '-' }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- 商品编码 -->
        <el-table-column
          :label="$t('product.commodityCode')"
          min-width="180"
        >
          <template #default="scope">
            <div
              v-for="(sku) in scope.row.skuList"
              :key="sku.skuId"
              class="price-container"
            >
              {{ sku.partyCode }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.commodityPrice')"
          align="center"
          width="120"
          class="prodPrice"
        >
          <template #default="scope">
            <div
              v-for="(sku) in scope.row.skuList"
              :key="sku.skuId"
              class="price-container"
            >
              {{ sku.price }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.transferOutStorage')"
          align="center"
        >
          <template #default="scope">
            <div
              v-for="(sku) in scope.row.skuList"
              :key="sku.skuId"
              class="stocks-container"
            >
              {{ sku.stocks }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('stock.transferInStorage')"
          align="center"
        >
          <template #default="scope">
            <div
              v-for="(sku) in scope.row.skuList"
              :key="sku.skuId"
              class="stocks-container"
            >
              {{ sku.inStockPointStock }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-pagination
      v-show="page.total > 0"
      style="text-align: right;padding-top: 20px;"
      :total="page.total"
      :current-page="searchForm.current"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="searchForm.size"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="onSizeChangeHandle"
      @current-change="onCurrentChangeHandle"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="visible = false"
        >
          {{ $t('crud.filter.cancelBtn') }}
        </el-button>
        <el-button
          type="primary"
          @click="onSubmit()"
        >
          {{ $t('crud.filter.submitBtn') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
const emit = defineEmits('refreshSelectProds')

const props = defineProps({
  chosenCheckItems: {
    default: () => [],
    type: Array // 已选择的数据项
  },
  // 获取指定的商品类别（0.普通商品；1.虚拟商品；2.组合商品；3.电子卡券）
  moldList: {
    default: () => [], // 默认获取所有
    type: Array
  }
})

const pageLoading = ref(true)
const page = ref({
  total: 0
})

let tempSearchForm = null // 保存上次点击查询的请求条件
const visible = ref(false)
const searchForm = reactive({
  size: 10,
  current: 1,
  spuName: '',
  stockPointId: null
})
const init = (data) => {
  page.value.total = 0
  visible.value = true
  pageLoading.value = false
  chosenProdMap.value = {}
  currentCheckProdMap.value = {}
  searchForm.spuName = ''
  searchForm.partyCode = ''
  searchForm.stockPointId = data.outWarehouseId
  searchForm.inStockPointId = data.inWarehouseId
  searchForm.stockPointType = data.nowOutPoint.stockPointType
  searchForm.stockMode = data.nowOutPoint.stockMode
  searchForm.type = data.nowOutPoint.type
  onGetPageData(true)
  initDefaultCheckedKeys()
}
const dataList = ref([])
// 获取数据列表
const onGetPageData = (newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  } else {
    tempSearchForm.current = searchForm.current
    tempSearchForm.size = searchForm.size
  }
  http({
    url: '/m/stockPointSku/pageHasStockSpu',
    method: 'get',
    params: http.adornParams({
      ...tempSearchForm,
      moldList: props.moldList
    })
  }).then(({ data }) => {
    setProdCheckStatus(data?.records || [])
    dataList.value = data?.records || []
    page.value.total = data.total
  })
}

// 根据已选择的商品项设置商品选择状态
const setProdCheckStatus = (prodList) => {
  prodList.forEach(prod => {
    const skuList = prod.skuList
    if (!currentCheckProdMap.value[prod.prodId]) {
      skuList.forEach(sku => {
        sku.check = false
        sku.allotCount = 0
      })
      prod.check = false
      prod.indeterminate = false
    } else {
      // 当前商品已选择的sku数量
      let selectCount = 0
      const checkSkuMap = currentCheckProdMap.value[prod.prodId].currentCheckSkuMap || {}
      skuList.forEach(sku => {
        sku.allotCount = 0
        if (checkSkuMap[sku.skuId]) {
          ++selectCount
          sku.check = true
        } else {
          sku.check = false
        }
      })
      prod.check = selectCount > 0
      prod.indeterminate = selectCount > 0 && selectCount < skuList.length
    }
  })
}

const onSearch = () => {
  searchForm.current = 1
  onGetPageData(true)
}

// 搜索表单重置
const searchFormRef = ref(null)
const onResetSearch = (formEl) => {
  formEl.resetFields()
}

const chosenProdMap = ref({}) // 已选择的商品结点对象map
const currentCheckProdMap = ref({}) // 当前选择的结点对象map
// 初始化默认选择的商品项记录
const initDefaultCheckedKeys = () => {
  if (props.chosenCheckItems && props.chosenCheckItems.length > 0) {
    props.chosenCheckItems.forEach(sku => {
      sku = sku || {}
      if (currentCheckProdMap.value[sku.spuId]) {
        currentCheckProdMap.value[sku.spuId].currentCheckSkuMap[sku.skuId] = sku
      } else {
        const prodObj = {}
        prodObj.spuId = sku.spuId
        prodObj.prodName = sku.prodName
        prodObj.pic = sku.pic
        prodObj.currentCheckSkuMap = {}
        prodObj.currentCheckSkuMap[sku.skuId] = sku
        currentCheckProdMap.value[sku.spuId] = prodObj
      }
    })
  }
}

// 选择或取消选择商品
const checkProd = (index) => {
  const prod = dataList.value[index]
  if (prod.check) {
    // 把该商品的可选择的sku置为选择状态并保存选择的商品信息
    if (!currentCheckProdMap.value[prod.prodId]) {
      currentCheckProdMap.value[prod.prodId] = prod
    }
    const skus = prod.skuList
    const currentCheckSkuMap = {}
    if (props.limit !== -1 && props.limitType === 2) {
      for (let i = 0; i < skus.length; i++) {
        if (!skus[i].check) {
          skus[i].check = true
        }
        currentCheckSkuMap[skus[i].skuId] = skus[i]
      }
    } else {
      skus.forEach(sku => {
        if (!sku.check) {
          sku.check = true
        }
        currentCheckSkuMap[sku.skuId] = sku
      })
    }
    prod.currentCheckSkuMap = currentCheckSkuMap
    prod.indeterminate = false
  } else {
    // 商品取消选择事件，删除对应的商品结点
    delete currentCheckProdMap.value[prod.prodId]
    prod.skuList.forEach(sku => {
      if (sku.check) {
        sku.check = false
      }
    })
    prod.check = false
    prod.indeterminate = false
  }
}

// 选择或取消选择sku
const checkSku = (prodIndex, skuIndex) => {
  const prod = dataList.value[prodIndex]
  const sku = prod.skuList[skuIndex]
  if (sku.check) {
    if (currentCheckProdMap.value[prod.prodId]) {
      // 该商品存在已选择的sku
      currentCheckProdMap.value[prod.prodId].currentCheckSkuMap[sku.skuId] = sku
    } else {
      // 该商品没有已选择的sku
      currentCheckProdMap.value[prod.prodId] = prod
      const currentCheckSkuMap = {}
      currentCheckSkuMap[sku.skuId] = sku
      currentCheckProdMap.value[prod.prodId].currentCheckSkuMap = currentCheckSkuMap
    }
    const choseCount = Object.keys(currentCheckProdMap.value[prod.prodId].currentCheckSkuMap).length
    prod.check = true
    prod.indeterminate = !(choseCount === prod.skuList.length)
  } else {
    // sku取消选择
    sku.check = false
    const choseCount = Object.keys(currentCheckProdMap.value[prod.prodId].currentCheckSkuMap).length
    if (choseCount === 1) {
      prod.check = false
      prod.indeterminate = false
      delete currentCheckProdMap.value[prod.prodId]
    } else {
      prod.indeterminate = true
      delete currentCheckProdMap.value[prod.prodId].currentCheckSkuMap[sku.skuId]
    }
  }
}

// 确定事件
const onSubmit = () => {
  const selectList = []
  for (const spuId in currentCheckProdMap.value) {
    const prodObj = currentCheckProdMap.value[spuId]
    const choseProdMap = chosenProdMap.value[spuId] || {}
    for (const skuId in prodObj.currentCheckSkuMap) {
      const skuObj = (choseProdMap.chosenSkuMap !== undefined && choseProdMap.chosenSkuMap[skuId]) ? choseProdMap.chosenSkuMap[skuId] : prodObj.currentCheckSkuMap[skuId]
      skuObj.spuId = prodObj.prodId
      skuObj.prodName = prodObj.prodName || prodObj.prodLangVOList[0]?.prodName
      skuObj.pic = prodObj.pic
      selectList.push(skuObj)
    }
  }
  emit('refreshSelectProds', selectList)
  visible.value = false
}

// 每页数
const onSizeChangeHandle = (val) => {
  searchForm.size = val
  searchForm.current = 1
  onGetPageData()
}

// 当前页
const onCurrentChangeHandle = (val) => {
  searchForm.current = val
  onGetPageData()
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.component-transfer-prod-select {
  .main {
    .prod-item {
      display: flex;
      align-items: center;
      align-content: center;
      flex-direction: row;

      .select-dor {
        width: 10px;
        padding: 2px;
      }

      .prod-info {
        display: flex;
        align-items: center;
      }

      .prod-image {
        width: 100px;
        height: 100px;
        padding: 10px;
      }

      .prod-name {
        // width: 240px;
        flex: 1;
        font-size: 14px;
        color: #333333;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }

    .sku-container {
      width: 120px;
      display: flex;
      flex-direction: row;
      min-height: 30px;
      align-items: center;

      .select-dor {
        width: 10px;
        padding: 2px;
        margin-right: 10px;
        display: flex;
        align-items: center;

        .select-dor-check {
          height: 100%;
        }
      }

      .name {
        width: 100px;
        font-size: 14px;
        color: #333333;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .price-container {
      height: 30px;
      line-height: 30px;
      align-content: center;
    }

    .stocks-container {
      height: 30px;
      line-height: 30px;
      align-content: center;
    }
  }
}
</style>
