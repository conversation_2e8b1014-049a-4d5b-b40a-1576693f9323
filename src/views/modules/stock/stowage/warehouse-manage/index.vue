<template>
  <div class="page-warehouse-manage">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('stock.warehouseName') + ':'"
            prop="warehouseName"
          >
            <el-input
              v-model="searchForm.warehouseName"
              :placeholder="$t('stock.inputWareHouseName')"
            />
          </el-form-item>
          <el-form-item
            label-width="100"
            :label="$t('stock.warehouseType') + ':'"
            prop="type"
          >
            <el-select
              v-model="searchForm.type"
              :placeholder="$t('stock.pleaseSelect')"
              clearable
            >
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label-width="120"
            :label="$t('stock.custodian') + ':'"
            prop="manage"
          >
            <el-input
              v-model="searchForm.manage"
              :placeholder="$t('stock.inputCustodian')"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="onSearch(true)"
            >
              {{ $t('stock.search') }}
            </el-button>
            <el-button
              @click="onResetSearch(searchFormRef)"
            >
              {{ $t('stock.reset') }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <!-- 搜索栏end -->
    <!-- 表格主体 -->
    <div class="main main-content">
      <div class="operation-bar">
        <el-button
          type="primary"
          @click="onAddOrUpdate(-1, 1)"
        >
          {{ $t('stock.newlyIncreased') }}
        </el-button>
      </div>
      <!-- 表格 -->
      <div class="table-con">
        <el-table
          v-loading="pageLoading"
          :data="pageData.dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            prop="warehouseName"
            :label="$t('stock.warehouseName')"
          >
            <template #default="scope">
              <span class="table-cell-text line-clamp-one">{{ scope.row.warehouseName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('stock.warehouseAddress')"
            width="320"
          >
            <template #default="{row}">
              <div class="table-cell-con">
                <span class="table-cell-text">{{ ((row.province || '') + (row.city || '') + (row.area || '') + (row.address || '')) || '-' }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="manage"
            :label="$t('stock.custodian')"
          >
            <template #default="scope">
              <span
                class="table-cell-text line-clamp-one"
              >{{ scope.row.manage || '-' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="phone"
            :label="$t('stock.custodianPhone')"
          >
            <template #default="scope">
              {{ scope.row.phone || '-' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="soldNum"
            :label="$t('stock.warehouseType')"
          >
            <template #default="scope">
              {{ scope.row.type === 1 ? $t('stock.regionalWarehouse') : $t('stock.defaultWarehouse') }}
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="280"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <el-button
                  type="primary"
                  link
                  @click="onAddOrUpdate(scope.row.warehouseId, scope.row.type)"
                >
                  {{ $t('stock.edit') }}
                </el-button>
                <el-button
                  type="primary"
                  link
                  @click="onViewInventory(scope.row.warehouseId, scope.row.type)"
                >
                  {{ $t('stock.merchandiseInventory') }}
                </el-button>
                <el-button
                  v-if="scope.row.type!==0"
                  type="primary"
                  link
                  @click="onDeleteWarehouse(scope.row.warehouseId)"
                >
                  {{ $t('stock.delete') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-show="pageData.total > 0"
        style="text-align: right"
        :total="pageData.total"
        :current-page="searchForm.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.size"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onSizeChangeHandle"
        @current-change="onCurrentChangeHandle"
      />
    </div>
    <!-- 表格主体end -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="onGetPageData()"
      @react-add-or-update="onReactAddOrUpdate()"
    />
    <!-- 商品库存 -->
    <prod-inventory
      v-if="prodinventoryVisible"
      ref="prodInventoryRef"
      :stock-point-type="1"
    />
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import AddOrUpdate from './add-or-update.vue'

const typeList = [
  {
    value: 0,
    label: $t('stock.defaultWarehouse')
  },
  {
    value: 1,
    label: $t('stock.regionalWarehouse')
  }
]
let tempSearchForm = null // 保存上次点击查询的请求条件

// 头部搜索表单
const searchForm = reactive({
  size: 10,
  current: 1,
  warehouseName: '',
  manage: '',
  type: ''
})

// 条件查询
const onSearch = (newData = false) => {
  searchForm.current = 1
  onGetPageData(newData)
}

const searchFormRef = ref(null)
const onResetSearch = (formEl) => {
  formEl.resetFields()
}

const pageLoading = ref(true)

const pageData = ref({
  dataList: [],
  total: 0,
  pages: 0
})

// 获取数据列表
const onGetPageData = (newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  } else {
    tempSearchForm.current = searchForm.current
    tempSearchForm.size = searchForm.size
  }
  http({
    url: '/m/warehouse/page',
    method: 'get',
    params: http.adornParams(tempSearchForm)
  }).then(({ data }) => {
    pageData.value.dataList = data.records
    pageData.value.pages = data.pages
    pageData.value.total = data.total
    pageLoading.value = false
  }).catch(() => {
    pageLoading.value = false
  })
}

onMounted(() => {
  onGetPageData()
})

// 新增 / 修改
const addOrUpdateRef = ref(null)
const onAddOrUpdate = (id, type) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id, type)
  })
}

const addOrUpdateVisible = ref(false)
const onReactAddOrUpdate = () => {
  addOrUpdateVisible.value = false
  onGetPageData()
}

const prodInventoryRef = ref(null)
const prodinventoryVisible = ref(false)
/**
 * 查看仓库的商品库存
 */
const onViewInventory = (id, type) => {
  prodinventoryVisible.value = true
  nextTick(() => {
    prodInventoryRef.value.init(id, type)
  })
}

const onDeleteWarehouse = (warehouseId) => {
  ElMessageBox.confirm($t('stock.isDeleOper') + '?', $t('stock.tip'), {
    confirmButtonText: $t('stock.confirm'),
    cancelButtonText: $t('stock.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: '/m/warehouse',
      method: 'delete',
      params: http.adornParams({ warehouseId })
    }).then(() => {
      ElMessage({
        message: $t('stock.success'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          onGetPageData()
        }
      })
    })
  }).catch(() => { })
}

// 每页数
const onSizeChangeHandle = (val) => {
  searchForm.size = val
  searchForm.current = 1
  onGetPageData()
}

// 当前页
const onCurrentChangeHandle = (val) => {
  searchForm.current = val
  onGetPageData()
}
</script>
<style lang="scss" scoped>
.page-warehouse-manage {
  // 操作按钮容器
  .text-btn-con {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .main-content {
    .table-con {
      margin-top: 20px;
      padding-bottom: 30px;
    }
  }
}
</style>
