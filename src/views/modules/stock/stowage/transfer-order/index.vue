<template>
  <div class="page-transfer-order">
    <div class="search-bar">
      <el-form
        :inline="true"
        :model="searchForm"
        label-width="auto"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :label="$t('stock.transferNumber') + ':'"
            prop="allotOrderId"
          >
            <el-input
              v-model="searchForm.allotOrderId"
              oninput="value=value.replace(/[^0-9]/g,'')"
              :placeholder="$t('stock.transferNumber')"
              clearable
              :maxlength="16"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </el-button>
            <el-button
              @click="onResetSearch()"
            >
              {{ $t("shop.resetMap") }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-content">
      <div class="operation-bar">
        <el-button
          type="primary"
          @click="onAddOrUpdate(0)"
        >
          {{ $t('product.addNew') }}
        </el-button>
      </div>
      <!-- 表格 -->
      <div class="table-con">
        <!-- 导航 -->
        <el-tabs
          v-model="activeName"
          @tab-click="selectNav"
        >
          <el-tab-pane
            :name="0"
            :label="$t('stock.all')"
          />
          <el-tab-pane
            v-for="item in statusList"
            :key="item.value"
            :name="item.value"
            :label="item.label"
          />
        </el-tabs>
        <el-table
          ref="imgTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <!-- 调拨编号 -->
          <el-table-column
            prop="seq"
            :label="$t('stock.transferNumber')"
            width="250"
          >
            <template #default="scope">
              <span>{{ scope.row.allotOrderId || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 调出库存点 -->
          <el-table-column
            :label="$t('stock.outStockPoints')"
            min-width="140"
          >
            <template #default="scope">
              <span>{{ scope.row.outStockPointName || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 调入库存点 -->
          <el-table-column
            :label="$t('stock.inStockPoints')"
            min-width="140"
          >
            <template #default="scope">
              <span>{{ scope.row.inStockPointName || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 调拨类型 -->
          <el-table-column
            :label="$t('stock.transferType')"
            width="120"
          >
            <template #default="scope">
              <span>{{ ['', $t('stock.warehouse'), $t('stock.station')][scope.row.outStockPointType] + '-' + ['', $t('stock.warehouse'), $t('stock.station')][scope.row.inStockPointType] }}</span>
            </template>
          </el-table-column>
          <!-- 调拨数量 -->
          <el-table-column
            :label="$t('stock.transferQuantity')"
          >
            <template #default="scope">
              <span>{{ scope.row.totalAllotCount || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 调拨日期 -->
          <el-table-column
            :label="$t('stock.transferDate')"
            min-width="180"
          >
            <template #default="scope">
              <span>{{ scope.row.createTime || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 状态 -->
          <el-table-column
            :label="$t('product.status')"
            width="120"
          >
            <template #default="scope">
              <span v-if="scope.row.status === 0">{{ $t('stock.voided') }}</span>
              <span v-if="scope.row.status === 1">{{ $t('stock.warehoused') }}</span>
              <span v-if="scope.row.status === 2">{{ $t('stock.partiallyComplete') }}</span>
              <span v-if="scope.row.status === 3">{{ $t('stock.complete') }}</span>
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="220"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <el-button
                  v-if="scope.row.status === 1||scope.row.status === 2"
                  type="primary"
                  link
                  @click="onAddOrUpdate(scope.row.allotOrderId)"
                >
                  {{ $t('stock.receiveStock') }}
                </el-button>
                <el-button
                  v-if="scope.row.status !== 3 &&scope.row.status !== 0"
                  type="primary"
                  link
                  @click="nullifyHandle(scope.row.allotOrderId)"
                >
                  {{ $t('stock.voidInventory') }}
                </el-button>
                <el-button
                  type="primary"
                  link
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.allotOrderId, true)"
                >
                  {{ $t('stock.details') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      />
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'

const statusList = [
  {
    value: 1,
    label: $t('stock.voided')
  }, {
    value: 2,
    label: $t('stock.warehoused')
  }, {
    value: 3,
    label: $t('stock.partiallyComplete')
  }, {
    value: 4,
    label: $t('stock.complete')
  }
]

let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
const searchForm = reactive({
  allotOrderId: ''
})
const activeName = ref(0)
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})

onMounted(() => {
  // 携带参数查询
  getDataList(page)
})

const status = ref('')
/**
 * 获取数据列表
 */
const getDataList = (pageParam, newData = false) => {
  pageParam = (page === undefined ? pageParam : page)
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/allotOrder/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize,
          status: status.value
        },
        tempSearchForm
      ))
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}

// 每页数
const sizeChangeHandle = (val) => {
  page.pageSize = val
  page.currentPage = 1
  getDataList(page)
}
// 当前页
const currentChangeHandle = (val) => {
  page.currentPage = val
  getDataList(page)
}
/**
 * 导航选择状态
 */
const selectNav = (e) => {
  const orderStatus = Number(e.paneName)
  status.value = orderStatus ? orderStatus - 1 : ''
  onSearch(true)
}
// 清空按钮
const onResetSearch = () => {
  searchForm.allotOrderId = ''
}
// 搜索查询
const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}
const router = useRouter()
// 新增 / 修改
const onAddOrUpdate = (id, view) => {
  router.push({
    path: '/stock/stowage/transfer-order-create/index',
    query: {
      allotOrderId: id,
      view
    }
  })
}

let isSubmit = false
// 作废
const nullifyHandle = (id) => {
  ElMessageBox.confirm($t('stock.transferVoidInventoryTip'), $t('stock.tip'), {
    confirmButtonText: $t('stock.confirm'),
    cancelButtonText: $t('stock.cancel'),
    type: 'warning'
  }).then(() => {
    if (isSubmit) {
      return
    }
    isSubmit = true
    http({
      url: http.adornUrl('/allotOrder/nullify/' + id),
      method: 'delete'
    }).then(() => {
      ElMessage({
        message: $t('stock.success'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          setTimeout(() => getDataList(page), 1000)
        }
      })
    }).finally(() => {
      isSubmit = false
    })
  })
}

</script>
<style lang="scss" scoped>
.page-transfer-order{
  :deep(.el-pagination){
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style>
