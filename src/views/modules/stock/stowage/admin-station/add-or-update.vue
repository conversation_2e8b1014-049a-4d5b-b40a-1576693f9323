<template>
  <div class="page-station-add-or-update">
    <div class="new-page-title">
      <div class="line" />
      <div class="text">
        {{ pageTitle }}
      </div>
    </div>
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="100px"
      style="min-width:630px;"
      class="form-box"
      @submit.prevent
    >
      <el-form-item
        prop="stockMode"
        :label="$t('stock.stockMold')"
      >
        <el-radio-group
          v-model="dataForm.stockMode"
          :disabled="!!dataForm.stationId"
        >
          <el-radio :label="1">
            {{ $t('stock.sharedHeadquartersInventory') }}
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="$t('stock.stockMoldTip1')"
              placement="top"
            >
              <el-icon style="vertical-align: middle">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </el-radio>
          <el-radio :label="2">
            {{ $t('stock.independentSellingInventory') }}
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="$t('stock.stockMoldTip2')"
              placement="top"
            >
              <el-icon style="vertical-align: middle">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        class="stationName"
        :label="$t('station.stationNames')"
        prop="stationName"
        style="width:624px;"
      >
        <el-input
          v-model="dataForm.stationName"
          maxlength="20"
          show-word-limit
          @change="checkStationNames"
        />
      </el-form-item>
      <el-form-item
        :label="$t('station.stationLogo')"
        prop="pic"
      >
        <div class="part-form-item">
          <imgs-upload
            v-model:modelValue="dataForm.pic"
            :limit="1"
            :prompt="false"
          />
          <div class="upload-tips">
            {{ $t('station.stationLogoTips') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('station.stationImg')"
        prop="imgUrls"
      >
        <div class="part-form-item">
          <imgs-upload
            v-model:modelValue="dataForm.imgUrls"
            :prompt="false"
          />
          <div class="upload-tips">
            {{ $t('station.stationImgTips') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        :label="$t('admin.phoNumber')"
        prop="phone"
      >
        <el-input
          v-model="dataForm.phonePrefix"
          style="width: 140px"
          :placeholder="$t('admin.areaCode')"
          maxlength="10"
          @change="checkPhonePrefix"
        />
        <span>—</span>
        <el-input
          v-model="dataForm.phone"
          style="width: 320px"
          :placeholder="$t('admin.number')"
          maxlength="11"
          @change="checkPhone"
        />
      </el-form-item>
      <el-form-item
        :label="$t('admin.businTime')"
        prop="shopTime"
      >
        <el-time-picker
          v-model="timeDateModeVO.shopTime[0]"
          :clearable="false"
          format="HH:mm"
          :placeholder="$t('admin.businStartTime')"
          @change="startChange"
        />
        <span style="margin: 0 10px;">{{ $t('time.tip') }}</span>
        <el-time-picker
          v-model="timeDateModeVO.shopTime[1]"
          :clearable="false"
          format="HH:mm"
          :placeholder="$t('admin.businEndTime')"
          @change="endChange"
        />
      </el-form-item>
      <el-form-item :label="$t('station.businessStatus')">
        <el-radio-group
          v-model="status"
          :disabled="dataForm.status > 1 ||dataForm.shopStatus !== 1"
        >
          <el-radio :label="1">
            {{ $t('station.business') }}
          </el-radio>
          <el-radio :label="0">
            {{ $t('station.close') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        class="is-required"
        :label="$t('station.stationUse')"
      >
        <el-checkbox
          v-model="dataForm.selfPickup"
          :true-label="1"
          :false-label="0"
          :label="$t('station.selfPickup')"
        />
        <el-checkbox
          v-model="dataForm.sameCityDelivery"
          :true-label="1"
          :false-label="0"
          :label="$t('station.sameCityDelivery')"
        />
        <div class="tips">
          {{ $t('station.stationTips') }}
        </div>
      </el-form-item>

      <template v-if="dataForm.sameCityDelivery===1">
        <el-form-item :label="$t('shop.charges') + '：'">
          <div>
            <div>
              <el-radio
                v-model="dataForm.sameCityVO.chargeType"
                :label="1"
              >
                {{ $t("shop.chaDelFeeByReg") }}
              </el-radio>
              <el-radio
                v-model="dataForm.sameCityVO.chargeType"
                :label="2"
              >
                {{ $t("shop.delFeeChaDis") }}
              </el-radio>
            </div>
            <span
              v-if="dataForm.sameCityVO.chargeType === 1"
              style="color: gray"
            >{{ $t("shop.chaFixDelFeeByRe") }}</span>
            <span
              v-if="dataForm.sameCityVO.chargeType === 2"
              style="color: gray"
            >{{ $t("shop.byWalkDis") }}</span>
          </div>
        </el-form-item>
        <el-form-item
          :label="$t('shop.startingFeeY')"
          prop="startingFee"
        >
          <el-col :span="6">
            <el-input-number
              v-model="dataForm.sameCityVO.startingFee"
              :max="100000"
              :placeholder="$t('shop.startingFee')"
              :precision="2"
              :step="0.01"
              :min="0.0"
            />
          </el-col>
        </el-form-item>
        <el-form-item
          v-if="dataForm.sameCityVO.chargeType === 1"
          :label="$t('shop.deliveryFeeY')"
          prop="deliveryFee"
        >
          <el-col :span="6">
            <el-input-number
              v-model="dataForm.sameCityVO.deliveryFee"
              :max="100000"
              :placeholder="$t('shop.deliveryFee')"
              :min="0.00"
              :precision="2"
              :step="0.01"
            />
          </el-col>
        </el-form-item>
        <el-form-item
          v-if="dataForm.sameCityVO.chargeType === 2"
          :label="$t('shop.costAllocation')"
          style="color: gray"
          prop="headDistance"
        >
          <el-input-number
            v-model="dataForm.sameCityVO.headDistance"
            :max="100000"
            :precision="1"
            :min="0.1"
            :step="0.1"
          />
          <span>{{ $t("shop.withinKm") }}</span>
          <el-input-number
            v-model="dataForm.sameCityVO.deliveryFee"
            :max="100000"
            :min="0.00"
            :precision="2"
            :step="0.01"
          />
          <span>{{ $t("shop.yuanToChaDelFee") }}</span>
          <el-input-number
            v-model="dataForm.sameCityVO.overDistance"
            :max="100000"
            :precision="1"
            :step="0.1"
            :min="0.1"
          />
          <span>km，{{ $t("shop.incInDelFees") }}</span>
          <el-input-number
            v-model="dataForm.sameCityVO.overDistanceFee"
            :max="100000"
            :precision="2"
            :step="0.01"
            :min="0.01"
          />
          <span>{{ $t("admin.dollar") }}。</span>
        </el-form-item>
        <el-form-item
          :label="$t('shop.renewalCharge')"
          prop="prodName"
          style="color: gray"
        >
          <span>{{ $t("shop.commodityWeight") }}</span>
          <el-input-number
            v-model="dataForm.sameCityVO.freeWeight"
            :max="100000"
            :precision="2"
            :step="0.01"
            :min="0.01"
            :placeholder="$t('shop.pleaseEnterTheWeight')"
          />
          <span>{{ $t("shop.noExtraCharge") }}</span>
          <el-input-number
            v-model="dataForm.sameCityVO.overWeight"
            :max="100000"
            :precision="2"
            :step="0.01"
            :min="0.01"
            :placeholder="$t('shop.pleaseEnterTheWeight')"
          />
          <span>kg，{{ $t("shop.renewalFeeIncrease") }}</span>
          <el-input-number
            v-model="dataForm.sameCityVO.overWeightFee"
            :max="100000"
            :precision="2"
            :step="0.01"
            :min="0.01"
          />
          <span>{{ $t("admin.dollar") }}。</span>
        </el-form-item>
      </template>
      <el-form-item
        style="margin-bottom:0"
        :label="$t('shopProcess.addr')"
        prop="areaInfo"
      >
        <div class="area-select">
          <el-form-item prop="province">
            <el-select
              v-model="dataForm.provinceId"
              :placeholder="$t('tip.select')"
              @change="onSelectProvince"
            >
              <el-option
                v-for="province in provinceList"
                :key="province.areaId"
                :label="province.areaName"
                :value="province.areaId"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('address.city')"
            label-width="50px"
            prop="city"
          >
            <el-select
              v-model="dataForm.cityId"
              :placeholder="$t('address.city')"
              @change="onSelectCity"
            >
              <el-option
                v-for="city in cityList"
                :key="city.areaId"
                :label="city.areaName"
                :value="city.areaId"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('address.area')"
            label-width="50px"
            prop="area"
          >
            <el-select
              v-model="dataForm.areaId"
              :placeholder="$t('address.area')"
              @change="onSelectArea"
            >
              <el-option
                v-for="area in areaList"
                :key="area.areaId"
                :label="area.areaName"
                :value="area.areaId"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item
        class="addr"
        :label="$t('shopProcess.detailAddr')"
        prop="addr"
      >
        <el-input
          v-model="dataForm.addr"
          maxlength="100"
          style="width: 742px"
          show-word-limit
          @change="onMapLocation"
        />
      </el-form-item>
      <!--map组件-->
      <el-form-item
        prop="brief"
        :label="$t('product.deliveryArea')"
      >
        <div class="map">
          <tmap
            ref="tmapRef"
            :lat-lng="latLng"
            :is-update-coordinates="false"
            :is-show-control="true"
            @set-polygon-path="onSetPolygonPath"
            @change-lat-lng="onChangeLatLng"
          />
        </div>
      </el-form-item>
      <el-form-item>
        <div
          class="default-btn text-btn"
          @click="onResetMapPoint()"
        >
          {{ $t("shop.resetMap") }}
        </div>
      </el-form-item>
      <el-form-item label=" ">
        <el-button
          @click="close"
        >
          {{ $t('crud.filter.cancelBtn') }}
        </el-button>
        <el-button
          type="primary"
          @click="onSubmit()"
        >
          {{ $t('crud.filter.submitBtn') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import Big from 'big.js'

const dataForm = ref({
  stationId: null,
  stationName: null,
  pic: null,
  phonePrefix: null,
  phone: null,
  startTime: '',
  endTime: '',
  status: 1,
  shopStatus: 1,
  provinceId: null,
  province: null,
  cityId: null,
  city: null,
  areaId: null,
  area: null,
  addr: null,
  lng: 116.397469,
  lat: 39.908821,
  timeDateModeVO: {},
  stockMode: 1,
  selfPickup: 0, // 自提门店用途是否支持自提（0：不支持，1：支持）
  sameCityDelivery: 0, // 自提门店用途是否支持同城配送（0：不支持，1：支持）
  sameCityVO: {
    chargeType: 1, // 收费标准 1：按区域收费 2：按距离收费
    polygonPath: [], // 同城配送区域坐标点
    startingFee: 0.00, // 起送费
    deliveryFee: 0.00, // 配送费
    freeWeight: 0.00, // 续重收费-商品重量
    overWeight: 0.00, // 超出重量
    overWeightFee: 0.00, // 超重增加续重费
    headDistance: 0.00, // 距离首次收费
    overDistance: 0.00, // 超出距离
    overDistanceFee: 0.00 // 超出距离收费
  }
})
const timeDateModeVO = ref({
  shopTime: [new Date(2020, 1, 1, 8, 0, 0), new Date(2020, 1, 1, 20, 0, 0)]
})

// eslint-disable-next-line no-unused-vars
const validateProvince = (rule, value, callback) => {
  if (!dataForm.value.province) {
    callback(new Error($t('admin.seleProv')))
  } else if (!dataForm.value.city) {
    callback(new Error($t('admin.seleCity')))
  } else if (!dataForm.value.area) {
    callback(new Error($t('admin.seleDC')))
  } else {
    callback()
  }
}
// eslint-disable-next-line no-unused-vars
const validateShopTime = (rule, value, callback) => {
  if (!timeDateModeVO.value.shopTime) {
    callback(new Error($t('admin.seleBusTime')))
  } else {
    callback()
  }
}
const validateStationName = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('admin.stationNameNoNull')))
  } else {
    callback()
  }
}
const validateAddr = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('admin.addrNoNull')))
  } else {
    callback()
  }
}
const dataRule = {
  stationName: [
    { required: true, message: $t('admin.stationNameNoNull'), trigger: 'blur' },
    { validator: validateStationName, trigger: 'blur' }
  ],
  areaInfo: [
    { required: true, validator: validateProvince, trigger: 'blur' }
  ],
  shopTime: [
    { validator: validateShopTime, trigger: 'blur' }
  ],
  addr: [
    { required: true, message: $t('admin.addrNoNull'), trigger: 'blur' },
    { validator: validateAddr, trigger: 'blur' }
  ],
  phone: [
    { required: true, message: $t('admin.numberNoNull'), trigger: 'blur' }
  ]
}

const startChange = (val) => {
  if (!val) {
    timeDateModeVO.value.shopTime[0] = new Date(2020, 1, 1, 8, 0, 0)
  }
}

const endChange = (val) => {
  if (!val) {
    timeDateModeVO.value.shopTime[1] = new Date(2020, 1, 1, 20, 0, 0)
  }
}

let TMap = window.TMap
const pageTitle = ref('')
const route = useRoute()
onMounted(() => {
  const stationId = +route.query.stationId
  pageTitle.value = stationId ? $t('station.stationEdit') : $t('station.stationAdd')

  if (TMap) {
    init(stationId)
  } else {
    onGetTMap(stationId)
  }
})

let time = null
const onGetTMap = (stationId) => {
  time = setTimeout(() => {
    TMap = window.TMap
    if (TMap) {
      time = null
      init(stationId)
    } else {
      onGetTMap(stationId)
    }
  }, 500)
}

let polygonPath = []
const isConfigureAreaDefault = ref(true)
const cityList = ref([])
const areaList = ref([])
const provinceList = ref([])
const show = ref(false)
const isSubmit = ref(false)
const status = ref(1)
const dataFormRef = ref(null)
const commonStore = useCommonStore()
const init = (stationId) => {
  // 更新菜单路径
  const navTitles = JSON.parse(JSON.stringify(commonStore.selectMenu))
  navTitles.splice(navTitles.length - 1, 1, pageTitle.value)
  commonStore.updateSelectMenu(navTitles)
  dataForm.value.stationId = stationId || 0
  show.value = false
  isSubmit.value = false
  nextTick(() => {
    status.value = 1
    dataFormRef.value?.resetFields()
    cityList.value = []
    areaList.value = []
    dataForm.value.provinceId = null
    dataForm.value.cityId = null
    dataForm.value.areaId = null
    dataForm.value.phonePrefix = null
    timeDateModeVO.value = {
      shopTime: [new Date(2020, 1, 1, 8, 0, 0), new Date(2020, 1, 1, 20, 0, 0)]
    }
    listAreaByParentId().then(({ data }) => {
      provinceList.value = data
      if (dataForm.value.stationId) {
        getDataInfo()
      } else {
        const points = onGetPoint({ shopLng: dataForm.value.lng, shopLat: dataForm.value.lat })
        polygonPath.length = 0
        polygonPath.push(...points)
        nextTick(() => {
          isConfigureAreaDefault.value = true
          tmapRef.value?.onInitGemotryEditor(polygonPath)
          tmapRef.value?.onSetZoom(13)
        })
      }
    })
  })
}
defineExpose({ init })

/**
 * 获取店铺详情数据
 */
const getDataInfo = () => {
  http({
    url: http.adornUrl('/admin/station/info/' + dataForm.value.stationId),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (!data.sameCityVO) {
      data.sameCityVO = {
        chargeType: 1,
        polygonPath: [],
        startingFee: 0.00,
        deliveryFee: 0.00,
        freeWeight: 0.00,
        overWeight: 0.00,
        overWeightFee: 0.00,
        headDistance: 0.00,
        overDistance: 0.00,
        overDistanceFee: 0.00
      }
    }
    dataForm.value = data
    polygonPath = data.sameCityVO.polygonPath
    status.value = dataForm.value.status === 1 ? 1 : 0
    timeDateModeVO.value = JSON.parse(data.timeDate)
    listAreaByParentId(data.provinceId).then(({ data }) => {
      cityList.value = data
    })
    listAreaByParentId(data.cityId).then(({ data }) => {
      areaList.value = data
      if (dataForm.value.lng && dataForm.value.lat) {
        latLng.value = {
          lng: dataForm.value.lng,
          lat: dataForm.value.lat
        }
      }
    })
    if (!polygonPath || polygonPath.length <= 0) {
      const points = onGetPoint({ shopLng: dataForm.value.lng, shopLat: dataForm.value.lat })
      polygonPath.length = 0
      polygonPath.push(...points)
      nextTick(() => {
        isConfigureAreaDefault.value = true
        tmapRef.value?.onInitGemotryEditor(polygonPath)
        tmapRef.value?.onSetZoom(13)
      })
    } else {
      const polygonArray = JSON.parse(JSON.stringify(polygonPath))
      polygonPath.length = 0
      polygonArray.forEach((item) => {
        const TMap = window.TMap
        const point = new TMap.LatLng(item.lat, item.lng)
        polygonPath.push(point)
      })
      tmapRef.value?.onInitGemotryEditor(polygonPath)
      tmapRef.value?.onSetZoom(13)
    }
  })
}

const listAreaByParentId = (pid) => {
  let paramData
  if (!pid) {
    paramData = { level: 1 }
  } else {
    paramData = { pid }
  }
  return http({
    url: http.adornUrl('/admin/area/listByPid'),
    method: 'get',
    params: http.adornParams(paramData)
  })
}

const getCurrentChild = (curList, curId) => {
  for (const item of curList) {
    if (item.areaId === curId) {
      return {
        curNode: item,
        areas: item.areas
      }
    }
  }
}

/**
 * 选择省
 * @param val
 */
const onSelectProvince = (val) => {
  dataForm.value.cityId = null
  dataForm.value.city = ''
  areaList.value = []
  dataForm.value.areaId = null
  dataForm.value.area = ''
  const { curNode, areas } = getCurrentChild(provinceList.value, val)
  if (areas) {
    cityList.value = areas
  } else {
    listAreaByParentId(val).then(({ data }) => {
      curNode.areas = data
      cityList.value = data
    })
  }
  onGetProvinceName() // 获取省名称
  onMapLocation()
}

/**
 * 选择市
 * @param val
 */
const onSelectCity = (val) => {
  dataForm.value.areaId = null
  dataForm.value.area = ''
  const { curNode, areas } = getCurrentChild(cityList.value, val)
  if (areas) {
    areaList.value = areas
  } else {
    listAreaByParentId(val).then(({ data }) => {
      curNode.areas = data
      areaList.value = data
    })
  }
  onGetCityName() // 获取市名称
  onMapLocation()
}

const onSelectArea = () => {
  onGetDistrictName() // 获取区名称
  onMapLocation()
}

const checkStationNames = () => {
  dataForm.value.stationName = dataForm.value.stationName.trim()
}

const checkPhonePrefix = () => {
  if (dataForm.value.phonePrefix) {
    const reg = /^[0-9]*$/
    if (!reg.test(dataForm.value.phonePrefix)) {
      message($t('admin.onlyInNum'))
      dataForm.value.phonePrefix = null
      return false
    }
  }
}

const checkPhone = () => {
  if (dataForm.value.phone) {
    const reg = /^[0-9]*$/
    if (!reg.test(dataForm.value.phone)) {
      message($t('admin.onlyInNum'))
      dataForm.value.phone = null
      return false
    }
  }
}

/**
 * 表单提交
 */
const onSubmit = () => {
  if (!dataForm.value.selfPickup && !dataForm.value.sameCityDelivery) {
    ElMessage.error($t('station.stationUseSelTips'))
    return
  }

  if (!dataForm.value.areaId) {
    ElMessage.error($t('shopProcess.addrInputTips'))
    return
  }

  if (dataForm.value.sameCityDelivery) {
    // 开启同城配送时检测是否框选配送区域
    let flag = false
    for (let i = 0; i < polygonPath.length; i++) {
      if (polygonPath[i].lat.toString().split('.')[1].length > 6) {
        flag = true
        break
      }
    }
    if (!flag) {
      ElMessage.error($t('station.deliveryAreaNullTips'))
      return
    }
  }

  onGetAreaName()
  dataForm.value.lng = latLng.value.lng
  dataForm.value.lat = latLng.value.lat
  const timeDateMode = timeDateModeVO.value
  if (timeDateMode.shopTime) {
    if (timeDateMode.shopTime[0] instanceof Date) {
      timeDateMode.shopTime[0] = timeDateMode.shopTime[0].getTime()
    }
    if (timeDateMode.shopTime[1] instanceof Date) {
      timeDateMode.shopTime[1] = timeDateMode.shopTime[1].getTime()
    }
    const oneDay = 24 * 60 * 60 * 1000 // 一天对应的毫秒数
    // 如果结束时间小于开始时间，则给结束时间加一天，表示第二天的时间
    if (timeDateMode.shopTime[1] < timeDateMode.shopTime[0]) {
      timeDateMode.shopTime[1] = timeDateMode.shopTime[1] + oneDay
    }
    dataForm.value.timeDate = JSON.stringify(timeDateMode)
  }
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      if (isSubmit.value) {
        return false
      }
      isSubmit.value = true
      dataForm.value.status = dataForm.value.status > 1 ? dataForm.value.status : status.value
      const dataFormPar = JSON.parse(JSON.stringify(dataForm.value))
      dataFormPar.sameCityVO.polygonPath = polygonPath
      if (dataFormPar.sameCityVO) {
        dataFormPar.sameCityVO.lat = dataFormPar.lat
        dataFormPar.sameCityVO.lng = dataFormPar.lng
      }
      http({
        url: http.adornUrl('/admin/station'),
        method: dataForm.value.stationId ? 'put' : 'post',
        data: http.adornData(dataFormPar)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            close()
          }
        })
      }).catch(() => {
        isSubmit.value = false
      })
    }
  })
}

const message = (msg) => {
  ElMessage({
    message: msg,
    type: 'error',
    duration: 1500
  })
}

const tmapRef = ref(null)
const latLng = ref(null)
// 通过省市区id,得到省市区名称
const onGetAreaName = () => {
  onGetProvinceName() // 保存省名字
  onGetCityName() // 保存市名字
  onGetDistrictName() // 保存区名字
}
const onGetProvinceName = () => {
  for (let i = 0; i < provinceList.value.length; i++) {
    if (provinceList.value[i].areaId === dataForm.value.provinceId) {
      // 将省名字保存起来
      dataForm.value.province = provinceList.value[i].areaName
    }
  }
}

const onGetCityName = () => {
  for (let i = 0; i < cityList.value.length; i++) {
    if (cityList.value[i].areaId === dataForm.value.cityId) {
      // 将市名字保存起来
      dataForm.value.city = cityList.value[i].areaName
    }
  }
}

const onGetDistrictName = () => {
  for (let i = 0; i < areaList.value.length; i++) {
    if (areaList.value[i].areaId === dataForm.value.areaId) {
      // 将市名字保存起来
      dataForm.value.area = areaList.value[i].areaName
    }
  }
}

// 腾讯地图
const onMapLocation = () => {
  let address = ''
  const { provinceId, province, cityId, city, areaId, area, addr } = dataForm.value
  // 选择省市区时，定位地图
  if (provinceId) {
    address = province
    tmapRef.value.onSetZoom(6)
    if (cityId) {
      address = address + city
      tmapRef.value.onSetZoom(10)
      if (areaId) {
        address = address + area
        tmapRef.value.onSetZoom(13)
        if (addr) {
          // 详细地址
          address = address + addr
          tmapRef.value.onSetZoom(13)
        }
      }
    }
  }
  if (address) {
    tmapRef.value.onGetLocation(address).then(res => {
      onChangeLatLng(res)
    })
  }
}

/**
 * 获取区域点坐标
 * @param latLng
 * @returns {[TMap.LatLng,TMap.LatLng,TMap.LatLng,TMap.LatLng]}
 */
const onGetPoint = (latLng) => {
  const lng = new Big(Number.parseFloat(latLng.shopLng))
  const lat = new Big(Number.parseFloat(latLng.shopLat))
  const point1 = new TMap.LatLng(
    Number.parseFloat(lat),
    Number.parseFloat(lng)
  )
  const point2 = new TMap.LatLng(
    Number.parseFloat(lat.plus(0.01)),
    Number.parseFloat(lng.plus(0.01))
  )
  const point3 = new TMap.LatLng(
    Number.parseFloat(lat.plus(0.02)),
    Number.parseFloat(lng.plus(0.02))
  )
  const point4 = new TMap.LatLng(
    Number.parseFloat(lat.plus(0.01001)),
    Number.parseFloat(lng.plus(0.01001))
  )
  return [point1, point2, point3, point4]
}

// 修改经纬度
const onChangeLatLng = (latLngInfo) => {
  const { lng, lat } = latLngInfo
  latLng.value = {
    lat,
    lng
  }
  dataForm.value.shopLng = lng
  dataForm.value.shopLat = lat
  // 更改坐标点后，重新设置地图选区
  const points = onGetPoint({ shopLng: dataForm.value.shopLng, shopLat: dataForm.value.shopLat })
  polygonPath.length = 0
  polygonPath.push(...points)
  nextTick(() => {
    isConfigureAreaDefault.value = true
    tmapRef.value?.onResetGemotryEditor(polygonPath)
  })
}

const onResetMapPoint = () => {
  isConfigureAreaDefault.value = true
  dataForm.value.sameCityVO.polygonPath = null
  polygonPath.length = 0
  polygonPath = onGetPoint({ shopLng: dataForm.value.shopLng, shopLat: dataForm.value.shopLat })
  tmapRef.value?.onResetGemotryEditor(polygonPath)
  ElMessage({
    message: $t('publics.operation'),
    type: 'success',
    duration: 1500
  })
}

const onSetPolygonPath = (data) => {
  polygonPath.length = 0
  polygonPath.push(...data.paths)
  isConfigureAreaDefault.value = false
}

const router = useRouter()
const close = () => {
  router.push({
    path: '/stock/stowage/admin-station/index'
  })
}

onUnmounted(() => {
  if (time) {
    clearTimeout(time)
  }
})
</script>

<style lang="scss" scoped>
.map {
  width: 50%;
  height: 500px;
}
.area-select {
  display: flex;
  margin-bottom: 18px;
  justify-content: left;
  width: 100%;
  :deep(.el-select) {
    width: 200px;
  }
}
:deep(.el-form-item__content .el-col) {
  display: flex;
}
:deep(.el-form-item__content .up-img-box) {
  margin-right: 100%;
}
:deep(.up-img-box .plugin-images .el-upload--picture-card),
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

.tips{
  font-size: 12px;
  color: #999;
  margin-left: 20px;
}
// 轮播图
.upload-tips {
  font-size: 13px;
  color: #999;
}
</style>
