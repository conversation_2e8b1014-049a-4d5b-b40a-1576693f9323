<template>
  <el-dialog
    v-model="visible"
    :title="stockMode === 2 ? ($t('stock.switchTo') + $t('stock.sharedHeadquartersInventory')) : ($t('stock.switchTo') + $t('stock.independentSellingInventory'))"
    :close-on-click-modal="false"
    width="760px"
    class="component-stock-mode"
  >
    <div class="main">
      <el-alert
        type="warning"
        :closable="false"
      >
        <template #default>
          <div class="alert">
            <div class="warning-icon">
              <el-icon><WarningFilled /></el-icon>
            </div>
            <div
              v-if="stockMode===2"
              class="tips-box"
            >
              <p class="tip">
                {{ $t('stock.sharedHeadquartersInventoryTip1') }}
              </p>
              <p class="tip">
                {{ $t('stock.sharedHeadquartersInventoryTip2') }}
              </p>
              <p class="tip">
                {{ $t('stock.sharedHeadquartersInventoryTip3') }}
              </p>
              <p class="tip">
                {{ $t('stock.sharedHeadquartersInventoryTip4') }}
              </p>
            </div>
            <div
              v-else
              class="tips-box"
            >
              <p class="tip">
                {{ $t('stock.independentSellingInventoryTip1') }}
              </p>
              <p class="tip">
                {{ $t('stock.independentSellingInventoryTip2') }}
              </p>
              <p class="tip">
                {{ $t('stock.independentSellingInventoryTip3') }}
              </p>
            </div>
          </div>
        </template>
      </el-alert>
      <div
        v-if="stockMode===2"
        class="select-box"
      >
        <div class="title">
          {{ $t('stock.originalStock') }}
        </div>
        <el-radio-group v-model="type">
          <el-radio :label="1">
            {{ $t('stock.originalStockSelect1') }}
          </el-radio>
          <el-radio :label="2">
            {{ $t('stock.originalStockSelect2') }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="visible = false"
        >{{ $t('crud.filter.cancelBtn') }}</el-button>
        <el-button
          type="primary"
          @click="onSubmit()"
        >{{ $t('crud.filter.submitBtn') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const emit = defineEmits([
  'refreshDataList'
])

const visible = ref(false)
const stationId = ref('')
const stockMode = ref(1) // 库存模式 1共享总部库存 2独立销售库存
const type = ref(1) // 更新共享库存模式（1同步到默认仓库 2库存清零）
const isSubmit = ref(false)
const init = (id, mode) => {
  visible.value = true
  stationId.value = id
  stockMode.value = mode
  isSubmit.value = false
}

// 表单提交
const onSubmit = () => {
  if (isSubmit.value) {
    return
  }
  isSubmit.value = true
  http({
    url: http.adornUrl('/admin/station/switch_station_stock_mode'),
    method: 'put',
    params: http.adornParams({
      stationId: stationId.value,
      type: stockMode.value === 2 ? type.value : null
    })
  }).then(() => {
    ElMessage({
      message: $t('stock.success'),
      type: 'success',
      duration: 1500,
      onClose: () => {
        visible.value = false
        isSubmit.value = false
        emit('refreshDataList')
      }
    })
  })
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.component-stock-mode {
  .main {
    .alert {
      display: flex;
      justify-content: center;
      padding: 10px 0;
      .warning-icon {
        font-size: 16px;
        color: #e6a23c;
        margin-right: 10px;
        padding-top: 2px;
      }
      .tip {
        font-size: 14px;
        margin: 4px 0;
        &:first-child {
          margin-top: 0;
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
    .select-box {
      display: flex;
      align-items: center;
      margin: 20px 0 0 50px;
      .title {
        margin-right: 10px;
      }
    }
  }
}
</style>
