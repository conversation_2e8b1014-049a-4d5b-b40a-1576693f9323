<template>
  <div class="page-station-mod">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        :model="searchForm"
        label-width="auto"

        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="keyword"
            :label="$t('station.stationName')+':'"
          >
            <el-input
              v-model="searchForm.keyword"
              type="text"
              clearable
              :placeholder="$t('station.stationName')"
            />
          </el-form-item>
          <el-form-item
            prop="addr"
            :label="$t('station.addr')+':'"
          >
            <el-input
              v-model="searchForm.addr"
              type="text"
              clearable
              :placeholder="$t('station.addr')"
            />
          </el-form-item>
          <el-form-item
            prop="account"
            :label="$t('station.account')+':'"
          >
            <el-input
              v-model="searchForm.account"
              type="text"
              clearable
              :placeholder="$t('station.account')"
            />
          </el-form-item>
          <el-form-item
            prop="stationStatus"
            :label="$t('publics.status')+':'"
          >
            <el-select
              v-model="searchForm.stationStatus"
              clearable
              :placeholder="$t('publics.status')"
            >
              <el-option
                :label="$t('station.close')"
                :value="0"
              />
              <el-option
                :label="$t('station.business')"
                :value="1"
              />
              <el-option
                :label="$t('components.platformOffline')"
                :value="2"
              />
              <el-option
                :label="$t('station.underReview')"
                :value="3"
              />
              <!--              <el-option-->
              <!--                :label="$t('station.auditFailure')"-->
              <!--                :value="4"-->
              <!--              />-->
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="operation-bar">
        <div
          v-if="isAuth('admin:station:save')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ $t("crud.addTitle") }}
        </div>
      </div>
      <!-- 表格 -->
      <div
        ref="closePopoverRef"
        class="table-con prod-table"
      >
        <el-table
          ref="stationTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            :label="$t('formData.serialNumber')"
            type="index"
            width="85"
          />
          <el-table-column
            align="left"
            prop="stationName"
            :label="$t('station.stationName')"
            min-width="270"
          >
            <template #default="scope">
              <span
                class="table-cell-text"
                style="display:block;white-space: nowrap;"
              >{{ scope.row.stationName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            :label="$t('门店用途')"
            min-width="120"
          >
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.selfPickup?'自提 ':'' }}{{ scope.row.sameCityDelivery?' 同城配送':'' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="addr"
            :label="$t('station.addr')"
            min-width="390"
          >
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.province }}{{ scope.row.city }}{{ scope.row.area }}{{ scope.row.addr || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="phone"
            :label="$t('station.number')"
            width="220"
          >
            <template #default="scope">
              <span v-if="scope.row.phonePrefix">{{ scope.row.phonePrefix }}-</span>
              <span>{{ scope.row.phone || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="account"
            :label="$t('station.account')"
            width="210"
          >
            <template #default="scope">
              <span>{{ scope.row.account || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="stationScore"
            :label="$t('门店评分')"
            width="100"
          >
            <template #default="scope">
              {{ scope.row.stationScore }}
            </template>
          </el-table-column>
          <el-table-column
            prop="stockMode"
            :label="$t('stock.stockMold')"
            width="400"
          >
            <template #default="scope">
              {{ ['', $t('stock.sharedHeadquartersInventory'), $t('stock.independentSellingInventory')][scope.row.stockMode] }}
            </template>
          </el-table-column>
          <el-table-column
            align="left"
            prop="status"
            :label="$t('publics.status')"
            width="150"
          >
            <template #default="scope">
              <div v-if="scope.row.status === 4">
                {{ $t("station.auditFailure") }}
              </div>
              <div v-else-if="scope.row.status === 3">
                {{ $t("station.underReview") }}
              </div>
              <div v-else-if="scope.row.status === 2">
                {{ $t("components.platformOffline") }}
              </div>
              <div v-else-if="scope.row.status === 1">
                {{ $t("station.business") }}
              </div>
              <div v-else>
                {{ $t("station.close") }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            align="center"
            prop="account"
            :label="$t('text.menu')"
            width="300"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('admin:station:update')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row.stationId)"
                >
                  {{ $t("crud.updateBtn") }}
                </div>
                <div
                  v-if="isAuth('admin:station:changeAccountInfo')"
                  class="default-btn text-btn"
                  @click="checkStationAccount(scope.row.stationId)"
                >
                  {{ scope.row.account?$t("admin.resetPwd"):$t("admin.createAcc") }}
                </div>
                <div
                  v-if="isAuth('admin:station:auditApply') && scope.row.status > 1"
                  class="default-btn text-btn"
                  @click="auditEventHandle(scope.row.stationId)"
                >
                  {{ scope.row.status === 3?$t("coupon.waitReview"):$t("groups.applyForListing") }}
                </div>
                <div
                  class="default-btn text-btn"
                  @click="onViewInventory(scope.row.stationId, scope.row.stockMode)"
                >
                  {{ $t('stock.merchandiseInventory') }}
                </div>
                <el-popover
                  placement="bottom-end"
                  trigger="click"
                  :width="160"
                  :show-arrow="false"
                  @show="onShowPopover(scope.row)"
                  @hide="onHidePopver(scope.row)"
                >
                  <div
                    style="cursor: pointer;color: #303133"
                    @click="onChangeMode(scope.row)"
                  >
                    {{ ['', $t('stock.independentSellingInventory'), $t('stock.sharedHeadquartersInventory')][scope.row.stockMode] }}
                  </div>
                  <template #reference>
                    <div class="default-btn text-btn">
                      {{ $t('stock.switchInventoryMode') }}
                      <el-icon
                        v-if="scope.row.popoverVisible === false"
                        class="stock-mode-icon"
                      >
                        <ArrowDown />
                      </el-icon>
                      <el-icon
                        v-else
                        class="stock-mode-icon"
                      >
                        <ArrowUp />
                      </el-icon>
                    </div>
                  </template>
                </el-popover>
                <div
                  v-if="isAuth('admin:station:delete')"
                  class="default-btn text-btn"
                  @click="onDelete(scope.row.stationId)"
                >
                  {{ $t("text.delBtn") }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <el-pagination
        v-if="dataList.length"
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <!-- 新增编辑弹窗-->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="refreshChange"
    />
    <!-- 账号弹窗-->
    <station-account
      v-if="stationAccountVisible"
      ref="stationAccountRef"
      @refresh-data-list="refreshChange"
    />
    <!-- 下线管理弹窗-->
    <offline-event-handle
      v-if="offlineEventHandleVisible"
      ref="offlineEventRef"
      select-url="/admin/station/getOfflineHandleEventByStatonId"
      apply-url="/admin/station/auditApply"
      type="station"
      @refresh-data-list="refreshChange"
    />
    <!-- 商品库存 -->
    <prod-inventory
      v-if="prodinventoryVisible"
      ref="prodInventoryRef"
      :stock-point-type="2"
    />

    <!--   门店库存模式 -->
    <stock-mode
      v-if="stockModeVisible"
      ref="stockModeRef"
      @refresh-data-list="refreshChange"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import StationAccount from './components/station-account.vue'
import AddOrUpdate from './add-or-update.vue'
import offlineEventHandle from '@/components/offline-event-handle/index.vue'
import StockMode from './components/stock-mode/index.vue'

let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  keyword: '', // 自提点
  addr: '', // 地址
  account: '', // 账号
  stationStatus: '' // 状态
}) // 搜索
const dataListLoading = ref(false)
const offlineEventHandleVisible = ref(false)
const addOrUpdateVisible = ref(false)
const stationAccountVisible = ref(false)

onMounted(() => {
  getDataList()
})
const getDataList = (pageParam, newData = false) => {
  dataListLoading.value = true
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/admin/search/station/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  })
    .then(({ data }) => {
      data.records.map(item => {
        item.popoverVisible = false
        return item
      })
      dataList.value = data.records
      page.total = data.total
      dataListLoading.value = false
    })
}

const router = useRouter()
/**
 * 新增 / 修改
 * @param id
 */
const addOrUpdateRef = ref(null)
const onAddOrUpdate = (id) => {
  router.push({
    path: '/stock/stowage/admin-station/add-or-update',
    query: {
      stationId: id
    }
  })
}

const onDelete = (id) => {
  ElMessageBox.confirm($t('admin.isDeleOper') + '?', $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  })
    .then(() => {
      http({
        url: http.adornUrl('/admin/station/' + id),
        method: 'delete',
        data: http.adornData({})
      })
        .then(() => {
          ElMessage({
            message: $t('publics.operation'),
            type: 'success',
            duration: 1500,
            onClose: () => {
              refreshChange()
            }
          })
        })
    })
}

const offlineEventRef = ref(null)
/**
 * 下线管理
 */
const auditEventHandle = (id) => {
  offlineEventHandleVisible.value = true
  nextTick(() => {
    offlineEventRef.value?.init(id)
  })
}

const stationAccountRef = ref(null)
/**
 * 账号管理
 */
const checkStationAccount = (id) => {
  stationAccountVisible.value = true
  nextTick(() => {
    stationAccountRef.value?.init(id)
  })
}

const prodInventoryRef = ref(null)
const prodinventoryVisible = ref(false)
/**
 * 查看门店的商品库存
 */
const onViewInventory = (id, stockMode) => {
  prodinventoryVisible.value = true
  nextTick(() => {
    prodInventoryRef.value.init(id, 1, stockMode)
  })
}

const stockModeVisible = ref(false)
const stockModeRef = ref(null)
const closePopoverRef = ref(null)
// 修改门店库存模式
const onChangeMode = (row) => {
  stockModeVisible.value = true
  nextTick(() => {
    closePopoverRef.value.click()
    stockModeRef.value.init(row.stationId, row.stockMode)
  })
}

const onShowPopover = (row) => {
  row.popoverVisible = true
}

const onHidePopver = (row) => {
  row.popoverVisible = false
}

/**
 * 刷新回调
 */
const refreshChange = () => {
  getDataList(page)
}

const onSearch = (newData = false) => {
  page.currentPage = 1
  getDataList(page, newData)
}

const searchFormRef = ref(null)
/**
 * 重置表单
 */
const onResetSearch = () => {
  searchFormRef.value?.resetFields()
}

const onPageSizeChange = (val) => {
  page.pageSize = val
  getDataList()
}

const onPageChange = (val) => {
  page.currentPage = val
  getDataList()
}

</script>
<style lang="scss" scoped>
.page-station-mod {
  .text-btn-con {
    flex-wrap: wrap;

    .stock-mode-icon {
      vertical-align: text-bottom;
    }
  }
}
</style>
