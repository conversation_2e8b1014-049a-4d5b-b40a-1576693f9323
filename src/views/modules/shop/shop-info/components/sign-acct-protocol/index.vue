<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('allinpay.signAgreement')"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="378px"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      label-width="100px"

      @submit.prevent
    >
      <el-form-item
        prop="signAcctType"
        :label="$t('allinpay.signType')"
        :rules="[{ required: true, message: $t('allinpay.pleaseSelectCategory') }]"
      >
        <el-radio-group v-model="dataForm.signAcctType">
          <el-radio
            :disabled="legalAcctProtocolNo?true:undefined"
            :label="2"
          >
            {{ $t('allinpay.privateContract') }}
          </el-radio>
          <el-radio
            :disabled="acctProtocolNo?true:undefined"
            :label="3"
          >
            {{ $t('allinpay.contractWithPublic') }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button

          @click="dialogVisible = false"
        >
          {{ $t('allinpay.cancel') }}
        </el-button>
        <el-button

          type="primary"
          @click="onSubmit"
        >
          {{ $t('allinpay.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Debounce } from '@/utils/debounce'
const props = defineProps({
  acctProtocolNo: {
    type: String,
    default: ''
  },
  legalAcctProtocolNo: {
    type: String,
    default: ''
  },
  mobile: {
    type: String,
    default: ''
  },
  legalCardNumber: {
    type: Number,
    default: 0
  }
})

const dialogVisible = ref(false)
const dataForm = {
  signAcctType: '' // 签约类型 1.个人 2.法人 3.企业
}

const init = () => {
  dialogVisible.value = true
}
const onSubmit = Debounce(() => {
  if (dataForm.signAcctType === '') {
    ElMessage.error($t('allinpay.pleaseSelectCategory'))
    return
  }
  if (dataForm.signAcctType === 3 && (props.mobile === '' || props.mobile === null)) {
    ElMessage.error($t('allinpay.pleaseBindPhone'))
    return
  }
  if (dataForm.signAcctType === 2 && props.legalCardNumber === 0) {
    ElMessage.error($t('allinpay.pleaseBindLegalAccNo'))
    return
  }
  const { protocol, host } = window.location
  const data = {
    signAcctType: dataForm.signAcctType,
    jumpUrl: `${protocol}//${host}/#/shop-shopInfo?isSuccess=1`,
    noContractUrl: `${protocol}//${host}/#/shop-shopInfo?isSuccess=0`
  }
  http({
    url: http.adornUrl('/shop/allinpay/company/signAcctProtocol'),
    method: 'post',
    data: http.adornData(data)
  }).then(({ data }) => {
    window.location.href = data
  })
}, 1500)

defineExpose({
  init
})
</script>

<style scoped>

</style>
