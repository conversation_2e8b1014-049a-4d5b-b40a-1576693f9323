<template>
  <el-dialog
    v-model="operateDialogVisible"
    :close-on-click-modal="false"
    :title="$t('shopProcess.applyOnline')"
    :lock-scroll="true"
    :append-to-body="true"
    :destroy-on-close="true"
    class="component-shop-info-offline-manage"
  >
    <div class="off-shelf-mag">
      <div class="msg">
        <div class="msg-item">
          <div class="tit">
            {{ $t('components.handler') }}：
          </div>
          <div class="int">
            {{ offlineDetail.handler }}
          </div>
        </div>
        <div class="msg-item">
          <div class="tit">
            {{ $t('components.offlineTime') }}：
          </div>
          <div class="int">
            {{ offlineDetail.createTime }}
          </div>
        </div>
        <div class="msg-item">
          <div class="tit">
            {{ $t('components.processingStatus') }}：
          </div>
          <el-tag
            v-if="offlineDetail.status === 1"
            type="danger"
          >
            {{ $t('shop.PlatformOff') }}
          </el-tag>
          <el-tag
            v-if="offlineDetail.status === 2"
            type="warning"
          >
            {{ $t('groups.moderated') }}
          </el-tag>
          <el-tag
            v-if="offlineDetail.status === 3"
            type="success"
          >
            {{ $t('productComm.pass') }}
          </el-tag>
          <el-tag
            v-if="offlineDetail.status === 4"
            type="danger"
          >
            {{ $t('productComm.noPass') }}
          </el-tag>
        </div>
        <div class="msg-item">
          <div class="tit">
            {{ $t('components.reasonForOffline') }}：
          </div>
          <div class="int">
            {{ offlineDetail.offlineReason }}
          </div>
        </div>
        <div
          v-if="offlineDetail.status === 1 || offlineDetail.status === 4"
          class="msg-item"
        >
          <div class="tit">
            {{ $t('components.reasonForApply') }}：
          </div>
          <div class="int">
            <el-input
              v-model="reapplyReason"
              maxlength="200"
              type="textarea"
              :placeholder="$t('shop.reapplyReason')"
            />
          </div>
        </div>
      </div>
      <div
        v-if="offlineDetail.offlineHandleEventItemList && offlineDetail.offlineHandleEventItemList.length && offlineDetail.offlineHandleEventItemList[0].reapplyReason"
        class="log"
      >
        <div class="log-tit">
          {{ $t('shop.applicationHistory') }}
        </div>
        <div
          v-for="(item,index) in offlineDetail.offlineHandleEventItemList"
          :key="index"
          class="log-item"
        >
          <p>
            <span class="tit">{{ $t("components.applicationTime") }}：</span>
            <span class="txt">{{ item.reapplyTime }}</span>
          </p>
          <p>
            <span class="tit">{{ $t('components.reasonForApply') }}：</span>
            <span class="txt">{{ item.reapplyReason }}</span>
          </p>
          <p v-if="item.auditTime">
            {{ $t('components.reviewTime') }}：{{ item.auditTime }}
          </p>
          <p v-if="item.refuseReason">
            {{ $t('components.denialReason') }}：{{ item.refuseReason }}
          </p>
        </div>
      </div>
    </div>
    <template #footer>
      <div>
        <div
          class="default-btn"
          @click="operateDialogVisible = false"
        >
          {{ $t('resource.cancel') }}
        </div>
        <div
          v-if="offlineDetail.status === 1 || offlineDetail.status === 4"
          class="default-btn primary-btn"
          @click="onRereapplyDataSubmit()"
        >
          {{ $t('shopProcess.submitApply') }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
const emit = defineEmits(['rereapplyDataSubmit'])

const reapplyReason = ref('') // 申请理由

const operateDialogVisible = ref(false) // 操作对话框
const offlineDetail = ref({}) // 最新下线商品
const init = (data) => {
  operateDialogVisible.value = true
  offlineDetail.value = data
}

/**
 * 提交上架申请
 */
const onRereapplyDataSubmit = () => {
  if (!reapplyReason.value || !(reapplyReason.value.trim())) {
    ElMessage({
      message: $t('shop.applicationTips'),
      type: 'warning',
      duration: 1000
    })
    return
  }
  const data = {
    eventId: offlineDetail.value.eventId,
    reapplyReason: reapplyReason.value
  }
  emit('rereapplyDataSubmit', data)
}
defineExpose({
  init
})
</script>

<style lang="scss" scoped>
// 下架管理-操作弹窗
.component-shop-info-offline-manage.el-dialog__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.component-shop-info-offline-manage {
  &:deep(.el-dialog__body) {
    padding: 10px 20px;
  }
  .off-shelf-mag {
    .msg {
      padding: 10px 0;
      margin-bottom: 10px;
      .msg-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        .tit {
          width: 90px;
          min-width: 90px;
          padding-right: 10px;
          text-align: right;
        }
        .int {
          width: 100%;
          word-break: break-word;
        }
      }
      .msg-item:not(:last-child) {
        margin-bottom: 20px;
      }
    }
    .log {
      border-top: 1px solid #eee;
      padding: 10px 0;
      p {
        margin: 0;
        padding: 0;
        line-height: 1.5em;
      }
      .log-tit {
        font-size: 15px;
        font-weight: bold;
        margin-bottom: 15px;
        .tit {
          min-width: 70px;
        }
      }
      .log-item {
        p {
          word-break: break-word;
        }
      }
      .log-item:not(:last-child) {
        padding-bottom: 10px;
        border-bottom: 1px dashed #eee;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
