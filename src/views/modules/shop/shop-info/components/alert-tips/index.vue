<template>
  <div
    v-if="paySettlementType===1"
    class="component-alert-tips"
  >
    <el-alert
      v-if="companyInfoProcessStatus === 3 && auditStatus===0"
      :title="$t('allinpay.allinpayBusinessTip2')"
      type="error"
      show-icon
      style="margin: 20px 0;"
    />
    <el-alert
      v-else-if="(companyInfoProcessStatus === 3 && (auditStatus===-1 || shopStatus===6)) || companyInfoProcessStatus === 0"
      :title="companyInfoProcessStatus === 0?$t('allinpay.allinpayBusinessTip1'):$t('shopProcess.applyFailTips')+statusRemark"
      type="error"
      show-icon
      style="margin: 20px 0;"
    />
    <el-alert
      v-else-if="idCardCollectProcessStatus !== 4 && (shopStatus===3 || shopStatus===5)"
      :title="$t('allinpay.allinpayBusinessTip3')"
      type="error"
      show-icon
      style="margin-bottom: 20px;"
    />
  </div>
</template>

<script setup>
defineProps({
  auditStatus: {
    type: Number,
    default: 0
  },
  statusRemark: {
    type: String,
    default: ''
  },
  shopStatus: {
    type: Number,
    default: 0
  },
  idCardCollectProcessStatus: {
    type: Number,
    default: 0
  },
  companyInfoProcessStatus: {
    type: Number,
    default: 0
  }
})

const allinpayStore = useAllinpayStore
const paySettlementType = computed(() => {
  return allinpayStore.paySettlementType
})

</script>

<style lang="scss" scoped>

</style>
