<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('allinpay.photocopyUpload')"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="500px"
  >
    <el-alert
      :title="msg"
      type="error"
      show-icon
      style="margin-bottom: 20px;"
    />
    <el-form
      ref="companyInfoFormRef"
      :model="companyInfoForm"
      :rules="companyInfoRule"
      label-width="140px"

      class="business-info"
      @submit.prevent
    >
      <el-form-item
        :label="$t('shopProcess.businessLicense')"
        prop="businessLicense"
      >
        <div class="business-license-box">
          <div class="license-content">
            <img-upload v-model="companyInfoForm.businessLicense" />
            <div class="example-box">
              <img src="~@/assets/img/example-img/Business-license.png">
              <div class="tips">
                {{ $t('shopProcess.example') }}
              </div>
            </div>
          </div>
          <div class="upload-tips">
            {{ $t('shopProcess.logoTips') }}
          </div>
        </div>
      </el-form-item>
      <div class="id-box">
        <div class="upload-content">
          <div class="upload-img">
            <el-form-item
              class="idcard"
              :label="$t('shopProcess.corporateIdentityCard')"
              prop="identityCardFront"
            >
              <img-upload v-model="companyInfoForm.identityCardFront" />
            </el-form-item>
            <el-form-item
              class="idcard"
              label-width="0"
              prop="identityCardLater"
            >
              <img-upload v-model="companyInfoForm.identityCardLater" />
            </el-form-item>
          </div>
          <div class="upload-example">
            <div class="example-box">
              <img src="~@/assets/img/example-img/idcard1.png">
              <div class="tips">
                {{ $t('shopProcess.identityCardFront') }}
              </div>
            </div>
            <div class="example-box">
              <img src="~@/assets/img/example-img/idcard2.png">
              <div class="tips">
                {{ $t('shopProcess.identityCardLater') }}
              </div>
            </div>
          </div>
          <div class="upload-tips">
            {{ $t('shopProcess.identityCardTips') }}
          </div>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div>
        <el-button @click="dialogVisible = false">
          {{ $t('shopProcess.cancel') }}
        </el-button>
        <el-button
          type="primary"
          @click="onSubmit"
        >
          {{ $t('shopProcess.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Debounce } from '@/utils/debounce'
const props = defineProps({
  shopCompanyId: {
    type: Number,
    default: 0
  },
  shopId: {
    type: Number,
    default: 0
  }
})

const companyInfoRule = {
  businessLicense: [
    { required: true, message: $t('multishop.shopInformation.businessLicenseTips'), trigger: 'change' }
  ],
  identityCardFront: [
    { required: true, message: $t('multishop.shopInformation.identityCardFrontTips'), trigger: 'change' }
  ],
  identityCardLater: [
    { required: true, message: $t('multishop.shopInformation.identityCardLaterTips'), trigger: 'change' }
  ]
}
const companyInfoFormRef = ref(null)
const dialogVisible = ref(false)
const msg = ref('') // 提示信息
const init = (data) => {
  dialogVisible.value = true
  if (data === 0) {
    msg.value = $t('allinpay.auditFailureTip1')
  } else if (data === 2) {
    msg.value = $t('allinpay.auditFailureTip2')
  } else if (data === 3) {
    msg.value = $t('allinpay.auditFailureTip3')
  }
  nextTick(() => {
    companyInfoFormRef.value?.resetFields()
  })
}

const companyInfoForm = reactive({
  businessLicense: '',
  identityCardFront: '',
  identityCardLater: ''
})
const onSubmit = Debounce(() => {
  companyInfoForm.shopCompanyId = props.shopCompanyId
  companyInfoForm.shopId = props.shopId
  companyInfoFormRef.value?.validate(valid => {
    if (valid) {
      http({
        url: http.adornUrl('/shop/shopCompany/uploadIdCard'),
        method: 'put',
        data: http.adornData(companyInfoForm)
      }).then((data) => {
        ElMessage({
          message: data,
          type: 'success',
          onClose: () => {
            // 上传成功后，刷新页面
            location.reload()
          }
        })
      })
    }
  })
}, 1500)
defineExpose({
  init
})
</script>

<style lang="scss" scoped>

  .business-license-box {
    .license-content {
      display: flex;
      height: 120px;
    }
  }
  .id-box {
    .upload-content {
      .upload-img {
        display: flex;
        div {
          &:nth-child(2) {
            margin-left: 20px;
          }
        }
      }
      .upload-example {
        display: flex;
        // margin-top: 15px;
        margin-left: 140px;
        .example-box {
          margin-left: 0;
          &:nth-child(2) {
            margin-left: 20px;
          }
        }
      }
      .upload-tips {
        margin-left: 140px;
      }
    }
  }

  // 示例框
  .example-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 120px;
    height: 120px;
    background: #FFFFFF;
    border: 1px solid #EAEAEA;
    border-radius: 3px;
    box-sizing: border-box;
    margin-left: 20px;
    img {
      display: block;
      width: auto;
      max-width: 100%;
      height: auto;
      max-height: 100%;
    }
    .tips {
      position: absolute;
      left: -1px;
      bottom: 0;
      width: 120px;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      background: rgba(51, 51, 51, 0.5);
      text-align: center;
      border-radius: 0px 0px 3px 3px;
    }
  }

  .upload-tips {
    font-size: 12px;
    color: #999;
    line-height: 1.5em;
    margin-top: 10px;
  }

  // 图片上传框样式修改
  :deep(.el-upload) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 121px;
    background: #FFFFFF;
    border: 1px solid #EAEAEA;
    border-radius: 3px;
    box-sizing: border-box;
    // img {
    //   width: 120px;
    // }
    .el-icon-plus {
      font-size: 22px;
      color: #EAEAEA;
    }
  }
</style>
