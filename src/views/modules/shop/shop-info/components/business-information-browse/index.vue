<template>
  <div class="component-business-information-browse">
    <div class="business-info-mod">
      <el-form
        ref="companyInfoFormRef"
        label-width="140px"
        :model="companyInfoForm"
        @submit.prevent
      >
        <div class="ci-wrapper">
          <div class="left-item-wrap">
            <el-form-item
              :label="$t('shopProcess.creditCode') + '：'"
              required
            >
              {{ companyInfoForm.creditCode }}
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.firmName') + '：'"
              required
            >
              {{ companyInfoForm.firmName }}
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.representative') + '：'"
              required
            >
              {{ companyInfoForm.representative }}
            </el-form-item>
            <el-form-item
              v-if="paySettlementType===0"
              :label="$t('shopProcess.representative') + '：'"
              required
            >
              {{ companyInfoForm.representative }}
            </el-form-item>
            <el-form-item :label="$t('shopProcess.capital') + '：'">
              <span v-if="companyInfoForm.capital">{{ companyInfoForm.capital }} {{ $t("shopProcess.tenThousandYuan") }}</span>
              <span
                v-else
                class="noyet-wrap"
              >{{ $t('shopProcess.noYet') }}</span>
            </el-form-item>
            <el-form-item :label="$t('shopProcess.residence') + '：'">
              <span v-if="companyInfoForm.residence">{{ companyInfoForm.residence }}</span>
              <span
                v-else
                class="noyet-wrap"
              >{{ $t('shopProcess.noYet') }}</span>
            </el-form-item>
            <el-form-item :label="$t('shopProcess.fountTime') + '：'">
              <span v-if="companyInfoForm.foundTime">{{ companyInfoForm.foundTime.split(' ')[0] }}</span>
              <span
                v-else
                class="noyet-wrap"
              >{{ $t('shopProcess.noYet') }}</span>
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.businessTerm') + '：'"
            >
              <span v-if="companyInfoForm.startTime && companyInfoForm.endTime">
                {{ companyInfoForm.startTime.slice(0, 10) + ' - ' + companyInfoForm.endTime.slice(0, 10) }}
              </span>
              <span v-else-if="companyInfoForm.startTime">
                {{ companyInfoForm.startTime.slice(0, 10) + ' - ' + $t('shopProcess.noFixedTerm') }}
              </span>
              <span
                v-else
                class="noyet-wrap"
              >
                {{ $t('shopProcess.noYet') }}
              </span>
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.businessScope') + '：'"
              required
            >
              {{ companyInfoForm.businessScope }}
            </el-form-item>
            <template v-if="paySettlementType===1">
              <el-form-item
                :label="$t('shopProcess.representative') + '：'"
                required
              >
                {{ companyInfoForm.representative }}
              </el-form-item>
              <el-form-item
                :label="$t('shopProcess.corporateIdentityCard') + '：'"
                required
              >
                {{ companyInfoForm.legalIds }}
              </el-form-item>
              <el-form-item
                :label="$t('allinpay.legalPhone')+'：'"
                required
              >
                {{ companyInfoForm.legalPhone }}
              </el-form-item>
            </template>
          </div>
          <div class="right-item-wrap">
            <!-- 右侧图片 -->
            <el-form-item
              :label="$t('shopProcess.businessLicense') + '：'"
              required
            >
              <div class="business-license-box">
                <div class="logo-image-box">
                  <img-upload
                    v-if="companyInfoForm.businessLicense"
                    v-model="companyInfoForm.businessLicense"
                    :disabled="true"
                    :custom-style="{ width: '100px', height: '100px' }"
                  />
                  <span
                    v-else
                    class="noyet-wrap"
                  >{{ $t('shopProcess.noYet') }}</span>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.corporateIdentityCard') + '：'"
              required
            >
              <div class="business-license-box">
                <div
                  v-if="companyInfoForm.identityCardFront || companyInfoForm.identityCardLater"
                  class="logo-image-box"
                >
                  <img-upload
                    v-model="companyInfoForm.identityCardFront"
                    :disabled="true"
                    :custom-style="{ width: '100px', height: '100px' }"
                  />

                  <img-upload
                    v-model="companyInfoForm.identityCardLater"
                    :disabled="true"
                    :custom-style="{ width: '100px', height: '100px' }"
                  />
                </div>
                <span
                  v-else
                  class="noyet-wrap"
                >{{ $t('shopProcess.noYet') }}</span>
              </div>
            </el-form-item>
            <el-form-item v-if="paySettlementType === 1 && idCardCollectProcessStatus!==4 && (shopStatus===3 || shopStatus===5)">
              <span @click="onDataUpdate">
                <el-icon color="#082BA6"><RefreshRight /></el-icon>
                <span>{{ companyInfoForm.updateTime }}{{ $t('allinpay.updateTime') }}</span>
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="footer">
      <div class="foot-box">
        <div
          v-show="(newStatus !== 0 && paySettlementType!==1) || (paySettlementType===1 && (newStatus!==0 || companyInfoProcessStatus===3 && companyInfoProcessStatus===0) && companyInfoProcessStatus!==1)"
          class="btn default-btn primary-btn"
          @click="onModifyBasicInfor"
        >
          {{ $t('shopProcess.modify') }}
        </div>
        <div
          v-show="(companyInfoForm.edit && paySettlementType!==1) || (paySettlementType===1 && companyInfoForm.edit && companyInfoProcessStatus!==0)"
          class="btn default-btn primary-btn"
          @click="onViewModify"
        >
          {{ $t('shopProcess.viewModify') }}
        </div>
      </div>
    </div>

    <!-- 查看修改信息 -->
    <view-modify-business-information
      v-if="isModifyBusinessInfor"
      ref="viewModifyBusinessInforRef"
      :modify-information="companyInfoForm"
      @close-modify-business="onCloseModifyBusinessInfor"
    />
    <businessInfoReupload
      v-if="reuploadVisible"
      ref="businessInfoReuploadRef"
      :shop-company-id="companyInfoForm.shopCompanyId"
      :shop-id="companyInfoForm.shopId"
    />
  </div>
</template>

<script setup>
import viewModifyBusinessInformation from '../view-modify-business-information/index.vue'
import businessInfoReupload from '../business-info-reupload/index.vue'
import { Debounce } from '@/utils/debounce'

const emit = defineEmits(['closeBusinessBrowse'])

defineProps({
  // 企业信息审核状态 0.未提交 1.待审核 2.审核成功 3.审核失败
  companyInfoProcessStatus: {
    type: Number,
    default: 0
  }
})
const allinpayStore = useAllinpayStore
const shopStatus = computed(() => {
  return allinpayStore.shopStatus // 店铺状态 -1:已删除 0: 停业中 1:营业中 2:平台下线 3:待审核 4:店铺申请中 5:申请失败 6:上线申请中
})
const paySettlementType = computed(() => {
  return allinpayStore.paySettlementType
})
const idCardCollectProcessStatus = computed(() => {
  return allinpayStore.idCardCollectProcessStatus
})

onMounted(() => {
  init()
})

const init = () => {
  onGetBusinessInfor()
  onGetmodifyInformation()
}
// 修改信息
const onModifyBasicInfor = () => {
  emit('closeBusinessBrowse')
}
// 获取工商信息
const companyInfoForm = ref({ // 工商信息
  creditCode: '',
  identityCardLater: '',
  identityCardFront: '',
  businessLicense: '',
  businessScope: '',
  startTime: '',
  endTime: '',
  foundTime: '',
  residence: '',
  capital: '',
  representative: '',
  firmName: ''
})
const onGetBusinessInfor = () => {
  http({
    url: http.adornUrl('/shop/shopCompany'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    companyInfoForm.value = data
  })
}
// 查看审核提交信息
const newStatus = ref('') // 状态
const onGetmodifyInformation = () => {
  http({
    url: http.adornUrl('/shop/companyAuditing/auditInfo'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    if (data) {
      nextTick(() => {
        newStatus.value = data.status
      })
    }
  })
}
// 查看修改信息
const viewModifyBusinessInforRef = ref(null)
const isModifyBusinessInfor = ref(false)
const onViewModify = () => {
  isModifyBusinessInfor.value = true
  nextTick(() => {
    viewModifyBusinessInforRef.value.init()
  })
}
const onCloseModifyBusinessInfor = () => {
  nextTick(() => {
    onGetBusinessInfor()
    onGetmodifyInformation()
  })
}
// 数据更新
const businessInfoReuploadRef = ref(null)
const reuploadVisible = ref(false) // 重新上传弹窗
const onDataUpdate = Debounce(() => {
  http({
    url: http.adornUrl('/shop/shopCompany/updateIdCardStatus'),
    method: 'put',
    params: http.adornParams()
  }).then(data => {
    if (data === 4) {
      // 审核成功就刷新页面
      location.reload()
    } else if (data === 0 || data === 2 || data === 3) {
      // 审核失败，弹窗让用户重新上传
      reuploadVisible.value = true
      nextTick(() => {
        businessInfoReuploadRef.value.init(data)
      })
      onGetBusinessInfor()
    } else if (data === 1) {
      // 待审核则更新工商信息
      onGetBusinessInfor()
    }
  })
}, 1500)

</script>

<style lang="scss" scoped>
  .component-business-information-browse {
    color: #606266;
    font-size: 14px;
    margin-top: 30px;
  }
  .logo-image-box {
    height: 98px;
    display: flex;
    flex-wrap: nowrap;
    .up-img-box + .up-img-box {
      margin-left: 20px;
    }
  }
  .ci-wrapper {
    display: flex;
    justify-content: space-evenly;
    .left-item-wrap {
      width: 50%;
    }
    .right-item-wrap {
      width: 50%;
    }
  }
  .foot-box {
    margin-left: 140px;
  }
  .noyet-wrap {
    color: #999;
  }
  div :deep(.el-form-item__label) {
    min-width: 150px;
  }
</style>
