<template>
  <el-dialog
    v-model="visible"
    :title="$t('live.view')"
    :close-on-click-modal="false"
    class="sign-add-or-update"
  >
    <!-- 审核信息 -->
    <el-form
      label-width="auto"
      @submit.prevent
    >
      <el-form-item
        v-if="dataList.length"
        :label="$t('withdrawal.reviewStatus')+'：'"
        prop="status"
      >
        <div class="status-row">
          <div
            v-if="dataForm.status === 0"
            class="status-item"
          >
            {{ $t('shop.unreviewed') }}
          </div>
          <div
            v-if="dataForm.status === 1"
            class="status-item"
          >
            {{ $t('shop.passed') }}
          </div>
          <div
            v-if="dataForm.status === -1"
            class="status-item"
          >
            {{ $t('shop.notPass') }}
          </div>
          <!-- 撤回申请 状态为0未审核（即申请中）时允许撤回申请 -->
          <div
            v-if="dataForm.status === 0 && isAuth('shop:signCategory:cancel')"
            class="default-btn primary-btn"
            @click="withdrawalApplication"
          >
            {{ $t('order.withdrawApplication') }}
          </div>
        </div>
      </el-form-item>
      <el-form-item
        v-if="dataForm.status === -1"
        :label="$t('order.denialReason')+'：'"
        prop="remarks"
      >
        <div class="remarks">
          {{ dataForm.remarks }}
        </div>
      </el-form-item>
    </el-form>

    <!-- 申请签约的类目/品牌列表 -->
    <div class="main-container">
      <div class="table-con">
        <!-- 类目 -->
        <el-table
          ref="shopSignTable"
          :data="dataList"
          header-cell-class-name="table-header"
          style="width: 100%"
        >
          <el-table-column
            prop="name"
            :label="$t('shopProcess.categoryName')"
            align="center"
          />
          <el-table-column
            prop="parentName"
            :label="$t('shopProcess.parentCategoryName')"
            align="center"
          />
          <el-table-column
            prop="platformRate"
            :label="$t('shopProcess.categoryRate')"
            align="center"
          >
            <template #default="{ row }">
              <div v-if="row.customizeRate || row.customizeRate === 0 || row.platformRate || row.platformRate === 0">
                <span v-if="row.customizeRate === null">{{ row.platformRate }} %</span>
                <span v-else>{{ row.customizeRate }} %</span>
              </div>
              <div v-else>
                --
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="qualifications"
            :label="$t('shopProcess.categoryQualifications')"
            align="center"
          >
            <template #default="{row}">
              <div
                v-if="row.imgs && row.imgs.length"
                class="img-box"
              >
                <el-image
                  v-for="(img,index) in row.imgs"
                  :key="index"
                  class="rotating-img"
                  :src="checkFileUrl(img)"
                  :preview-src-list="checkFileUrl(row.imgs)"
                  preview-teleported
                />
              </div>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="categoryStatus"
            :label="$t('shopProcess.categoryStatus')"
            align="center"
          >
            <template #default="scope">
              <div
                v-if="scope.row.categoryStatus === 1"
                class="tag-txt"
              >
                {{ $t('publics.normal') }}
              </div>
              <div
                v-if="scope.row.categoryStatus === 0"
                class="tag-txt red-tag-txt"
              >
                {{ $t('publics.LowerShelf') }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div
          v-if="!dataList.length"
          class="default-btn"
          @click="closeAndUpdate"
        >
          {{ $t("station.closeAndUpdate") }}
        </div>
        <div
          v-else
          class="default-btn"
          @click="visible = false"
        >
          {{ $t("station.close") }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { isAuth } from '@/utils'
const emit = defineEmits(['refreshChange'])
const visible = ref(false)
const dataList = ref([])
const platformBrandList = ref([]) // 平台品牌
const customizeBrandList = ref([]) // 自定义品牌

// 审核
const dataForm = ref({
  status: '', // 0 未审核 1已通过 -1未通过 不传返回全部
  remarks: '',
  type: '' // 1类目 2品牌
})

const init = (type, data) => {
  visible.value = true
  dataForm.value = data || {}
  dataForm.value.type = type // 1类目 2品牌
  nextTick(() => {
    dataList.value = []
    platformBrandList.value = []
    customizeBrandList.value = []
    getDataList()
  })
}

// 获取签约的分类列表（状态参数为空则返回所有）
const getDataList = () => {
  // status 0 未审核 1已通过 -1未通过 不传返回全部
  http({
    url: http.adornUrl('/prod/category/listSigningCategory'),
    method: 'get',
    params: http.adornParams({
      status: dataForm.value.status
    })
  }).then(({ data }) => {
    dataList.value = handleImgData(data)
  })
}

const handleImgData = (dataList) => {
  dataList.forEach(item => {
    const imgUrls = item.qualifications ? item.qualifications.split(',') : []
    if (imgUrls.length) {
      item.imgs = handleImgUrl(imgUrls)
    } else {
      item.imgs = []
    }
  })
  return dataList
}

const handleImgUrl = (imgUrls) => {
  const imgs = []
  imgUrls.forEach((img, index) => {
    imgs.push(checkFileUrl(img))
  })
  return imgs
}

/**
 * 撤回申请
 */
const withdrawalApplication = () => {
  ElMessageBox.confirm($t('shopProcess.withdrawalApplicationOfContract'), $t('resource.tips'), {
    confirmButtonText: $t('resource.confirm'),
    cancelButtonText: $t('resource.cancel'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/signingAuditing/revoke'),
      method: 'PUT',
      data: dataForm.value.type
    }).then(() => {
      ElMessage({
        message: $t('shopProcess.contractApplicationWithdrawn'),
        type: 'success',
        duration: 1000,
        onClose: () => {
          visible.value = false
          emit('refreshChange', dataForm.value.type)
        }
      })
    })
  }).catch(() => {})
}

// 关闭并返回刷新（列表为空时）
const closeAndUpdate = () => {
  visible.value = false
  emit('refreshChange', dataForm.value.type)
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.sign-add-or-update {
  & :deep(.el-dialog__body) {
    padding-top: 20px;
  }
  .status-row {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .remarks {
    word-break: break-word;
  }
  .main-container {
    padding: 0;
    margin-top: 30px;
    .table-con {
      padding-bottom: 0;
    }
    & :deep(.el-table__body-wrapper) {
      max-height: 350px;
      overflow-y: auto;
    }
    & :deep(.img-box) {
      text-align: center;
    }
    .rotating-img {
      display: inline-block;
      width: 60px;
      height: 60px;
    }
    .rotating-img:not(:last-child) {
      margin-right: 5px;
    }
  }
}
</style>
