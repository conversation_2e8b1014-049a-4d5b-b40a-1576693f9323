<template>
  <el-dialog
    v-model="visible"
    :title="$t('shopProcess.changeBusinessInfor')"
    :append-to-body="true"
    :close-on-click-modal="false"
    top="10vh"
    width="70%"
    class="component-view-modify-business-information"
  >
    <div class="popup">
      <!-- 审核状态 -->
      <div class="topModifyColumn">
        <div>
          {{ $t('shopProcess.auditStatus') }} : {{ auditStatus == -1 ? $t('shopProcess.notPass') : auditStatus == 1 ? $t('shopProcess.passed') : $t('shopProcess.notAudit') }}
          <span v-if="auditStatus == -1">&nbsp;&nbsp;&nbsp;&nbsp;{{ $t('shopProcess.denialReason') + '：' }} {{ modifyInformation.remarks ? modifyInformation.remarks : $t('shopProcess.noYet') }}</span>
        </div>
        <div
          v-if="auditStatus == 0 && paySettlementType!==1"
          class="default-btn primary-btn"
          @click="onWithdrawReview"
        >
          {{ $t('shopProcess.reAmendmentRequest') }}
        </div>
      </div>
      <!-- 修改信息 -->
      <el-form
        ref="modifyInformationRef"
        label-width="180px"
        :model="modifyInformation"
        @submit.prevent
      >
        <div class="ci-wrapper">
          <div class="left-item-wrap">
            <el-form-item
              :label="$t('shopProcess.creditCode') + '：'"
              required
            >
              {{ modifyInformation.creditCode }}
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.firmName') + '：'"
              required
            >
              {{ modifyInformation.firmName }}
            </el-form-item>
            <el-form-item
              v-if="paySettlementType!==1"
              :label="$t('shopProcess.representative') + '：'"
              required
            >
              {{ modifyInformation.representative }}
            </el-form-item>
            <el-form-item :label="$t('shopProcess.capital') + '：'">
              <span v-if="modifyInformation.capital">{{ modifyInformation.capital }} {{ $t("shopProcess.tenThousandYuan") }}</span>
              <span
                v-else
                class="noyet-wrap"
              >{{ $t('shopProcess.noYet') }}</span>
            </el-form-item>
            <el-form-item :label="$t('shopProcess.residence') + '：'">
              <span v-if="modifyInformation.residence">{{ modifyInformation.residence }}</span>
              <span
                v-else
                class="noyet-wrap"
              >{{ $t('shopProcess.noYet') }}</span>
            </el-form-item>

            <el-form-item :label="$t('shopProcess.fountTime') + '：'">
              <span v-if="modifyInformation.foundTime">{{ modifyInformation.foundTime.split(' ')[0] }}</span>
              <span
                v-else
                class="noyet-wrap"
              >{{ $t('shopProcess.noYet') }}</span>
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.businessTerm') + '：'"
            >
              <span v-if="modifyInformation.startTime && modifyInformation.endTime">
                {{ modifyInformation.startTime.slice(0, 10) + ' - ' + modifyInformation.endTime.slice(0, 10) }}
              </span>
              <span v-else-if="modifyInformation.startTime">
                {{ modifyInformation.startTime.slice(0, 10) + ' - ' + $t('shopProcess.noFixedTerm') }}
              </span>
              <span
                v-else
                class="noyet-wrap"
              >
                {{ $t('shopProcess.noYet') }}
              </span>
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.businessScope') + '：'"
              required
            >
              <span class="businessScope">
                {{ modifyInformation.businessScope }}
              </span>
            </el-form-item>
            <template v-if="paySettlementType===1">
              <el-form-item
                :label="$t('shopProcess.representative') + '：'"
                required
              >
                {{ modifyInformation.representative }}
              </el-form-item>
              <el-form-item
                :label="$t('allinpay.corporateIdentityCard') + '：'"
                required
              >
                {{ modifyInformation.legalIds }}
              </el-form-item>
              <el-form-item
                :label="$t('allinpay.legalPhone') + '：'"
                required
              >
                {{ modifyInformation.legalPhone }}
              </el-form-item>
            </template>
          </div>
          <div class="right-item-wrap">
            <!-- 右侧图片 -->
            <el-form-item
              :label="$t('shopProcess.businessLicense') + '：'"
              required
            >
              <div class="business-license-box">
                <div class="logo-image-box">
                  <img-upload
                    v-model="modifyInformation.businessLicense"
                    :disabled="true"
                    :custom-style="{ width: '100px', height: '100px' }"
                  />
                </div>
              </div>
            </el-form-item>
            <el-form-item
              :label="$t('shopProcess.corporateIdentityCard') + '：'"
              required
            >
              <div class="business-license-box">
                <div class="logo-image-box">
                  <img-upload
                    v-model="modifyInformation.identityCardFront"
                    :disabled="true"
                    :custom-style="{ width: '100px', height: '100px' }"
                  />
                  <img-upload
                    v-model="modifyInformation.identityCardLater"
                    :disabled="true"
                    :custom-style="{ width: '100px', height: '100px' }"
                  />
                </div>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div
          class="default-btn"
          @click="visible = false"
        >
          {{ $t('shopProcess.close') }}
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
const emit = defineEmits(['closeModifyBusiness'])

const paySettlementType = computed(() => {
  return useAllinpayStore().paySettlementType
})

// 控制显示
const visible = ref(false)
const init = () => {
  visible.value = true
  onGetmodifyInformation()
}

// 审核情况
const auditStatus = ref('')
const modifyInformation = ref({
  creditCode: '',
  firmName: '',
  representative: '',
  capital: '',
  residence: '',
  fountTime: '',
  startTime: '',
  endTime: '',
  businessScope: '',
  businessLicense: '',
  identityCardFront: '',
  identityCardLater: '',
  legalIds: '',
  legalPhone: ''
})
const onGetmodifyInformation = () => {
  http({
    url: http.adornUrl('/shop/companyAuditing/auditInfo'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    nextTick(() => {
      modifyInformation.value = { ...data, ...data.shopCompany }
      auditStatus.value = data.status
    })
  })
}
// 撤销申请
const onWithdrawReview = () => {
  http({
    url: http.adornUrl('/shop/companyAuditing/revoke'),
    method: 'put',
    data: modifyInformation.value.companyAuditingId
  }).then(() => {
    ElMessage({
      message: $t('shopProcess.auditTip2'),
      type: 'success',
      duration: 1000,
      onClose: () => {
        onClosePop()
      }
    })
  }).catch(() => {
    onClosePop()
  })
}
const onClosePop = () => {
  visible.value = false
  nextTick(() => {
    emit('closeModifyBusiness')
  })
}
defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.component-view-modify-business-information {
  &:deep(.el-dialog) {
    min-width: 950px;
  }
  .popup {
    display: block;
    color: #606266;
    font-size: 14px;
    .topModifyColumn {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff2f2;
      padding: 10px;
    }
    .logo-image-box {
      height: 98px;
      display: flex;
      flex-wrap: nowrap;
      * + * {
        margin-left: 10px;
      }
    }

    .ci-wrapper {
      display: flex;
      justify-content: space-evenly;
      .left-item-wrap {
        width: 50%;
      }
      .right-item-wrap {
        width: 50%;
      }
    }
  }
}
.noyet-wrap {
  color: #999;
}
.businessScope {
  word-break: break-word;
}
</style>
