
.shop-name {
  font-size: 15px;
}
div :deep(.el-input .el-input--suffix input.el-input__inner) {
  height: 30px !important;
  line-height: 30px !important;
}
div :deep(.el-textarea__inner) {
  width: 60%;
  height: 130px;
}
.yzm-tips {
  display: block;
  color: #aaa;
  margin-left: 120px;
  line-height: 2em;
  margin-bottom: 10px;
}
.error-tips {
  color: #e43130;
}
.amount-btn {
  width: 35%;
}
.add-card {
  display: block;
  margin: 0 0 20px 120px;
  line-height: 1em;
}

div :deep(.el-dialog__body) {
  padding: 10px 20px !important;
}
/* 银行卡 */
.bankcard {
  position: relative;
  display: inline-block;
  width: 290px;
  height: 100px;
  border: 1px solid #eee;
  border-radius: 2px;
  padding: 10px;
  margin-right: 20px;
  cursor: pointer;
  margin-top: 10px;
}
.close-icon {
  display: block;
  position: absolute;
  top: 6px;
  right: 6px;
  border: 1px solid #bbb;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  line-height: 15px;
  text-align: center;
  font-size: 12px;
  color: #bbb;
  cursor: pointer;
  z-index: 11;
}
.card-num {
  font-size: 15px;
  line-height: 1.5em;
  color: #333;
  text-align: left;
  margin-bottom: 20px;
  width: 210px;
}
.card-num .tip {
  font-size: 12px;
  transform: scale(0.83,0.83);
  border: 1px solid #155BD4;
  color: #155BD4;
  padding: 0 4px;
  border-radius: 2px;
  height: 17px;
  line-height: 17px;
  box-sizing: border-box;
}
.card-bottom {
  position: absolute;
  bottom: 10px;
  font-size: 14px;
  display: flex;
  width: 90%;
  justify-content: space-between;
  line-height: 1.5em;
}
.card-name {
  width: 145px;
  flex: 1;
}
.chooseDefault {
  text-align: right;
  color: #155BD4;
  cursor: pointer;
  flex: 1;
}
.default {
  color: #999;
  cursor: auto;
}
.active {
  border: 1px solid #155bd4;
}
.bankcard-box{
  display: flex;
  flex-wrap: wrap;
}
.verification-box {
  display: flex;
  width: 350px;
  .verification-input {
    flex: 1;
  }
}