<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    :title="$t('product.basicInformation')"
  >
    <el-form
      :model="dataForm"
      label-width="140px"
      @submit.prevent
    >
      <el-form-item :label="$t('shop.withdrawalAmount') + '：'">
        <span>{{ dataForm.amount }}</span> {{ $t('user.yuan') }}
      </el-form-item>
      <el-form-item :label="$t('withdrawal.toTheBank') + '：'">
        <span v-if="dataForm.shopBankCard && dataForm.shopBankCard.bankName">{{ dataForm.shopBankCard.bankName }}</span>
      </el-form-item>
      <el-form-item :label="$t('withdrawal.merchantNotes') + '：'">
        <div class="remarks-txt">
          {{ dataForm.shopRemarks }}
        </div>
      </el-form-item>
      <el-form-item
        v-if="paySettlementType!==1"
        :label="$t('withdrawal.reviewStatus') + '：'"
      >
        <el-tag
          v-if="dataForm.status === -1"
          type="danger"
        >
          {{ $t('shop.notThrough') }}
        </el-tag>
        <el-tag
          v-if="dataForm.status === 1"
          type="success"
        >
          {{
            $t('shop.withPass')
          }}
        </el-tag>
        <el-tag
          v-else-if="dataForm.status === 0"
        >
          {{
            $t('shop.notAudit')
          }}
        </el-tag>
        <el-tag
          v-else-if="dataForm.status === 2"

          type="success"
        >
          {{
            $t('shop.succIss')
          }}
        </el-tag>
        <el-tag
          v-else
          type="error"
        >
          {{ $t('shop.failIss') }}
        </el-tag>
      </el-form-item>
      <el-form-item
        v-else
        :label="$t('withdrawal.reviewStatus') + '：'"
      >
        <el-tag>
          {{
            [
              $t('allinpay.allinpayWithdrawalUnpaid'),
              $t('allinpay.allinpayWithdrawalApproved'),
              $t('allinpay.allinpayWithdrawalSuccessful'),
              $t('allinpay.allinpayWithdrawalFailure')
            ][dataForm.allinpayStatus]
          }}
        </el-tag>
      </el-form-item>
      <el-form-item
        v-if="dataForm.status !== 0 && dataForm.payingAccount!==null"
        :label="$t('withdrawal.payingAccount') + '：'"
      >
        <span>{{ dataForm.payingAccount }}</span>
      </el-form-item>
      <el-form-item
        v-if="dataForm.status !== 0 && dataForm.payingCardNo!==null"
        :label="$t('withdrawal.payingCardNo') + '：'"
      >
        <span>{{ dataForm.payingCardNo }}</span>
      </el-form-item>
      <el-form-item
        v-if="dataForm.status !== 0 && dataForm.payingTime!==null"
        :label="$t('withdrawal.payingTime') + '：'"
      >
        <span>{{ dataForm.payingTime }}</span>
      </el-form-item>
      <el-form-item
        v-if="paySettlementType!==1"
        :label="$t('withdrawal.platformNote') + '：'"
      >
        <span style="word-break: break-word;">{{ dataForm.remarks }}</span>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
const visible = ref(false)
const dataForm = ref({
  status: 0,
  payingAccount: '', // 付款户名
  payingCardNo: '', // 付款账号
  payingTime: '' // 付款日期
})
const allinpayStore = useAllinpayStore()
const paySettlementType = computed(() => {
  return allinpayStore.paySettlementType
})

const init = (rowData) => {
  dataForm.value = Object.assign({}, rowData)
  if (dataForm.value.payingTime) {
    dataForm.value.payingTime = dataForm.value.payingTime.substr(0, 10)
  }
  visible.value = true
}
defineExpose({
  init
})
</script>
<style scoped>
.remarks-txt {
  word-break: break-word;
}
</style>
