<template>
  <div class="component-withdrawal">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            :prop="paySettlementType===1 ? 'allinpayStatus' : 'status'"
            :label="$t('shop.withdrawalStatus')+':'"
          >
            <el-select
              v-model="status"
              clearable
              :placeholder="$t('shop.withdrawalStatus')"
              @change="onSetStatus"
            >
              <el-option
                v-for="item in withdrawalStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="table-con">
        <el-table
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
        >
          <el-table-column
            type="index"
            :label="$t('number')"
            align="center"
            width="85"
          />
          <el-table-column
            :label="$t('shop.withdrawalAmount')"
            prop="amount"
            align="center"
          />
          <el-table-column
            :label="$t('shop.withdrawalTime')"
            prop="createTime"
            align="center"
          />
          <el-table-column
            align="center"
            :label="$t('shop.withdrawalStatus')"
          >
            <template #default="scope">
              <span v-if="paySettlementType !== 1">{{ [$t('station.auditFailure'),$t('station.pendingReview'),$t('station.successfulAudit')][scope.row.status + 1] }}</span>
              <span v-else>{{ [$t('allinpay.allinpayWithdrawalUnpaid'), $t('allinpay.allinpayWithdrawalApproved'), $t('allinpay.allinpayWithdrawalSuccessful'), $t('allinpay.allinpayWithdrawalFailure')][scope.row.allinpayStatus] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            width="220"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  class="default-btn text-btn"
                  @click="onHandleDetail(scope.row)"
                >
                  {{ $t('shop.withdrawalDetail') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
    <withdrawal
      v-if="detailVisible"
      ref="detailRef"
    />
  </div>
</template>

<script setup>
import Withdrawal from '../withdrawal-detail/index.vue'

const status = ref(null)

const allinpayStore = useAllinpayStore()
const paySettlementType = computed(() => {
  return allinpayStore.paySettlementType
})
const withdrawalStatus = computed(() => {
  if (paySettlementType.value === 1) {
    return [
      {
        label: $t('allinpay.allinpayWithdrawalUnpaid'),
        value: 0
      }, {
        label: $t('allinpay.allinpayWithdrawalApproved'),
        value: 1
      }, {
        label: $t('allinpay.allinpayWithdrawalSuccessful'),
        value: 2
      }, {
        label: $t('allinpay.allinpayWithdrawalFailure'),
        value: 3
      }
    ]
  } else {
    return [
      {
        label: $t('station.auditFailure'),
        value: -1
      }, {
        label: $t('station.pendingReview'),
        value: 0
      }, {
        label: $t('station.successfulAudit'),
        value: 1
      }
    ]
  }
})

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
onMounted(() => {
  onGetDataList(page)
})

let tempSearchForm = null // 保存上次点击查询的请求条件
const searchForm = reactive({
  status: null,
  allinpayStatus: null
}) // 搜索
const dataList = ref([])
const onGetDataList = (pageParam, newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/shop/withdrawCash/page'),
    method: 'get',
    params: http.adornParams(
      Object.assign(
        {
          current: pageParam == null ? page.currentPage : pageParam.currentPage,
          size: pageParam == null ? page.pageSize : pageParam.pageSize
        },
        tempSearchForm
      )
    )
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}
const detailRef = ref(null)
const detailVisible = ref(false)
const onHandleDetail = (rowData) => {
  detailVisible.value = true
  nextTick(() => {
    detailRef.value?.init(rowData)
  })
}
const onSearch = (newData = false) => {
  onGetDataList(page, newData)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  onGetDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  onGetDataList(page)
}
const searchFormRef = ref(null)
const onResetSearch = () => {
  searchFormRef.value?.resetFields()
  status.value = null
}
const onSetStatus = (val) => {
  if (paySettlementType.value === 1) {
    searchForm.allinpayStatus = val
  } else {
    searchForm.status = val
  }
}
defineExpose({
  onGetDataList
})
</script>
<style lang="scss" scoped>
</style>
