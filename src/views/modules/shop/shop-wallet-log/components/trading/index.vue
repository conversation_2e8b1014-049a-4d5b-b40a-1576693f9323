<template>
  <div class="component-trading">
    <div class="search-bar">
      <el-form
        clearable
        :inline="true"
        :model="dataFrom"
        class="demo-form-inline from"

        @submit.prevent
      >
        <div class="input-row">
          <el-form-item :label="$t('chat.orderNumber') + ':'">
            <el-input
              v-model="dataFrom.orderNumber"
              maxlength="20"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              clearable
              :placeholder="$t('chat.orderNumber')"
            />
          </el-form-item>
          <el-form-item :label="$t('order.refundId') + ':'">
            <el-input
              v-model="dataFrom.refundSn"
              maxlength="19"
              onkeyup="value=value.replace(/[^\d]/g,'')"
              clearable
              :placeholder="$t('order.refundId')"
            />
          </el-form-item>
          <el-form-item :label="$t('shop.ioType') + ':'">
            <el-select
              v-model="dataFrom.ioType"
              clearable
              :placeholder="$t('shop.ioType')"
            >
              <el-option
                v-for="item in ioType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('shop.amountType') + ':'">
            <el-select
              v-model="dataFrom.amountType"
              clearable
              :placeholder="$t('shop.amountType')"
            >
              <el-option
                v-for="item in amountType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('shop.reason') + ':'">
            <el-select
              v-model="dataFrom.reason"
              style="min-width: 230px;"
              clearable
              :placeholder="$t('shop.reason')"
            >
              <el-option
                v-for="item in reason"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('formData.timeLimit')+':'">
            <cm-date-pic-ker
              v-model="dateRangeParam"
            />
          </el-form-item>
          <el-form-item :label="$t('station.stationNames') + ':'">
            <el-select v-model="dataFrom.stationId">
              <el-option
                v-for="node in stationList"
                :key="node.stationId"
                :label="node.stationName"
                :value="node.stationId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSubmit(true)"
            >
              {{ $t('order.query') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch"
            >
              {{ $t('shop.resetMap') }}
            </div>
            <div
              class="default-btn"
              @click="getExportExcel()"
            >
              {{ $t("formData.export") }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="table-con">
        <el-table
          :data="tableData"
          style="width: 100%"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
        >
          <el-table-column
            type="index"
            :label="$t('number')"
            align="left"
            width="85"
          />
          <el-table-column
            prop="operation"
            :label="$t('shop.amountType')"
            width="120"
          >
            <template #default="scope">
              <span>{{ [$t('shop.amoSett'),$t('shop.avaStoAmo'),$t('shopWallet.unusableBalance'),$t('shop.summAmo')][scope.row.amountType] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            type="index"
            :label="$t('station.stationNames')"
            align="left"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.stationName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="changeAomunt"
            :label="$t('shop.changeAomunt')"
            width="120"
          >
            <template #default="scope">
              <span>{{ ['-','+'][scope.row.ioType] }} ￥{{ Math.abs(scope.row.changeAomunt) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="orderNumber"
            :label="$t('chat.orderNumber')"
            align="left"
            width="220"
          >
            <template #default="scope">
              <span>{{ scope.row.orderNumber || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundSn"
            :label="$t('order.refundId')"
            width="220"
          >
            <template #default="scope">
              <span>{{ scope.row.refundSn || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="userAmount"
            :label="$t('shopWallet.operationAmount')"
            width="140"
          >
            <template #default="scope">
              <span v-if="scope.row.userAmount === 0 || !scope.row.userAmount">0</span>
              <span v-else>{{ scope.row.reason === 2 ? '-' : ['-','+'][scope.row.ioType] }}{{ scope.row.userAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformAmount"
            :label="$t('settlementDetail.platformShareReduce')"
            width="150"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.platformAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="shopReduceAmount"
            :label="$t('settlementDetail.multishopReduce')"
            width="150"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.shopReduceAmount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="distributionAmount"
            :label="$t('settlementDetail.distributionAmount')"
            min-width="130"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.distributionAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformCommission"
            :label="$t('shopWallet.platformCommission')"
            min-width="130"
          >
            <template #default="scope">
              <span>￥ {{ scope.row.platformCommission || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="reason"
            min-width="150"
            align="left"
            :label="$t('shopWallet.amountChangeReason')"
          >
            <template #default="scope">
              <span>{{ [$t('shop.userPay'),$t('shopWallet.orderSettlement'),$t('shopWallet.applyRefund'),$t('shopWallet.rejectApplyRefund'),$t('shop.withApply'),
                        $t('shop.withNoPass'),$t('shop.withPass'),$t('shop.succIss'),$t('shop.failIss')][scope.row.reason] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            min-width="150"
            :label="$t('shopFeature.chooseFeature.createTime')"
          />
          <el-table-column
            prop="operation"
            :label="$t('text.menu')"
            align="center"
            fixed="right"
            width="120"
          >
            <template #default="scope">
              <el-button
                :disabled="!scope.row.orderNumber && !scope.row.refundSn"
                type="primary"
                link
                @click="onHandleDetail(scope.row.orderNumber,scope.row.refundSn,scope.row.reason)"
              >
                {{ $t('shop.withdrawalDetail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页条 -->
      <el-pagination
        v-if="total > 0"
        :current-page="params.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="params.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup>
import CmDatePicKer from '../cm-date-picker/index.vue'
import { ElLoading, ElMessageBox } from 'element-plus'

const ioType = [{
  value: 0,
  label: $t('shop.pay')
}, {
  value: 1,
  label: $t('shop.income')
}]
const amountType = [{
  value: 0,
  label: $t('shop.amoSett')

}, {
  value: 1,
  label: $t('shop.avaStoAmo')
}, {
  value: 2,
  label: $t('shopWallet.unusableBalance')
}] // '未结算金额', '可提现金额', '冻结金额'
const reason = [
  {
    value: 0,
    label: $t('shop.userPay')
  },
  {
    value: 1,
    label: $t('shopWallet.orderSettlement')
  },
  {
    value: 2,
    label: $t('shopWallet.applyRefund')
  },
  {
    value: 4,
    label: $t('shop.withApply')
  },
  {
    value: 5,
    label: $t('shop.withNoPass')
  },
  {
    value: 6,
    label: $t('shop.withPass')
  }
]

onMounted(() => {
  onGetData()
  onGetStationList()
})

const stationList = ref([])
// 所有门店列表
const onGetStationList = () => {
  http({
    url: http.adornUrl('/admin/station/listShopAllStation'),
    method: 'get'
  }).then(({ data }) => {
    stationList.value = data
  })
}

// 获取列表数据
let tempSearchForm = null // 保存上次点击查询的请求条件
const total = ref(0)
const params = reactive({
  current: 1,
  size: 10
})
const dataFrom = ref({
  ioType: '',
  amountType: '',
  orderNumber: '',
  refundSn: '',
  reason: '',
  startTime: '',
  endTime: ''
})
const tableData = ref([{
  balance: 111
}])
const onGetData = (newData = false) => {
  if (newData || !tempSearchForm) {
    tempSearchForm = { ...dataFrom.value, ...params }
  } else {
    tempSearchForm = { ...tempSearchForm, ...params }
  }
  http({
    url: http.adornUrl('/shop/shopWalletLog/page'),
    method: 'get',
    params: tempSearchForm
  }).then(({ data }) => {
    tableData.value = data.records
    total.value = data.total
  })
}

// 查询
const dateRangeParam = ref(null)
const onSubmit = (newData = false) => {
  dataFrom.value.orderNumber = dataFrom.value.orderNumber.trim()
  dataFrom.value.refundSn = dataFrom.value.refundSn.trim()
  dataFrom.value.startTime = dateRangeParam.value ? dateRangeParam.value[0] : ''
  dataFrom.value.endTime = dateRangeParam.value ? dateRangeParam.value[1] : ''
  params.current = 1
  onGetData(newData)
}
const onPageSizeChange = (val) => {
  params.size = val
  onGetData()
}
const onPageChange = (val) => {
  params.current = val
  onGetData()
}
// 跳转到详情页
const router = useRouter()
const onHandleDetail = (orderNumber, refundSn, reason) => {
  router.push({
    path: '/finance/billing-details/index',
    query: {
      orderNumber,
      refundSn,
      reason
    }
  })
}
// excel导出
const getExportExcel = () => {
  ElMessageBox.confirm(`${$t('shopWallet.exportTips')}`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      target: '.prod-list-mod',
      customClass: 'export-load',
      background: 'transparent',
      text: $t('formData.exportIng')
    })
    http({
      url: http.adornUrl('/shop/shopWalletLog/export'),
      method: 'get',
      params: http.adornParams(
        Object.assign(
          tempSearchForm
        )
      ),
      responseType: 'blob' // 解决文件下载乱码问题
    }).then(({ data }) => {
      loading.close()
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
      const fileName = $t('shopWallet.excelName')
      const elink = document.createElement('a')
      if ('download' in elink) { // 非IE下载
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
      } else { // IE10+下载
        navigator.msSaveBlob(blob, fileName)
      }
    }).catch(() => {
      loading.close()
    })
  })
}
// 清空查询 的表单
const onResetSearch = () => {
  dataFrom.value = {
    ioType: '',
    amountType: '',
    orderNumber: '',
    refundSn: '',
    reason: '',
    startTime: '',
    endTime: ''
  }
  dateRangeParam.value = null
}
defineExpose({
  onGetData
})
</script>

<style lang="scss" scoped>
  :deep(.el-range-separator) {
    width: 18px;
  }
  :deep(.el-date-editor--daterange) {
    width: 300px !important;
  }
</style>
