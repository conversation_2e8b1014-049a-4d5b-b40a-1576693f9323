<template>
  <div class="page-shop-wallet-log">
    <!-- 店铺余额栏 -->
    <div class="balance-wrapper">
      <div class="balance-item">
        <div class="balance-item-title">
          <span class="title-tips">{{ $t("shop.availableStoreBalance") }}</span>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('shop.incAvaStoBal')"
            placement="right"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="balance-show">
          <span class="balance-num">{{ shopWallet.settledAmount }}</span>
          <div
            v-if="isAuth('shop:withdrawCash:apply')"
            class="default-btn primary-btn"
            @click="onApplyWithdrawCash"
          >
            {{ $t("shop.withdraw") }}
          </div>
        </div>
      </div>

      <div class="balance-item">
        <div class="balance-item-title">
          <span class="title-tips">{{ $t("shop.pendingSettlement") }}</span>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('shop.pendingSettlementTip')"
            placement="right"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="balance-show">
          <span class="balance-num">{{ shopWallet.unsettledAmount }}</span>
        </div>
      </div>

      <div class="balance-item">
        <div class="balance-item-title">
          <span class="title-tips">{{ $t("shop.unusableBalanceYuan") }}</span>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('shop.unusableBalanceYuanTip')"
            placement="right"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="balance-show">
          <span class="balance-num">{{ shopWallet.freezeAmount }}</span>
        </div>
      </div>

      <div class="balance-item">
        <div class="balance-item-title">
          <span class="title-tips">{{ $t("shop.totalSettlementAmount") }}</span>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('shop.totalSettlementAmountTip2')"
            placement="right"
          >
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="balance-show">
          <span class="balance-num">{{ shopWallet.totalSettledAmount }}</span>
        </div>
      </div>
    </div>

    <el-tabs>
      <el-tab-pane :label="$t('shop.recentTransactions')">
        <trading ref="tradingRef" />
      </el-tab-pane>
      <el-tab-pane :label="$t('shop.withdrawalRecord')">
        <withdrawal ref="withdrawalRef" />
      </el-tab-pane>
    </el-tabs>
    <cashWithdrawal
      v-if="isCashWithdrawal"
      ref="cashWithdrawalRef"
      @refresh-data-list="onRefreshChange"
    />
  </div>
</template>

<script setup>
import { isAuth } from '@/utils'
import cashWithdrawal from './components/cash-withdrawal/index.vue'
import trading from './components/trading/index.vue'
import withdrawal from './components/withdrawal/index.vue'

onMounted(() => {
  onGetShopWalletInfo()
})

const shopWallet = ref({})
const isCashWithdrawal = ref(false) // 提现弹框
const onGetShopWalletInfo = () => {
  http({
    url: http.adornUrl('/shop/shopWallet/info'),
    method: 'get',
    params: http.adornParams()
  }).then(({ data }) => {
    shopWallet.value = data
  })
}
// 提现
const tradingRef = ref(null)
const cashWithdrawalRef = ref(null)
const onApplyWithdrawCash = () => {
  tradingRef.value?.onGetData()
  isCashWithdrawal.value = true
  nextTick(() => {
    cashWithdrawalRef.value.init()
  })
}
const withdrawalRef = ref(null)
const onRefreshChange = () => {
  onGetShopWalletInfo()
  withdrawalRef.value?.onGetDataList()
  tradingRef.value?.onGetData()
}

</script>
<style lang="scss" scoped>
.page-shop-wallet-log {
  .balance-wrapper {
    display: flex;
    background: #f7f8fa;
    padding: 30px 30px;
    margin-bottom: 20px;
    justify-content: space-between;
  }
  .balance-item {
    display: inline-block;
  }
  .balance-item-title {
    padding-bottom: 25px;
  }
  .title-tips {
    vertical-align: middle;
  }
  .balance-show {
    text-align: left;
  }
  .balance-num {
    font-size: 28px;
    vertical-align: middle;
    margin-right: 30px;
  }
}
</style>
