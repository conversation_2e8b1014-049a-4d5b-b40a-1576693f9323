<template>
  <el-dialog
    v-model="visible"
    :title="
      !dataForm.refundAddrId
        ? $t('shop.newShipAdd')
        : $t('shop.modifyShipAdd')
    "
    :close-on-click-modal="false"
    :width="dialogWidth"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      :inline="true"
      label-width="80px"
      @submit.prevent
      @keyup.enter="onSubmit()"
    >
      <el-form-item
        class="addressee"
        :label="$t('publics.addressee')"
        prop="receiverName"
        :label-width="labelWidth"
      >
        <el-input
          v-model="dataForm.receiverName"
          :placeholder="$t('shop.consigneeName')"
          maxlength="30"

          style="width: 250px"
          show-word-limit
        />
      </el-form-item>

      <el-form-item
        :label="$t('publics.mobilePhone')"
        prop="receiverMobile"
        :label-width="labelWidth"
      >
        <el-input
          v-model="dataForm.receiverMobile"
          maxlength="11"
          style="width: 250px"
          :placeholder="$t('publics.mobilePhone')"
        />
      </el-form-item>
      <el-form-item
        class="mobilePhone"
        :label="$t('shop.companyLandline')"
        prop="receiverTelephone"
        :label-width="labelWidth"
      >
        <el-input
          v-model="receiverTelephonePrefix"
          maxlength="4"
          style="width: 100px"
          :placeholder="$t('admin.areaCode')"
        />
        ——
        <el-input
          v-model="receiverTelephone"
          maxlength="8"
          style="width: 250px"
          :placeholder="$t('shop.companyLandline')"
        />
      </el-form-item>
      <el-form-item
        :label-width="labelWidth"
        :label="$t('address.postalCode')"
        prop="postCode"
      >
        <el-input
          v-model="dataForm.postCode"

          style="width: 250px"
          :placeholder="$t('address.postalCode')"
          maxlength="9"
        />
      </el-form-item>
      <el-form-item
        :label="$t('address.province')"
        required
        :label-width="labelWidth"
      >
        <el-col :span="8">
          <el-form-item prop="provinceId">
            <el-select
              v-model="dataForm.provinceId"
              :placeholder="$t('tip.select')"
              style="width: 150px"
              @change="onSelectProvince"
            >
              <el-option
                v-for="province in provinceList"
                :key="province.areaId"
                :label="province.areaName"
                :value="province.areaId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="cityId">
            <el-select
              v-model="dataForm.cityId"
              :placeholder="$t('tip.select')"
              style="width: 150px"
              @change="onSelectCity"
            >
              <el-option
                v-for="city in cityList"
                :key="city.areaId"
                :label="city.areaName"
                :value="city.areaId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="areaId">
            <el-select
              v-model="dataForm.areaId"
              :placeholder="$t('tip.select')"
              style="width: 150px"
              @change="onValidateField('areaId')"
            >
              <el-option
                v-for="area in areaList"
                :key="area.areaId"
                :label="area.areaName"
                :value="area.areaId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item
        :label-width="labelWidth"
        :label="$t('address.detailed')"
        prop="addr"
      >
        <el-input
          v-model="dataForm.addr"

          :placeholder="$t('address.detailed')"
          maxlength="50"
          style="width: 600px"
        />
      </el-form-item>
      <el-form-item
        :label-width="labelWidth"
        :label="$t('address.defaultRefundAddr')"
        prop=""
      >
        <el-checkbox v-model="defaultAddr">
          {{ $t('address.defaultRefundAddr') }}
        </el-checkbox>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <div
          class="default-btn"
          @click="visible = false"
        >{{ $t("crud.filter.cancelBtn") }}</div>
        <div
          class="default-btn primary-btn"
          @click="onSubmit()"
        >{{ $t("crud.filter.submitBtn") }}</div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Debounce } from '@/utils/debounce'
import { isPhone, isPhoneStar } from '@/utils/validate'

const emit = defineEmits(['refreshDataList'])

const dataForm = reactive({
  refundAddrId: null,
  addrId: 0,
  addr: '',
  receiverName: '',
  receiverMobile: '',
  receiverTelephone: '',
  postCode: '',
  area: '',
  city: '',
  province: '',
  areaId: null,
  cityId: null,
  provinceId: null,
  defaultAddr: 0
})
const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})

const validateReceiverName = (rule, value, callback) => {
  if (!value.trim()) {
    dataForm.receiverName = ''
    callback(new Error($t('shop.coneeNameCanEmpty')))
  } else {
    callback()
  }
}
// eslint-disable-next-line no-unused-vars
const validatePhone = (rule, value, callback) => {
  if (!onCheckPhonePrefix()) {
    callback(new Error($t('shop.pleeNormaeNumF')))
  } else {
    callback()
  }
}
const validateAddr = (rule, value, callback) => {
  if (!value.trim()) {
    dataForm.addr = ''
    callback(new Error($t('shop.addressCannotBeEmpty')))
  } else {
    callback()
  }
}
const validateNumber = (rule, value, callback) => {
  if (value && !/^[0-9]\d*$/.test(value)) {
    callback(new Error($t('shop.pleaseInputNumber')))
  } else {
    callback()
  }
}
const validateReceiverTelephone = (rule, value, callback) => {
  const validateVal = receiverTelephonePrefix.value + '-' + receiverTelephone.value
  if (value && (dataForm.refundAddrId ? !isPhoneStar(validateVal) : !isPhone(validateVal))) {
    callback(new Error($t('shop.pleaseInputNumber')))
  } else {
    callback()
  }
}
const dataRule = reactive({
  receiverName: [
    { required: true, message: $t('shop.coneeNameCanEmpty'), trigger: 'blur' },
    { validator: validateReceiverName, trigger: 'blur' }
  ],
  addr: [{ required: true, message: $t('shop.addressCannotBeEmpty'), trigger: 'blur' },
    { validator: validateAddr, trigger: 'blur' }
  ],
  cityId: [{ required: true, message: $t('shop.cityCannotBeEmpty'), trigger: 'blur' }],
  provinceId: [
    { required: true, message: $t('shop.provinceCannotBeEmpty'), trigger: 'blur' }
  ],
  areaId: [{ required: true, message: $t('shop.districtCounEmpty'), trigger: 'blur' }],
  receiverMobile: [
    { required: true, message: $t('sys.mobilePhoneNoNull'), trigger: 'blur' },
    { validator: validatePhone, trigger: 'blur' }
  ],
  receiverTelephone: [{ validator: validateReceiverTelephone, trigger: 'blur' }],
  postCode: [{ validator: validateNumber, trigger: 'blur' }]
})
const dialogWidth = ref('')
const defWidth = localStorage.getItem('bbcLang') === 'en' ? 850 : 750
const labelWidth = localStorage.getItem('bbcLang') === 'en' ? '130px' : '120px'

onMounted(() => {
  dialogWidth.value = onSetDialogWidth(defWidth)
  window.onresize = () => {
    return (() => {
      dialogWidth.value = onSetDialogWidth(defWidth)
    })()
  }
})
const onSetDialogWidth = function (defWidthVal) {
  const val = document.body.clientWidth
  const def = defWidthVal || 850 // 默认宽度
  if (val < def) {
    return '97%'
  } else {
    return def + 'px'
  }
}

const dataFormRef = ref(null)
const visible = ref(false)
const defaultAddr = ref(false)
const provinceList = ref([])
const cityList = ref([])
const areaList = ref([])

const receiverTelephonePrefix = ref('')
const receiverTelephone = ref('')
watch([receiverTelephonePrefix, receiverTelephone], ([newPrefix, newPhone]) => {
  newPrefix ??= ''
  newPhone ??= ''
  dataForm.receiverTelephone = newPrefix + (newPrefix && newPhone ? '-' : '') + newPhone
})
const init = (id) => {
  defaultAddr.value = false
  dataForm.refundAddrId = id || 0
  visible.value = true
  nextTick(() => {
    dataFormRef.value?.resetFields()
    cityList.value = []
    areaList.value = []
    dataForm.provinceId = null
    dataForm.cityId = null
    dataForm.areaId = null
    receiverTelephonePrefix.value = ''
    receiverTelephone.value = ''
  })
  onListAreaByParentId().then(({ data }) => {
    provinceList.value = data
  })
  if (dataForm.refundAddrId) {
    http({
      url: http.adornUrl(
        `/shop/refundAddr/info/${dataForm.refundAddrId}`
      ),
      method: 'get',
      params: http.adornParams()
    }).then(({ data }) => {
      dataForm.addr = data.addr
      dataForm.receiverMobile = data.receiverMobile
      if (data.receiverTelephone) {
        if (data.receiverTelephone.includes('-')) {
          [receiverTelephonePrefix.value, receiverTelephone.value] = data.receiverTelephone.split('-')
        } else {
          [receiverTelephonePrefix.value, receiverTelephone.value] = ['', data.receiverTelephone]
        }
      } else {
        [receiverTelephonePrefix.value, receiverTelephone.value] = ['', '']
      }
      // dataForm.receiverTelephone = data.receiverTelephone
      dataForm.areaId = data.areaId
      dataForm.cityId = data.cityId
      dataForm.provinceId = data.provinceId
      dataForm.receiverName = data.receiverName
      dataForm.postCode = data.postCode
      dataForm.defaultAddr = data.defaultAddr
      defaultAddr.value = data.defaultAddr === 1
      onListAreaByParentId(data.provinceId).then(({ data }) => {
        cityList.value = data
      })
      onListAreaByParentId(data.cityId).then(({ data }) => {
        areaList.value = data
      })
    })
  }
}
const onListAreaByParentId = (pid) => {
  let paramData = {}
  if (!pid) {
    paramData = { level: 1 }
  } else {
    paramData = { pid }
  }
  return http({
    url: http.adornUrl('/admin/area/listByPid'),
    method: 'get',
    params: http.adornParams(paramData)
  })
}
const onGetCurrentChild = (curList, curId) => {
  for (const item of curList) {
    if (item.areaId === curId) {
      return {
        curNode: item,
        areas: item.areas
      }
    }
  }
}

// 验证单个字段信息
const onValidateField = (attr) => {
  dataFormRef.value?.validateField(attr)
}

// 选择省
const onSelectProvince = (val) => {
  dataForm.cityId = null
  dataForm.city = ''
  dataForm.areaId = null
  areaList.value = []
  onValidateField('provinceId')
  // 获取城市的select
  const { curNode, areas } = onGetCurrentChild(provinceList.value, val)
  if (areas) {
    cityList.value = areas
  } else {
    onListAreaByParentId(val).then(({ data }) => {
      curNode.areas = data
      cityList.value = data
    })
  }
}
// 选择市
const onSelectCity = (val) => {
  dataForm.areaId = null
  dataForm.area = ''
  onValidateField('cityId')
  // 获取区的select
  const { curNode, areas } = onGetCurrentChild(cityList.value, val)
  if (areas) {
    areaList.value = areas
  } else {
    onListAreaByParentId(val).then(({ data }) => {
      curNode.areas = data
      areaList.value = data
    })
  }
}
const onCheckPhonePrefix = () => {
  if (dataForm.receiverMobile) {
    const reg = /^(?:(?:\+|00)86)?1\d{2}([\d*]{4})\d{4}$/
    if (!reg.test(dataForm.receiverMobile)) {
      return false
    }
  }
  return true
}
// 表单提交
const isSubmitting = ref(false) // 正在提交
const onSubmit = Debounce(() => {
  for (let i = 0; i < provinceList.value.length; i++) {
    if (provinceList.value[i].areaId === dataForm.provinceId) {
      // 将省名字保存起来
      dataForm.province = provinceList.value[i].areaName
    }
  }
  for (let i = 0; i < cityList.value.length; i++) {
    if (cityList.value[i].areaId === dataForm.cityId) {
      // 将市名字保存起来
      dataForm.city = cityList.value[i].areaName
    }
  }
  for (let i = 0; i < areaList.value.length; i++) {
    if (areaList.value[i].areaId === dataForm.areaId) {
      // 将市名字保存起来
      dataForm.area = areaList.value[i].areaName
    }
  }
  // 将是否设为默认地址保存起来
  dataForm.defaultAddr = defaultAddr.value ? 1 : 0
  dataFormRef.value?.validate(valid => {
    if (valid) {
      if (isSubmitting.value) {
        return
      }
      isSubmitting.value = true
      http({
        url: http.adornUrl('/shop/refundAddr'),
        method: dataForm.refundAddrId ? 'put' : 'post',
        data: http.adornData({
          refundAddrId: dataForm.refundAddrId || undefined,
          addr: dataForm.addr,
          receiverName: dataForm.receiverName,
          receiverMobile: dataForm.receiverMobile,
          receiverTelephone: dataForm.receiverTelephone,
          area: dataForm.area,
          city: dataForm.city,
          province: dataForm.province,
          areaId: dataForm.areaId,
          cityId: dataForm.cityId,
          provinceId: dataForm.provinceId,
          postCode: dataForm.postCode,
          defaultAddr: dataForm.defaultAddr
        })
      }).then(() => {
        isSubmitting.value = false
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList', page)
          }
        })
      }).catch(() => {
        isSubmitting.value = false
      })
    }
  })
}, 1500)
defineExpose({
  init
})
</script>

<style scoped lang="scss">
:deep(.el-form-item__error) {
  width: max-content;
}
</style>
