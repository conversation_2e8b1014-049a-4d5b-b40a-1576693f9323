<template>
  <div class="page-notice">
    <div class="notice-list-mod">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form
          ref="searchFormRef"
          :inline="true"
          class="search-form"
          :model="searchForm"
          label-width="auto"
          @submit.prevent
        >
          <div class="input-row">
            <el-form-item
              prop="title"
              :label="$t('shop.announcementTitle')+':'"
            >
              <el-input
                v-model="searchForm.title"
                type="text"
                clearable
                :placeholder="$t('shop.announcementTitle')"
              />
            </el-form-item>
            <el-form-item
              prop="status"
              :label="$t('product.status')+':'"
            >
              <el-select
                v-model="searchForm.status"
                clearable
                :placeholder="$t('product.status')"
              >
                <el-option
                  v-for="item in status"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              prop="isTop"
              :label="$t('shop.isTop')+':'"
            >
              <el-select
                v-model="searchForm.isTop"
                clearable
                :placeholder="$t('shop.isTop')"
              >
                <el-option
                  v-for="item in isTopOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <div
                class="default-btn primary-btn"
                @click="onSearch(true)"
              >
                {{ $t('crud.searchBtn') }}
              </div>
              <div
                class="default-btn"
                @click="onResetForm()"
              >
                {{ $t('shop.resetMap') }}
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 搜索栏end -->
      <!-- 表格 -->
      <div class="main-container">
        <div class="operation-bar">
          <div
            v-if="isAuth('shop:notice:save')"
            class="default-btn primary-btn"
            @click="onAddOrUpdate(0)"
          >
            {{ $t('crud.addBtn') }}
          </div>
        </div>
        <div class="table-con notice-table">
          <el-table
            :data="dataList"
            header-cell-class-name="table-header"
            row-class-name="table-row-low"
            style="width: 100%"
          >
            <el-table-column
              :label="$t('number')"
              type="index"
              align="left"
              width="150"
            />
            <el-table-column
              align="left"
              prop="title"
              :label="$t('shop.announcementTitle')"
            >
              <template #default="scope">
                <span class="table-cell-text">{{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="left"
              :label="$t('product.status')"
            >
              <template #default="scope">
                <span>{{ [$t('publics.cancel'),$t('publics.publicar')][scope.row.status] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="left"
              prop="isTop"
              :label="$t('product.isTop')"
            >
              <template #default="scope">
                <span>{{ [$t('publics.no'),$t('publics.yes')][scope.row.isTop] }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="left"
              prop="isTop"
              width="150"
              :label="$t('publics.releaseTime')"
            >
              <template #default="scope">
                <span>{{ scope.row.publishTime }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              :label="$t('crud.menu')"
              width="200"
              fixed="right"
            >
              <template #default="scope">
                <div class="text-btn-con">
                  <div
                    v-if="isAuth('shop:notice:update')"
                    class="default-btn text-btn"
                    @click="onAddOrUpdate(scope.row.id)"
                  >
                    {{ $t('text.updateBtn') }}
                  </div>
                  <div
                    v-if="isAuth('shop:notice:delete')"
                    class="default-btn text-btn"
                    @click="onDelete(scope.row.id)"
                  >
                    {{ $t('text.delBtn') }}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="page.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.pageSize"
          :total="page.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onPageSizeChange"
          @current-change="onPageChange"
        />
      </div>
      <!-- 表格end -->
    </div>
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="onRefreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import { isAuth } from '@/utils/index.js'
import AddOrUpdate from './add-or-update.vue'

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const isTopOptions = [
  {
    label: $t('publics.no'),
    value: 0
  }, {
    label: $t('publics.yes'),
    value: 1
  }
]
const status = [
  {
    label: $t('publics.cancel'),
    value: 0
  }, {
    label: $t('publics.publicar'),
    value: 1
  }
]
onMounted(() => {
  onGetDataList()
})

let tempSearchForm = null // 保存上次点击查询的请求条件
const dataList = ref([])
const searchForm = reactive({
  title: null,
  status: null,
  isTop: null
})
const onGetDataList = (pageParam, newData = false) => {
  if (page) {
    const size = Math.ceil(page.total / page.pageSize)
    page.currentPage = (page.currentPage > size ? size : page.currentPage) || 1
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/shop/notice/page'),
    method: 'get',
    params: http.adornParams(Object.assign({
      current: pageParam == null ? page.currentPage : pageParam.currentPage,
      size: pageParam == null ? page.pageSize : pageParam.pageSize
    }, tempSearchForm))
  }).then(({ data }) => {
    dataList.value = data.records
    page.total = data.total
  })
}
// 新增 / 修改
const addOrUpdateRef = ref(null)
const addOrUpdateVisible = ref(false)
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id)
  })
}
const onDelete = (id) => {
  const ids = id ? [id] : []
  ElMessageBox.confirm($t('admin.isDeleOper') + '?', $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/shop/notice/' + id),
      method: 'delete',
      data: http.adornData({})
    }).then(() => {
      page.total = page.total - ids.length
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          onRefreshChange()
        }
      })
    })
  }).catch(() => { })
}
// 刷新回调用
const onRefreshChange = () => {
  onGetDataList(page)
}
const onSearch = (newData = false) => {
  page.currentPage = 1
  page.pageSize = 10
  onGetDataList(page, newData)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  onGetDataList()
}
const onPageChange = (val) => {
  page.currentPage = val
  onGetDataList()
}
const searchFormRef = ref(null)
const onResetForm = () => {
  searchFormRef.value.resetFields()
}

</script>

<style lang="scss" scoped>

</style>
