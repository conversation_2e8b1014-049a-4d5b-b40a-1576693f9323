<template>
  <el-dialog
    v-model="visible"
    :title=" !dataForm.id ? $t('crud.addTitle') : $t('temp.modify') "
    :close-on-click-modal="false"
    :width="dialogWidth"
    :z-index="1300"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRule"
      label-width="80px"
      @submit.prevent
      @keyup.enter="onSubmit()"
    >
      <el-form-item
        :label="$t('shop.announcementTitle')"
        prop="title"
      >
        <el-input
          v-model="dataForm.title"
          maxlength="36"
          show-word-limit
        />
      </el-form-item>
      <el-form-item
        :label="$t('product.status')"
        prop="status"
      >
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="1">
            {{ $t("publics.publicar") }}
          </el-radio>
          <el-radio :label="0">
            {{ $t("publics.cancel") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="$t('publics.isTop')"
        prop="isTop"
      >
        <el-radio-group v-model="dataForm.isTop">
          <el-radio :label="1">
            {{ $t("publics.yes") }}
          </el-radio>
          <el-radio :label="0">
            {{ $t("publics.no") }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="$t('shop.noticeContent')"
        prop="content"
      >
        <tiny-mce
          v-if="visible"
          ref="tinyMceRef"
          v-model="dataForm.content"
          :height="277"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span

        class="dialog-footer"
      >
        <div
          class="default-btn"
          @click="visible = false"
        >{{ $t("crud.filter.cancelBtn") }}</div>
        <div
          class="default-btn primary-btn"
          @click="onSubmit()"
        >{{ $t("crud.filter.submitBtn") }}</div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { validHtmlLength, isHtmlNull } from '@/utils/index.js'
import { Debounce } from '@/utils/debounce'

const emit = defineEmits(['refreshDataList'])

const validateTitle = (rule, value, callback) => {
  if (!value.trim()) {
    callback(new Error($t('shop.titCanNoBlank')))
  } else {
    callback()
  }
}
// eslint-disable-next-line no-unused-vars
const validateContent = (rule, value, callback) => {
  if (isHtmlNull(value)) {
    callback(new Error($t('product.content')))
  } else {
    callback()
  }
}
const dataRule = reactive({
  title: [
    { required: true, message: $t('shop.titCanNoBlank'), trigger: 'blur' },
    { validator: validateTitle, trigger: 'blur' }
  ],
  content: [
    { required: true, message: $t('product.content'), trigger: 'blur' },
    { validator: validateContent, trigger: 'blur' }
  ]
})

const dialogWidth = ref('740px')
const defWidth = 740
onMounted(() => {
  dialogWidth.value = setDialogWidth(defWidth)
  window.onresize = () => {
    return (() => {
      dialogWidth.value = setDialogWidth(defWidth)
    })()
  }
})
const setDialogWidth = function (defWidthVal) {
  const val = document.body.clientWidth
  const def = defWidthVal || 850 // 默认宽度
  if (val < def) {
    return '97%'
  } else {
    return def + 'px'
  }
}

const dataFormRef = ref(null)
const tinyMceRef = ref(null)
const dataForm = ref({
  title: null,
  content: null,
  url: null,
  status: 1,
  isTop: 0
})

const resetForm = () => {
  dataForm.value = {
    title: null,
    content: null,
    url: null,
    status: 1,
    isTop: 0
  }
}

const visible = ref(false)
const init = (id) => {
  dataForm.value.id = id || 0
  nextTick(() => {
    dataFormRef.value?.resetFields()
    if (dataForm.value.id) {
      http({
        url: http.adornUrl('/shop/notice/info/' + dataForm.value.id),
        method: 'get',
        params: http.adornParams()
      }).then(({ data }) => {
        dataForm.value = data
        visible.value = true
      })
    } else {
      resetForm()
      visible.value = true
    }
  })
}
// 表单提交
const onSubmit = Debounce(() => {
  dataFormRef.value?.validate((valid) => {
    if (valid) {
      if (validHtmlLength(dataForm.value.content)) {
        return
      }
      http({
        url: http.adornUrl('/shop/notice'),
        method: dataForm.value.id ? 'put' : 'post',
        data: http.adornData(dataForm.value)
      }).then(() => {
        ElMessage({
          message: $t('publics.operation'),
          type: 'success',
          duration: 1500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
            dataForm.value.content = ''
          }
        })
      })
    }
  })
}, 1500)
defineExpose({ init })
</script>
