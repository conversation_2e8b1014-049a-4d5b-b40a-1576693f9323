<template>
  <div class="page-hot-searcch">
    <div class="search-bar">
      <el-form
        ref="searchFormRef"
        :inline="true"
        class="search-form"
        :model="searchForm"
        label-width="auto"

        @submit.prevent
      >
        <div class="input-row">
          <el-form-item
            prop="title"
            :label="$t('shop.hotTitle')+':'"
          >
            <el-input
              v-model="searchForm.title"
              type="text"
              clearable
              :placeholder="$t('shop.hotTitle')"
            />
          </el-form-item>
          <el-form-item
            prop="content"
            :label="$t('shop.hotContent')+':'"
          >
            <el-input
              v-model="searchForm.content"
              type="text"
              clearable
              :placeholder="$t('shop.hotContent')"
            />
          </el-form-item>
          <el-form-item
            prop="status"
            :label="$t('shop.enableStatus')+':'"
          >
            <el-select
              v-model="searchForm.status"
              clearable
              :placeholder="$t('shop.enableStatus')"
            >
              <el-option
                v-for="item in enableStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div
              class="default-btn primary-btn"
              @click="onSearch(true)"
            >
              {{ $t('crud.searchBtn') }}
            </div>
            <div
              class="default-btn"
              @click="onResetSearch()"
            >
              {{ $t('shop.resetMap') }}
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="main-container">
      <div class="operation-bar">
        <el-checkbox
          v-if="isAuth('admin:indexImg:delete')"
          v-model="selectAll"
          class="all-check-btn"
          :disabled="dataList.length===0"
          @change="onSelectAll"
        >
          {{ $t('publics.selectAll') }}
        </el-checkbox>
        <span
          v-if="dataListSelections.length"
          class="had-selected"
        >{{ $t('publics.selected') }} {{ dataListSelections.length }}</span>
        <div
          v-if="isAuth('admin:hotSearch:save')"
          class="default-btn primary-btn"
          @click="onAddOrUpdate()"
        >
          {{ $t("crud.addTitle") }}
        </div>
        <div
          v-if="isAuth('admin:hotSearch:delete')"
          :class="[!dataListSelections.length?'disabled-btn':'','default-btn']"
          @click.stop="onDelete"
        >
          {{ $t("sys.batchDelete") }}
        </div>
      </div>
      <div class="table-con">
        <el-table
          ref="hotTableRef"
          :data="dataList"
          header-cell-class-name="table-header"
          row-class-name="table-row-low"
          style="width: 100%"
          @selection-change="onSelectionChange"
          @select-all="onClearAllSelection"
        >
          <el-table-column
            type="selection"
            width="65"
          />
          <el-table-column
            :label="$t('shop.hotTitle')"
            align="left"
            prop="title"
          >
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="content"
            align="left"
            :label="$t('shop.hotContent')"
          >
            <template #default="scope">
              <span class="table-cell-text">{{ scope.row.content }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="recDate"
            align="left"
            :label="$t('shop.recDate')"
          />
          <el-table-column
            align="left"
            prop="seq"
            :label="$t('temp.sequence')"
          />
          <el-table-column
            align="left"
            :label="$t('shop.enableStatus')"
          >
            <template #default="scope">
              <span>{{ [$t('live.offline'),$t('publics.normal')][scope.row.status] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('crud.menu')"
            fixed="right"
            width="180"
          >
            <template #default="scope">
              <div class="text-btn-con">
                <div
                  v-if="isAuth('admin:hotSearch:update')"
                  class="default-btn text-btn"
                  @click="onAddOrUpdate(scope.row)"
                >
                  {{ $t('text.updateBtn') }}
                </div>
                <div
                  v-if="isAuth('admin:hotSearch:delete')"
                  class="default-btn text-btn"
                  @click="onDelete(scope.row,scope.index)"
                >
                  {{ $t('text.delBtn') }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        :current-page="page.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.pageSize"
        :total="page.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdateRef"
      @refresh-data-list="onRefreshChange"
    />
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import { isAuth } from '@/utils/index.js'
import AddOrUpdate from './add-or-update.vue'

const page = reactive({
  total: 0, // 总页数
  currentPage: 1, // 当前页数
  pageSize: 10 // 每页显示多少条
})
const searchForm = reactive({
  title: '',
  content: '',
  status: null
})
const enableStatus = [
  {
    label: $t('live.offline'),
    value: 0
  }, {
    label: $t('publics.normal'),
    value: 1
  }
]
onMounted(() => {
  onGetDataList(page)
})

// 清空全选框选中状态
const hotTableRef = ref(null)
const dataList = ref([])
const onClearAllSelection = () => {
  if (!dataList.value.length) {
    hotTableRef.value?.clearSelection()
  }
}
// 获取数据列表
let tempSearchForm = null // 保存上次点击查询的请求条件
const onGetDataList = (pageParam, newData = false) => {
  if (page) {
    const size = Math.ceil(page.total / page.pageSize)
    page.currentPage = (page.currentPage > size ? size : page.currentPage) || 1
  }
  if (newData || !tempSearchForm) {
    tempSearchForm = JSON.parse(JSON.stringify(searchForm))
  }
  http({
    url: http.adornUrl('/admin/hotSearch/page'),
    method: 'get',
    params: http.adornParams(Object.assign({
      current: pageParam ? pageParam.currentPage : 1,
      size: pageParam ? pageParam.pageSize : 20
    }, tempSearchForm))
  }).then(({ data }) => {
    page.total = data.total
    page.pageSize = data.size
    page.currentPage = data.current
    dataList.value = data.records
  })
}
/**
 * 全选按钮
 */
const dataListRef = ref(null)
const selectAll = ref(false)
const onSelectAll = () => {
  if (hotTableRef.value.selection?.length < dataListRef.value?.length) {
    selectAll.value = true
  } else {
    selectAll.value = false
  }
  hotTableRef.value?.toggleAllSelection()
}
// 多选回调
const dataListSelections = ref([])
const onSelectionChange = (list) => {
  dataListSelections.value = list
  selectAll.value = list.length === dataList.value.length
}
// 新增 / 修改
const addOrUpdateRef = ref(null)
const addOrUpdateVisible = ref(false)
const onAddOrUpdate = (id) => {
  addOrUpdateVisible.value = true
  nextTick(() => {
    addOrUpdateRef.value?.init(id)
  })
}
// 点击查询
const onSearch = (newData = false) => {
  page.currentPage = 1
  page.pageSize = 10
  onGetDataList(page, newData)
}
// 删除
const onDelete = (row) => {
  const ids = row.hotSearchId ? [row.hotSearchId] : dataListSelections.value.map(item => {
    return item.hotSearchId
  })
  if (!ids.length) {
    return
  }
  ElMessageBox.confirm(`${$t('sys.makeSure')}[${row.hotSearchId ? $t('text.delBtn') : $t('sys.batchDelete')}]${$t('text.menu')}?`, $t('text.tips'), {
    confirmButtonText: $t('crud.filter.submitBtn'),
    cancelButtonText: $t('crud.filter.cancelBtn'),
    type: 'warning'
  }).then(() => {
    http({
      url: http.adornUrl('/admin/hotSearch'),
      method: 'delete',
      data: http.adornData(ids, false)
    }).then(() => {
      page.total = page.total - ids.length
      ElMessage({
        message: $t('publics.operation'),
        type: 'success',
        duration: 1500,
        onClose: () => {
          onRefreshChange()
        }
      })
    })
  }).catch(() => { })
}
// 刷新回调用
const onRefreshChange = () => {
  onGetDataList(page)
}
const onPageSizeChange = (val) => {
  page.pageSize = val
  onGetDataList(page)
}
const onPageChange = (val) => {
  page.currentPage = val
  onGetDataList(page)
}
const searchFormRef = ref(null)
const onResetSearch = () => {
  searchFormRef.value.resetFields()
}

</script>
<style lang="scss" scoped>

</style>
